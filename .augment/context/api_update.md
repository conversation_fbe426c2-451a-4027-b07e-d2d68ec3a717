# New Updated API (V2)

## 1. auth/token

### URL

https://stg.workspace.dge.gov.ae/app/v2/auth/token/

### Request Body

```json
{
  "emailAddress": "<EMAIL>",
  "password": "2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG"
}
```

### Response Body

```json
{
  "data": {
    "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1MTg5Njc4OCwiaWF0IjoxNzUxODEwMzg4LCJqdGkiOiJhNDU4MTBlMjgyZWY0NzExOGZiYTVlYmUwYTFhZWQ1NCIsInVzZXJfaWQiOjEyfQ.DtfvhpkK7X5WsKhGNgc164XqM2XvjWous_bttvrqMVA",
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxODUzNTg4LCJpYXQiOjE3NTE4MTAzODgsImp0aSI6Ijc2MzY2ZjM4YmM0ZTRmZWJiYWJkNDM1OTNhOTBkYzViIiwidXNlcl9pZCI6MTJ9.KbyqCmrD6c9Z3P5lqIpQlK9eAPowGL39l9h00bgcsGY"
  },
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 2. crm/references

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/references/

### Request Body

(Empty - GET request)

### Response Body

```json
{
  "data": [
    {
      "key": "6",
      "value": "I want to report an incident",
      "categories": [
        {
          "key": "61",
          "value": "Technical",
          "services": [
            { "key": "1", "value": "Course Access" },
            { "key": "2", "value": "Certificate Download" },
            { "key": "3", "value": "Update Profile" },
            { "key": "4", "value": "Attendance Confirmation" },
            { "key": "5", "value": "Refer A Course" },
            { "key": "6", "value": "My Streak" },
            { "key": "7", "value": "Course Enrollment" },
            { "key": "8", "value": "Learning Record" },
            { "key": "9", "value": "Recommend a Talent" },
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" }
          ]
        },
        {
          "key": "62",
          "value": "Non-Technical",
          "services": [
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" },
            { "key": "16", "value": "Assessment" },
            { "key": "17", "value": "Course" }
          ]
        },
        {
          "key": "63",
          "value": "Gov Academy Mobile APP",
          "services": [{ "key": "10", "value": "Report A Bug" }]
        }
      ]
    },
    {
      "key": "8",
      "value": "I want to raise a complaint",
      "categories": [
        {
          "key": "81",
          "value": "Register a Complaint",
          "services": [
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" },
            { "key": "16", "value": "Assessment" },
            { "key": "17", "value": "Course" }
          ]
        }
      ]
    },
    {
      "key": "4",
      "value": "I want to make a suggestion",
      "categories": [
        {
          "key": "41",
          "value": "Register a Suggestion",
          "services": [
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" },
            { "key": "16", "value": "Assessment" },
            { "key": "17", "value": "Course" }
          ]
        }
      ]
    },
    {
      "key": "5",
      "value": "I want to give a compliment",
      "categories": [
        {
          "key": "51",
          "value": "Register a Compliment",
          "services": [
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" },
            { "key": "16", "value": "Assessment" },
            { "key": "17", "value": "Course" }
          ]
        }
      ]
    },
    {
      "key": "1",
      "value": "I want to request more information",
      "categories": [
        {
          "key": "11",
          "value": "Request Information",
          "services": [
            { "key": "11", "value": "Academic Calendar" },
            { "key": "12", "value": "Mentorship" },
            { "key": "13", "value": "Coaching" },
            { "key": "14", "value": "Scholarship" },
            { "key": "15", "value": "Social" },
            { "key": "16", "value": "Assessment" },
            { "key": "17", "value": "Course" }
          ]
        }
      ]
    }
  ],
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 3. crm/cases (all)

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/cases/?emirates_id=[NEEDS_VALUE]

### Request Body

(Empty - GET request)

### Response Body

```json
{
  "data": [
    {
      "number": "250728-0000001",
      "serviceNumber": "250728-65836",
      "title": "number brand - this is in v2 - not pedning",
      "description": "This is a test case description",
      "type": {
        "key": "6",
        "value": "أود التقديم على بلاغ"
      },
      "category": {
        "key": "61",
        "value": "مشكلة تقنية"
      },
      "serviceName": {
        "key": "1",
        "value": "صلاحية الوصول الى الدورة"
      },
      "status": {
        "key": "INT_STAT_000",
        "value": "تم التقديم"
      },
      "comments": [
        {
          "createdAt": "2025-07-28T07:13:02.486757+00:00",
          "commentBody": "تم استلام طلبكم بنجاح وهو قيد المراجعة.",
          "status": {
            "key": "INT_STAT_000",
            "value": "تم التقديم"
          }
        }
      ],
      "attachments": [],
      "daysAgo": 0,
      "createdAt": "2025-07-28T07:13:02.467281Z",
      "updatedAt": "2025-07-28T07:14:30.141137Z",
      "emiratesId": "784199029524750"
    }
  ],
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 4. crm/cases (details)

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/cases/[CASE_NUMBER]/

### Request Body

(Empty - GET request)

### Response Body

```json
{
  "data": {
    "number": "250706-0000001",
    "serviceNumber": "PENDING-ca2737cd",
    "title": "number brand",
    "description": "This is a test case description",
    "type": {
      "key": "6",
      "value": "أود التقديم على بلاغ"
    },
    "category": {
      "key": "61",
      "value": "مشكلة تقنية"
    },
    "serviceName": {
      "key": "1",
      "value": "صلاحية الوصول الى الدورة"
    },
    "status": {
      "key": "INT_STAT_000",
      "value": "تم التقديم"
    },
    "comments": [],
    "attachments": [],
    "daysAgo": 0,
    "createdAt": "2025-07-06T14:00:26.190571Z",
    "updatedAt": "2025-07-06T14:00:26.224870Z",
    "emiratesId": "000-0000-0000000-1"
  },
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 5. crm/cases (create)

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/cases/

### Request Body

(The request body in the collection is commented out, but based on the saved response, it should be):

```json
{
  "emiratesId": "784200112345678",
  "englishFirstName": "John",
  "englishLastName": "Doe",
  "mobile": "971501234567",
  "emailAddress": "<EMAIL>",
  "prefCommChannel": 3,
  "languagePreference": 1,
  "caseTitle": "number brand",
  "caseDescription": "This is a test case description",
  "caseType": {
    "key": "6",
    "value": "I want to report an incident"
  },
  "caseCategory": {
    "key": "61",
    "value": "Technical"
  },
  "serviceName": {
    "key": "1",
    "value": "Course Access"
  },
  "region": {
    "key": "21234234",
    "value": "abu dhabi"
  },
  "area": {
    "key": "12131",
    "value": "abu dhabi"
  }
}
```

### Response Body

```json
{
  "data": {
    "caseNumber": "250706-0000001",
    "serviceNumber": "PENDING-ca2737cd",
    "status": {
      "key": "INT_STAT_000",
      "value": "Submitted"
    }
  },
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 6. crm/comments (create)

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/comments/

### Request Body

```json
{
  "emiratesId": "[NEEDS_VALUE]",
  "caseNumber": "[NEEDS_VALUE]",
  "commentBody": "I don't have more information, it is your problem, not mine"
}
```

### Response Body

```json
{
  "data": null,
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

## 7. crm/attachments (create)

### URL

https://stg.workspace.dge.gov.ae/app/v2/crm/attachments/

### Request Body

```json
{
  "emiratesId": "[NEEDS_VALUE]",
  "caseNumber": "[NEEDS_VALUE]",
  "attachmentName": "mytestdoc.txt",
  "attachmentFileBytes": "VEVTVCBURVNULSBURVNUIFRFU1Q=",
  "attachmentFileSize": 7266944,
  "attachmentExtension": "txt"
}
```

### Response Body

```json
{
  "data": null,
  "pagination": null,
  "operationResponse": {
    "success": true,
    "message": null,
    "errorCode": null,
    "errorDescription": null,
    "validationErrors": null
  }
}
```

# Old API (V1)

## 1. Login - Get JWT Token

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/auth/token

### Request Body

```json
{
  "email_address": "[TAMM_USERNAME_NEEDS_VALUE]",
  "password": "2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG"
}
```

### Response Body

```json
{
  "CaseTypes": [
    {
      "Key": "6",
      "Value": "I want to report an incident",
      "Categories": [
        {
          "Key": "1",
          "Value": "Technical",
          "Services": [
            {
              "Key": "1",
              "Value": "Course Access"
            },
            {
              "Key": "2",
              "Value": "Certificate Download"
            },
            {
              "Key": "3",
              "Value": "Update Profile"
            },
            {
              "Key": "4",
              "Value": "Attendance Confirmation"
            },
            {
              "Key": "5",
              "Value": "Refer A Course"
            },
            {
              "Key": "6",
              "Value": "My Streak"
            },
            {
              "Key": "7",
              "Value": "Course Enrollment"
            },
            {
              "Key": "8",
              "Value": "Learning Record"
            },
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "12",
              "Value": "Recommend a Talent"
            },
            {
              "Key": "13",
              "Value": "Social"
            }
          ]
        },
        {
          "Key": "2",
          "Value": "Non-Technical",
          "Services": [
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "13",
              "Value": "Social"
            },
            {
              "Key": "14",
              "Value": "Assessment"
            },
            {
              "Key": "15",
              "Value": "Course"
            }
          ]
        },
        {
          "Key": "3",
          "Value": "Gov Academy Mobile APP",
          "Services": [
            {
              "Key": "16",
              "Value": "Report A Bug"
            }
          ]
        }
      ]
    },
    {
      "Key": "8",
      "Value": "I want to raise a complaint",
      "Categories": [
        {
          "Key": "4",
          "Value": "Register a Complaint",
          "Services": [
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "13",
              "Value": "Social"
            },
            {
              "Key": "14",
              "Value": "Assessment"
            },
            {
              "Key": "15",
              "Value": "Course"
            }
          ]
        }
      ]
    },
    {
      "Key": "4",
      "Value": "I want to make a suggestion",
      "Categories": [
        {
          "Key": "5",
          "Value": "Register a Suggestion",
          "Services": [
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "13",
              "Value": "Social"
            },
            {
              "Key": "14",
              "Value": "Assessment"
            },
            {
              "Key": "15",
              "Value": "Course"
            }
          ]
        }
      ]
    },
    {
      "Key": "5",
      "Value": "I want to give a compliment",
      "Categories": [
        {
          "Key": "7",
          "Value": "Register a Compliment",
          "Services": [
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "13",
              "Value": "Social"
            },
            {
              "Key": "14",
              "Value": "Assessment"
            },
            {
              "Key": "15",
              "Value": "Course"
            }
          ]
        }
      ]
    },
    {
      "Key": "1",
      "Value": "I wan to request more Information",
      "Categories": [
        {
          "Key": "6",
          "Value": "Request Information",
          "Services": [
            {
              "Key": "9",
              "Value": "Mentorship"
            },
            {
              "Key": "10",
              "Value": "Coaching"
            },
            {
              "Key": "11",
              "Value": "Scholarship"
            },
            {
              "Key": "13",
              "Value": "Social"
            },
            {
              "Key": "14",
              "Value": "Assessment"
            },
            {
              "Key": "15",
              "Value": "Course"
            },
            {
              "Key": "17",
              "Value": "Academic Calendar"
            }
          ]
        }
      ]
    }
  ],
  "Refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1Mzc3MzgzMSwiaWF0IjoxNzUzNjg3NDMxLCJqdGkiOiIyNTUwMzU0YjVmOGM0OTkxOWRjZjU5Yjk4OTQwZTVhYSIsInVzZXJfaWQiOjEyfQ.Z0EUG0NGvL2_t9hku1VDj1QX6pHGUIOTfJdwaSBvHIw",
  "Access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzMwNjMxLCJpYXQiOjE3NTM2ODc0MzEsImp0aSI6ImExYjlmNmFiZTA5ZjRmZTdhYjlmMjE5ZGQ4YzMxZWExIiwidXNlcl9pZCI6MTJ9.H6vOe0krF87DKSYUHC0Ohvmsb8cU45ugL8Vy_gWqxmY"
}
```

## 2. List Cases

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/crm/cases/?emirates_id=[EMIRATES_ID_NEEDS_VALUE]

### Request Body

(Empty - GET request)

### Response Body

```json
[
  {
    "CaseNumber": "250722-0000001",
    "ServiceNumber": "b795a1fc-c604-4d69-a49b-d56d9fb46918",
    "CaseTitle": "Course Access Issue v1 - jul 22",
    "CaseDescription": "Unable to access my enrolled course materials. 22 jul. v1",
    "CaseType": {
      "Key": "6",
      "Value": "I want to report an incident"
    },
    "CaseCategory": {
      "Key": "1",
      "Value": "Technical"
    },
    "ServiceName": {
      "Key": "1",
      "Value": "Course Access"
    },
    "Status": {
      "Key": "INT_STAT_000",
      "Value": "Submitted"
    },
    "Comments": [
      {
        "CreatedAt": "2025-07-22T06:24:29.053445+00:00",
        "CommentBody": "We have received your case and you will be notified by the updates soon.",
        "Status": {
          "Key": "INT_STAT_000",
          "Value": "Submitted"
        }
      }
    ],
    "Attachments": [],
    "DaysAgo": 6,
    "CreatedAt": "2025-07-22T06:24:29.047197Z",
    "UpdatedAt": "2025-07-22T06:24:29.047206Z",
    "EmiratesId": "784199110258615"
  }
]
```

## 3. Create Case

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/crm/cases/

### Request Body

```json
{
  "EmiratesId": "[EMIRATES_ID_NEEDS_VALUE]",
  "EnglishFirstName": "[FIRST_NAME_NEEDS_VALUE]",
  "EnglishLastName": "[LAST_NAME_NEEDS_VALUE]",
  "EmailAddress": "[EMAIL_NEEDS_VALUE]",
  "Mobile": "[MOBILE_NEEDS_VALUE]",
  "PrefCommChannel": 1,
  "LanguagePreference": 2,
  "Region": {
    "Key": "1687d310-be66-ea11-a811-000d3ab3900c",
    "Value": "Abu Dhabi"
  },
  "Area": {
    "Key": "d3867b96-6cbf-ea11-a812-000d3a6c31c1",
    "Value": "[AREA_VALUE_NEEDS_VALUE]"
  },
  "CaseTitle": "[CASE_TITLE_NEEDS_VALUE]",
  "CaseDescription": "Unable to access my enrolled course materials. 22 jul. v1",
  "CaseType": {
    "Key": "[CASE_TYPE_KEY_NEEDS_VALUE]",
    "Value": "[CASE_TYPE_VALUE_NEEDS_VALUE]"
  },
  "CaseCategory": {
    "Key": "1",
    "Value": "Technical"
  },
  "ServiceName": {
    "Key": "[SERVICE_KEY_NEEDS_VALUE]",
    "Value": "Course Access"
  }
}
```

### Response Body

```json
{
  "ContactId": null,
  "CaseNumber": "250728-0000001",
  "ServiceNumber": "74c96cc3-5bab-40fe-a658-91d5a4851745",
  "ErrorCode": "",
  "ErrorMessage": "",
  "Status": "Submitted",
  "Response": ""
}
```

## 4. Get Case Details

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/crm/cases/[CASE_NUMBER_NEEDS_VALUE]/

### Request Body

(Empty - GET request)

### Response Body

```json
{
  "CaseNumber": "250722-0000001",
  "ServiceNumber": "b795a1fc-c604-4d69-a49b-d56d9fb46918",
  "CaseTitle": "Course Access Issue v1 - jul 22",
  "CaseDescription": "Unable to access my enrolled course materials. 22 jul. v1",
  "CaseType": {
    "Key": "6",
    "Value": "I want to report an incident"
  },
  "CaseCategory": {
    "Key": "1",
    "Value": "Technical"
  },
  "ServiceName": {
    "Key": "1",
    "Value": "Course Access"
  },
  "Status": {
    "Key": "INT_STAT_000",
    "Value": "Submitted"
  },
  "Comments": [
    {
      "CreatedAt": "2025-07-22T06:24:29.053445+00:00",
      "CommentBody": "We have received your case and you will be notified by the updates soon.",
      "Status": {
        "Key": "INT_STAT_000",
        "Value": "Submitted"
      }
    }
  ],
  "Attachments": [],
  "DaysAgo": 6,
  "CreatedAt": "2025-07-22T06:24:29.047197Z",
  "UpdatedAt": "2025-07-22T06:24:29.047206Z",
  "EmiratesId": "784199110258615"
}
```

## 5. Add Comment

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/crm/cases/comments/

### Request Body

(No body specified in the collection)

### Response Body

```json
{
  "OperationResponse": {
    "OperationSucceeded": true,
    "ErrorCode": "",
    "ErrorDescription": "",
    "ArabicErrorDescription": ""
  }
}
```

## 6. Add Attachment

### URL

http://stg.workspace.dge.gov.ae/app/v1/api/crm/cases/attachments/

### Request Body

(No body specified in the collection)

### Response Body

```json
{
  "OperationResponse": {
    "OperationSucceeded": true,
    "ErrorCode": "",
    "ErrorDescription": "",
    "ArabicErrorDescription": ""
  }
}
```

---

**Key Differences between DGE CRM and DGE CRM Legacy:**

1. **API Version**:
   - DGE CRM uses v2: `https://stg.workspace.dge.gov.ae/app/v2/`
   - DGE CRM Legacy uses v1: `http://stg.workspace.dge.gov.ae/app/v1/api/`

2. **Field Naming Convention**:
   - DGE CRM uses camelCase: `emailAddress`, `caseTitle`
   - DGE CRM Legacy uses PascalCase: `EmailAddress`, `CaseTitle`

3. **Authentication Endpoint**:
   - DGE CRM: `/auth/token/` (with trailing slash)
   - DGE CRM Legacy: `/auth/token` (without trailing slash)

4. **Authentication Field Names**:
   - DGE CRM: `emailAddress` and `password`
   - DGE CRM Legacy: `email_address` and `password`
