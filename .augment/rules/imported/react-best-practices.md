---
type: "always_apply"
---

- Use functional components and hooks for better performance and readability
- Optimize images and assets for mobile to reduce load times
- Implement error handling and fallback UI for better user experience
- Use the latest React Native features and APIs for improved functionality
- Use React's built-in hooks for state and lifecycle management
- Keep components small and focused on a single responsibility
- Leverage React's context API for global state management when appropriate
- Optimize rendering with React.memo and useCallback
