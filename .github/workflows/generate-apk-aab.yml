name: Android CI - Generated APK AAB

env:
  # The name of the main module repository
  main_project_module: app

  # The name of the Play Store
  playstore_name: EY - DGE


on:
  push:
    tags:
      - 'v*' # Match tags starting with "v" (e.g., v1.0, v2.1.0)

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      # Set Current Date As Env Variable
      - name: Set current date as env variable
        run: echo "date_today=$(date +'%Y-%m-%d')" >> $GITHUB_ENV

      # Set Repository Name As Env Variable
      - name: Set repository name as env variable
        run: echo "repository_name=$(echo '${{ github.repository }}' | awk -F '/' '{print $2}')" >> $GITHUB_ENV

      - name: Create android/local.properties
        run: |
          echo "sdk.dir=/usr/local/lib/android/sdk" >> android/local.properties
          echo "GM_API_KEY=AIzaSyCYD8oG26CwLOgQa51Gk0_jRyYlf3s63VQ" >> android/local.properties
          cat android/local.properties

      - name: Set Up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu' # See 'Supported distributions' for available options
          java-version: '17'
          cache: 'gradle'

      - name: Find Java home location and set it to build.gradle and org.eclipse.buildship.core.prefs
        run: |
          echo "Where is Java?"
          java -XshowSettings:properties -version 2>&1 > /dev/null | grep 'java.home'
          echo "Java home is at $JAVA_HOME and we will set the same to android/.settings/org.eclipse.buildship.core.prefs and android/gradle.properties."
          sed -i "s|^java\.home=.*|java.home=$JAVA_HOME|g" android/.settings/org.eclipse.buildship.core.prefs
          sed -i "s|^org\.gradle\.java\.home=.*|org.gradle.java.home=$JAVA_HOME|g" android/gradle.properties
          echo "Checking values for java home:"
          grep -i "java.home" android/.settings/org.eclipse.buildship.core.prefs android/gradle.properties

      # Step 2: Set up Node.js and npm
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.0'

      - name: Check node version
        run: |
          echo "The node version is $(node -v)"

      # Step 4: Set up Android SDK
      - name: Set up Android SDK
        run: |
          # Install prerequisites
          sudo apt-get update
          sudo apt-get install -y wget unzip

          # Install specific Android SDK version
          ANDROID_SDK_ROOT=$HOME/android-sdk
          mkdir -p $ANDROID_SDK_ROOT
          wget https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O commandlinetools.zip
          unzip commandlinetools.zip -d $ANDROID_SDK_ROOT/cmdline-tools
          mv $ANDROID_SDK_ROOT/cmdline-tools/cmdline-tools $ANDROID_SDK_ROOT/cmdline-tools/latest

          # Add SDK tools to PATH
          export PATH=$PATH:$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/platform-tools

          # Accept licenses
          yes | sdkmanager --licenses

          # Install SDK Platform 34
          # sdkmanager "platforms;android-34"
          sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"

          # Verify installation
          sdkmanager --list | grep -A 10 "Installed packages:"

          echo $ANDROID_HOME

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-


      - name: Install yarn dependencies
        run: yarn

      - name: Fix yarn dependancies
        run: |
          #fix: Could not find method compile() for arguments [com.facebook.react:react-native:+] on object of type org.gradle.api.internal.artifacts.dsl.dependencies.DefaultDependencyHandler.
          grep compile /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle
          sed -i 's/\bcompile\b/implementation/g' /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle
          grep compile /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle
          grep -i facebook /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle
          sed -i 's/\bcompileSdkVersion 23\b/compileSdkVersion 33/g' /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle
          grep -i compileSdkVersion /home/<USER>/work/dge-adsg-mobile-app/dge-adsg-mobile-app/node_modules/react-native-check-app-install/android/build.gradle

      - name: Change wrapper permissions
        run: cd android && chmod +x ./gradlew

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      # Staging - Set env to STG
      - name: Staging - Set "ENV" in src/api/api_client.tsx to STG
        run: |
          echo "Current directory is $(pwd)"
          echo "The current ENV is set to:"
          grep "export const ENV" src/api/api_client.tsx
          echo "Set the ENV to STG"
          sed -i "/export const ENV: string = /s/'[^']*'/'stg'/" src/api/api_client.tsx
          echo "The new ENV value is set to:"
          grep "export const ENV" src/api/api_client.tsx
          echo "Get the current versionCode number"
          grep versionCode android/app/build.gradle


      # Staging - Clean Build Project
      - name: Staging Build gradle project
        run: cd android && ./gradlew clean

      # Staging - Create APK Release
      - name: Staging Build apk release project (APK) - ${{ env.main_project_module }} module
        run: cd android && ./gradlew ${{ env.main_project_module }}:assembleStagingRelease

      # Staging - Noted For Output [main_project_module]/build/outputs/apk/release/
      - name: Upload APK Release - ${{ env.repository_name }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.date_today }} - ${{ env.playstore_name }} - ${{ env.repository_name }} - Staging APK(s) release generated
          path: android/${{ env.main_project_module }}/build/outputs/apk/staging/release/




      # Production - Set ENV to "prod"
      - name: Production - Set "ENV" in src/api/api_client.tsx to prod
        run: |
          echo "Current directory is $(pwd)"
          echo "The current ENV is set to:"
          grep "export const ENV" src/api/api_client.tsx
          echo "Set the ENV to prod"
          sed -i "/export const ENV: string = /s/'[^']*'/'prod'/" src/api/api_client.tsx
          echo "Verify ENV is set to prod:"
          grep "export const ENV" src/api/api_client.tsx
          echo "Get the current versionCode number"
          grep versionCode android/app/build.gradle


      # Production - Clean Build Project
      - name: Production - Build gradle project
        run: cd android && ./gradlew clean

      # Production - Create APK Release
      - name: Production - Build apk release project (APK) - ${{ env.main_project_module }} module
        run: cd android && ./gradlew ${{ env.main_project_module }}:assembleProductionRelease

      # Production - Create Bundle AAB Release
      # Noted for main module build [main_project_module]:bundleRelease
      - name: Production - Build app bundle release (AAB) - ${{ env.main_project_module }} module
        run: cd android && ./gradlew ${{ env.main_project_module }}:bundleProductionRelease

      # Production - Noted For Output [main_project_module]/build/outputs/apk/release/
      - name: Production - Upload APK Release - ${{ env.repository_name }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.date_today }} - ${{ env.playstore_name }} - ${{ env.repository_name }} - APK(s) release generated
          path: android/${{ env.main_project_module }}/build/outputs/apk/production/release/


      # Production - Noted For Output [main_project_module]/build/outputs/bundle/release/
      - name: Production - Upload AAB (App Bundle) Release - ${{ env.repository_name }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.date_today }} - ${{ env.playstore_name }} - ${{ env.repository_name }} - App bundle(s) AAB release generated
          path: android/${{ env.main_project_module }}/build/outputs/bundle/productionRelease/

