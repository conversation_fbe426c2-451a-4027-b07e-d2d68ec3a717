# OSX, ignore and prevent accidental commits .DS_Store files in all directories
.git/info/exclude
**/.DS_Store

# Private Keys
*.pem

# Xcode legacy
# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local
**/.xcode.env.local

# Xcode
xcuserdata/
project.xcworkspace/
DerivedData/

# Automatic backup files
*~.nib
*.swp
*~
*(Autosaved).rtfd/
Backup[ ]of[ ]*.pages/
Backup[ ]of[ ]*.key/
Backup[ ]of[ ]*.numbers/

# Android build

android/app/build
.env

# iOS build

# Dependency directories
/node_modules/

android/app/google-service.json
android/app/google-service_old.json
android/local.properties
# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
android/app/release

node_modules/

npm-debug.log
yarn-error.log

# Cocoapods
.xcode.env.local
Podfile.lock
Pods/
ios/Podfile.lock

# BUCK
buck-out/
\.buckd/

# Fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Testing
/coverage
/.jest

# Lock files
Gemfile.lock

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Other files
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
ios/Podfile.lock
yarn.lock
.vscode/docs/
