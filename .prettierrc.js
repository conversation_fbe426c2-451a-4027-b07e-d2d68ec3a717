// eslint-disable-next-line no-undef
module.exports = {
  singleQuote: true,
  tabWidth: 2,
  useTabs: false,
  trailingComma: 'all',
  printWidth: 100,
  importOrder: [
    '^(next/(.*)$)|^(next$)',
    '^(react/(.*)$)|^(react$)',
    '^(react-native/(.*)$)|^(react-native$)',
    '<THIRD_PARTY_MODULES>',
    '^@/api/(.*)$',
    '^@/assets/(.*)$',
    '^@/components/(.*)$',
    '^@/components/ui/(.*)$',
    '^@/config/(.*)$',
    '^@/hooks/(.*)$',
    '^@/interfaces/(.*)$',
    '^@/lib/(.*)$',
    '^@/models/(.*)$',
    '^@/navigation/(.*)$',
    '^@/resources/(.*)$',
    '^@/screens/(.*)$',
    '^@/src(.*)$',
    '^@/styles/(.*)$',
    '^@/theme/(.*)$',
    '^@/totara/(.*)$',
    '^@/types/(.*)$',
    '^@/__mocks__/(.*)$',
    '^types$',
    '^[./]',
  ],
  importOrderParserPlugins: ['typescript', 'jsx', 'decorators-legacy'],
  plugins: ['@ianvs/prettier-plugin-sort-imports'],
};