/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is not neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();
  else if (typeof define === 'function' && define.amd) define([], factory);
  else if (typeof exports === 'object') exports['SCORMapi1_2'] = factory();
  else root['SCORMapi1_2'] = factory();
})(self, function () {
  return /******/ (() => {
    // webpackBootstrap
    /******/ 'use strict';
    /******/ var __webpack_modules__ = {
      /***/ './TotaraMobileOfflineScorm/getScormDataModel.js':
        /*!*******************************************************!*\
  !*** ./TotaraMobileOfflineScorm/getScormDataModel.js ***!
  \*******************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\nconst CMIString256 = "^[\\\\u0000-\\\\uFFFF]{0,255}$";\nconst CMIString4096 = "^[\\\\u0000-\\\\uFFFF]{0,4096}$";\nconst CMITime = "^([0-2]{1}[0-9]{1}):([0-5]{1}[0-9]{1}):([0-5]{1}[0-9]{1})(.[0-9]{1,2})?$";\nconst CMITimespan = "^([0-9]{2,4}):([0-9]{2}):([0-9]{2})(.[0-9]{1,2})?$";\n// const CMIInteger = "^\\\\d+$";\nconst CMISInteger = "^-?([0-9]+)$";\nconst CMIDecimal = "^-?([0-9]{0,3})(.[0-9]*)?$";\nconst CMIIdentifier = "^[\\\\u0021-\\\\u007E]{0,255}$";\nconst CMIFeedback = CMIString256; // This must be redefined\nconst CMIIndex = "[._](\\\\d+).";\n// Vocabulary Data Type Definition\nconst CMIStatus = "^passed$|^completed$|^failed$|^incomplete$|^browsed$";\nconst CMIStatus2 = "^passed$|^completed$|^failed$|^incomplete$|^browsed$|^not attempted$";\nconst CMIExit = "^time-out$|^suspend$|^logout$|^$";\nconst CMIType = "^true-false$|^choice$|^fill-in$|^matching$|^performance$|^sequencing$|^likert$|^numeric$";\nconst CMIResult = "^correct$|^wrong$|^unanticipated$|^neutral$|^([0-9]{0,3})?(.[0-9]*)?$";\nconst NAVEvent = "^previous$|^continue$";\nconst cmi_children = "core,suspend_data,launch_data,comments,objectives,student_data,student_preference,interactions";\nconst core_children =\n  "student_id,student_name,lesson_location,credit,lesson_status,entry,score,total_time,lesson_mode,exit,session_time";\nconst score_children = "raw,min,max";\nconst comments_children = "content,location,time";\nconst objectives_children = "id,score,status";\n// const correct_responses_children = "pattern";\nconst student_data_children = "mastery_score,max_time_allowed,time_limit_action";\nconst student_preference_children = "audio,language,speed,text";\nconst interactions_children = "id,objectives,time,type,correct_responses,weighting,student_response,result,latency";\n// Data ranges\nconst score_range = "0#100";\nconst audio_range = "-1#100";\nconst speed_range = "-100#100";\nconst weighting_range = "-100#100";\nconst text_range = "-1#1";\n\nfunction getDataModel1_2(defaults) {\n  return Object.keys(defaults).reduce((datamodel, id) => {\n    const def = defaults[id];\n\n    datamodel[id] = {\n      "cmi._children": {\n        defaultvalue: cmi_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi._version": {\n        defaultvalue: "3.4",\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.core._children": {\n        defaultvalue: core_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.core.student_id": {\n        defaultvalue: def["cmi.core.student_id"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.student_name": {\n        defaultvalue: def["cmi.core.student_name"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.lesson_location": {\n        defaultvalue: def["cmi.core.lesson_location"],\n        format: CMIString256,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.core.credit": {\n        defaultvalue: def["cmi.core.credit"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.lesson_status": {\n        defaultvalue: def["cmi.core.lesson_status"],\n        format: CMIStatus,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.core.entry": {\n        defaultvalue: def["cmi.core.entry"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.score._children": {\n        defaultvalue: score_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.core.score.raw": {\n        defaultvalue: def["cmi.core.score.raw"],\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.core.score.max": {\n        defaultvalue: def["cmi.core.score.max"],\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.core.score.min": {\n        defaultvalue: def["cmi.core.score.min"],\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.core.total_time": {\n        defaultvalue: def["cmi.core.total_time"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.lesson_mode": {\n        defaultvalue: def["cmi.core.lesson_mode"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.core.exit": {\n        defaultvalue: def["cmi.core.exit"],\n        format: CMIExit,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.core.session_time": {\n        format: CMITimespan,\n        mod: "w",\n        defaultvalue: "00:00:00",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.suspend_data": {\n        defaultvalue: def["cmi.suspend_data"],\n        format: CMIString4096,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.launch_data": {\n        defaultvalue: def["cmi.launch_data"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.comments": {\n        defaultvalue: def["cmi.comments"],\n        format: CMIString4096,\n        mod: "rw",\n        writeerror: "405"\n      },\n      // deprecated evaluation attributes\n      "cmi.evaluation.comments._count": {\n        defaultvalue: "0",\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.evaluation.comments._children": {\n        defaultvalue: comments_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.evaluation.comments.n.content": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMIString256,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.evaluation.comments.n.location": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMIString256,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.evaluation.comments.n.time": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMITime,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.comments_from_lms": { mod: "r", writeerror: "403" },\n\n      "cmi.objectives._children": {\n        defaultvalue: objectives_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.objectives._count": {\n        mod: "r",\n        defaultvalue: "0",\n        writeerror: "402"\n      },\n      "cmi.objectives.n.id": {\n        pattern: CMIIndex,\n        format: CMIIdentifier,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.objectives.n.score._children": {\n        pattern: CMIIndex,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.objectives.n.score.raw": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.objectives.n.score.min": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.objectives.n.score.max": {\n        defaultvalue: "",\n        pattern: CMIIndex,\n        format: CMIDecimal,\n        range: score_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.objectives.n.status": {\n        pattern: CMIIndex,\n        format: CMIStatus2,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.student_data._children": {\n        defaultvalue: student_data_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.student_data.mastery_score": {\n        defaultvalue: def["cmi.student_data.mastery_score"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.student_data.max_time_allowed": {\n        defaultvalue: def["cmi.student_data.max_time_allowed"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.student_data.time_limit_action": {\n        defaultvalue: def["cmi.student_data.time_limit_action"],\n        mod: "r",\n        writeerror: "403"\n      },\n      "cmi.student_preference._children": {\n        defaultvalue: student_preference_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.student_preference.audio": {\n        defaultvalue: def["cmi.student_preference.audio"],\n        format: CMISInteger,\n        range: audio_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.student_preference.language": {\n        defaultvalue: def["cmi.student_preference.language"],\n        format: CMIString256,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.student_preference.speed": {\n        defaultvalue: def["cmi.student_preference.speed"],\n        format: CMISInteger,\n        range: speed_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n      "cmi.student_preference.text": {\n        defaultvalue: def["cmi.student_preference.text"],\n        format: CMISInteger,\n        range: text_range,\n        mod: "rw",\n        writeerror: "405"\n      },\n\n      "cmi.interactions._children": {\n        defaultvalue: interactions_children,\n        mod: "r",\n        writeerror: "402"\n      },\n      "cmi.interactions._count": {\n        mod: "r",\n        defaultvalue: "0",\n        writeerror: "402"\n      },\n      "cmi.interactions.n.id": {\n        pattern: CMIIndex,\n        format: CMIIdentifier,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.objectives._count": {\n        pattern: CMIIndex,\n        mod: "r",\n        defaultvalue: "0",\n        writeerror: "402"\n      },\n      "cmi.interactions.n.objectives.n.id": {\n        pattern: CMIIndex,\n        format: CMIIdentifier,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.time": {\n        pattern: CMIIndex,\n        format: CMITime,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.type": {\n        pattern: CMIIndex,\n        format: CMIType,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.correct_responses._count": {\n        pattern: CMIIndex,\n        mod: "r",\n        defaultvalue: "0",\n        writeerror: "402"\n      },\n      "cmi.interactions.n.correct_responses.n.pattern": {\n        pattern: CMIIndex,\n        format: CMIFeedback,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.weighting": {\n        pattern: CMIIndex,\n        format: CMIDecimal,\n        range: weighting_range,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.student_response": {\n        pattern: CMIIndex,\n        format: CMIFeedback,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.result": {\n        pattern: CMIIndex,\n        format: CMIResult,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "cmi.interactions.n.latency": {\n        pattern: CMIIndex,\n        format: CMITimespan,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      },\n      "nav.event": {\n        defaultvalue: "",\n        format: NAVEvent,\n        mod: "w",\n        readerror: "404",\n        writeerror: "405"\n      }\n    };\n\n    return datamodel;\n  }, {});\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getDataModel1_2);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/getScormDataModel.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/initializeCmi.js':
        /*!***************************************************!*\
  !*** ./TotaraMobileOfflineScorm/initializeCmi.js ***!
  \***************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__, __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/* harmony import */ var _utils_object__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/object */ "./TotaraMobileOfflineScorm/utils/object.js");\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\n\n\nfunction initializeCMI(model) {\n  const newCMI = {\n    core: {\n      score: {},\n      lesson_status: "not attempted"\n    },\n    objectives: {\n      _count: 0\n    },\n    student_data: {},\n    student_preference: {},\n    interactions: {\n      _count: 0\n    },\n    evaluation: {\n      comments: {}\n    }\n  };\n  return Object.keys(model).reduce((newCMI, pathString) => {\n    // remove \'cmi\' from the path\n    const path = pathString.split(".").slice(1);\n\n    // property specified as an array of objects\n    if (path.includes("n")) {\n      return newCMI;\n    }\n\n    // initialize cmi properties\n    const { defaultvalue } = model[pathString];\n    const val =\n      pathString === "cmi.core.lesson_status"\n        ? "not attempted"\n        : typeof defaultvalue != "undefined"\n        ? defaultvalue\n        : "";\n\n    (0,_utils_object__WEBPACK_IMPORTED_MODULE_0__.set)(newCMI, path, val);\n    return newCMI;\n  }, newCMI);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (initializeCMI);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/initializeCmi.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/scormApi.1.2.js':
        /*!**************************************************!*\
  !*** ./TotaraMobileOfflineScorm/scormApi.1.2.js ***!
  \**************************************************/
        /*! namespace exports */
        /*! export default [provided] [used in main] [usage prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__, __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/* harmony import */ var _scormModels__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scormModels */ "./TotaraMobileOfflineScorm/scormModels.js");\n/* harmony import */ var _scormTotalTime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scormTotalTime */ "./TotaraMobileOfflineScorm/scormTotalTime.js");\n/* harmony import */ var _scormCollectData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./scormCollectData */ "./TotaraMobileOfflineScorm/scormCollectData.js");\n/* harmony import */ var _utils_object__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/object */ "./TotaraMobileOfflineScorm/utils/object.js");\n/* harmony import */ var _scormMutateLessonStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scormMutateLessonStatus */ "./TotaraMobileOfflineScorm/scormMutateLessonStatus.js");\n/* harmony import */ var _initializeCmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initializeCmi */ "./TotaraMobileOfflineScorm/initializeCmi.js");\n/* harmony import */ var _getScormDataModel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getScormDataModel */ "./TotaraMobileOfflineScorm/getScormDataModel.js");\n/* harmony import */ var _utils_index__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/index */ "./TotaraMobileOfflineScorm/utils/index.js");\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n *\n * <AUTHOR> Tegg <<EMAIL>>\n */\n\n/* global onpostCommitDataToNative */\n\n\n\n\n\n\n\n\n\nfunction SCORMapi1_2(\n  defaults,\n  cmiobj,\n  cmiint,\n  scormdebugging,\n  scormauto,\n  scormid,\n  objectId,\n  attempt,\n  autocommit,\n  masteryOverride,\n  hidetoc,\n  oldCMI\n) {\n  let errorCode;\n  let cmi;\n  let timeStarted = null;\n  let isInitialized = false;\n  const dataModel = (0,_getScormDataModel__WEBPACK_IMPORTED_MODULE_6__.default)(defaults);\n\n  // SCORM 1.2 API\n  function LMSInitialize(param) {\n    if (oldCMI == "null") {\n      cmi = (0,_initializeCmi__WEBPACK_IMPORTED_MODULE_5__.default)({ model: dataModel[objectId] });\n    } else {\n      try {\n        cmi = JSON.parse(oldCMI);\n      } catch (error) {\n        throw new Error("ParseError: param \'oldCMI\' must be the string \'null\' or a JSON object");\n      }\n    }\n\n    // QUESTION: are these needed?\n    // eval(cmiobj[scoid]);\n    // eval(cmiint[scoid]);\n\n    errorCode = "0";\n    if (param !== "") {\n      errorCode = "201";\n      return "false";\n    }\n\n    if (!isInitialized) {\n      isInitialized = true;\n      errorCode = "0";\n      timeStarted = (0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.getCurrentTimeStampInSeconds)();\n      return "true";\n    }\n\n    errorCode = "101";\n    return "false";\n  }\n\n  // SCORM 1.2 API\n  function LMSGetLastError() {\n    return errorCode;\n  }\n\n  // SCORM 1.2 API\n  // NOTE: does not appear to do much or meet spec:\n  // LMSGetDiagnostic( errorCode : CMIErrorCode ) : string – Returns detailed information about the last error that occurred.\n  // https://scorm.com/scorm-explained/technical-scorm/run-time/run-time-reference/#section-2\n  function LMSGetDiagnostic(param) {\n    return param === "" ? errorCode : param;\n  }\n\n  // SCORM 1.2 API\n  function LMSFinish(param) {\n    if (param !== "") {\n      errorCode = "201";\n      return "false";\n    }\n\n    if (!isInitialized) {\n      errorCode = "301";\n      return "false";\n    }\n\n    isInitialized = false;\n\n    // mutate cmi (formerly StoreData)\n    (0,_scormMutateLessonStatus__WEBPACK_IMPORTED_MODULE_4__.default)(cmi, dataModel[objectId]["cmi.core.lesson_status"].defaultvalue, masteryOverride);\n\n    const dataList = (0,_scormCollectData__WEBPACK_IMPORTED_MODULE_2__.default)({ dataModel, objectId, data: cmi, parent: "cmi" });\n\n    if (!cmi.core.total_time || !cmi.core.session_time) {\n      throw new Error(\'Cannot calculate totalTime. "total_time" or "session_time" not present\');\n    }\n\n    dataList.push(\n      (0,_scormTotalTime__WEBPACK_IMPORTED_MODULE_1__.default)({\n        totalTime: cmi.core.total_time,\n        sessionTime: cmi.core.session_time,\n        objectId\n      })\n    );\n\n    const commitData = { timestarted: timeStarted, tracks: dataList };\n    // console.warn("commitData: ", commitData);\n    onpostCommitDataToNative(scormid, objectId, attempt, commitData, cmi); // global\n\n    setTimeout("mod_scorm_launch_prev_sco();", 500);\n    // NOTE: the \'nav\' object and \'event\' property do not appear elsewhere in the codebase\n    // and the previous nav logic unreachable\n    // if (nav.event != "") {\n    // if (nav.event == "continue") {\n    // setTimeout("mod_scorm_launch_next_sco();", 500);\n    // } else {\n    // setTimeout("mod_scorm_launch_prev_sco();", 500);\n    // }\n    // } else {\n    // if (scormauto == 1) {\n    // setTimeout("mod_scorm_launch_next_sco();", 500);\n    // }\n    // }\n\n    errorCode = "0";\n    return "true";\n  }\n\n  // SCORM 1.2 API\n  function LMSGetValue(pathString) {\n    if (!isInitialized) {\n      errorCode = "301";\n      return "";\n    }\n\n    if (!pathString) {\n      errorCode = "201";\n      return "";\n    }\n\n    errorCode = "0";\n\n    const genericPath = pathString.replace(_utils_index__WEBPACK_IMPORTED_MODULE_7__.CMIIndex, ".n.");\n    const genericValue = dataModel[objectId][genericPath];\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.isDefined)(genericValue)) {\n      if (genericValue.mod === "w") {\n        errorCode = genericValue.readerror;\n        return "";\n      }\n\n      // remove \'cmi\' and give indices underscores\n      const element = pathString\n        .replace(_utils_index__WEBPACK_IMPORTED_MODULE_7__.CMIIndex, "._$1.")\n        .split(".")\n        .filter((_, i) => i !== 0);\n\n      return (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.get)(cmi, element) || "";\n    }\n\n    // adjust errorCode for ede cases\n    const modelPath = genericPath.split(".");\n    const parentPath = modelPath.slice(0, modelPath.length - 1);\n    const parentValue = dataModel[objectId][parentPath];\n\n    const getChildrenAttempt = modelPath[modelPath.length - 1] === "_children";\n    if (getChildrenAttempt) {\n      // TODO actually check of datamodel parent supports _children\n      errorCode = (0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.isDefined)(parentValue) ? "202" : "201";\n      return "";\n    }\n\n    const getCountAttempt = modelPath[modelPath.length - 1] === "_count";\n    if (getCountAttempt) {\n      // TODO actually check of datamodel parent supports _count\n      errorCode = (0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.isDefined)(parentValue) ? "203" : "201";\n      return "";\n    }\n\n    errorCode = "201";\n    return "";\n  }\n\n  // SCORM 1.2 API\n  function LMSSetValue(pathString, value) {\n    if (!isInitialized) {\n      errorCode = "301";\n      return "";\n    }\n\n    if (!pathString) {\n      errorCode = "201";\n      return "";\n    }\n\n    errorCode = "0";\n\n    const genericPath = pathString.replace(_utils_index__WEBPACK_IMPORTED_MODULE_7__.CMIIndex, ".n.");\n\n    const genericValue = dataModel[objectId][genericPath];\n    if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.isDefined)(genericValue)) {\n      // check write permissions\n      if (genericValue.mod === "r") {\n        errorCode = genericValue.writeerror;\n        return "false";\n      }\n\n      // check data format\n      const formatRe = new RegExp(genericValue.format);\n      const matchesFormat = String(value).match(formatRe);\n      if (!matchesFormat) {\n        errorCode = genericValue.writeerror;\n        return "false";\n      }\n\n      const path = pathString.split(".").slice(1);\n\n      // ensure value has ancestor objects\n      const ancestorPath = [];\n      path.forEach((part) => {\n        if (part.match(/\\d/)) {\n          // store under \'private\' keys: _n\n          ancestorPath.push(`_${part}`);\n          const ancestor = (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.get)(cmi, ancestorPath);\n          if (!ancestor) {\n            const modelId = ancestorPath.filter((part) => !part.match(/\\d/)).join(".");\n            const Model = _scormModels__WEBPACK_IMPORTED_MODULE_0__.default[modelId];\n            const update = Model();\n            (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.set)(cmi, ancestorPath, update);\n\n            // update collectioncount\n            const collectionPath = ancestorPath.slice(0, ancestorPath.length - 1);\n            const countPath = collectionPath.concat(["_count"]);\n            const count = (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.get)(cmi, countPath);\n            (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.set)(cmi, countPath, count + 1);\n          }\n        } else {\n          ancestorPath.push(part);\n        }\n      });\n\n      // check that value falls within range specified in datamodel\n      if ((0,_utils_index__WEBPACK_IMPORTED_MODULE_7__.isDefined)(genericValue.range)) {\n        const range = genericValue.range.split("#").map(Number);\n        const number = Number(value);\n        if (!(number >= range[0] && number <= range[1])) {\n          errorCode = genericValue.writeerror;\n          return "false";\n        }\n      }\n\n      // concatenate comments\n      if (pathString === "cmi.comments") {\n        cmi.comments = cmi.comments + value;\n        return "true";\n      }\n\n      // finally update the specified value\n      (0,_utils_object__WEBPACK_IMPORTED_MODULE_3__.set)(cmi, path, value);\n      return "true";\n    }\n  }\n\n  // SCORM 1.2 API\n  function LMSCommit(param) {\n    // NOTE original code referenced the SCORMapi1_2.timeout global\n    // but this is unreachable in the mobile version\n\n    if (!isInitialized) {\n      errorCode = "301";\n      return "false";\n    }\n\n    if (param !== "") {\n      errorCode = "201";\n      return "false";\n    }\n\n    const dataList = (0,_scormCollectData__WEBPACK_IMPORTED_MODULE_2__.default)({ dataModel, objectId, data: cmi, parent: "cmi" });\n    const commitData = { timestarted: timeStarted, tracks: dataList };\n    // console.warn("commitData: ", commitData);\n    onpostCommitDataToNative(scormid, objectId, attempt, commitData, cmi);\n    return "true";\n  }\n\n  // SCORM 1.2 API\n  function LMSGetErrorString(errorCode) {\n    switch (errorCode) {\n      case "0":\n        return "No error";\n      case "101":\n        return "General exception";\n      case "201":\n        return "Invalid argument error";\n      case "202":\n        return "Element cannot have children";\n      case "203":\n        return "Element not an array - cannot have count";\n      case "301":\n        return "Not initialized";\n      case "401":\n        return "Not implemented error";\n      case "402":\n        return "Invalid set value, element is a keyword";\n      case "403":\n        return "Element is read only";\n      case "404":\n        return "Element is write only";\n      case "405":\n        return "Incorrect data type";\n      default:\n        return "No error string found!";\n    }\n  }\n\n  function _getCMI() {\n    return cmi;\n  }\n\n  function _setCMI(update) {\n    cmi = update;\n  }\n\n  return {\n    LMSInitialize,\n    LMSGetValue,\n    LMSSetValue,\n    LMSCommit,\n    LMSFinish,\n    LMSGetLastError,\n    LMSGetErrorString,\n    LMSGetDiagnostic,\n    _getCMI,\n    _setCMI\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SCORMapi1_2);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/scormApi.1.2.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/scormCollectData.js':
        /*!******************************************************!*\
  !*** ./TotaraMobileOfflineScorm/scormCollectData.js ***!
  \******************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__, __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/* harmony import */ var _utils_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/index */ "./TotaraMobileOfflineScorm/utils/index.js");\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\n\n\n// main\nfunction collectData({ dataModel, objectId, data, parent }) {\n  let dataList = [];\n\n  Object.keys(data).forEach((property) => {\n    const value = data[property];\n\n    if (typeof value == "object") {\n      const subDataList = collectData({\n        dataModel,\n        objectId,\n        data: value,\n        parent: `${parent}.${property}`\n      });\n\n      dataList = dataList.concat(subDataList);\n      return;\n    }\n\n    const pathString = `${parent}.${property}`;\n\n    // ignore the session time element\n    if (pathString === "cmi.core.session_time") {\n      return;\n    }\n\n    const genericPath = pathString.replace(_utils_index__WEBPACK_IMPORTED_MODULE_0__.CMIIndex, ".n.");\n    const modelValue = dataModel[objectId][pathString];\n    const genericValue = dataModel[objectId][genericPath];\n\n    if (!(0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.isDefined)(modelValue) && (0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.isDefined)(genericValue)) {\n      dataModel[objectId][pathString] = Object.assign({}, genericValue);\n    }\n\n    if (!(0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.isDefined)(modelValue)) {\n      return;\n    }\n\n    // make sure this is not a read only element\n    if (modelValue.mod && value.mod === "r") {\n      return;\n    }\n\n    if (shouldUpdate(modelValue.defaultvalue, value)) {\n      dataList.push({\n        identifier: objectId,\n        element: pathString,\n        value,\n        timemodified: (0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.getCurrentTimeStampInSeconds)()\n      });\n\n      dataModel[objectId][pathString].defaultvalue = value;\n    }\n  });\n\n  return dataList;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (collectData);\n\n// helpers\nfunction shouldUpdate(defaultvalue, value) {\n  if (!(0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.isDefined)(defaultvalue)) {\n    return true;\n  }\n\n  return defaultvalue !== value || typeof defaultvalue !== value;\n}\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/scormCollectData.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/scormModels.js':
        /*!*************************************************!*\
  !*** ./TotaraMobileOfflineScorm/scormModels.js ***!
  \*************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\nfunction Interaction(values = {}) {\n  return Object.assign(\n    {\n      id: "",\n      // “true-false”, “choice”, “fill-in”, “long-fill-in”, “matching”, “performance”, “sequencing”, “likert”, “numeric” or “other”,\n      type: "",\n      objectives: {\n        _count: 0\n      },\n      correct_responses: {\n        _count: 0\n      },\n      timestamp: "",\n      weighting: "",\n      student_response: "",\n      result: "",\n      latency: "",\n      description: ""\n    },\n    values\n  );\n}\n\nfunction InteractionObjective(values = {}) {\n  return Object.assign(\n    {\n      id: ""\n    },\n    values\n  );\n}\n\nfunction InteractionCorrectResponse(values = {}) {\n  return Object.assign(\n    {\n      pattern: ""\n    },\n    values\n  );\n}\n\nfunction Objective(values = {}) {\n  return Object.assign(\n    {\n      id: "",\n      score: {\n        raw: "",\n        max: "",\n        min: ""\n      },\n      status: "" // “passed”, “completed”, “failed”, “incomplete”, “browsed”, “not attempted”\n    },\n    values\n  );\n}\n\nconst Models = {\n  interactions: Interaction,\n  "interactions.objectives": InteractionObjective,\n  "interactions.correct_responses": InteractionCorrectResponse,\n  objectives: Objective\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Models);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/scormModels.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/scormMutateLessonStatus.js':
        /*!*************************************************************!*\
  !*** ./TotaraMobileOfflineScorm/scormMutateLessonStatus.js ***!
  \*************************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\nfunction lessonStatus(cmi, defaultvalue, masteryOverride) {\n  if (cmi.core.lesson_mode == "browse") {\n    if (defaultvalue == "" && cmi.core.lesson_status == "not attempted") {\n      cmi.core.lesson_status = "browsed";\n    }\n  }\n\n  if (cmi.core.lesson_mode == "normal") {\n    if (cmi.core.credit == "credit") {\n      if (masteryOverride && cmi.student_data.mastery_score !== "" && cmi.core.score.raw !== "") {\n        if (parseFloat(cmi.core.score.raw) >= parseFloat(cmi.student_data.mastery_score)) {\n          cmi.core.lesson_status = "passed";\n        } else {\n          cmi.core.lesson_status = "failed";\n        }\n      }\n    }\n  }\n\n  if (cmi.core.lesson_status == "not attempted") {\n    cmi.core.lesson_status = "completed";\n  }\n\n  return cmi;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (lessonStatus);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/scormMutateLessonStatus.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/scormTotalTime.js':
        /*!****************************************************!*\
  !*** ./TotaraMobileOfflineScorm/scormTotalTime.js ***!
  \****************************************************/
        /*! namespace exports */
        /*! export default [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__, __webpack_exports__, __webpack_require__.r, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "default": () => __WEBPACK_DEFAULT_EXPORT__\n/* harmony export */ });\n/* harmony import */ var _utils_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/index */ "./TotaraMobileOfflineScorm/utils/index.js");\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\n\n\nfunction addTime(first, second) {\n  let sFirst = first.split(":");\n  let sSecond = second.split(":");\n  let cFirst = sFirst[2].split(".");\n  let cSecond = sSecond[2].split(".");\n  let change = 0;\n\n  let FirstCents = 0; //Cents\n  if (cFirst.length > 1) {\n    FirstCents = parseInt(cFirst[1], 10);\n  }\n  let SecondCents = 0;\n  if (cSecond.length > 1) {\n    SecondCents = parseInt(cSecond[1], 10);\n  }\n  let cents = FirstCents + SecondCents;\n  change = Math.floor(cents / 100);\n  cents = cents - change * 100;\n  if (Math.floor(cents) < 10) {\n    cents = "0" + cents.toString();\n  }\n\n  let secs = parseInt(cFirst[0], 10) + parseInt(cSecond[0], 10) + change; //Seconds\n  change = Math.floor(secs / 60);\n  secs = secs - change * 60;\n  if (Math.floor(secs) < 10) {\n    secs = "0" + secs.toString();\n  }\n\n  let mins = parseInt(sFirst[1], 10) + parseInt(sSecond[1], 10) + change; //Minutes\n  change = Math.floor(mins / 60);\n  mins = mins - change * 60;\n  if (mins < 10) {\n    mins = "0" + mins.toString();\n  }\n\n  let hours = parseInt(sFirst[0], 10) + parseInt(sSecond[0], 10) + change; //Hours\n  if (hours < 10) {\n    hours = "0" + hours.toString();\n  }\n\n  if (cents != "0") {\n    return hours + ":" + mins + ":" + secs + "." + cents;\n  } else {\n    return hours + ":" + mins + ":" + secs;\n  }\n}\n\nfunction totalTime({ totalTime, sessionTime, objectId }) {\n  return {\n    identifier: objectId,\n    element: "cmi.core.total_time",\n    value: addTime(totalTime, sessionTime),\n    timemodified: (0,_utils_index__WEBPACK_IMPORTED_MODULE_0__.getCurrentTimeStampInSeconds)()\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (totalTime);\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/scormTotalTime.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/utils/index.js':
        /*!*************************************************!*\
  !*** ./TotaraMobileOfflineScorm/utils/index.js ***!
  \*************************************************/
        /*! namespace exports */
        /*! export CMIIndex [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export getCurrentTimeStampInSeconds [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export isDefined [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__.r, __webpack_exports__, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            '__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   "CMIIndex": () => /* binding */ CMIIndex,\n/* harmony export */   "isDefined": () => /* binding */ isDefined,\n/* harmony export */   "getCurrentTimeStampInSeconds": () => /* binding */ getCurrentTimeStampInSeconds\n/* harmony export */ });\n/**\n * This file is part of Totara Enterprise.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise is provided only to Totara Learning Solutions\n * LTD’s customers and partners, pursuant to the terms and\n * conditions of a separate agreement with Totara Learning\n * Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n * \n * <AUTHOR> Tegg <<EMAIL>>\n */\n\nconst CMIIndex = /[._](\\d+)./g;\n\nfunction isDefined(val) {\n  return typeof val !== \'undefined\'\n}\n\nfunction getCurrentTimeStampInSeconds() {\n  return Math.round(new Date().getTime() / 1000);\n}\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/utils/index.js?',
          );

          /***/
        },

      /***/ './TotaraMobileOfflineScorm/utils/object.js':
        /*!**************************************************!*\
  !*** ./TotaraMobileOfflineScorm/utils/object.js ***!
  \**************************************************/
        /*! namespace exports */
        /*! export baseSet [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export get [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export hasOwnProperty [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export isIndex [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export isPlainObject [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export set [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export structuralDeepClone [provided] [no usage info] [missing usage info prevents renaming] */
        /*! export structuralShallowClone [provided] [no usage info] [missing usage info prevents renaming] */
        /*! other exports [not provided] [no usage info] */
        /*! runtime requirements: __webpack_require__.r, __webpack_exports__, __webpack_require__.d, __webpack_require__.* */
        /***/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
          eval(
            "__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"hasOwnProperty\": () => /* binding */ hasOwnProperty,\n/* harmony export */   \"isPlainObject\": () => /* binding */ isPlainObject,\n/* harmony export */   \"structuralDeepClone\": () => /* binding */ structuralDeepClone,\n/* harmony export */   \"structuralShallowClone\": () => /* binding */ structuralShallowClone,\n/* harmony export */   \"get\": () => /* binding */ get,\n/* harmony export */   \"isIndex\": () => /* binding */ isIndex,\n/* harmony export */   \"set\": () => /* binding */ set,\n/* harmony export */   \"baseSet\": () => /* binding */ baseSet\n/* harmony export */ });\n/**\n * This file is part of Totara Enterprise Extensions.\n *\n * Copyright (C) 2020 onwards Totara Learning Solutions LTD\n *\n * Totara Enterprise Extensions is provided only to Totara\n * Learning Solutions LTD's customers and partners, pursuant to\n * the terms and conditions of a separate agreement with Totara\n * Learning Solutions LTD or its affiliate.\n *\n * If you do not have an agreement with Totara Learning Solutions\n * LTD, you may not access, use, modify, or distribute this software.\n * Please contact [<EMAIL>] for more information.\n *\n * <AUTHOR> Chester <<EMAIL>>\n * @module tui\n *\n *\n * TODO import as a package from totara repo\n */\n\n/**\n * Object.prototype.hasOwnProperty wrapper.\n *\n * @param {object} obj\n * @param {string} key\n * @returns {boolean}\n */\nfunction hasOwnProperty(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nconst objectConstructorString = Function.prototype.toString.call(Object);\n\n/**\n * Check if an object is a plain object (i.e. {}), not a class instance etc.\n *\n * @param {*} value\n * @returns {boolean}\n */\nfunction isPlainObject(value) {\n  if (!value || typeof value != 'object') {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  if (proto == null) {\n    return true;\n  }\n  const ctor = hasOwnProperty(proto, 'constructor') && proto.constructor;\n  return (\n    typeof ctor == 'function' &&\n    ctor instanceof ctor &&\n    Function.prototype.toString.call(ctor) === objectConstructorString\n  );\n}\n\n/**\n * Structural deep clone.\n *\n * Only clones plain objects and arrays, other values are left alone.\n *\n * @param {(object|array)} obj\n * @return {(object|array)}\n */\nfunction structuralDeepClone(obj) {\n  return structuralCloneImpl(obj, true, []);\n}\n\n/**\n * Structural shallow clone.\n *\n * Only clones plain objects and arrays, other values are left alone.\n *\n * @param {(object|array)} obj\n * @return {(object|array)}\n */\nfunction structuralShallowClone(obj) {\n  return structuralCloneImpl(obj, false);\n}\n\nfunction structuralCloneImpl(obj, deep, stack) {\n  if (stack && stack.includes(obj)) {\n    throw new TypeError('Cannot clone circular structure.');\n  }\n\n  let result = obj;\n  const isArr = Array.isArray(obj),\n    isObj = isPlainObject(obj);\n\n  if (isArr || isObj) {\n    if (isArr) {\n      result = [];\n    } else {\n      result = {};\n    }\n\n    for (let key in obj) {\n      if (hasOwnProperty(obj, key)) {\n        let substack;\n        if (deep) {\n          substack = stack.slice();\n          substack.push(result);\n        }\n        result[key] = deep\n          ? structuralCloneImpl(obj[key], deep, substack)\n          : obj[key];\n      }\n    }\n  }\n\n  return result;\n}\n\n/**\n * Get the value of path at object.\n *\n * @param {object} object\n * @param {array} path Path of property to get, e.g. ['a', 2, 'q']\n * @returns {*} Value at path.\n */\nfunction get(object, path) {\n  if (typeof path === 'string') {\n    return object[path];\n  }\n\n  let i = 0;\n  while (object != null && i < path.length) {\n    object = object[path[i++]];\n  }\n  return object;\n}\n\nconst isIndex = val =>\n  typeof val == 'number' || /^(?:0|[1-9]\\d*)$/.test(val);\n\nconst setKey = (target, key, value) => {\n  target[key] = value;\n};\n\n/**\n * Set the value at path of object.\n *\n * Arrays are created for missing index properties and objects are created for other missing properties.\n *\n * @param {object} object Object to modify.\n * @param {array} path Path of property to set, e.g. ['a', 2, 'q']\n * @param {*} value\n * @returns {object}\n */\nfunction set(object, path, value) {\n  return baseSet(object, path, value, setKey);\n}\n\n/**\n * Base implementation for set and vue_util.set.\n *\n * @param {object} object Object to modify.\n * @param {array} path Path of property to set, e.g. ['a', 2, 'q']\n * @param {*} value\n * @param {*} setKey\n * @returns {object}\n */\nfunction baseSet(object, path, value, setKey) {\n  if (typeof path === 'string') {\n    setKey(object, path, value);\n    return;\n  }\n\n  const lastIndex = path.length - 1;\n  let index = 0;\n\n  while (object != null && index <= lastIndex) {\n    const key = path[index];\n    if (index == lastIndex) {\n      setKey(object, key, value);\n    } else {\n      if (object[key] == null) {\n        // no object at key, create one\n        setKey(object, key, isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    object = object[key];\n    index++;\n  }\n  return object;\n}\n\n\n\n//# sourceURL=webpack://SCORMapi1_2/./TotaraMobileOfflineScorm/utils/object.js?",
          );

          /***/
        },

      /******/
    };
    /************************************************************************/
    /******/ // The module cache
    /******/ var __webpack_module_cache__ = {};
    /******/
    /******/ // The require function
    /******/ function __webpack_require__(moduleId) {
      /******/ // Check if module is in cache
      /******/ if (__webpack_module_cache__[moduleId]) {
        /******/ return __webpack_module_cache__[moduleId].exports;
        /******/
      }
      /******/ // Create a new module (and put it into the cache)
      /******/ var module = (__webpack_module_cache__[moduleId] = {
        /******/ // no module.id needed
        /******/ // no module.loaded needed
        /******/ exports: {},
        /******/
      });
      /******/
      /******/ // Execute the module function
      /******/ __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
      /******/
      /******/ // Return the exports of the module
      /******/ return module.exports;
      /******/
    }
    /******/
    /************************************************************************/
    /******/ /* webpack/runtime/define property getters */
    /******/ (() => {
      /******/ // define getter functions for harmony exports
      /******/ __webpack_require__.d = (exports, definition) => {
        /******/ for (var key in definition) {
          /******/ if (
            __webpack_require__.o(definition, key) &&
            !__webpack_require__.o(exports, key)
          ) {
            /******/ Object.defineProperty(exports, key, {
              enumerable: true,
              get: definition[key],
            });
            /******/
          }
          /******/
        }
        /******/
      };
      /******/
    })();
    /******/
    /******/ /* webpack/runtime/hasOwnProperty shorthand */
    /******/ (() => {
      /******/ __webpack_require__.o = (obj, prop) =>
        Object.prototype.hasOwnProperty.call(obj, prop);
      /******/
    })();
    /******/
    /******/ /* webpack/runtime/make namespace object */
    /******/ (() => {
      /******/ // define __esModule on exports
      /******/ __webpack_require__.r = (exports) => {
        /******/ if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
          /******/ Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
          /******/
        }
        /******/ Object.defineProperty(exports, '__esModule', { value: true });
        /******/
      };
      /******/
    })();
    /******/
    /************************************************************************/
    /******/ // module exports must be returned from runtime so entry inlining is disabled
    /******/ // startup
    /******/ // Load entry module and return exports
    /******/ return __webpack_require__('./TotaraMobileOfflineScorm/scormApi.1.2.js');
    /******/
  })().default;
});
