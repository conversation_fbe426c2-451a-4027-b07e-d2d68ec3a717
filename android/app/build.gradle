apply plugin: "com.android.application"
apply plugin: 'com.google.firebase.firebase-perf'
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

apply plugin: "org.jetbrains.kotlin.android"

apply plugin: "com.facebook.react"

project.ext.envConfigFiles = [
   production: ".env.production",
   staging: ".env.staging",
   development: ".env.development"
]
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
    autolinkLibrariesWithApp()
}

apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

/**
 * Set this to true to create two separate APKs instead of one:
 *   - An APK that only works on ARM devices
 *   - An APK that only works on x86 devices
 * The advantage is the size of the APK is reduced by about 4MB.
 * Upload all the APKs to the Play Store and people will download
 * the correct one based on the CPU architecture of their device.
 */
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Run Proguard to shrink the Java bytecode in release builds.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore.
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US.  Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Whether to enable the Hermes VM.
 *
 * This should be set on project.ext.react and that value will be read here.  If it is not set
 * on project.ext.react, JavaScript will not be compiled to Hermes Bytecode
 * and the benefits of using Hermes will therefore be sharply reduced.
 */

/**
 * Architectures to build native code for in debug.
 */

android {
    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
    }
    namespace "com.dge.academy"
    ndkVersion rootProject.ext.ndkVersion
    compileSdkVersion rootProject.ext.compileSdkVersion
    flavorDimensions "default"
    defaultConfig {
        multiDexEnabled true
        applicationId "com.dge.academy"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 441
        versionName "25.07.11"

        setProperty("archivesBaseName", "v" + versionName + "." + versionCode)

        //UAE PASS START -- Adding Custom Scheme Variables
        buildConfigField "String", "URI_SCHEME", "\"mynavigatorapp\"" // Change to your app name or any custom scheme. Donot use uaepasssample
        buildConfigField "String", "URI_HOST_SUCCESS", "\"success\""
        buildConfigField "String", "URI_HOST_FAILURE", "\"failure\""
        buildConfigField "String", "CLIENT_ID", "\"sandbox_stage\"" // Change the client id to the one provided to you.
        buildConfigField "String", "CLIENT_SECRET", "\"sandbox_stage\"" // Change the client secret to the one provided to you.
        buildConfigField "String", "REDIRECT_URL", "\"https://google.com/\"" // Change the redirect url to the one provided to you.
        buildConfigField "Integer", "ENVIRONMENT", "1" // Change the environment to 0, 1 or 2. 0 is for qa, 1 is for staging and 2 is for production.
        resValue "string", "build_config_package", "com.dge.academy"
        manifestPlaceholders = [
                appAuthRedirectScheme: "com.dge.academy",
                host_success: "success",
                host_failure: "failure",
                scheme      : "mynavigatorapp" // Change to your app name or any custom scheme. Donot use uaepasssample
        ]
        //UAE PASS END -- Adding Custom Scheme Variables
    }
  productFlavors {
    staging {
      applicationId "com.dge.academy.staging"
      versionCode 440
    }
    production {
        flavorDimensions "default"
        applicationId "com.dge.academy"
        versionCode 441
    }
    development {
      applicationId "com.dge.academy.development"
      versionCode 238
    }
  }
    sourceSets {
        // All flavors use the same main res directory
        main {
            assets {
                srcDirs 'src\\main\\assets'
            }
        }
    }
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }

            if (project.hasProperty('MYAPP_RELEASE_SELF_SIGNED')) {
                storeFile file('debug.keystore')
                storePassword 'android'
                keyAlias 'androiddebugkey'
                keyPassword 'android'
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            matchingFallbacks = ['debug', 'release']
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            def flavor = variant.productFlavors[0].name
            def buildType = variant.buildType.name
            def versionName = defaultConfig.versionName
            def versionCode = variant.productFlavors[0].versionCode
            def newApkName = "v" + versionName + "." + versionCode + "-" + flavor + "-" + buildType + ".apk"
            output.outputFileName = newApkName
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation project(":react-native-vector-icons")
    implementation 'com.android.support:multidex:1.0.3' //enter the latest version
    implementation(platform("com.google.firebase:firebase-bom:33.0.0"))
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")

    // WorkManager for background notifications
    implementation "androidx.work:work-runtime:2.9.0"
    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.fbjni'
    }

    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.flipper'
        exclude group:'com.squareup.okhttp3', module:'okhttp'
    }

    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.facebook.flipper'
    }

    if (hermesEnabled.toBoolean()) {
       implementation("com.facebook.react:hermes-engine:+") {
        exclude group: 'com.facebook.fbjni'
    }
    } else {
      implementation jscFlavor
    }
}

// apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
apply plugin: "com.google.android.libraries.mapsplatform.secrets-gradle-plugin"

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
