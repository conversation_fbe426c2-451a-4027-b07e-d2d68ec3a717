<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:tools="http://schemas.android.com/tools"
  xmlns:android="http://schemas.android.com/apk/res/android" package="com.dge.academy">

  <queries>
    <package android:name="ae.uaepass.mainapp" />
    <package android:name="ae.uaepass.mainapp.qa" />
    <package android:name="ae.uaepass.mainapp.stg" />
  </queries>

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
  <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.WRITE_CONTACTS" />

  <uses-feature
    android:name="android.hardware.camera"
    android:required="false" />

  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />

  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:mimeType="*/*" />
    </intent>
  </queries>

  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher"
    android:allowBackup="true"
    android:supportsRtl="true"
    android:theme="@style/AppTheme"
    android:usesCleartextTraffic="true"
    android:windowSoftInputMode="adjustPan"
    android:screenOrientation="portrait"
    android:networkSecurityConfig="@xml/network_security_config">
    <meta-data
      android:name="com.google.firebase.messaging.default_notification_icon"
      android:resource="@mipmap/ic_launcher" />

    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="${GM_API_KEY}" />

    <meta-data
      android:name="firebase_performance_logcat_enabled"
      android:value="true" />

    <meta-data
      android:name="firebase_performance_collection_enabled"
      android:value="true" />

    <meta-data
      android:name="firebase_performance_collection_deactivated"
      android:value="false" />

    <!-- //UAE PASS START Adding Custom Scheme and Host -->
    <activity
      android:name="com.uaepass.LoginActivity"
      android:launchMode="singleTask"
      android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:host="${host_success}"
          android:scheme="${scheme}" />
        <data
          android:host="${host_failure}"
          android:scheme="${scheme}" />
      </intent-filter>
    </activity>
    <!-- //UAE PASS END Adding Custom Scheme and Host -->

    <activity
      android:name=".MainActivity"
      android:launchMode="singleTask"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:windowSoftInputMode="adjustPan"
      android:exported="true">

      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>

      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https" android:host="adsgstaging.elearning.ae" android:pathPattern="/local/wstotara/redirect.php" />
        <data android:scheme="https" android:host="learn.adsg.gov.ae" android:pathPattern="/local/wstotara/redirect.php" />
      </intent-filter>

    </activity>
    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
  </application>

</manifest>
