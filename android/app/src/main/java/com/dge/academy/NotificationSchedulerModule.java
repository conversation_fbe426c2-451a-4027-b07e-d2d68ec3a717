package com.dge.academy;

import android.Manifest;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.work.Data;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.Arguments;

import java.util.concurrent.TimeUnit;

public class NotificationSchedulerModule extends ReactContextBaseJavaModule {

    private static final String MODULE_NAME = "NotificationScheduler";
    private static final String CHANNEL_ID = "sign_in_reminder_channel";
    private static final String CHANNEL_NAME = "Sign In Reminders";
    private static final String PREFS_NAME = "notification_scheduler_prefs";
    private static final String KEY_ENABLED = "notifications_enabled";
    private static final String WORK_TAG = "sign_in_reminder_work";

    public NotificationSchedulerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @ReactMethod
    public void checkPermission(Promise promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+
                boolean hasPermission = ActivityCompat.checkSelfPermission(
                    getReactApplicationContext(),
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED;

                WritableMap result = Arguments.createMap();
                result.putBoolean("granted", hasPermission);
                result.putString("status", hasPermission ? "granted" : "denied");
                promise.resolve(result);
            } else {
                // Pre-Android 13
                NotificationManagerCompat notificationManager = NotificationManagerCompat.from(getReactApplicationContext());
                boolean enabled = notificationManager.areNotificationsEnabled();

                WritableMap result = Arguments.createMap();
                result.putBoolean("granted", enabled);
                result.putString("status", enabled ? "granted" : "denied");
                promise.resolve(result);
            }
        } catch (Exception e) {
            promise.reject("PERMISSION_CHECK_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void requestPermission(Promise promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                WritableMap result = Arguments.createMap();
                result.putBoolean("granted", false);
                result.putString("status", "denied");
                result.putString("message", "Please enable notifications in system settings");
                promise.resolve(result);
            } else {
                // Pre-Android 13, notifications are enabled by default
                WritableMap result = Arguments.createMap();
                result.putBoolean("granted", true);
                result.putString("status", "granted");
                promise.resolve(result);
            }
        } catch (Exception e) {
            promise.reject("PERMISSION_REQUEST_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void startNotifications(double intervalMinutes, String title, String body, Promise promise) {
        try {
            createNotificationChannel();
            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean(KEY_ENABLED, true)
                .putLong("interval_minutes", (long) intervalMinutes)
                .putString("notification_title", title)
                .putString("notification_body", body)
                .apply();

            WorkManager.getInstance(getReactApplicationContext()).cancelAllWorkByTag(WORK_TAG);

            Data workData = new Data.Builder()
                .putLong("scheduled_time", System.currentTimeMillis())
                .build();

            OneTimeWorkRequest workRequest = new OneTimeWorkRequest.Builder(NotificationWorker.class)
                .setInitialDelay((long) intervalMinutes, TimeUnit.MINUTES)
                .addTag(WORK_TAG)
                .setInputData(workData)
                .build();

            WorkManager.getInstance(getReactApplicationContext()).enqueue(workRequest);

            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("message", "Notifications started successfully");
            result.putDouble("intervalMinutes", intervalMinutes);
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("START_NOTIFICATIONS_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void stopNotifications(Promise promise) {
        try {
            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean(KEY_ENABLED, false)
                .apply();

            WorkManager.getInstance(getReactApplicationContext()).cancelAllWorkByTag(WORK_TAG);

            NotificationManager notificationManager = (NotificationManager) getReactApplicationContext().getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.cancelAll();
            }

            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("message", "Notifications stopped successfully");
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("STOP_NOTIFICATIONS_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void getStatus(Promise promise) {
        try {
            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            boolean enabled = prefs.getBoolean(KEY_ENABLED, false);
            long intervalMinutes = prefs.getLong("interval_minutes", 1);

            WritableMap result = Arguments.createMap();
            result.putBoolean("enabled", enabled);
            result.putDouble("intervalMinutes", intervalMinutes);
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("GET_STATUS_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void sendTestNotification(String title, String body, Promise promise) {
        try {
            createNotificationChannel();

            // Set notification content from parameters
            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit()
                .putString("notification_title", title)
                .putString("notification_body", body)
                .apply();

            OneTimeWorkRequest workRequest = new OneTimeWorkRequest.Builder(NotificationWorker.class)
                .setInitialDelay(1, TimeUnit.SECONDS) // 1 second delay for immediate testing
                .addTag(WORK_TAG + "_test")
                .build();

            WorkManager.getInstance(getReactApplicationContext()).enqueue(workRequest);

            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("message", "Test notification scheduled");
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("TEST_NOTIFICATION_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void setAppLanguage(String language, Promise promise) {
        try {
            // Convert React Native language values to locale codes
            String localeCode;
            if ("arabic".equals(language)) {
                localeCode = "ar";
            } else if ("english".equals(language)) {
                localeCode = "en";
            } else {
                localeCode = "en"; // default to English
            }

            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit()
                .putString("app_language", localeCode)
                .apply();

            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("language", language);
            result.putString("localeCode", localeCode);
            result.putString("message", "App language saved successfully");
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("SET_LANGUAGE_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void updateNotificationContent(String title, String body, Promise promise) {
        try {
            SharedPreferences prefs = getReactApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            prefs.edit()
                .putString("notification_title", title)
                .putString("notification_body", body)
                .apply();

            WritableMap result = Arguments.createMap();
            result.putBoolean("success", true);
            result.putString("title", title);
            result.putString("body", body);
            result.putString("message", "Notification content updated successfully");
            promise.resolve(result);

        } catch (Exception e) {
            promise.reject("UPDATE_NOTIFICATION_CONTENT_ERROR", e.getMessage());
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription("Notifications to remind you to sign in to Tomouh");
            channel.setShowBadge(true);

            NotificationManager notificationManager = getReactApplicationContext().getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }
}
