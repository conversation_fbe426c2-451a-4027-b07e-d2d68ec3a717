package com.dge.academy;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.work.Worker;
import androidx.work.WorkerParameters;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;
import androidx.work.Data;

import java.util.concurrent.TimeUnit;
import com.dge.academy.R;
import android.util.Log;

public class NotificationWorker extends Worker {

    private static final String CHANNEL_ID = "sign_in_reminder_channel";
    private static final String CHANNEL_NAME = "Sign In Reminders";
    private static final String PREFS_NAME = "notification_scheduler_prefs";
    private static final String KEY_ENABLED = "notifications_enabled";
    private static final String WORK_TAG = "sign_in_reminder_work";

    public NotificationWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }

    @NonNull
    @Override
    public Result doWork() {
        try {
            SharedPreferences prefs = getApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            boolean enabled = prefs.getBoolean(KEY_ENABLED, false);
            if (!enabled) {
                return Result.success();
            }
            createNotificationChannel();
            sendNotification();
            scheduleNextNotification();

            return Result.success();

        } catch (Exception e) {
            return Result.retry();
        }
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription("Notifications to remind you to sign in to Tomouh");
            channel.setShowBadge(true);

            NotificationManager notificationManager = getApplicationContext().getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private void sendNotification() {
        try {
            SharedPreferences prefs = getApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            String title = prefs.getString("notification_title", "Sign In Reminder");
            String body = prefs.getString("notification_body", "Don't forget to sign in to continue your learning journey!");
            Intent intent = new Intent(getApplicationContext(), MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

            PendingIntent pendingIntent = PendingIntent.getActivity(
                getApplicationContext(),
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            NotificationCompat.Builder builder = new NotificationCompat.Builder(getApplicationContext(), CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(title)
                .setContentText(body)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setVibrate(new long[]{1000, 1000, 1000, 1000, 1000})
                .setSound(android.provider.Settings.System.DEFAULT_NOTIFICATION_URI);

            NotificationManager notificationManager = (NotificationManager) getApplicationContext().getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.notify(1, builder.build());
            }
        } catch (Exception e) {
            // Handle error silently
        }
    }

    private void scheduleNextNotification() {
        SharedPreferences prefs = getApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        long intervalMinutes = prefs.getLong("interval_minutes", 1);
        Data workData = new Data.Builder()
            .putLong("scheduled_time", System.currentTimeMillis())
            .build();

        OneTimeWorkRequest workRequest = new OneTimeWorkRequest.Builder(NotificationWorker.class)
            .setInitialDelay(intervalMinutes, TimeUnit.MINUTES)
            .addTag(WORK_TAG)
            .setInputData(workData)
            .build();

        WorkManager.getInstance(getApplicationContext()).enqueue(workRequest);
    }
}
