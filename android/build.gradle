// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        pdfViewer = "3.1.0-beta.1"
        // fix clickable hyperlink in the pdf on android(react-native-view-pdf)

        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.0")  //kotlin plugin
        classpath('com.google.gms:google-services:4.3.14')
        classpath("com.google.firebase:firebase-crashlytics-gradle:2.9.0")
        classpath("com.google.firebase:perf-plugin:1.4.2")
        classpath('com.google.android.libraries.mapsplatform.secrets-gradle-plugin:secrets-gradle-plugin:2.0.1')
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}
def REACT_NATIVE_VERSION = new File(['node', '--print', "JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())

allprojects {
subprojects {
    afterEvaluate { project ->
        if (project.name == 'react-native-check-app-install') {
            project.android.compileSdkVersion = 34
        }
    }
}
    configurations.all {
        resolutionStrategy {
            // Remove this override in 0.65+, as a proper fix is included in react-native itself.
            force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
        }
    }
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }

        google()
        mavenCentral()//this is required for resolving the dependencies declared in app/build.gradle
        jcenter()//pdf view library is using a version from this repository jcenter
        maven { url 'https://www.jitpack.io' }
        flatDir{
            dirs "$rootDir/../node_modules/react-native-uaepass/android/libs"
        }
    }
}

apply plugin: "com.facebook.react.rootproject"
