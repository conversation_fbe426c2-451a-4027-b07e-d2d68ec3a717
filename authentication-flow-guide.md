# DGE Academy Mobile App - Complete Authentication Flow Guide

## Overview

This guide provides a comprehensive walkthrough of the authentication flows used in the DGE Academy React Native mobile application. The app supports multiple authentication methods including UAE Pass OAuth, biometric authentication, and email OTP login.

## Table of Contents

1. [Authentication Architecture](#authentication-architecture)
2. [UAE Pass OAuth Flow](#uae-pass-oauth-flow)
3. [Biometric Authentication](#biometric-authentication)
4. [Email OTP Authentication](#email-otp-authentication)
5. [Totara LMS Authentication](#totara-lms-authentication)
6. [Token Management](#token-management)
7. [Environment Configuration](#environment-configuration)
8. [Implementation Examples](#implementation-examples)

---

## Authentication Architecture

The DGE Academy app uses a multi-layered authentication system:

```mermaid
graph TD
    A[User Login Request] --> B{Authentication Method}
    B -->|UAE Pass| C[UAE Pass OAuth Flow]
    B -->|Biometric| D[Biometric Authentication]
    B -->|Email OTP| E[Email OTP Flow]

    C --> F[Get Authorization Code]
    F --> G[Exchange for Access Token]
    G --> H[Get User Profile]
    H --> I[Authenticate with DGE Workspace]

    D --> J[Biometric Challenge]
    J --> K[Sign Challenge]
    K --> L[Verify Signature]
    L --> I

    E --> M[Send OTP]
    M --> N[Verify OTP]
    N --> I

    I --> O[Get Liferay Access Token]
    O --> P[Initialize Totara Session]
    P --> Q[User Authenticated]
```

### Key Components

- **UAE Pass**: Government identity provider for UAE citizens
- **DGE Workspace**: Government workspace platform APIs
- **Totara LMS**: Learning management system
- **Biometric Authentication**: Device-based biometric login
- **Token Management**: JWT token handling and refresh

---

## UAE Pass OAuth Flow

### 1. Environment Configuration

```javascript
// Environment-specific UAE Pass URLs
const UAE_PASS_BASE_URL_STG = 'https://stg-id.uaepass.ae';
const UAE_PASS_BASE_URL_PROD = 'https://id.uaepass.ae';

const BASE_URL = ENV === 'prod' ? UAE_PASS_BASE_URL_PROD : UAE_PASS_BASE_URL_STG;

// OAuth Endpoints
const AUTH_URL = BASE_URL + '/idshub/authorize';
const ACCESS_TOKEN_URL = BASE_URL + '/idshub/token?grant_type=authorization_code';
const PROFILE_URL = BASE_URL + '/idshub/userinfo';
const LOGOUT_URL = BASE_URL + '/idshub/logout';
```

### 2. Client Configuration by Environment

```javascript
// Production Configuration
const PROD_CONFIG = {
  clientId: 'dgeworkspace_web_prod',
  mobileClientId: 'dgemylearning_mob_prod',
  clientSecret: '', // Empty - Security Risk
};

// Staging Configuration
const STG_CONFIG = {
  clientId: 'dgegovwmsw_web_stage',
  mobileClientId: 'dgegovlearningw_web_stage',
  clientSecret: 'QDGtVPagCdEX7GEY',
};

// Development Configuration
const DEV_CONFIG = {
  clientId: 'sandbox_stage',
  clientSecret: 'fkUNvRXUaQSqxUgrs5wcoThu',
};
```

### 3. OAuth Flow Steps

#### Step 1: Authorization Request

```javascript
const getAuthUrl = (isUaePassAppInstalled) => {
  return (
    AUTH_URL +
    '?redirect_uri=' +
    REDIRECT_URL +
    '&client_id=' +
    UAE_PASS_CLIENT_ID +
    '&response_type=code' +
    '&scope=' +
    SCOPE +
    '&acr_values=' +
    (isUaePassAppInstalled ? ACR_VALUES_MOBILE : ACR_VALUES_WEB) +
    '&ui_locales=' +
    LANGUAGE
  );
};

// OAuth Parameters
const OAUTH_PARAMS = {
  responseType: 'code',
  scope: 'urn:uae:digitalid:profile:general', // prod/staging
  acrValuesMobile: 'urn:digitalid:authentication:flow:mobileondevice',
  acrValuesWeb: 'urn:safelayer:tws:policies:authentication:level:low',
  language: 'en',
  redirectUri: 'mynavigatorapp://uaePassSuccess',
};
```

#### Step 2: Handle Authorization Response

```javascript
// iOS Deep Link Handling (AppDelegate.m)
- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
  UAEPass *obj = [[UAEPass alloc] init];
  NSString *successHost = [obj getSuccessHost];
  NSString *failureHost = [obj getFailureHost];

  if ([url.absoluteString containsString:successHost]) {
    [obj handleLoginSuccess];
    return YES;
  } else if ([url.absoluteString containsString:failureHost]) {
    [obj handleLoginFailure];
    return NO;
  }

  return [RCTLinkingManager application:application openURL:url options:options];
}
```

#### Step 3: Exchange Authorization Code for Access Token

```javascript
export const getAccessTokenRequest = (code) => {
  return async (dispatch) => {
    dispatch(getAccessToken());

    try {
      const response = await fetch(
        `${ACCESS_TOKEN_URL}&redirect_uri=${REDIRECT_URL}&code=${code}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: 'Basic ' + base64.encode(`${UAE_PASS_CLIENT_ID}:${UAE_PASS_SECRET_ID}`),
          },
        },
      );

      const responseJson = await response.json();

      if (responseJson.errorCode) {
        dispatch(getAccessTokenFailure(responseJson));
      } else {
        dispatch(getAccessTokenSuccess(responseJson));
      }
    } catch (error) {
      dispatch(getAccessTokenFailure(error));
    }
  };
};
```

#### Step 4: Get User Profile

```javascript
export const getProfileRequest = (token) => {
  return async (dispatch) => {
    dispatch(getProfile());

    try {
      const response = await fetch(PROFILE_URL, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
      });

      const responseJson = await response.json();

      if (responseJson.errorCode) {
        dispatch(getProfileFailure(responseJson));
      } else {
        dispatch(getProfileSuccess(responseJson));
      }
    } catch (error) {
      dispatch(getProfileFailure(error));
    }
  };
};
```

### 4. Mobile App vs WebView Authentication

```javascript
const onPress = async () => {
  if (Platform.OS === 'ios') {
    const uaepassUrlScheme = ENV === 'prod' ? 'uaepass://' : 'uaepassstg://';

    try {
      const supported = await Linking.canOpenURL(uaepassUrlScheme);

      if (supported) {
        // Use native UAE Pass app
        const config = {
          ...UAEPassConfig,
          clientId: ENV === 'prod' ? UAE_PASS_CLIENT_ID_MOBILE_PROD : UAE_PASS_CLIENT_ID_MOBILE,
        };
        setMobileLoggedIn(true);
        await onLoginUAEPassModule(config);
      } else {
        // Fallback to WebView
        setMobileLoggedIn(false);
        setIsMyWebViewEnable(true);
      }
    } catch (error) {
      // Handle error - fallback to WebView
      setIsMyWebViewEnable(true);
      setMobileLoggedIn(false);
    }
  } else {
    // Android - Check if UAE Pass app is installed
    const isInstalled = await AppInstalledChecker.checkPackageName(UAE_PASS_PACKAGE_ID);

    const config = {
      ...UAEPassConfig,
      clientId:
        isInstalled && ENV === 'prod'
          ? UAE_PASS_CLIENT_ID_MOBILE_PROD
          : isInstalled && ENV === 'stg'
            ? UAE_PASS_CLIENT_ID_MOBILE
            : UAE_PASS_CLIENT_ID,
    };

    setMobileLoggedIn(isInstalled);
    await onLoginUAEPassModule(config);
  }
};
```

---

## Biometric Authentication

### 1. Biometric Setup

```javascript
const turnBiometricsOn = async () => {
  try {
    const email = store.getState().getWFMProfile.response?.email;
    if (!email) throw new Error('Email not found');

    await AsyncStorage.setItem('email', email);
    const { publicKey } = await rnBiometrics.createKeys();
    const deviceId = await getOrCreateDeviceId();

    // Send public key to server
    const success = await sendPublicKeyToServer(publicKey, deviceId);

    if (success) {
      await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.BIOMETRICS.LOGIN, BiometricLoginEnum.ENABLED);
    }

    return success;
  } catch (error) {
    throw error;
  }
};
```

### 2. Biometric Authentication Flow

```javascript
const authenticateWithBiometrics = async (email) => {
  try {
    // Generate challenge
    const challenge = getBiometricsChallenge(email);

    // Sign challenge with biometric
    const signature = await signBiometricChallenge(challenge);

    // Verify with server
    const deviceId = await getOrCreateDeviceId();
    return await verifySignatureWithServer(email, challenge, signature, deviceId);
  } catch (error) {
    throw error;
  }
};

const getBiometricsChallenge = (email) => {
  const epochTimeSeconds = Math.round(new Date().getTime() / 1000).toString();
  return epochTimeSeconds + ' challenge ' + email;
};

const signBiometricChallenge = async (challenge) => {
  const { signature, error } = await rnBiometrics.createSignature({
    promptMessage: 'Sign in',
    payload: challenge,
  });

  if (error) throw new Error(error);
  if (!signature) throw new Error('Signature failed');

  return signature;
};
```

### 3. Server-Side Verification

```javascript
// Send public key to server
export const sendPublicKeyToServer = async (publicKey, deviceId) => {
  const url = `${BASE_URL}${API_URL.mobileBiometricCreate.url}`;

  try {
    const headers = getCustomHeaders(store.getState().getWFMProfile.response?.liferayaccesstoken);

    const body = {
      biometricId: publicKey,
      deviceId,
    };

    const result = await axios.post(url, body, { headers });
    return result?.data?.code === 200;
  } catch (error) {
    return false;
  }
};

// Verify signature with server
export const verifySignatureWithServer = async (email, challenge, signature, deviceId) => {
  const url = `${BASE_URL}${API_URL.mobileBiometricVerify.url}`;

  try {
    const body = {
      email,
      challenge,
      signature,
      deviceId,
    };

    const result = await axios.post(url, body, {
      headers: { 'content-type': 'application/json' },
    });

    return result;
  } catch (error) {
    throw error;
  }
};
```

---

## Email OTP Authentication

### 1. Send OTP

```javascript
export const sendCode = async (token, body) => {
  const url = BASE_URL + API_URL.generateCode.url;

  try {
    const headers = getCustomHeaders(token);
    const result = await customFetch(url, 'POST', body, {}, headers);
    return result;
  } catch (error) {
    Toast.show({
      text: JSON.stringify(error),
      textStyle: { textAlign: 'left' },
    });
  }
};
```

### 2. Verify OTP

```javascript
const verifyOtp = async (otp) => {
  try {
    setIsLoading(true);

    const result = await verifyCode(token, {
      email: email,
      code: otp,
    });

    if (result?.status === 'whitelisted') {
      dispatch(loginRequest(result));
      dispatch(getProfileSuccess(result));

      // Set token expiry
      const expiresInSeconds = result.expiresin || 0;
      const currentTime = Math.floor(Date.now() / 1000);
      const expiryTimestamp = currentTime + expiresInSeconds;
      await AsyncStorage.setItem('tokenExpiry', JSON.stringify(expiryTimestamp));

      dispatch(startTokenExpiryTimer(expiresInSeconds, navigation));
      setIsLoading(false);

      navigation.navigate('SessionContainer');
      navigation.replace('SessionContainer', { from: 'Login' });
    }
  } catch (error) {
    setIsLoading(false);
    setIsOtpVerifyError(true);
  }
};
```

---

## Totara LMS Authentication

### 1. Native Login Setup

```javascript
const loginSecretPromise = () =>
  fetchData(TOTARA_AUTH_BASE_URL + '/totara/mobile/login_setup.php', {
    method: 'GET',
    headers: { [DEVICE_REGISTRATION]: config.userAgent },
  });

const loginPromise = (loginSecret) => {
  return fetchData(TOTARA_AUTH_BASE_URL + '/totara/mobile/login.php', {
    method: 'POST',
    body: JSON.stringify({
      loginsecret: loginSecret,
      username: nativeLoginState.inputUsername,
      password: nativeLoginState.inputPassword,
    }),
    headers: {
      [DEVICE_REGISTRATION]: config.userAgent,
      'Content-Type': 'application/json',
    },
  });
};
```

### 2. Device Registration

```javascript
export const registerDevice = (fetchData, storage) => async (setup) => {
  try {
    const apiKey = await fetchData(TOTARA_AUTH_BASE_URL + '/totara/mobile/device_register.php', {
      method: 'POST',
      body: JSON.stringify({
        setupsecret: setup.secret,
      }),
    });

    const logindate = new Date().toString();
    const siteInfoData = JSON.stringify(setup.siteInfo);

    await Promise.all([
      storage.setItem('logindate', logindate),
      storage.setItem('apiKey', apiKey.apikey),
      storage.setItem('siteInfo', siteInfoData),
      storage.setItem('host', TOTARA_AUTH_BASE_URL),
    ]);

    return {
      apiKey: apiKey.apikey,
      host: TOTARA_AUTH_BASE_URL,
      siteInfo: setup.siteInfo,
    };
  } catch (error) {
    return {};
  }
};
```

### 3. Apollo GraphQL Client Setup

```javascript
export const createApolloClient = (apiKey, host, cache) => {
  const authLink = setContext((_, { headers }) => ({
    headers: {
      ...headers,
      [AUTH_HEADER_FIELD]: apiKey,
    },
    http: { includeQuery: !config.mobileApi.persistentQuery },
  }));

  const httpLink = new HttpLink({ uri: config.apiUri(host) });
  const timeOutLinkWithHttpLink = new ApolloLinkTimeout(10 * 1000).concat(httpLink);

  const link = ApolloLink.from([
    errorLink,
    new RetryLink({
      attempts: {
        max: 2,
        retryIf: (error) => {
          if (error.statusCode && error.statusCode === 401) {
            return false; // do not retry on 401 errors
          }
          return !!error;
        },
      },
    }),
    authLink,
    timeOutLinkWithHttpLink,
  ]);

  return new ApolloClient({
    link: link,
    cache,
  });
};
```

---

## Token Management

### 1. Token Storage and Retrieval

```javascript
// Bootstrap session from stored tokens
export const bootstrap = (asyncStorage) => async () => {
  const [apiKey, host, siteInfo] = await Promise.all([
    asyncStorage.getItem('apiKey'),
    asyncStorage.getItem('host'),
    asyncStorage.getItem('siteInfo'),
  ]);

  if (apiKey !== null && host !== null && siteInfo !== null) {
    return {
      apiKey: apiKey,
      host: host,
      siteInfo: JSON.parse(siteInfo),
    };
  }

  return undefined;
};
```

### 2. Token Expiry Management

```javascript
export const startTokenExpiryTimer = (expiresInSeconds, navigation) => {
  return (dispatch) => {
    const timer = setTimeout(() => {
      dispatch(confirmedLogout(navigation));
    }, expiresInSeconds * 1000);

    dispatch({
      type: 'SET_TOKEN_EXPIRY_TIMER',
      payload: timer,
    });
  };
};

// Check token expiry on API errors
const checkTokenExpiry = async (error) => {
  if (error.response && error.response.status === 401) {
    const expiryTimestampString = await AsyncStorage.getItem('tokenExpiry');
    const expiryTimestamp = JSON.parse(expiryTimestampString);
    const currentTime = Math.floor(Date.now() / 1000);

    if (currentTime >= expiryTimestamp) {
      dispatch(confirmedLogout(navigationRef.current));
    }
  }
};
```

### 3. Logout Process

```javascript
const logout = async () => {
  try {
    await AsyncStorage.removeItem('logindate');
    await AsyncStorage.removeItem('tokenExpiry');
    await AsyncStorage.removeItem('apiKey');
    await AsyncStorage.removeItem('host');
    await AsyncStorage.removeItem('siteInfo');
    await EncryptedStorage.clear();
  } catch (error) {
    // Handle logout error
  }
};
```

---

## Environment Configuration

### Production Environment

```javascript
const PROD_CONFIG = {
  dgeWorkspace: 'https://admin.workspace.dge.gov.ae/',
  uaePass: 'https://id.uaepass.ae',
  totara: 'https://learn.adsg.gov.ae',
  tomouh: 'https://api.tomouh.gov.ae/',
  supportCenter: 'https://workspace.dge.gov.ae/app/v2',
};
```

### Staging Environment

```javascript
const STG_CONFIG = {
  dgeWorkspace: 'https://admin.stg.workspace.dge.gov.ae/',
  uaePass: 'https://stg-id.uaepass.ae',
  totara: 'https://adsgstaging.elearning.ae',
  tomouh: 'https://stg.api.tomouh.gov.ae/',
  supportCenter: 'https://stg.workspace.dge.gov.ae/app/v2',
};
```

### Development Environment

```javascript
const DEV_CONFIG = {
  dgeWorkspace: 'https://admin.dev.workspace.dge.gov.ae/',
  uaePass: 'https://stg-id.uaepass.ae', // shares staging
  totara: 'https://adsgstaging.elearning.ae', // shares staging
  tomouh: 'https://stg.api.tomouh.gov.ae/', // shares staging
};
```

## Implementation Examples

### Complete Authentication Flow Example

```javascript
// Main authentication handler
export const authenticateUser = async (method, credentials) => {
  try {
    let authResult;

    switch (method) {
      case 'uaepass':
        authResult = await handleUAEPassAuth(credentials.accessCode);
        break;
      case 'biometric':
        authResult = await handleBiometricAuth(credentials.email);
        break;
      case 'otp':
        authResult = await handleOTPAuth(credentials.email, credentials.otp);
        break;
      default:
        throw new Error('Invalid authentication method');
    }

    // Common post-authentication steps
    if (authResult.success) {
      await storeTokens(authResult.tokens);
      await initializeTotaraSession(authResult.apiKey);
      return { success: true, user: authResult.user };
    }

    return { success: false, error: authResult.error };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// UAE Pass authentication handler
const handleUAEPassAuth = async (accessCode) => {
  // 1. Exchange access code for tokens
  const tokenResponse = await getAccessTokenRequest(accessCode);

  // 2. Get user profile from UAE Pass
  const profileResponse = await getProfileRequest(tokenResponse.access_token);

  // 3. Authenticate with DGE Workspace
  const dgeResponse = await authenticateUserToWFM(accessCode);

  return {
    success: true,
    tokens: {
      uaePassToken: tokenResponse.access_token,
      liferayToken: dgeResponse.liferayaccesstoken,
      expiresIn: dgeResponse.expiresin,
    },
    user: profileResponse,
    apiKey: dgeResponse.apiKey,
  };
};
```

### Error Handling and Retry Logic

```javascript
// Global error handler for authentication
const handleAuthError = (error, method) => {
  const errorMap = {
    401: 'Invalid credentials',
    403: 'Access denied',
    429: 'Too many requests',
    500: 'Server error',
  };

  const message = errorMap[error.status] || 'Authentication failed';

  crashReportLogger(error, {
    component: `Authentication ${method}`,
    additionalInfo: message,
  });

  return { success: false, error: message };
};

// Retry mechanism for network failures
const retryAuth = async (authFunction, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await authFunction();
    } catch (error) {
      if (attempt === maxRetries) throw error;

      const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
};
```

### Security Best Practices

```javascript
// Secure token storage
const storeTokens = async (tokens) => {
  try {
    // Store sensitive tokens in encrypted storage
    await EncryptedStorage.setItem('liferayToken', tokens.liferayToken);
    await EncryptedStorage.setItem('uaePassToken', tokens.uaePassToken);

    // Store expiry in regular storage
    const expiryTimestamp = Math.floor(Date.now() / 1000) + tokens.expiresIn;
    await AsyncStorage.setItem('tokenExpiry', JSON.stringify(expiryTimestamp));
  } catch (error) {
    throw new Error('Failed to store authentication tokens');
  }
};

// Token validation
const validateToken = async () => {
  try {
    const expiryTimestamp = await AsyncStorage.getItem('tokenExpiry');
    const currentTime = Math.floor(Date.now() / 1000);

    if (!expiryTimestamp || currentTime >= JSON.parse(expiryTimestamp)) {
      throw new Error('Token expired');
    }

    return true;
  } catch (error) {
    return false;
  }
};
```

---

## Summary

This comprehensive authentication flow guide covers all authentication methods used in the DGE Academy mobile application:

1. **UAE Pass OAuth Flow**: Government identity provider integration with mobile app and WebView fallback
2. **Biometric Authentication**: Device-based biometric login with server-side verification
3. **Email OTP Authentication**: SMS/Email-based one-time password authentication
4. **Totara LMS Authentication**: Learning management system integration
5. **Token Management**: Secure token storage, expiry handling, and session management

### Key Security Considerations

- **Environment Separation**: Different configurations for production, staging, and development
- **Secure Storage**: Use encrypted storage for sensitive tokens
- **Token Expiry**: Implement proper token expiry and refresh mechanisms
- **Error Handling**: Comprehensive error handling with proper logging
- **Retry Logic**: Network failure handling with exponential backoff
- **Deep Link Security**: Proper validation of deep link redirects

### Backend Proxy Integration

When implementing the backend middleman architecture:

1. **Proxy all authentication endpoints** through your backend
2. **Store sensitive credentials** (API keys, client secrets) on the backend only
3. **Implement token validation** and refresh on the backend
4. **Add rate limiting** and security headers
5. **Log all authentication attempts** for security monitoring

This guide provides the foundation for implementing secure authentication flows and migrating to a backend proxy architecture.
