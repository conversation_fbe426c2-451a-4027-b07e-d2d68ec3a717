module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  overrides: [
    {
      test: (fileName) => !fileName.includes('node_modules/react-native-maps'),
      plugins: [[require('@babel/plugin-transform-private-methods'), { loose: true }]],
    },
  ],
  plugins: [
    'react-native-reanimated/plugin',
    [
      'module-resolver',
      {
        root: ['./src'],
        extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
        alias: {
          '*': '.',
          '@__mocks__': './src/__mocks__',
          '@api': './src/api',
          '@assets': './src/assets',
          '@components': './src/components',
          '@hooks': './src/hooks',
          '@interfaces': './src/interfaces',
          '@lib': './src/lib',
          '@models': './src/models',
          '@navigation': './src/navigation',
          '@resources': './src/resources',
          '@screens': './src/screens',
          '@theme': './src/theme',
          '@totara': './src/totara',
          '@utils': './src/utils',
          '@analytics': './src/analytics',
        },
      },
    ],
  ],
};
