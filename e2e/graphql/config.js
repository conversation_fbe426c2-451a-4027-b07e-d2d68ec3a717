/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2021 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD’s customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */
const port = 8089;
const mockServerUrl = `http://127.0.0.1:${port}`;
const mockUsername = 'mockeduser';
const mockPassword = 'mockedpw';
module.exports = { port, mockServerUrl, mockUsername, mockPassword };
