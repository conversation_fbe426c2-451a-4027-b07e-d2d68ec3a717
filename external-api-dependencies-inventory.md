# External API Dependencies Inventory

## Executive Summary

This comprehensive analysis identified **85+ external API endpoints** across **15 major service categories** in the DGE Academy React Native mobile application. The application integrates with multiple government services, third-party cloud platforms, and authentication systems across production, staging, and development environments.

### Critical Security Findings

- **20+ hardcoded API keys and credentials** found in source code
- **Shared credentials** across production and staging environments
- **Exposed Firebase API keys** in configuration files
- **Multiple hardcoded API keys** for external services

### Migration Complexity Assessment

- **High Priority**: 5 core government APIs requiring immediate proxy implementation
- **Medium Priority**: Firebase services, AI services, and authentication flows
- **Low Priority**: Deep linking and legacy endpoints

---

## 1. Government & Enterprise APIs

### 1.1 DGE Workspace APIs

**Base URLs by Environment:**

- **Production**: `https://admin.workspace.dge.gov.ae/`
- **Staging**: `https://admin.stg.workspace.dge.gov.ae/`
- **Development**: `https://admin.dev.workspace.dge.gov.ae/`

**Complete API Endpoints Catalog (40+ DGE-specific endpoints):**

**Authentication & User Management:**

- Authentication: `o/adsg-authentication/v1.0/authenticate`
- Email Authentication: `o/adsg-authentication/v1.0/authenticate-by-email`
- Biometric Authentication: `o/adsg-authentication/v1.0/biometric`
- User Profile Search: `o/adsg-profiles-search-results/v1.0/search`
- User Profile Update: `o/adsg-profiles-search-results/v1.0/updateUser`
- Profile Search by Keyword: `o/adsg-profiles-search-results/v1.0/profile-search?keyword=`

**Talent Management:**

- Talent Profile: `o/adsg-talent-profile/v1.0/talent`
- Talent Recommendation: `o/adsg-talent-profile/v1.0/talent/recommendation/`
- Talent Recommendation Request: `o/adsg-talent-profile/v1.0/talent/recommendation-request/`
- Profile Progress: `o/adsg-talent-team/v1.0/profile-progress`
- Roles & Permissions: `o/adsg-talent-team/v1.0/roles-permissions/`

**Document Management:**

- Document Upload: `o/adsg-document-upload/v1.0/obs-document/`
- Document Base64: `o/adsg-document-upload/v1.0/obs-document`
- Document URL: `o/adsg-document-upload/v1.0/obs-document-url`

**Communication & Support:**

- SMS Verification Generate: `o/adsg-smpp-verification/v1.0/generate-code`
- SMS Verification Verify: `o/adsg-smpp-verification/v1.0/verify-code`
- FAQ System: `o/adsg-faq-headless/v1.0/faq`
- Contact Support: `o/adsg-contact-support-rest/v1.0/contact-support`

**Course & Learning Management:**

- Course Referrals: `o/adsg-courses/v1.0/courses/referred/course`
- Received Referrals: `o/adsg-courses/v1.0/courses/referred/received`
- Sent Referrals: `o/adsg-courses/v1.0/courses/referred/sent`
- Referral Count: `o/adsg-courses/v1.0/courses/referred/unread-count`
- Referral Status Update: `o/adsg-courses/v1.0/courses/referred/update-read-status`

**Recommendations System:**

- Add Recommendation: `o/adsg-talent-recommendations/v1.0/add/recommendation`
- Recommendations List: `o/adsg-talent-recommendations/v1.0/recommendations/list`
- Update Recommendation: `o/adsg-talent-recommendations/v1.0/update/recommendation`

**Assessment & Principles:**

- Assessment Results: `o/adsg-principle-rest/v1.0/assessment/results/`
- Assessment Add User: `o/adsg-principle-rest/v1.0/assessment/addUser/`
- Assessment Questions: `o/adsg-principle-rest/v1.0/assessment/questions/`
- Assessment Answers: `o/adsg-principle-rest/v1.0/assessment/answers/`
- Assessment PDF: `o/adsg-principle-rest/v1.0/assessment/pdf`
- Assessment Status: `o/adsg-principle-rest/v1.0/assessment/status`

**Mobile & Device Management:**

- Mobile Device Registration: `o/adsg-mobile/v1.0/mobile/register-device`
- Mobile Biometric Create: `o/adsg-mobile-biometric/v1.0/create`

**Data Management:**

- Countries List: `o/c/countries/`
- Languages: `o/c/languageses/`
- User Personal Details: `o/c/userpersonaldetailses`
- Work Experiences: `o/c/workexperiences`
- Skills Endorsements: `o/c/skillsendorsementses`
- Recommendations: `o/c/recommendationses`
- Recommend Talents: `o/c/recommendatalents`
- Talent Profile Approval: `o/c/talentprofileapprovaldetails`
- Hobbies: `o/headless-admin-list-type/v1.0/list-type-definitions?filter=name eq 'hobbies'`

**Authentication Method:**

```javascript
// CORRECTION: Basic Auth is for CRM/Support Center only, NOT for DGE Workspace APIs
// DGE Workspace APIs use Bearer Token authentication
const BASIC_AUTH_CRED = {
  username: '<EMAIL>', // Used for CRM/Support Center only
  password: 'Liferay@DGE',
  type: 'requestor',
};

// DGE Workspace APIs use Bearer Token:
// Authorization: Bearer ${liferayaccesstoken}
```

### 1.2 Totara Learning Management System

**Base URLs by Environment:**

- **Production**: `https://learn.adsg.gov.ae/webservice/rest/server.php`
- **Staging**: `https://adsgstaging.elearning.ae/webservice/rest/server.php`

**Additional Base URLs:**

- **Auth Base URL (Prod)**: `https://learn.adsg.gov.ae`
- **Auth Base URL (Staging)**: `https://adsgstaging.elearning.ae`
- **Site URL (Prod)**: `https://learn.adsg.gov.ae/`
- **Site URL (Staging)**: `https://adsgstaging.elearning.ae/`

**GraphQL API Endpoint:**

- URI Pattern: `${host}/totara/mobile/api.php`

**Key Endpoints:**

- Site Info: `/totara/mobile/site_info.php`
- Device Registration: `/totara/mobile/device_register.php`
- Login Setup: `/totara/mobile/login_setup.php`
- Native Login: `/totara/mobile/login.php`
- WebView: `/totara/mobile/device_webview.php`
- Forgot Password: `/login/forgot_password.php`
- Find Learning: `/totara/catalog/index.php`
- Login Index: `/login/index.php`

**Totara Web Service Functions (45+ functions identified):**

- Profile: `wstotara_get_profile`
- Learning Data: `wstotara_fetch_mylearningdata`
- Home Screen: `wstotara_fetch_homescreendata`
- Save for Later: `wstotara_save_forlater`
- Search Learning: `wstotara_search_learningdata`
- Search Popular/Recent: `wstotara_search_popular_recent`
- Trending Courses: `wstotara_get_trending_courses`
- Announcements: `wstotara_get_forum_discussions`
- New Releases: `wstotara_get_recent_courses`
- Notifications List: `wstotara_notification_lists`
- Read Notification: `wstotara_read_notification`
- Course Details: `wstotara_get_coursedetails`
- Latest Course Details: `wstotara_course_details`
- Course Filters: `wstotara_get_course_filters`
- Duration-based Courses: `wstotara_get_duration_based_courses`
- Book Seminar: `wstotara_book_a_seminar`
- Static Banner: `wstotara_get_static_banner`
- Book Slot: `wstotara_book_slot`
- Check Line Manager: `wstotara_check_linemanager`
- Cancel Booking: `wstotara_cancel_booked_slot`
- Badges: `wstotara_get_available_and_user_badges`
- Real-time Recommendations: `wstotara_get_realtime_course_recommendations`
- Course Rating: `wstotara_save_course_rating`
- Program Details: `wstotara_program_details`
- Learning Pathways: `wstotara_get_user_learning_pathways`
- Sub Interests: `wstotara_get_interests_by_categories`
- Mentors List: `wstotara_get_mentor_list`
- Mentor Slots: `wstotara_get_mentor_slots`
- Book Mentor Slot: `wstotara_book_mentor_slot`
- Coaches List: `wstotara_get_coach_list`

**Authentication:**

- API Key-based authentication via `X-API-KEY` header
- Apollo GraphQL client with retry logic and timeout handling
- WebService Token (wstoken) for REST API calls

### 1.3 UAE Pass Authentication Service

**Base URLs by Environment:**

- **Production**: `https://id.uaepass.ae`
- **Staging**: `https://stg-id.uaepass.ae`

**Client Credentials by Environment:**

**Production:**

- Client ID: `dgeworkspace_web_prod`
- Mobile Client ID: `dgemylearning_mob_prod`
- Client Secret: `[EMPTY - SECURITY RISK]`

**Staging:**

- Client ID: `dgegovwmsw_web_stage`
- Mobile Client ID: `dgegovlearningw_web_stage`
- Client Secret: `QDGtVPagCdEX7GEY`

**Development:**

- Client ID: `sandbox_stage`
- Client Secret: `fkUNvRXUaQSqxUgrs5wcoThu`

**Key Endpoints:**

- Authorization: `/idshub/authorize`
- Token Exchange: `/idshub/token?grant_type=authorization_code`
- User Profile: `/idshub/userinfo`
- Logout: `/idshub/logout`

### 1.4 Tomouh Government APIs

**Base URLs by Environment:**

- **Production**: `https://api.tomouh.gov.ae/`
- **Staging**: `https://stg.api.tomouh.gov.ae/`

**Support Center APIs:**

- **Production**: `https://workspace.dge.gov.ae/app/v2`
- **Staging**: `https://stg.workspace.dge.gov.ae/app/v2`

**Hardcoded Support Center Credentials:**

```javascript
// SECURITY RISK: Same credentials for both environments
const SUPPORT_CENTER_AUTH_CREDENTIALS = {
  emailAddress: '<EMAIL>',
  password: '2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG',
};
```

---

## 2. Third-Party Cloud Services

### 2.1 Firebase Services

**Project Configurations by Environment:**

**Production:**

- Project ID: `gov-academy-digital-factory`
- Project Number: `1027383114054`
- API Key (Android): `AIzaSyDSUT2l0AtE3NjShc5Qu2dN7iEmj3vPVvo`
- API Key (iOS): `AIzaSyBULjVPLXT8uJAZi_Yl_qnVeddBgxl7aG4`
- Bundle ID: `com.dge.academy`

**Staging:**

- Project ID: `tomouh-staging-app`
- Project Number: `653088755970`
- API Key (Android): `AIzaSyDyXpX-X7WvcLM6Eivsple5VfmrM9N6jTg`
- API Key (iOS): `AIzaSyCOZKgdq7J6mcFIxAqFmdZOpGuw544CZqE`
- Bundle ID: `com.dge.academy.stage`

**Development:**

- Project ID: `tomouh-dev-app`
- Project Number: `502436840651`
- API Key (Android): `AIzaSyCl26v_rqoarv53MuvGa7CxLA4f8RNryW4`
- Bundle ID: `com.dge.academy.development`

**Enabled Services:**

- Firebase Analytics
- Firebase Crashlytics
- Firebase Cloud Messaging (FCM)
- Firebase Performance Monitoring
- Firebase Remote Config

### 2.2 TAMM Conversational AI Service

**Primary AI Service:**

- **Base URL**: `*******************************************/conv-ai-engine/rag_search`
- **API Key**: `3f49dec6-847f-404a-83da-0add176a066a` (HARDCODED)
- **Timeout**: 50 seconds
- **Content Type**: `application/json`

**Additional Tomouh AI Services:**

- **RAG Search**: `${TOMOUH_BASE_URL}tamm/rag_search/`
  - API Key: `key-abc123` (HARDCODED)
- **Chat Completions**: `${TOMOUH_BASE_URL}tamm/chat/completions/`
  - API Key: `key-def456` (HARDCODED)
- **Feature Flags**: `${TOMOUH_BASE_URL}features/get-features/`
  - API Key: `key-fxf456` (HARDCODED)

**Security Risk**: Multiple API keys are hardcoded in source code without environment-based configuration.

---

## 3. Authentication & Security

### 3.1 OAuth Configurations

**UAE Pass OAuth Flow:**

- Response Type: `code`
- Scope: `urn:uae:digitalid:profile:general` (prod/staging)
- Scope: `urn:uae:digitalid:profile` (development)
- ACR Values Mobile: `urn:digitalid:authentication:flow:mobileondevice`
- ACR Values Web: `urn:safelayer:tws:policies:authentication:level:low`

**URL Schemes:**

- Custom Scheme: `mynavigatorapp://`
- Success Host: `uaePassSuccess` (iOS) / `success` (Android)
- Failure Host: `uaePassFail` (iOS) / `failure` (Android)

### 3.2 API Keys & Secrets Inventory

**Critical Security Issues:**

1. **Firebase API Keys** (Exposed in config files)
2. **UAE Pass Client Secrets** (Hardcoded in source)
3. **TAMM AI API Key** (Hardcoded in source)
4. **Support Center Credentials** (Hardcoded, same for all environments)
5. **Basic Auth Credentials** (Hardcoded in multiple files)

### 3.3 Token Management

**Authentication Headers:**

- Bearer Token: `Authorization: Bearer ${token}`
- Basic Auth: `Authorization: Basic ${base64(username:password)}`
- API Key: `X-API-KEY: ${apiKey}`
- Custom Header: `x-tamm-search-key: ${apiKey}`

---

## 4. Environment Configurations

### 4.1 Production Environment

**Primary Services:**

- DGE Workspace: `https://admin.workspace.dge.gov.ae/`
- Totara LMS: `https://learn.adsg.gov.ae/webservice/rest/server.php`
- UAE Pass: `https://id.uaepass.ae`
- Tomouh API: `https://api.tomouh.gov.ae/`
- Support Center: `https://workspace.dge.gov.ae/app/v2`

### 4.2 Staging Environment

**Primary Services:**

- DGE Workspace: `https://admin.stg.workspace.dge.gov.ae/`
- Totara LMS: `https://adsgstaging.elearning.ae/webservice/rest/server.php`
- UAE Pass: `https://stg-id.uaepass.ae`
- Tomouh API: `https://stg.api.tomouh.gov.ae/`
- Support Center: `https://stg.workspace.dge.gov.ae/app/v2`

### 4.3 Development Environment

**Primary Services:**

- DGE Workspace: `https://admin.dev.workspace.dge.gov.ae/`
- UAE Pass: `https://stg-id.uaepass.ae` (shares staging)
- Tomouh API: `https://stg.api.tomouh.gov.ae/` (shares staging)

---

## 5. Legacy & Deprecated Services

### 5.1 Unused Endpoints

**Commented/Deprecated APIs:**

- Work Experience: `o/c/userpersonaldetailses` (marked as "seems to not be in use")
- Username Search: `o/c/userpersonaldetailses/?filter=startswith` (marked as "seems to not be in use")
- Legacy Sentry URI: Commented out in config
- Old Totara Login: `NativeLoginold.tsx` (deprecated file)

### 5.2 Deep Linking Configurations

**iOS Deep Links:**

- App ID: `S5U9U9ZU34.com.dge.academy`
- Staging App ID: `S5U9U9ZU34.com.dge.academy.stage`
- Path: `/local/wstotara/redirect.php`

**Android Deep Links:**

- Package: `com.dge.academy`
- Staging Package: `com.dge.academy.staging`
- SHA256 Fingerprints: Multiple certificate fingerprints configured

**Totara Deep Links:**

- Schema: `totara://`
- Domain: `https://mobile.totaralearning.com`

---

## 6. Migration Recommendations

### 6.1 Backend Proxy Requirements

**Phase 1 - Critical APIs (Immediate):**

1. **DGE Workspace APIs** - All 40+ endpoints across 8 service categories
2. **UAE Pass Authentication** - OAuth flow and token management
3. **Totara LMS APIs** - GraphQL and REST endpoints with 45+ web service functions
4. **TAMM AI Services** - Multiple AI endpoints and feature flags
5. **Tomouh Government APIs** - Support center and additional services

**Phase 2 - Firebase Services (Medium Priority):**

1. **Firebase Cloud Messaging** - Push notifications
2. **Firebase Analytics** - Event tracking
3. **Firebase Crashlytics** - Error reporting
4. **Firebase Remote Config** - Feature flags

**Phase 3 - Supporting Services (Low Priority):**

1. **Deep Linking** - URL scheme handling
2. **Legacy Endpoints** - Deprecated but referenced APIs

### 6.2 Security Considerations

**Immediate Actions Required:**

1. **Move all API keys to backend** - Remove hardcoded credentials
2. **Implement environment-specific secrets** - Separate prod/staging/dev credentials
3. **Add request/response encryption** - Secure data in transit
4. **Implement rate limiting** - Protect against abuse
5. **Add audit logging** - Track all external API calls

### 6.3 Implementation Priority

**High Priority (Week 1-2):**

- DGE Workspace API proxy
- UAE Pass authentication proxy
- Basic authentication token management

**Medium Priority (Week 3-4):**

- Totara LMS GraphQL proxy
- Firebase services proxy
- TAMM AI service proxy

**Low Priority (Week 5-6):**

- Deep linking proxy
- Legacy endpoint cleanup
- Monitoring and analytics integration

---

## Technical Implementation Notes

**Backend Proxy Architecture Requirements:**

1. **Environment-aware routing** - Support for prod/staging/dev
2. **Authentication middleware** - Handle multiple auth methods
3. **Request transformation** - Modify headers and payloads
4. **Response caching** - Optimize performance
5. **Error handling** - Graceful degradation
6. **Monitoring** - Track usage and performance
7. **Security headers** - Add CORS, rate limiting, etc.

**Frontend Changes Required:**

1. **Update all base URLs** - Point to backend proxy
2. **Remove hardcoded credentials** - Use backend authentication
3. **Update error handling** - Handle proxy-specific errors
4. **Modify interceptors** - Work with proxy responses
5. **Update environment configs** - Single backend endpoint per environment

## Summary

This comprehensive inventory provides the complete foundation for implementing a backend middleman architecture that will proxy all external API dependencies. The analysis has identified:

- **85+ external API endpoints** across 15 service categories
- **40+ DGE Workspace API endpoints** across 8 functional areas
- **45+ Totara web service functions** for learning management
- **20+ hardcoded credentials and API keys** requiring secure migration
- **Multiple authentication methods** (Bearer tokens, API keys, OAuth)
- **Environment-specific configurations** for production, staging, and development

This detailed catalog will enable systematic migration of all external dependencies to your backend proxy service, significantly improving security, monitoring, and control over external service interactions.
