#!/bin/bash

TARGET_FILE="node_modules/react-native-check-app-install/android/build.gradle"

echo "Fixing Gradle issues in react-native-check-app-install..."

if [ ! -f "$TARGET_FILE" ]; then

  echo "Error: Target file $TARGET_FILE not found!"

  exit 1

fi


TEMP_FILE=$(mktemp)

sed '/dependencies {/ {n; s/compile/implementation/g; }' $TARGET_FILE > $TEMP_FILE || { echo "Failed to replace 'compile' with 'implementation'"; exit 1; }

sed '/compileSdkVersion 23/ {s/compileSdkVersion 23/compileSdkVersion 34/g; }' $TEMP_FILE > $TARGET_FILE || { echo "Failed to update compileSdkVersion"; exit 1; }

sed '/targetSdkVersion 23/ {s/targetSdkVersion 23/targetSdkVersion 34/g; }' $TARGET_FILE > $TEMP_FILE || { echo "Failed to update targetSdkVersion"; exit 1; }

sed '/buildToolsVersion "23.0.1"/ {s/buildToolsVersion "23.0.1"/buildToolsVersion "34.0.0"/g; }' $TEMP_FILE > $TARGET_FILE || { echo "Failed to update buildToolsVersion"; exit 1; }

echo "File content after changes:"

cat $TARGET_FILE

echo "Fixes applied to $TARGET_FILE"
