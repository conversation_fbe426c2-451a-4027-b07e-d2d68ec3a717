/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2019 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD’s customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */

/** @format */

import { AppRegistry, I18nManager } from 'react-native';
import App from './src/App';
import { name as appName } from './app.json';
import { firebase } from '@react-native-firebase/app';
import messaging from '@react-native-firebase/messaging';
import { enableScreens } from 'react-native-screens';
import './i18n';
import { setupKeyboardPolyfill } from './src/utils/keyboardPolyfill';

if (!firebase.apps.length) {
  firebase.initializeApp();
}

// Setup polyfill for deprecated Keyboard.removeListener
setupKeyboardPolyfill();

enableScreens();

messaging().setBackgroundMessageHandler(async (remoteMessage) => {});

AppRegistry.registerComponent(appName, () => App);
