#import "NotificationScheduler.h"
#import <UserNotifications/UserNotifications.h>
#import <React/RCTLog.h>
#import <React/RCTConvert.h>

@implementation NotificationScheduler

RCT_EXPORT_MODULE();

static NSString * const NOTIFICATION_ID = @"signInReminder";

- (void)startObserving {
    [[UNUserNotificationCenter currentNotificationCenter] setDelegate:self];
}

- (void)stopObserving {
    [[UNUserNotificationCenter currentNotificationCenter] setDelegate:nil];
}

- (NSArray<NSString *> *)supportedEvents {
    return @[];
}

RCT_EXPORT_METHOD(checkPermission:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
        BOOL granted = settings.authorizationStatus == UNAuthorizationStatusAuthorized;
        NSString *status = [self getStatusString:settings.authorizationStatus];
        resolve(@{@"granted": @(granted), @"status": status});
    }];
}

RCT_EXPORT_METHOD(requestPermission:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center requestAuthorizationWithOptions:(UNAuthorizationOptionAlert | UNAuthorizationOptionSound | UNAuthorizationOptionBadge)
                          completionHandler:^(BOOL granted, NSError * _Nullable error) {
        if (error) {
            reject(@"permission_error", error.localizedDescription, error);
            return;
        }
        [center getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings * _Nonnull settings) {
            NSString *status = [self getStatusString:settings.authorizationStatus];
            resolve(@{@"granted": @(granted), @"status": status});
        }];
    }];
}

RCT_EXPORT_METHOD(startNotifications:(double)intervalMinutes
                  title:(NSString *)title
                  body:(NSString *)body
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    content.title = title;
    content.body = body;
    content.sound = [UNNotificationSound defaultSound];

    NSTimeInterval interval = intervalMinutes * 60;
    if (interval < 60) {
        // iOS enforces a minimum of 60 seconds for repeating intervals.
        interval = 60;
    }

    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:interval repeats:YES];
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:NOTIFICATION_ID content:content trigger:trigger];

    [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            reject(@"schedule_error", error.localizedDescription, error);
        } else {
            resolve(@{@"success": @(YES), @"message": @"Notification scheduled successfully."});
        }
    }];
}

RCT_EXPORT_METHOD(stopNotifications:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    [[UNUserNotificationCenter currentNotificationCenter] removePendingNotificationRequestsWithIdentifiers:@[NOTIFICATION_ID]];
    resolve(@{@"success": @(YES), @"message": @"Notification stopped successfully."});
}

RCT_EXPORT_METHOD(getStatus:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    [[UNUserNotificationCenter currentNotificationCenter] getPendingNotificationRequestsWithCompletionHandler:^(NSArray<UNNotificationRequest *> * _Nonnull requests) {
        for (UNNotificationRequest *request in requests) {
            if ([request.identifier isEqualToString:NOTIFICATION_ID]) {
                if ([request.trigger isKindOfClass:[UNTimeIntervalNotificationTrigger class]]) {
                    UNTimeIntervalNotificationTrigger *trigger = (UNTimeIntervalNotificationTrigger *)request.trigger;
                    double intervalMinutes = trigger.timeInterval / 60;
                    resolve(@{@"enabled": @(YES), @"intervalMinutes": @(intervalMinutes)});
                    return;
                }
            }
        }
        resolve(@{@"enabled": @(NO), @"intervalMinutes": @(0)});
    }];
}

RCT_EXPORT_METHOD(sendTestNotification:(NSString *)title
                  body:(NSString *)body
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    UNMutableNotificationContent *content = [[UNMutableNotificationContent alloc] init];
    content.title = title;
    content.body = body;
    content.sound = [UNNotificationSound defaultSound];

    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:5 repeats:NO]; // 5 seconds from now
    NSString *testID = [NSString stringWithFormat:@"test-%f", [[NSDate date] timeIntervalSince1970]];
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:testID content:content trigger:trigger];

    [[UNUserNotificationCenter currentNotificationCenter] addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            reject(@"test_schedule_error", error.localizedDescription, error);
        } else {
            resolve(@{@"success": @(YES), @"message": @"Test notification scheduled successfully."});
        }
    }];
}

- (NSString *)getStatusString:(UNAuthorizationStatus)status {
    switch (status) {
        case UNAuthorizationStatusNotDetermined:
            return @"not_determined";
        case UNAuthorizationStatusDenied:
            return @"denied";
        case UNAuthorizationStatusAuthorized:
            return @"authorized";
        case UNAuthorizationStatusProvisional:
            return @"provisional";
        case UNAuthorizationStatusEphemeral:
            return @"ephemeral";
    }
}

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

// Delegate methods
- (void)userNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler {
    completionHandler(UNNotificationPresentationOptionAlert | UNNotificationPresentationOptionSound | UNNotificationPresentationOptionBadge);
}

@end 
