# Resolve react_native_pods.rb with node to allow for hoisting
def node_require(script)
   # Resolve script with node to allow for hoisting
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
 end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, 15.5
prepare_react_native_project!

setup_permissions([
  'Camera',
  'Microphone',
  'PhotoLibrary',
  'FaceID',
  'Notifications',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'SkillsHub' do
  config = use_native_modules!

use_frameworks! :linkage => :static
$RNFirebaseAsStaticFramework = true
pod 'GoogleUtilities', :modular_headers => true

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :hermes_enabled => false

  )

  pod 'RNVectorIcons', :path => '../node_modules/react-native-vector-icons'
  # pod 'RNSVG', :podspec => '../node_modules/react-native-svg/RNSVG.podspec'

  # UAEPass dependencies
  pod 'UAEPassClient', :path => '../node_modules/react-native-uaepass/ios/LocalPods/UAEPassClient'

# target 'SkillsHubDevelopment' do
#     inherit! :complete
#   end
#
  target 'SkillsHubStaging' do
    inherit! :complete
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
