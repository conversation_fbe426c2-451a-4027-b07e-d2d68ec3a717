// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		05C0B9B8A3184E53BC93AACC /* Noto-Kufi-Arabic-Extra-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0043FB0E868D4EBCB66D1042 /* Noto-Kufi-Arabic-Extra-Light.otf */; };
		1286A78D7D674419AE328D96 /* HelveticaNeueHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = 71E1521D003E4CCAA5A2397F /* HelveticaNeueHeavy.otf */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		25A3071BA7EE45F4BA401D63 /* HelveticaNeueUltraLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 159B2F54F8D54BA28AC1B7EB /* HelveticaNeueUltraLightItalic.otf */; };
		25E9E10516174EAAB79102D7 /* SF-Pro-Text-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 41CF78057B28486A9237C5DE /* SF-Pro-Text-Regular.otf */; };
		2886A018BC41433997BBC54C /* Noto-Kufi-Arabic-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3D49A887EDCA4C15B5A17E69 /* Noto-Kufi-Arabic-Regular.otf */; };
		2A40B6DC2BF2385A00762063 /* Fonts in Resources */ = {isa = PBXBuildFile; fileRef = 2A40B6DB2BF2385A00762063 /* Fonts */; };
		2A926AD02BF3F602003E5262 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A926ACF2BF3F602003E5262 /* AppDelegate.m */; };
		30E35F8C1CF84199AA8C98C4 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf in Resources */ = {isa = PBXBuildFile; fileRef = B232077692DC426A89C51563 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf */; };
		33B37EA900DF4647B5E1B27F /* SF-Pro-Text-Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = BF0B0C4CC9334037BE1C4B9C /* SF-Pro-Text-Thin.otf */; };
		375A5511609E43859F53FB5F /* HelveticaNeueMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = E95D6A3F83DD47DA8C84DD90 /* HelveticaNeueMedium.otf */; };
		3B3AC761A5514D838331DC05 /* Helvetica-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4CEBD045CCEE47BBAC387645 /* Helvetica-Bold.ttf */; };
		42E318C9C727459B845C63C1 /* Amiri-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 69F02BD0A2314BE5AEAEFD65 /* Amiri-Regular.ttf */; };
		45FC2F99083C49EDBBBB3242 /* HelveticaNeueLTArabic-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0ED3A00F551B43AF8B3E378D /* HelveticaNeueLTArabic-Light.ttf */; };
		475EE29C850A4F718A48B041 /* SF-Pro-Text-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 9716F08E3476436C93F07338 /* SF-Pro-Text-Bold.otf */; };
		4863BEA415E64881AF11B942 /* Noto-Kufi-Arabic-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 233BB12B20584EB29757FE19 /* Noto-Kufi-Arabic-Medium.otf */; };
		4C12BEDB2DD5473BBF693750 /* SF-Pro-Text-MediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 1C08F3E33513490B9E3B6F50 /* SF-Pro-Text-MediumItalic.otf */; };
		4E9D7620A7664127AA8DD72C /* Tajawal-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5AE188DE83F04DE7A95FDECE /* Tajawal-Bold.ttf */; };
		503E6ED72DA949B2BC47005B /* SF-Pro-Text-LightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = E7C53C086F964262BDA1D6C8 /* SF-Pro-Text-LightItalic.otf */; };
		54FFB7994E7E4D2D8EF44879 /* HelveticaNeueThin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3C9C3A04501D43B08679324F /* HelveticaNeueThin.otf */; };
		5D054FC7DE244480A5BD7A15 /* HelveticaNeueBlackItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = D45DABE34A1F4157B7DEEA82 /* HelveticaNeueBlackItalic.otf */; };
		5DDA283E07954AF0A135D184 /* HelveticaNeueRoman.otf in Resources */ = {isa = PBXBuildFile; fileRef = 803E4F46581E4E53BE83C3B3 /* HelveticaNeueRoman.otf */; };
		5EB86306E6F147FEB4FBC821 /* HelveticaNeueLTArabic-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CA73A465ACAE4F9A961F3C07 /* HelveticaNeueLTArabic-Bold.ttf */; };
		6276F8C61344428C94C23CA4 /* Amiri-Slanted.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4197F7A8253941C2992ABA17 /* Amiri-Slanted.ttf */; };
		658A3430384A46E9819231F7 /* Tajawal-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D62E080A23ED4CC280D25DDA /* Tajawal-Regular.ttf */; };
		65D1F4A2F5288770089ED278 /* Pods_SkillsHub_SkillsHubStaging.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 40D38BB8FAF39BCCEBC4A77D /* Pods_SkillsHub_SkillsHubStaging.framework */; };
		6869855EF6C1405E87CD77A9 /* Amiri-BoldSlanted.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 19788B30403549AAB9733377 /* Amiri-BoldSlanted.ttf */; };
		6892501E2A38447FA15A393F /* HelveticaNeueBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 071E1E8EBC964313B7EAAADC /* HelveticaNeueBoldItalic.otf */; };
		7256120F33B24E05B76A8789 /* HelveticaNeueBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 249EBAC463BB42E6BDDA9AC3 /* HelveticaNeueBold.otf */; };
		779B1838D0B44571AB802175 /* Noto-Kufi-Arabic-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 2A2E05D2691043D6BD56F429 /* Noto-Kufi-Arabic-Bold.otf */; };
		77AFC9B4D5054368ABEEEF57 /* HelveticaNeueHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = AA997101EE8146B1816D9BCD /* HelveticaNeueHeavyItalic.otf */; };
		7B77A46921DD4C8D99467332 /* SF-Pro-Text-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 836D77DCCF29419DB2FBA458 /* SF-Pro-Text-Medium.otf */; };
		7D8352C249F841149D219E0B /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf in Resources */ = {isa = PBXBuildFile; fileRef = B916AE6FE25C4A8593771ED7 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf */; };
		80FEFEC078514D6982538EC2 /* Noto-Kufi-Arabic-Extra-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A2A78B9B8FC84D5A81884719 /* Noto-Kufi-Arabic-Extra-Bold.otf */; };
		868133BB2E2E2A4E00DCE2D1 /* NotificationScheduler.m in Sources */ = {isa = PBXBuildFile; fileRef = 868133BA2E2E2A4E00DCE2D1 /* NotificationScheduler.m */; };
		868133BC2E2E2A4E00DCE2D1 /* NotificationScheduler.m in Sources */ = {isa = PBXBuildFile; fileRef = 868133BA2E2E2A4E00DCE2D1 /* NotificationScheduler.m */; };
		86C1FA062D2EA1240092DB83 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 2A926ACF2BF3F602003E5262 /* AppDelegate.m */; };
		86C1FA072D2EA1240092DB83 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		86C1FA0B2D2EA1240092DB83 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		86C1FA0C2D2EA1240092DB83 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 99806FFBB7E052D933C8802B /* PrivacyInfo.xcprivacy */; };
		86C1FA0D2D2EA1240092DB83 /* SF-Pro-Text-Black.otf in Resources */ = {isa = PBXBuildFile; fileRef = F6483B1E303247EFA860C514 /* SF-Pro-Text-Black.otf */; };
		86C1FA0E2D2EA1240092DB83 /* Fonts in Resources */ = {isa = PBXBuildFile; fileRef = 2A40B6DB2BF2385A00762063 /* Fonts */; };
		86C1FA0F2D2EA1240092DB83 /* SF-Pro-Text-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 9716F08E3476436C93F07338 /* SF-Pro-Text-Bold.otf */; };
		86C1FA112D2EA1240092DB83 /* SF-Pro-Text-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7084A1A3E64141CD8061F7D9 /* SF-Pro-Text-Light.otf */; };
		86C1FA122D2EA1240092DB83 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FAD53E2F2BF789C6008A1F2D /* LaunchScreen.storyboard */; };
		86C1FA132D2EA1240092DB83 /* SF-Pro-Text-LightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = E7C53C086F964262BDA1D6C8 /* SF-Pro-Text-LightItalic.otf */; };
		86C1FA142D2EA1240092DB83 /* SF-Pro-Text-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 836D77DCCF29419DB2FBA458 /* SF-Pro-Text-Medium.otf */; };
		86C1FA152D2EA1240092DB83 /* SF-Pro-Text-MediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 1C08F3E33513490B9E3B6F50 /* SF-Pro-Text-MediumItalic.otf */; };
		86C1FA162D2EA1240092DB83 /* SF-Pro-Text-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 41CF78057B28486A9237C5DE /* SF-Pro-Text-Regular.otf */; };
		86C1FA172D2EA1240092DB83 /* SF-Pro-Text-Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = BF0B0C4CC9334037BE1C4B9C /* SF-Pro-Text-Thin.otf */; };
		86C1FA182D2EA1240092DB83 /* Amiri-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7A67B3EBCFB745208217BE27 /* Amiri-Bold.ttf */; };
		86C1FA192D2EA1240092DB83 /* Amiri-BoldSlanted.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 19788B30403549AAB9733377 /* Amiri-BoldSlanted.ttf */; };
		86C1FA1A2D2EA1240092DB83 /* Amiri-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 69F02BD0A2314BE5AEAEFD65 /* Amiri-Regular.ttf */; };
		86C1FA1B2D2EA1240092DB83 /* Amiri-Slanted.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4197F7A8253941C2992ABA17 /* Amiri-Slanted.ttf */; };
		86C1FA1C2D2EA1240092DB83 /* HelveticaNeueBlack.otf in Resources */ = {isa = PBXBuildFile; fileRef = 86189CBEF06A4EE294DC488F /* HelveticaNeueBlack.otf */; };
		86C1FA1D2D2EA1240092DB83 /* HelveticaNeueBlackItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = D45DABE34A1F4157B7DEEA82 /* HelveticaNeueBlackItalic.otf */; };
		86C1FA1E2D2EA1240092DB83 /* HelveticaNeueBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 249EBAC463BB42E6BDDA9AC3 /* HelveticaNeueBold.otf */; };
		86C1FA1F2D2EA1240092DB83 /* HelveticaNeueBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 071E1E8EBC964313B7EAAADC /* HelveticaNeueBoldItalic.otf */; };
		86C1FA202D2EA1240092DB83 /* HelveticaNeueHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = 71E1521D003E4CCAA5A2397F /* HelveticaNeueHeavy.otf */; };
		86C1FA212D2EA1240092DB83 /* HelveticaNeueHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = AA997101EE8146B1816D9BCD /* HelveticaNeueHeavyItalic.otf */; };
		86C1FA222D2EA1240092DB83 /* HelveticaNeueLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 84324622EC70405FBB376490 /* HelveticaNeueLight.otf */; };
		86C1FA232D2EA1240092DB83 /* HelveticaNeueLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 47344B5E57064CC1A89757A9 /* HelveticaNeueLightItalic.otf */; };
		86C1FA242D2EA1240092DB83 /* HelveticaNeueMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = E95D6A3F83DD47DA8C84DD90 /* HelveticaNeueMedium.otf */; };
		86C1FA252D2EA1240092DB83 /* HelveticaNeueMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = FBAD8943EAB04E459E821337 /* HelveticaNeueMediumItalic.otf */; };
		86C1FA262D2EA1240092DB83 /* HelveticaNeueRoman.otf in Resources */ = {isa = PBXBuildFile; fileRef = 803E4F46581E4E53BE83C3B3 /* HelveticaNeueRoman.otf */; };
		86C1FA272D2EA1240092DB83 /* HelveticaNeueThin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3C9C3A04501D43B08679324F /* HelveticaNeueThin.otf */; };
		86C1FA282D2EA1240092DB83 /* HelveticaNeueThinItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 4985A07E3F1C4689AD39CAE1 /* HelveticaNeueThinItalic.otf */; };
		86C1FA292D2EA1240092DB83 /* HelveticaNeueUltraLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3A7834911E964051845794E0 /* HelveticaNeueUltraLight.otf */; };
		86C1FA2A2D2EA1240092DB83 /* HelveticaNeueUltraLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 159B2F54F8D54BA28AC1B7EB /* HelveticaNeueUltraLightItalic.otf */; };
		86C1FA2B2D2EA1240092DB83 /* HelveticaNeueItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A938E7D905984D13B8091C7D /* HelveticaNeueItalic.ttf */; };
		86C1FA2C2D2EA1240092DB83 /* HelveticaNeueLTArabic-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CA73A465ACAE4F9A961F3C07 /* HelveticaNeueLTArabic-Bold.ttf */; };
		86C1FA2D2D2EA1240092DB83 /* HelveticaNeueLTArabic-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0ED3A00F551B43AF8B3E378D /* HelveticaNeueLTArabic-Light.ttf */; };
		86C1FA2E2D2EA1240092DB83 /* HelveticaNeueLTArabic-Roman.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BA39A0C507774EA58C2E6DAA /* HelveticaNeueLTArabic-Roman.ttf */; };
		86C1FA2F2D2EA1240092DB83 /* Tajawal-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5AE188DE83F04DE7A95FDECE /* Tajawal-Bold.ttf */; };
		86C1FA302D2EA1240092DB83 /* Tajawal-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A24EA178DDE24AEDAE040038 /* Tajawal-Light.ttf */; };
		86C1FA312D2EA1240092DB83 /* Tajawal-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D62E080A23ED4CC280D25DDA /* Tajawal-Regular.ttf */; };
		86C1FA322D2EA1240092DB83 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf in Resources */ = {isa = PBXBuildFile; fileRef = B916AE6FE25C4A8593771ED7 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf */; };
		86C1FA332D2EA1240092DB83 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf in Resources */ = {isa = PBXBuildFile; fileRef = B232077692DC426A89C51563 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf */; };
		86C1FA342D2EA1240092DB83 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf in Resources */ = {isa = PBXBuildFile; fileRef = 408A1260E33846DE893B5AB0 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf */; };
		86C1FA352D2EA1240092DB83 /* Noto-Kufi-Arabic-Black.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0D812EE9420C4C0EA7C02FE5 /* Noto-Kufi-Arabic-Black.otf */; };
		86C1FA362D2EA1240092DB83 /* Noto-Kufi-Arabic-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 2A2E05D2691043D6BD56F429 /* Noto-Kufi-Arabic-Bold.otf */; };
		86C1FA372D2EA1240092DB83 /* Noto-Kufi-Arabic-Extra-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = A2A78B9B8FC84D5A81884719 /* Noto-Kufi-Arabic-Extra-Bold.otf */; };
		86C1FA382D2EA1240092DB83 /* Noto-Kufi-Arabic-Extra-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0043FB0E868D4EBCB66D1042 /* Noto-Kufi-Arabic-Extra-Light.otf */; };
		86C1FA392D2EA1240092DB83 /* Noto-Kufi-Arabic-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7E30C3C5B6C74582B300A692 /* Noto-Kufi-Arabic-Light.otf */; };
		86C1FA3A2D2EA1240092DB83 /* Noto-Kufi-Arabic-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 233BB12B20584EB29757FE19 /* Noto-Kufi-Arabic-Medium.otf */; };
		86C1FA3B2D2EA1240092DB83 /* Noto-Kufi-Arabic-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3D49A887EDCA4C15B5A17E69 /* Noto-Kufi-Arabic-Regular.otf */; };
		86C1FA3C2D2EA1240092DB83 /* Noto-Kufi-Arabic-Semi-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 5550C61832194470B0D76803 /* Noto-Kufi-Arabic-Semi-Bold.otf */; };
		86C1FA3D2D2EA1240092DB83 /* Noto-Kufi-Arabic-Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 988D87418A5B4FC0A0266B42 /* Noto-Kufi-Arabic-Thin.otf */; };
		86C1FA4C2D2EA4750092DB83 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 86C1FA4B2D2EA4750092DB83 /* GoogleService-Info.plist */; };
		86C1FA4E2D2EA4A80092DB83 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 86C1FA4D2D2EA4A80092DB83 /* GoogleService-Info.plist */; };
		8A6AA8892F023D52D56FA25E /* Pods_SkillsHub.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E76D137A1BB145634907CBC9 /* Pods_SkillsHub.framework */; };
		8C03D870B2E04DF5BC7932CA /* Tajawal-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A24EA178DDE24AEDAE040038 /* Tajawal-Light.ttf */; };
		8E4CA6F4B04046A8A014F227 /* Noto-Kufi-Arabic-Black.otf in Resources */ = {isa = PBXBuildFile; fileRef = 0D812EE9420C4C0EA7C02FE5 /* Noto-Kufi-Arabic-Black.otf */; };
		95D035DC422340A9AEC5F294 /* HelveticaNeueLTArabic-Roman.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BA39A0C507774EA58C2E6DAA /* HelveticaNeueLTArabic-Roman.ttf */; };
		A13795B228E54855B91D4B2B /* Noto-Kufi-Arabic-Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 988D87418A5B4FC0A0266B42 /* Noto-Kufi-Arabic-Thin.otf */; };
		A7D6A9DE3C7743328897E294 /* HelveticaNeueLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 84324622EC70405FBB376490 /* HelveticaNeueLight.otf */; };
		AA1935188424448997206E80 /* Amiri-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7A67B3EBCFB745208217BE27 /* Amiri-Bold.ttf */; };
		AC7CDC3C4B774E79869F1C80 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf in Resources */ = {isa = PBXBuildFile; fileRef = 408A1260E33846DE893B5AB0 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf */; };
		B288B79F93BD4BD3A8DFF2A3 /* SF-Pro-Text-Black.otf in Resources */ = {isa = PBXBuildFile; fileRef = F6483B1E303247EFA860C514 /* SF-Pro-Text-Black.otf */; };
		B361610096F14E249492C92F /* HelveticaNeueItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A938E7D905984D13B8091C7D /* HelveticaNeueItalic.ttf */; };
		B3FA52101DC6449EB0307D11 /* Noto-Kufi-Arabic-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7E30C3C5B6C74582B300A692 /* Noto-Kufi-Arabic-Light.otf */; };
		B68FF402FC4745B1A533C6CC /* HelveticaNeueMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = FBAD8943EAB04E459E821337 /* HelveticaNeueMediumItalic.otf */; };
		D0867B1FCF4543BB81DDD9DA /* HelveticaNeueBlack.otf in Resources */ = {isa = PBXBuildFile; fileRef = 86189CBEF06A4EE294DC488F /* HelveticaNeueBlack.otf */; };
		D5D4938DEBD54A54B5453B8B /* HelveticaNeueLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 47344B5E57064CC1A89757A9 /* HelveticaNeueLightItalic.otf */; };
		D6CD3720BD794F00AF2B3777 /* SF-Pro-Text-Light.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7084A1A3E64141CD8061F7D9 /* SF-Pro-Text-Light.otf */; };
		DAA9199C282D4BD09BD905DA /* HelveticaNeueThinItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = 4985A07E3F1C4689AD39CAE1 /* HelveticaNeueThinItalic.otf */; };
		DCDAED085F56A37A7E852AD5 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		E2AE7FCFEC462E55C514EE2E /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 99806FFBB7E052D933C8802B /* PrivacyInfo.xcprivacy */; };
		E8E7492EDCE34D69925AE22C /* HelveticaNeueUltraLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = 3A7834911E964051845794E0 /* HelveticaNeueUltraLight.otf */; };
		F6A530D54AC97D52B360466C /* Pods_SkillsHub.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3BFCCC4014BEDD86B3D2FFEF /* Pods_SkillsHub.framework */; };
		F9F274B892E54048923B8AAE /* Noto-Kufi-Arabic-Semi-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 5550C61832194470B0D76803 /* Noto-Kufi-Arabic-Semi-Bold.otf */; };
		FAD53E302BF789C6008A1F2D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FAD53E2F2BF789C6008A1F2D /* LaunchScreen.storyboard */; };
		FD9C93D846172134CF4DDC86 /* Pods_SkillsHub_SkillsHubStaging.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 711A2DFDAD28ABD8BB0A65A0 /* Pods_SkillsHub_SkillsHubStaging.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0043FB0E868D4EBCB66D1042 /* Noto-Kufi-Arabic-Extra-Light.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Extra-Light.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Extra-Light.otf"; sourceTree = "<group>"; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* SkillsHubTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SkillsHubTests.m; sourceTree = "<group>"; };
		071E1E8EBC964313B7EAAADC /* HelveticaNeueBoldItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueBoldItalic.otf; path = ../src/assets/fonts/HelveticaNeueBoldItalic.otf; sourceTree = "<group>"; };
		0D812EE9420C4C0EA7C02FE5 /* Noto-Kufi-Arabic-Black.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Black.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Black.otf"; sourceTree = "<group>"; };
		0ED3A00F551B43AF8B3E378D /* HelveticaNeueLTArabic-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HelveticaNeueLTArabic-Light.ttf"; path = "../src/assets/fonts/HelveticaNeueLTArabic-Light.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* SkillsHub.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SkillsHub.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = SkillsHub/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = SkillsHub/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = SkillsHub/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = SkillsHub/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = SkillsHub/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		159B2F54F8D54BA28AC1B7EB /* HelveticaNeueUltraLightItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueUltraLightItalic.otf; path = ../src/assets/fonts/HelveticaNeueUltraLightItalic.otf; sourceTree = "<group>"; };
		19788B30403549AAB9733377 /* Amiri-BoldSlanted.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Amiri-BoldSlanted.ttf"; path = "../src/assets/fonts/Amiri-BoldSlanted.ttf"; sourceTree = "<group>"; };
		1C08F3E33513490B9E3B6F50 /* SF-Pro-Text-MediumItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-MediumItalic.otf"; path = "../src/assets/fonts/SF-Pro-Text-MediumItalic.otf"; sourceTree = "<group>"; };
		233BB12B20584EB29757FE19 /* Noto-Kufi-Arabic-Medium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Medium.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Medium.otf"; sourceTree = "<group>"; };
		2393F677D7F5F8D950D4C72B /* Pods-SkillsHub-SkillsHubStaging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub-SkillsHubStaging.debug.xcconfig"; path = "Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging.debug.xcconfig"; sourceTree = "<group>"; };
		249EBAC463BB42E6BDDA9AC3 /* HelveticaNeueBold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueBold.otf; path = ../src/assets/fonts/HelveticaNeueBold.otf; sourceTree = "<group>"; };
		2A2E05D2691043D6BD56F429 /* Noto-Kufi-Arabic-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Bold.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Bold.otf"; sourceTree = "<group>"; };
		2A40B6DB2BF2385A00762063 /* Fonts */ = {isa = PBXFileReference; lastKnownFileType = folder; name = Fonts; path = "../node_modules/react-native-vector-icons/Fonts"; sourceTree = "<group>"; };
		2A926ACF2BF3F602003E5262 /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = SkillsHub/AppDelegate.m; sourceTree = "<group>"; };
		2CD5441147650714CD81A0B6 /* Pods-SkillsHub.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub.debug.xcconfig"; path = "Target Support Files/Pods-SkillsHub/Pods-SkillsHub.debug.xcconfig"; sourceTree = "<group>"; };
		3A7834911E964051845794E0 /* HelveticaNeueUltraLight.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueUltraLight.otf; path = ../src/assets/fonts/HelveticaNeueUltraLight.otf; sourceTree = "<group>"; };
		3BFCCC4014BEDD86B3D2FFEF /* Pods_SkillsHub.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SkillsHub.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3C9C3A04501D43B08679324F /* HelveticaNeueThin.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueThin.otf; path = ../src/assets/fonts/HelveticaNeueThin.otf; sourceTree = "<group>"; };
		3D49A887EDCA4C15B5A17E69 /* Noto-Kufi-Arabic-Regular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Regular.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Regular.otf"; sourceTree = "<group>"; };
		408A1260E33846DE893B5AB0 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf"; path = "../src/assets/fonts/HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf"; sourceTree = "<group>"; };
		40D38BB8FAF39BCCEBC4A77D /* Pods_SkillsHub_SkillsHubStaging.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SkillsHub_SkillsHubStaging.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4197F7A8253941C2992ABA17 /* Amiri-Slanted.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Amiri-Slanted.ttf"; path = "../src/assets/fonts/Amiri-Slanted.ttf"; sourceTree = "<group>"; };
		41CF78057B28486A9237C5DE /* SF-Pro-Text-Regular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Regular.otf"; path = "../src/assets/fonts/SF-Pro-Text-Regular.otf"; sourceTree = "<group>"; };
		47344B5E57064CC1A89757A9 /* HelveticaNeueLightItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueLightItalic.otf; path = ../src/assets/fonts/HelveticaNeueLightItalic.otf; sourceTree = "<group>"; };
		4985A07E3F1C4689AD39CAE1 /* HelveticaNeueThinItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueThinItalic.otf; path = ../src/assets/fonts/HelveticaNeueThinItalic.otf; sourceTree = "<group>"; };
		4CEBD045CCEE47BBAC387645 /* Helvetica-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Helvetica-Bold.ttf"; path = "../src/assets/fonts/Helvetica-Bold.ttf"; sourceTree = "<group>"; };
		5550C61832194470B0D76803 /* Noto-Kufi-Arabic-Semi-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Semi-Bold.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Semi-Bold.otf"; sourceTree = "<group>"; };
		5AE188DE83F04DE7A95FDECE /* Tajawal-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Tajawal-Bold.ttf"; path = "../src/assets/fonts/Tajawal-Bold.ttf"; sourceTree = "<group>"; };
		69F02BD0A2314BE5AEAEFD65 /* Amiri-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Amiri-Regular.ttf"; path = "../src/assets/fonts/Amiri-Regular.ttf"; sourceTree = "<group>"; };
		7084A1A3E64141CD8061F7D9 /* SF-Pro-Text-Light.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Light.otf"; path = "../src/assets/fonts/SF-Pro-Text-Light.otf"; sourceTree = "<group>"; };
		711A2DFDAD28ABD8BB0A65A0 /* Pods_SkillsHub_SkillsHubStaging.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SkillsHub_SkillsHubStaging.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		71E1521D003E4CCAA5A2397F /* HelveticaNeueHeavy.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueHeavy.otf; path = ../src/assets/fonts/HelveticaNeueHeavy.otf; sourceTree = "<group>"; };
		73AFD853266695C68EDF33B9 /* Pods-SkillsHub-SkillsHubStaging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub-SkillsHubStaging.debug.xcconfig"; path = "Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging.debug.xcconfig"; sourceTree = "<group>"; };
		7A67B3EBCFB745208217BE27 /* Amiri-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Amiri-Bold.ttf"; path = "../src/assets/fonts/Amiri-Bold.ttf"; sourceTree = "<group>"; };
		7DA7E3F7EAE1024949C28E38 /* Pods-SkillsHub.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub.release.xcconfig"; path = "Target Support Files/Pods-SkillsHub/Pods-SkillsHub.release.xcconfig"; sourceTree = "<group>"; };
		7E30C3C5B6C74582B300A692 /* Noto-Kufi-Arabic-Light.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Light.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Light.otf"; sourceTree = "<group>"; };
		803E4F46581E4E53BE83C3B3 /* HelveticaNeueRoman.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueRoman.otf; path = ../src/assets/fonts/HelveticaNeueRoman.otf; sourceTree = "<group>"; };
		836D77DCCF29419DB2FBA458 /* SF-Pro-Text-Medium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Medium.otf"; path = "../src/assets/fonts/SF-Pro-Text-Medium.otf"; sourceTree = "<group>"; };
		84324622EC70405FBB376490 /* HelveticaNeueLight.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueLight.otf; path = ../src/assets/fonts/HelveticaNeueLight.otf; sourceTree = "<group>"; };
		86189CBEF06A4EE294DC488F /* HelveticaNeueBlack.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueBlack.otf; path = ../src/assets/fonts/HelveticaNeueBlack.otf; sourceTree = "<group>"; };
		868133B92E2E2A4E00DCE2D1 /* NotificationScheduler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NotificationScheduler.h; sourceTree = "<group>"; };
		868133BA2E2E2A4E00DCE2D1 /* NotificationScheduler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NotificationScheduler.m; sourceTree = "<group>"; };
		86C1FA462D2EA1240092DB83 /* SkillsHubStaging.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SkillsHubStaging.app; sourceTree = BUILT_PRODUCTS_DIR; };
		86C1FA472D2EA1240092DB83 /* SkillsHubStaging-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "SkillsHubStaging-Info.plist"; path = "/Users/<USER>/Desktop/dgeApp/dge-ga-mobile-app/ios/SkillsHubStaging-Info.plist"; sourceTree = "<absolute>"; };
		86C1FA4B2D2EA4750092DB83 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		86C1FA4D2D2EA4A80092DB83 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		9716F08E3476436C93F07338 /* SF-Pro-Text-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Bold.otf"; path = "../src/assets/fonts/SF-Pro-Text-Bold.otf"; sourceTree = "<group>"; };
		988D87418A5B4FC0A0266B42 /* Noto-Kufi-Arabic-Thin.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Thin.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Thin.otf"; sourceTree = "<group>"; };
		99806FFBB7E052D933C8802B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = SkillsHub/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		A24EA178DDE24AEDAE040038 /* Tajawal-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Tajawal-Light.ttf"; path = "../src/assets/fonts/Tajawal-Light.ttf"; sourceTree = "<group>"; };
		A2A78B9B8FC84D5A81884719 /* Noto-Kufi-Arabic-Extra-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Noto-Kufi-Arabic-Extra-Bold.otf"; path = "../src/assets/fonts/Noto-Kufi-Arabic-Extra-Bold.otf"; sourceTree = "<group>"; };
		A938E7D905984D13B8091C7D /* HelveticaNeueItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueItalic.ttf; path = ../src/assets/fonts/HelveticaNeueItalic.ttf; sourceTree = "<group>"; };
		AA0575CC4F00B2E797BADE76 /* Pods-SkillsHub-SkillsHubStaging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub-SkillsHubStaging.release.xcconfig"; path = "Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging.release.xcconfig"; sourceTree = "<group>"; };
		AA997101EE8146B1816D9BCD /* HelveticaNeueHeavyItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueHeavyItalic.otf; path = ../src/assets/fonts/HelveticaNeueHeavyItalic.otf; sourceTree = "<group>"; };
		B232077692DC426A89C51563 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf"; path = "../src/assets/fonts/Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf"; sourceTree = "<group>"; };
		B916AE6FE25C4A8593771ED7 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf"; path = "../src/assets/fonts/Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf"; sourceTree = "<group>"; };
		BA39A0C507774EA58C2E6DAA /* HelveticaNeueLTArabic-Roman.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HelveticaNeueLTArabic-Roman.ttf"; path = "../src/assets/fonts/HelveticaNeueLTArabic-Roman.ttf"; sourceTree = "<group>"; };
		BF0B0C4CC9334037BE1C4B9C /* SF-Pro-Text-Thin.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Thin.otf"; path = "../src/assets/fonts/SF-Pro-Text-Thin.otf"; sourceTree = "<group>"; };
		CA73A465ACAE4F9A961F3C07 /* HelveticaNeueLTArabic-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "HelveticaNeueLTArabic-Bold.ttf"; path = "../src/assets/fonts/HelveticaNeueLTArabic-Bold.ttf"; sourceTree = "<group>"; };
		D45DABE34A1F4157B7DEEA82 /* HelveticaNeueBlackItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueBlackItalic.otf; path = ../src/assets/fonts/HelveticaNeueBlackItalic.otf; sourceTree = "<group>"; };
		D5BAD3E0E1606469E38421D2 /* Pods-SkillsHub-SkillsHubStaging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SkillsHub-SkillsHubStaging.release.xcconfig"; path = "Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging.release.xcconfig"; sourceTree = "<group>"; };
		D62E080A23ED4CC280D25DDA /* Tajawal-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Tajawal-Regular.ttf"; path = "../src/assets/fonts/Tajawal-Regular.ttf"; sourceTree = "<group>"; };
		E76D137A1BB145634907CBC9 /* Pods_SkillsHub.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SkillsHub.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E7C53C086F964262BDA1D6C8 /* SF-Pro-Text-LightItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-LightItalic.otf"; path = "../src/assets/fonts/SF-Pro-Text-LightItalic.otf"; sourceTree = "<group>"; };
		E95D6A3F83DD47DA8C84DD90 /* HelveticaNeueMedium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueMedium.otf; path = ../src/assets/fonts/HelveticaNeueMedium.otf; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F6483B1E303247EFA860C514 /* SF-Pro-Text-Black.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro-Text-Black.otf"; path = "../src/assets/fonts/SF-Pro-Text-Black.otf"; sourceTree = "<group>"; };
		FAD53E2F2BF789C6008A1F2D /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = SkillsHub/LaunchScreen.storyboard; sourceTree = "<group>"; };
		FBAD8943EAB04E459E821337 /* HelveticaNeueMediumItalic.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = HelveticaNeueMediumItalic.otf; path = ../src/assets/fonts/HelveticaNeueMediumItalic.otf; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				DCDAED085F56A37A7E852AD5 /* (null) in Frameworks */,
				8A6AA8892F023D52D56FA25E /* Pods_SkillsHub.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		86C1FA082D2EA1240092DB83 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FD9C93D846172134CF4DDC86 /* Pods_SkillsHub_SkillsHubStaging.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* SkillsHubTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* SkillsHubTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = SkillsHubTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* SkillsHub */ = {
			isa = PBXGroup;
			children = (
				868133B92E2E2A4E00DCE2D1 /* NotificationScheduler.h */,
				868133BA2E2E2A4E00DCE2D1 /* NotificationScheduler.m */,
				FAD53E2F2BF789C6008A1F2D /* LaunchScreen.storyboard */,
				2A926ACF2BF3F602003E5262 /* AppDelegate.m */,
				2A40B6DB2BF2385A00762063 /* Fonts */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				99806FFBB7E052D933C8802B /* PrivacyInfo.xcprivacy */,
			);
			name = SkillsHub;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				E76D137A1BB145634907CBC9 /* Pods_SkillsHub.framework */,
				711A2DFDAD28ABD8BB0A65A0 /* Pods_SkillsHub_SkillsHubStaging.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				86C1FA4A2D2EA4270092DB83 /* staging */,
				86C1FA492D2EA4180092DB83 /* production */,
				13B07FAE1A68108700A75B9A /* SkillsHub */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* SkillsHubTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				ADFFDF4431CE4D9EA17AB4A6 /* Resources */,
				86C1FA472D2EA1240092DB83 /* SkillsHubStaging-Info.plist */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* SkillsHub.app */,
				86C1FA462D2EA1240092DB83 /* SkillsHubStaging.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		86C1FA492D2EA4180092DB83 /* production */ = {
			isa = PBXGroup;
			children = (
				86C1FA4D2D2EA4A80092DB83 /* GoogleService-Info.plist */,
			);
			path = production;
			sourceTree = "<group>";
		};
		86C1FA4A2D2EA4270092DB83 /* staging */ = {
			isa = PBXGroup;
			children = (
				86C1FA4B2D2EA4750092DB83 /* GoogleService-Info.plist */,
			);
			path = staging;
			sourceTree = "<group>";
		};
		ADFFDF4431CE4D9EA17AB4A6 /* Resources */ = {
			isa = PBXGroup;
			children = (
				F6483B1E303247EFA860C514 /* SF-Pro-Text-Black.otf */,
				9716F08E3476436C93F07338 /* SF-Pro-Text-Bold.otf */,
				7084A1A3E64141CD8061F7D9 /* SF-Pro-Text-Light.otf */,
				E7C53C086F964262BDA1D6C8 /* SF-Pro-Text-LightItalic.otf */,
				836D77DCCF29419DB2FBA458 /* SF-Pro-Text-Medium.otf */,
				1C08F3E33513490B9E3B6F50 /* SF-Pro-Text-MediumItalic.otf */,
				41CF78057B28486A9237C5DE /* SF-Pro-Text-Regular.otf */,
				BF0B0C4CC9334037BE1C4B9C /* SF-Pro-Text-Thin.otf */,
				7A67B3EBCFB745208217BE27 /* Amiri-Bold.ttf */,
				19788B30403549AAB9733377 /* Amiri-BoldSlanted.ttf */,
				69F02BD0A2314BE5AEAEFD65 /* Amiri-Regular.ttf */,
				4197F7A8253941C2992ABA17 /* Amiri-Slanted.ttf */,
				86189CBEF06A4EE294DC488F /* HelveticaNeueBlack.otf */,
				D45DABE34A1F4157B7DEEA82 /* HelveticaNeueBlackItalic.otf */,
				249EBAC463BB42E6BDDA9AC3 /* HelveticaNeueBold.otf */,
				071E1E8EBC964313B7EAAADC /* HelveticaNeueBoldItalic.otf */,
				71E1521D003E4CCAA5A2397F /* HelveticaNeueHeavy.otf */,
				AA997101EE8146B1816D9BCD /* HelveticaNeueHeavyItalic.otf */,
				84324622EC70405FBB376490 /* HelveticaNeueLight.otf */,
				47344B5E57064CC1A89757A9 /* HelveticaNeueLightItalic.otf */,
				E95D6A3F83DD47DA8C84DD90 /* HelveticaNeueMedium.otf */,
				FBAD8943EAB04E459E821337 /* HelveticaNeueMediumItalic.otf */,
				803E4F46581E4E53BE83C3B3 /* HelveticaNeueRoman.otf */,
				3C9C3A04501D43B08679324F /* HelveticaNeueThin.otf */,
				4985A07E3F1C4689AD39CAE1 /* HelveticaNeueThinItalic.otf */,
				3A7834911E964051845794E0 /* HelveticaNeueUltraLight.otf */,
				159B2F54F8D54BA28AC1B7EB /* HelveticaNeueUltraLightItalic.otf */,
				A938E7D905984D13B8091C7D /* HelveticaNeueItalic.ttf */,
				CA73A465ACAE4F9A961F3C07 /* HelveticaNeueLTArabic-Bold.ttf */,
				0ED3A00F551B43AF8B3E378D /* HelveticaNeueLTArabic-Light.ttf */,
				BA39A0C507774EA58C2E6DAA /* HelveticaNeueLTArabic-Roman.ttf */,
				5AE188DE83F04DE7A95FDECE /* Tajawal-Bold.ttf */,
				A24EA178DDE24AEDAE040038 /* Tajawal-Light.ttf */,
				D62E080A23ED4CC280D25DDA /* Tajawal-Regular.ttf */,
				B916AE6FE25C4A8593771ED7 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf */,
				B232077692DC426A89C51563 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf */,
				408A1260E33846DE893B5AB0 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf */,
				0D812EE9420C4C0EA7C02FE5 /* Noto-Kufi-Arabic-Black.otf */,
				2A2E05D2691043D6BD56F429 /* Noto-Kufi-Arabic-Bold.otf */,
				A2A78B9B8FC84D5A81884719 /* Noto-Kufi-Arabic-Extra-Bold.otf */,
				0043FB0E868D4EBCB66D1042 /* Noto-Kufi-Arabic-Extra-Light.otf */,
				7E30C3C5B6C74582B300A692 /* Noto-Kufi-Arabic-Light.otf */,
				233BB12B20584EB29757FE19 /* Noto-Kufi-Arabic-Medium.otf */,
				3D49A887EDCA4C15B5A17E69 /* Noto-Kufi-Arabic-Regular.otf */,
				5550C61832194470B0D76803 /* Noto-Kufi-Arabic-Semi-Bold.otf */,
				988D87418A5B4FC0A0266B42 /* Noto-Kufi-Arabic-Thin.otf */,
				4CEBD045CCEE47BBAC387645 /* Helvetica-Bold.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				2CD5441147650714CD81A0B6 /* Pods-SkillsHub.debug.xcconfig */,
				7DA7E3F7EAE1024949C28E38 /* Pods-SkillsHub.release.xcconfig */,
				2393F677D7F5F8D950D4C72B /* Pods-SkillsHub-SkillsHubStaging.debug.xcconfig */,
				D5BAD3E0E1606469E38421D2 /* Pods-SkillsHub-SkillsHubStaging.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* SkillsHub */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SkillsHub" */;
			buildPhases = (
				A9A4713F53D3446C95DD61AE /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				4EB4511B2DDC870C0040EBD6 /* ShellScript */,
				874453614A64F08710A16320 /* [CP] Copy Pods Resources */,
				19CBE28228399526396D404F /* [CP-User] [RNFB] Core Configuration */,
				8FB3D4EC0AB5B657425687C4 /* [CP-User] [RNFB] Crashlytics Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SkillsHub;
			productName = SkillsHub;
			productReference = 13B07F961A680F5B00A75B9A /* SkillsHub.app */;
			productType = "com.apple.product-type.application";
		};
		86C1FA032D2EA1240092DB83 /* SkillsHubStaging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 86C1FA432D2EA1240092DB83 /* Build configuration list for PBXNativeTarget "SkillsHubStaging" */;
			buildPhases = (
				E4B2AB1B6D687A625414BE82 /* [CP] Check Pods Manifest.lock */,
				86C1FA052D2EA1240092DB83 /* Sources */,
				86C1FA082D2EA1240092DB83 /* Frameworks */,
				86C1FA0A2D2EA1240092DB83 /* Resources */,
				86C1FA3E2D2EA1240092DB83 /* Bundle React Native code and images */,
				4E5475E22DDC662C004842E5 /* ShellScript */,
				D728029BF2D1B028B172E62C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SkillsHubStaging;
			productName = SkillsHub;
			productReference = 86C1FA462D2EA1240092DB83 /* SkillsHubStaging.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SkillsHub" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			packageReferences = (
				4EE622042DDB7B7C00853152 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
			);
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* SkillsHub */,
				86C1FA032D2EA1240092DB83 /* SkillsHubStaging */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				E2AE7FCFEC462E55C514EE2E /* PrivacyInfo.xcprivacy in Resources */,
				B288B79F93BD4BD3A8DFF2A3 /* SF-Pro-Text-Black.otf in Resources */,
				2A40B6DC2BF2385A00762063 /* Fonts in Resources */,
				475EE29C850A4F718A48B041 /* SF-Pro-Text-Bold.otf in Resources */,
				D6CD3720BD794F00AF2B3777 /* SF-Pro-Text-Light.otf in Resources */,
				FAD53E302BF789C6008A1F2D /* LaunchScreen.storyboard in Resources */,
				503E6ED72DA949B2BC47005B /* SF-Pro-Text-LightItalic.otf in Resources */,
				7B77A46921DD4C8D99467332 /* SF-Pro-Text-Medium.otf in Resources */,
				4C12BEDB2DD5473BBF693750 /* SF-Pro-Text-MediumItalic.otf in Resources */,
				25E9E10516174EAAB79102D7 /* SF-Pro-Text-Regular.otf in Resources */,
				33B37EA900DF4647B5E1B27F /* SF-Pro-Text-Thin.otf in Resources */,
				86C1FA4E2D2EA4A80092DB83 /* GoogleService-Info.plist in Resources */,
				AA1935188424448997206E80 /* Amiri-Bold.ttf in Resources */,
				6869855EF6C1405E87CD77A9 /* Amiri-BoldSlanted.ttf in Resources */,
				42E318C9C727459B845C63C1 /* Amiri-Regular.ttf in Resources */,
				6276F8C61344428C94C23CA4 /* Amiri-Slanted.ttf in Resources */,
				D0867B1FCF4543BB81DDD9DA /* HelveticaNeueBlack.otf in Resources */,
				5D054FC7DE244480A5BD7A15 /* HelveticaNeueBlackItalic.otf in Resources */,
				7256120F33B24E05B76A8789 /* HelveticaNeueBold.otf in Resources */,
				6892501E2A38447FA15A393F /* HelveticaNeueBoldItalic.otf in Resources */,
				1286A78D7D674419AE328D96 /* HelveticaNeueHeavy.otf in Resources */,
				77AFC9B4D5054368ABEEEF57 /* HelveticaNeueHeavyItalic.otf in Resources */,
				A7D6A9DE3C7743328897E294 /* HelveticaNeueLight.otf in Resources */,
				D5D4938DEBD54A54B5453B8B /* HelveticaNeueLightItalic.otf in Resources */,
				375A5511609E43859F53FB5F /* HelveticaNeueMedium.otf in Resources */,
				B68FF402FC4745B1A533C6CC /* HelveticaNeueMediumItalic.otf in Resources */,
				5DDA283E07954AF0A135D184 /* HelveticaNeueRoman.otf in Resources */,
				54FFB7994E7E4D2D8EF44879 /* HelveticaNeueThin.otf in Resources */,
				DAA9199C282D4BD09BD905DA /* HelveticaNeueThinItalic.otf in Resources */,
				E8E7492EDCE34D69925AE22C /* HelveticaNeueUltraLight.otf in Resources */,
				25A3071BA7EE45F4BA401D63 /* HelveticaNeueUltraLightItalic.otf in Resources */,
				B361610096F14E249492C92F /* HelveticaNeueItalic.ttf in Resources */,
				5EB86306E6F147FEB4FBC821 /* HelveticaNeueLTArabic-Bold.ttf in Resources */,
				45FC2F99083C49EDBBBB3242 /* HelveticaNeueLTArabic-Light.ttf in Resources */,
				95D035DC422340A9AEC5F294 /* HelveticaNeueLTArabic-Roman.ttf in Resources */,
				4E9D7620A7664127AA8DD72C /* Tajawal-Bold.ttf in Resources */,
				8C03D870B2E04DF5BC7932CA /* Tajawal-Light.ttf in Resources */,
				658A3430384A46E9819231F7 /* Tajawal-Regular.ttf in Resources */,
				7D8352C249F841149D219E0B /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf in Resources */,
				30E35F8C1CF84199AA8C98C4 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf in Resources */,
				AC7CDC3C4B774E79869F1C80 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf in Resources */,
				8E4CA6F4B04046A8A014F227 /* Noto-Kufi-Arabic-Black.otf in Resources */,
				779B1838D0B44571AB802175 /* Noto-Kufi-Arabic-Bold.otf in Resources */,
				80FEFEC078514D6982538EC2 /* Noto-Kufi-Arabic-Extra-Bold.otf in Resources */,
				05C0B9B8A3184E53BC93AACC /* Noto-Kufi-Arabic-Extra-Light.otf in Resources */,
				B3FA52101DC6449EB0307D11 /* Noto-Kufi-Arabic-Light.otf in Resources */,
				4863BEA415E64881AF11B942 /* Noto-Kufi-Arabic-Medium.otf in Resources */,
				2886A018BC41433997BBC54C /* Noto-Kufi-Arabic-Regular.otf in Resources */,
				F9F274B892E54048923B8AAE /* Noto-Kufi-Arabic-Semi-Bold.otf in Resources */,
				A13795B228E54855B91D4B2B /* Noto-Kufi-Arabic-Thin.otf in Resources */,
				3B3AC761A5514D838331DC05 /* Helvetica-Bold.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		86C1FA0A2D2EA1240092DB83 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				86C1FA0B2D2EA1240092DB83 /* Images.xcassets in Resources */,
				86C1FA0C2D2EA1240092DB83 /* PrivacyInfo.xcprivacy in Resources */,
				86C1FA0D2D2EA1240092DB83 /* SF-Pro-Text-Black.otf in Resources */,
				86C1FA0E2D2EA1240092DB83 /* Fonts in Resources */,
				86C1FA0F2D2EA1240092DB83 /* SF-Pro-Text-Bold.otf in Resources */,
				86C1FA112D2EA1240092DB83 /* SF-Pro-Text-Light.otf in Resources */,
				86C1FA122D2EA1240092DB83 /* LaunchScreen.storyboard in Resources */,
				86C1FA132D2EA1240092DB83 /* SF-Pro-Text-LightItalic.otf in Resources */,
				86C1FA142D2EA1240092DB83 /* SF-Pro-Text-Medium.otf in Resources */,
				86C1FA152D2EA1240092DB83 /* SF-Pro-Text-MediumItalic.otf in Resources */,
				86C1FA162D2EA1240092DB83 /* SF-Pro-Text-Regular.otf in Resources */,
				86C1FA172D2EA1240092DB83 /* SF-Pro-Text-Thin.otf in Resources */,
				86C1FA4C2D2EA4750092DB83 /* GoogleService-Info.plist in Resources */,
				86C1FA182D2EA1240092DB83 /* Amiri-Bold.ttf in Resources */,
				86C1FA192D2EA1240092DB83 /* Amiri-BoldSlanted.ttf in Resources */,
				86C1FA1A2D2EA1240092DB83 /* Amiri-Regular.ttf in Resources */,
				86C1FA1B2D2EA1240092DB83 /* Amiri-Slanted.ttf in Resources */,
				86C1FA1C2D2EA1240092DB83 /* HelveticaNeueBlack.otf in Resources */,
				86C1FA1D2D2EA1240092DB83 /* HelveticaNeueBlackItalic.otf in Resources */,
				86C1FA1E2D2EA1240092DB83 /* HelveticaNeueBold.otf in Resources */,
				86C1FA1F2D2EA1240092DB83 /* HelveticaNeueBoldItalic.otf in Resources */,
				86C1FA202D2EA1240092DB83 /* HelveticaNeueHeavy.otf in Resources */,
				86C1FA212D2EA1240092DB83 /* HelveticaNeueHeavyItalic.otf in Resources */,
				86C1FA222D2EA1240092DB83 /* HelveticaNeueLight.otf in Resources */,
				86C1FA232D2EA1240092DB83 /* HelveticaNeueLightItalic.otf in Resources */,
				86C1FA242D2EA1240092DB83 /* HelveticaNeueMedium.otf in Resources */,
				86C1FA252D2EA1240092DB83 /* HelveticaNeueMediumItalic.otf in Resources */,
				86C1FA262D2EA1240092DB83 /* HelveticaNeueRoman.otf in Resources */,
				86C1FA272D2EA1240092DB83 /* HelveticaNeueThin.otf in Resources */,
				86C1FA282D2EA1240092DB83 /* HelveticaNeueThinItalic.otf in Resources */,
				86C1FA292D2EA1240092DB83 /* HelveticaNeueUltraLight.otf in Resources */,
				86C1FA2A2D2EA1240092DB83 /* HelveticaNeueUltraLightItalic.otf in Resources */,
				86C1FA2B2D2EA1240092DB83 /* HelveticaNeueItalic.ttf in Resources */,
				86C1FA2C2D2EA1240092DB83 /* HelveticaNeueLTArabic-Bold.ttf in Resources */,
				86C1FA2D2D2EA1240092DB83 /* HelveticaNeueLTArabic-Light.ttf in Resources */,
				86C1FA2E2D2EA1240092DB83 /* HelveticaNeueLTArabic-Roman.ttf in Resources */,
				86C1FA2F2D2EA1240092DB83 /* Tajawal-Bold.ttf in Resources */,
				86C1FA302D2EA1240092DB83 /* Tajawal-Light.ttf in Resources */,
				86C1FA312D2EA1240092DB83 /* Tajawal-Regular.ttf in Resources */,
				86C1FA322D2EA1240092DB83 /* Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf in Resources */,
				86C1FA332D2EA1240092DB83 /* Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf in Resources */,
				86C1FA342D2EA1240092DB83 /* HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf in Resources */,
				86C1FA352D2EA1240092DB83 /* Noto-Kufi-Arabic-Black.otf in Resources */,
				86C1FA362D2EA1240092DB83 /* Noto-Kufi-Arabic-Bold.otf in Resources */,
				86C1FA372D2EA1240092DB83 /* Noto-Kufi-Arabic-Extra-Bold.otf in Resources */,
				86C1FA382D2EA1240092DB83 /* Noto-Kufi-Arabic-Extra-Light.otf in Resources */,
				86C1FA392D2EA1240092DB83 /* Noto-Kufi-Arabic-Light.otf in Resources */,
				86C1FA3A2D2EA1240092DB83 /* Noto-Kufi-Arabic-Medium.otf in Resources */,
				86C1FA3B2D2EA1240092DB83 /* Noto-Kufi-Arabic-Regular.otf in Resources */,
				86C1FA3C2D2EA1240092DB83 /* Noto-Kufi-Arabic-Semi-Bold.otf in Resources */,
				86C1FA3D2D2EA1240092DB83 /* Noto-Kufi-Arabic-Thin.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		19CBE28228399526396D404F /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    _JSON_OUTPUT_BASE64=$(python -c 'import json,sys,base64;print(base64.b64encode(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"').read())['${_JSON_ROOT}'])))' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		4E5475E22DDC662C004842E5 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist",
				"$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist",
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
		4EB4511B2DDC870C0040EBD6 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist",
				"$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist",
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
		86C1FA3E2D2EA1240092DB83 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		874453614A64F08710A16320 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SkillsHub/Pods-SkillsHub-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SkillsHub/Pods-SkillsHub-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SkillsHub/Pods-SkillsHub-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8FB3D4EC0AB5B657425687C4 /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		A9A4713F53D3446C95DD61AE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SkillsHub-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D728029BF2D1B028B172E62C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SkillsHub-SkillsHubStaging/Pods-SkillsHub-SkillsHubStaging-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E4B2AB1B6D687A625414BE82 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SkillsHub-SkillsHubStaging-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				868133BC2E2E2A4E00DCE2D1 /* NotificationScheduler.m in Sources */,
				2A926AD02BF3F602003E5262 /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		86C1FA052D2EA1240092DB83 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				868133BB2E2E2A4E00DCE2D1 /* NotificationScheduler.m in Sources */,
				86C1FA062D2EA1240092DB83 /* AppDelegate.m in Sources */,
				86C1FA072D2EA1240092DB83 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2CD5441147650714CD81A0B6 /* Pods-SkillsHub.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SkillsHub/SkillsHub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 439;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = S5U9U9ZU34;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = SkillsHub/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Tomouh;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 25.07.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dge.academy;
				PRODUCT_NAME = SkillsHub;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Academy  - App Store";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DA7E3F7EAE1024949C28E38 /* Pods-SkillsHub.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SkillsHub/SkillsHub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 439;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = S5U9U9ZU34;
				INFOPLIST_FILE = SkillsHub/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Tomouh;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 25.07.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dge.academy;
				PRODUCT_NAME = SkillsHub;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Academy  - App Store";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "To select a photo from library to upload as profile picture in the app.";
				INFOPLIST_KEY_NSCameraUsageDescription = "Requested to enable a user to take a photo which they can then choose to upload as their profile picture in the mobile app.";
				INFOPLIST_KEY_NSDownloadsFolderUsageDescription = "The app enables a user to download certain information including e.g., a learning certificate earned.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Requested to enable a user to select a photo from their photo library which they can then choose to upload as their profile picture in the mobile app.";
				INFOPLIST_KEY_NSRemindersUsageDescription = "The app sends notifications to users e.g., to remind them of a learning course they have enrolled in, to remind them to complete learning to help them achieve their daily learning target etc.";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = false;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "To select a photo from library to upload as profile picture in the app.";
				INFOPLIST_KEY_NSCameraUsageDescription = "Requested to enable a user to take a photo which they can then choose to upload as their profile picture in the mobile app.";
				INFOPLIST_KEY_NSDownloadsFolderUsageDescription = "The app enables a user to download certain information including e.g., a learning certificate earned.";
				INFOPLIST_KEY_NSFileProviderDomainUsageDescription = "";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Requested to enable a user to select a photo from their photo library which they can then choose to upload as their profile picture in the mobile app.";
				INFOPLIST_KEY_NSRemindersUsageDescription = "The app sends notifications to users e.g., to remind them of a learning course they have enrolled in, to remind them to complete learning to help them achieve their daily learning target etc.";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = false;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		86C1FA442D2EA1240092DB83 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2393F677D7F5F8D950D4C72B /* Pods-SkillsHub-SkillsHubStaging.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SkillsHub/SkillsHub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 440;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = S5U9U9ZU34;
				ENABLE_BITCODE = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "SkillsHubStaging-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = TomouhStg;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 25.07.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dge.academy.stage;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Academy - Stage - App Store";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		86C1FA452D2EA1240092DB83 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D5BAD3E0E1606469E38421D2 /* Pods-SkillsHub-SkillsHubStaging.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SkillsHub/SkillsHub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Distribution: Abu Dhabi Systems and Information Centre (S5U9U9ZU34)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Distribution: Abu Dhabi Systems and Information Centre (S5U9U9ZU34)";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 440;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = S5U9U9ZU34;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "SkillsHubStaging-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = TomouhStg;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 25.07.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.dge.academy.stage;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Academy - Stage - App Store";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SkillsHub" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SkillsHub" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		86C1FA432D2EA1240092DB83 /* Build configuration list for PBXNativeTarget "SkillsHubStaging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				86C1FA442D2EA1240092DB83 /* Debug */,
				86C1FA452D2EA1240092DB83 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		4EE622042DDB7B7C00853152 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.12.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
