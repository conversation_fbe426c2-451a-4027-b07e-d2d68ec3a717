#import "AppDelegate.h"
#import <React/RCTBundleURLProvider.h>
#import <Firebase.h>
#import "RNFBMessagingModule.h"

#import <React/RCTLinkingManager.h>
#import "Orientation.h"
#import "UAEPass-Swift.h"
#import <React/RCTI18nUtil.h>  // Import RCTI18nUtil

// ADD THESE NEW IMPORTS FOR NOTIFICATIONS:
#import <UserNotifications/UserNotifications.h>
#import <RNNotifications.h>
#import "NotificationScheduler.h"

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  [[RCTI18nUtil sharedInstance] allowRTL:YES];
  [[RCTI18nUtil sharedInstance] forceRTL:NO];
  [RNNotifications startMonitorNotifications];
  // Initialize Firebase
  [FIRApp configure];
  
  // ADD THIS: Set up notification center delegate
  if (@available(iOS 10.0, *)) {
    [UNUserNotificationCenter currentNotificationCenter].delegate = (id<UNUserNotificationCenterDelegate>)self;
  }

  UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
  center.delegate = (id<UNUserNotificationCenterDelegate>)[[UIApplication sharedApplication] delegate];
  
  // Set up React Native module name and initial props
  self.moduleName = @"SkillsHub";
  self.initialProps = @{};
  
  // Call super method
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self bundleURL];
}

- (NSURL *)bundleURL
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

//- (BOOL)application:(UIApplication *)application
//            openURL:(NSURL *)url
//            options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
//{
//  return [RCTLinkingManager application:application openURL:url options:options];
//}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{

//  THIS block of code handles the UAE Pass success or failure redirects(links)
    UAEPass * obj = [[UAEPass alloc] init];
    NSString *successHost = [obj getSuccessHost];
    NSString *failureHost = [obj getFailureHost];

  NSLog(@"### URL --- : %@", url.absoluteString);
  NSLog(@"host success %@", successHost);
  NSLog(@"host fail %@", failureHost);

  if ([url.absoluteString containsString: successHost]) {
      [obj handleLoginSuccess];
      return YES;
    }else if ([url.absoluteString containsString: failureHost]){
      [obj handleLoginFailure];
      return NO;
    }
  // UAE pass link handler ends here
  // Other link handler code goes here

//  return YES;
  return [RCTLinkingManager application:application openURL:url options:options];
}

- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window
{
  return [Orientation getOrientation];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(nonnull NSUserActivity *)userActivity
 restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
 return [RCTLinkingManager application:application
                  continueUserActivity:userActivity
                    restorationHandler:restorationHandler];
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
  [RNNotifications didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
  [RNNotifications didFailToRegisterForRemoteNotificationsWithError:error];
}

@end
