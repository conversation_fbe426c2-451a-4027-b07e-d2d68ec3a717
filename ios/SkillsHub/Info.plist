<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Tomouh</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mynavigatorapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mynavigatorapp</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.education</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>mailto:</string>
		<string>https</string>
		<string>www</string>
		<string>facebook</string>
		<string>uaepass</string>
		<string>uaepassqa</string>
		<string>uaepassstg</string>
		<string>uaepassdev</string>
		<string>whatsapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app requires camera access to scan QR codes and take photos.</string>
	<key>NSDocumentPickerUsageDescription</key>
	<string>This enables you to select a photo from library to upload as profile picture in the app.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Authentication with TouchId or FaceID</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs to access your location. This helps us provide accurate information and better services tailored to you.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This enables you to record audio to share as recommendation for a user.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This enables you to select a photo from library to upload as profile picture in the app.</string>
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	<key>NSUserNotificationUsageDescription</key>
	<string>We need to send you notifications about your learning activities, updates, and important information.</string>
	<key>NSUserNotificationsEnabled</key>
	<true/>
	<key>NSContactsUsageDescription</key>
	<string>We need access to your contacts to add new ones.</string>
	<key>UIAppFonts</key>
	<array>
		<string>FontAwesome.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Fontisto.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Roboto_medium.ttf</string>
		<string>Roboto.ttf</string>
		<string>rubicon-icon-font.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Helvetica-Neue-LT-55-Roman-Regular-_UrduFonts.com_.otf</string>
		<string>Helvetica-NeueLT-W20-45-Light-_UrduFonts.com__1.otf</string>
		<string>HelveticaNeueBlack.otf</string>
		<string>HelveticaNeueBlackItalic.otf</string>
		<string>HelveticaNeueBold.otf</string>
		<string>HelveticaNeueBoldItalic.otf</string>
		<string>HelveticaNeueHeavy.otf</string>
		<string>HelveticaNeueHeavyItalic.otf</string>
		<string>HelveticaNeueLT-W20-75-Bold-_UrduFonts.com__1.otf</string>
		<string>HelveticaNeueLTProBd.otf</string>
		<string>HelveticaNeueLTProBlk.otf</string>
		<string>HelveticaNeueLight.otf</string>
		<string>HelveticaNeueLightItalic.otf</string>
		<string>HelveticaNeueLite.otf</string>
		<string>HelveticaNeueMedium.otf</string>
		<string>HelveticaNeueMediumItalic.otf</string>
		<string>HelveticaNeueRoman.otf</string>
		<string>HelveticaNeueThin.otf</string>
		<string>HelveticaNeueThinItalic.otf</string>
		<string>HelveticaNeueUltraLight.otf</string>
		<string>HelveticaNeueUltraLightItalic.otf</string>
		<string>SF-Pro-Text-Black.otf</string>
		<string>SF-Pro-Text-Bold.otf</string>
		<string>SF-Pro-Text-Light.otf</string>
		<string>SF-Pro-Text-LightItalic.otf</string>
		<string>SF-Pro-Text-Medium.otf</string>
		<string>SF-Pro-Text-MediumItalic.otf</string>
		<string>SF-Pro-Text-Regular.otf</string>
		<string>SF-Pro-Text-Thin.otf</string>
		<string>Amiri-Bold.ttf</string>
		<string>Amiri-BoldSlanted.ttf</string>
		<string>Amiri-Regular.ttf</string>
		<string>Amiri-Slanted.ttf</string>
		<string>HelveticaNeueItalic.ttf</string>
		<string>HelveticaNeueLTArabic-Bold.ttf</string>
		<string>HelveticaNeueLTArabic-Light.ttf</string>
		<string>HelveticaNeueLTArabic-Roman.ttf</string>
		<string>Tajawal-Bold.ttf</string>
		<string>Tajawal-Light.ttf</string>
		<string>Tajawal-Regular.ttf</string>
		<string>Noto-Kufi-Arabic-Black.otf</string>
		<string>Noto-Kufi-Arabic-Bold.otf</string>
		<string>Noto-Kufi-Arabic-Extra-Bold.otf</string>
		<string>Noto-Kufi-Arabic-Extra-Light.otf</string>
		<string>Noto-Kufi-Arabic-Light.otf</string>
		<string>Noto-Kufi-Arabic-Medium.otf</string>
		<string>Noto-Kufi-Arabic-Regular.otf</string>
		<string>Noto-Kufi-Arabic-Semi-Bold.otf</string>
		<string>Noto-Kufi-Arabic-Thin.otf</string>
		<string>Helvetica-Bold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDarkContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIViewSemanticContentAttribute</key>
	<string>ForceRightToLeft</string>
</dict>
</plist>
