{"name": "<PERSON>hub", "version": "1.1.2", "private": true, "scripts": {"android:dev": "react-native run-android --variant=developmentdebug --appId=com.dge.academy.development", "android:stag": "react-native run-android --variant=stagingrelease --appId=com.dge.academy.staging", "android:prod": "react-native run-android --variant=productionrelease --appId=com.dge.academy", "android:stag:debug": "react-native run-android --variant=stagingDebug --appId=com.dge.academy.staging", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:fix:working": "eslint --fix $(git diff --name-only --diff-filter d | grep -e '\\.[tj]sx\\?$' | xargs)", "lint:fix:staged": "eslint --fix $(git diff --name-only --cached --diff-filter=AM | grep -e '\\.[tj]sx\\?$' | xargs)", "lint:check:working": "eslint $(git diff --name-only --diff-filter d | grep -e '\\.[tj]sx\\?$' | xargs)", "lint:check:staged": "eslint $(git diff --name-only --cached --diff-filter=AM | grep -e '\\.[tj]sx\\?$' | xargs)", "prettier:fix:working": "prettier --write $(git diff --name-only --diff-filter d | grep -e '\\.[tj]sx\\?$' | xargs)", "prettier:fix:staged": "prettier --write $(git diff --name-only --cached --diff-filter=AM | grep -e '\\.[tj]sx\\?$' | xargs)", "format:working": "npm run prettier:fix:working & npm run lint:fix:working", "format:staged": "npm run prettier:fix:staged & npm run lint:fix:staged", "prettier": "prettier --check .", "ios": "react-native run-ios", "ios-stg": "npx react-native run-ios --scheme=SkillsHubStaging --mode=Debug", "android": "react-native run-android", "android-stg": " npx react-native run-android --tasks installStagingDebug", "local:android-release-apk": "cd android/ && ./gradlew installProdRelease", "local:android-release-aab": "cd android/ && ./gradlew bundleProdRelease", "load:env_test": "ENVFILE=.env.test", "ci:ios-test": "react-native run-ios --configuration Test.Debug", "ci:android-test": "react-native run-android --variant=qaDebug --appIdSuffix=qa", "local:ios-test": "npm-run-all load:env_test ci:ios-test", "local:android-test": "npm-run-all load:env_test ci:android-test", "android_release": "react-native run-android --variant release", "build_scorm": "webpack --config ./TotaraMobileOfflineScorm/webpack.config.js", "debug": "REACT_DEBUGGER=\"open -g 'rndebugger://set-debugger-loc?port=8001' ||\" react-native start", "start": "react-native start", "test": "jest", "test-coverage": "jest --config jest.config.js --collectCoverage", "test-junit": "jest --config jest.config.js --ci --reporters=jest-junit --reporters=default", "eslint": "eslint -c .eslintrc.json --ext .js --ext .ts --ext .tsx ./src/totara ./TotaraMobileOfflineScorm", "clean": "rm -rf node_modules", "reconnect": "adb reverse tcp:8081 tcp:8081 && yarn refresh", "menu": "adb shell input keyevent 82", "refresh": "adb shell input text \"RR\"", "xcode": "open ios/skillshub.xcworkspace", "clear_cache:temp": "rm -rf $TMPDIR/react-*", "clear_cache:watchman": "watchman watch-del-all", "clear_cache:ios": "rm -rf ios/build/ModuleCache/*", "clear_cache:npm": "npm cache clean --force", "clear:modules": "rm -rf node_modules", "clear:pods": "rm -rf ios/Pods/", "clear:ios": "pod deintegrate && pod cache clean --all && rm ios/PodFile.lock && yarn clear:pods", "clear:cache": "npm-run-all --parallel clear_cache:*", "clear:yarn": "rm yarn.lock && yarn cache clear", "clear:all": "npm-run-all clear:cache clear:modules && yarn && yarn clear:pods", "clear:jest": "npx jest --clearCache", "install:translations": "node scripts/post-install/install-translations.js", "install:pods": "cd ios && pod install && cd ..", "jetify": "npx jetify", "mock:schema:setup": "get-graphql-schema https://mobile.demo.totara.software/totara/webapi/dev_graphql_executor.php --json > e2e/graphql/schema.json", "mock:setup": "mv src/totara/lib/config.local.ts src/totara/lib/config.local.bak.ts &&  cp src/totara/lib/config.detox.ts src/totara/lib/config.local.ts", "mock:reset": "mv src/totara/lib/config.local.bak.ts src/totara/lib/config.local.ts", "detox:test": "detox build && detox test || echo '`detox test` completed.'", "detox:run": "npm-run-all mock:setup detox:test mock:reset", "devices": "npm-run-all devices:ios devices:android", "devices:ios": "xcrun xctrace list devices", "devices:android": "adb devices", "test:debug": "node --inspect-brk --inspect ./node_modules/.bin/jest -i", "test:memory": "node --expose-gc ./node_modules/.bin/jest --runInBand --logHeapUsage", "prepare": "husky", "postinstall": "patch-package", "prebuild:clean": "yarn clear:yarn && yarn clear:modules && yarn clear:ios", "prebuild:ios": "yarn install && yarn install:pods", "reset": "rm -rf node_modules yarn.lock ios/Podfile.lock && yarn cache clear && yarn install && cd ios && pod cache clean --all && pod deintegrate && pod install && cd .. "}, "resolutions": {"@codler/react-native-keyboard-aware-scroll-view": "2.0.1", "@react-native-community/datetimepicker": "^8.4.2", "@types/react": "17.0.43", "@react-native/gradle-plugin": "^0.75.1"}, "dependencies": {"@apollo/client": "^3.5.9", "@codler/react-native-keyboard-aware-scroll-view": "^2.0.1", "@fortawesome/fontawesome-svg-core": "^1.2.28", "@fortawesome/free-brands-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.13.0", "@fortawesome/react-native-fontawesome": "^0.2.3", "@gorhom/bottom-sheet": "^4.6.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@quidone/react-native-calendars": "^1.0.2", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/cli-platform-android": "^14.1.1", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-community/masked-view": "^0.1.10", "@react-native-community/netinfo": "^5.9.10", "@react-native-community/slider": "4.5.0", "@react-native-cookies/cookies": "github:react-native-cookies/cookies", "@react-native-firebase/analytics": "14.5.0", "@react-native-firebase/app": "14.5.0", "@react-native-firebase/crashlytics": "14.5.0", "@react-native-firebase/messaging": "14.5.0", "@react-native-firebase/perf": "14.5.0", "@react-native-firebase/remote-config": "14.5.0", "@react-navigation/compat": "^5.3.10", "@react-navigation/material-bottom-tabs": "^6.2.28", "@react-navigation/material-top-tabs": "^6.6.13", "@react-navigation/native": "^5.8.10", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^5.12.8", "add": "^2.0.6", "apollo-link-timeout": "^4.0.0", "apollo3-cache-persist": "^0.9.1", "axios": "^1.6.7", "dayjs": "^1.11.13", "eslint-plugin-unused-imports": "^4.1.4", "formik": "^2.4.6", "graphql": "^15.0.0", "i18n-js": "^3.3.0", "i18next": "^23.12.1", "jail-monkey-new": "^2.8.3", "lodash": "^4.17.15", "lottie-react-native": "^6.7.2", "moment": "^2.24.0", "moment-timezone": "^0.5.45", "native-base": "^2.13.14", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prop-types": "^15.7.2", "radash": "^12.1.0", "react": "18.2.0", "react-i18next": "^15.0.0", "react-native": "0.74.1", "react-native-animatable": "^1.3.3", "react-native-audio-recorder-player": "^3.6.7", "react-native-base64": "^0.2.1", "react-native-biometrics": "^3.0.1", "react-native-blob-util": "^0.19.9", "react-native-calendars": "^1.1305.0", "react-native-chart-kit": "^6.12.0", "react-native-check-app-install": "^0.0.5", "react-native-circular-progress": "^1.4.0", "react-native-circular-progress-indicator": "^4.4.2", "react-native-config": "^1.4.1", "react-native-contacts": "^8.0.5", "react-native-country-picker-modal": "^2.0.0", "react-native-date-picker": "4.2.2", "react-native-device-info": "^5.5.4", "react-native-document-picker": "^9.3.1", "react-native-dropdown-select-list": "^2.0.5", "react-native-element-dropdown": "^2.10.1", "react-native-elements": "^3.4.2", "react-native-encrypted-storage": "^4.0.3", "react-native-fast-image": "^8.6.3", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.16.6", "react-native-gesture-handler": "2.14.1", "react-native-gradle-plugin": "^0.71.19", "react-native-image-crop-picker": "^0.41.2", "react-native-image-picker": "^7.1.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-lightbox-v2": "^0.9.0", "react-native-linear-gradient": "2.8.3", "react-native-localize": "^1.3.4", "react-native-maps": "1.20.1", "react-native-markdown-display": "^7.0.2", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-notifications": "^5.1.0", "react-native-orientation-locker": "1.5.0", "react-native-otp-textinput": "^1.1.3", "react-native-pager-view": "^6.3.0", "react-native-paper": "^4.7.1", "react-native-pdf": "^6.7.5", "react-native-permissions": "^4.1.1", "react-native-progress": "^5.0.0", "react-native-ratings": "^8.1.0", "react-native-reanimated": "3.15.2", "react-native-reanimated-carousel": "^3.5.1", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^3.31.1", "react-native-shake": "5.6.2", "react-native-share": "^10.2.1", "react-native-skeleton-placeholder": "^4.0.0", "react-native-sound": "^0.11.2", "react-native-svg": "^15.2.0", "react-native-svg-transformer": "^1.5.0", "react-native-swiper-flatlist": "^3.2.3", "react-native-tab-view": "^3.5.2", "react-native-uaepass": "1.0.6", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.1.0", "react-native-video": "^5.2.1", "react-native-view-more-text": "^2.1.0", "react-native-view-shot": "^4.0.3", "react-native-vision-camera": "^4.6.3", "react-native-webview": "^13.10.0", "react-native-zip-archive": "^7.0.1", "react-redux": "^7.2.0", "reanimated-bottom-sheet": "^1.0.0-alpha.22", "recompose": "^0.30.0", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-persist-sensitive-storage": "^1.0.0", "redux-thunk": "^2.4.2", "rxjs": "^7.8.2", "victory": "^37.0.2", "victory-native": "^37.0.2", "victory-vendor": "^37.3.2", "xmldom": "^0.3.0", "xpath": "0.0.27", "yarn": "^1.22.22", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.26.0", "@eslint/js": "^9.7.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@testing-library/react-hooks": "^2.0.1", "@testing-library/react-native": "^7.1.0", "@types/add": "^2", "@types/eslint": "^9", "@types/i18n-js": "^3.0.1", "@types/jasmine": "^3.6.1", "@types/jest": "^27.4.0", "@types/node": "^22.10.10", "@types/react": "~18.0.0", "@types/react-dom": "17.0.14", "@types/react-native": "~0.69.1", "@types/react-native-video": "^5.0.1", "@types/react-redux": "^7.1.9", "@types/react-test-renderer": "^18.0.0", "@types/redux-logger": "^3.0.7", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.6", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "detox": "^17.11.4", "download": "^8.0.0", "enzyme": "^3.11.0", "eslint": "9.x", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "express": "^4.17.1", "globals": "^15.8.0", "husky": "^9.1.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.6.3", "jest-enzyme": "^7.1.2", "jest-junit": "^8.0.0", "jetifier": "^2.0.0", "lint-staged": "^15.2.7", "metro-react-native-babel-preset": "^0.66.2", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "react-dom": "^17.0.2", "react-test-renderer": "18.2.0", "ts-jest": "^27.1.3", "typescript": "^5.7.3", "typescript-eslint": "^7.17.0", "unzipper": "^0.10.11", "waait": "^1.0.5", "webpack": "^5.3.0", "webpack-cli": "^4.1.0"}, "config": {"maxPackagesNumber": 100, "maxSizeBites": 840400, "allowedLicenseTypes": ["weaklyProtective", "protective", "permissive", "publicDomain", "uncategorized"]}, "detox": {"configurations": {"ios.sim.debug": {"binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/Totara.app", "build": "xcodebuild -workspace ios/skillshub.xcworkspace -scheme skillshub -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build", "type": "ios.simulator", "device": {"type": "iPhone 11"}}}, "test-runner": "jest"}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.3.1", "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix"], "./src/**": ["prettier --write"]}}