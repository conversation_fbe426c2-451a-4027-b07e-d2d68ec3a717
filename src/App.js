/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2019 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD’s customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */
import 'react-native-gesture-handler';
import React, { useEffect } from 'react';
import { I18nManager, LogBox, Text, TextInput, Platform, NativeModules } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import crashlytics from '@react-native-firebase/crashlytics';
import perf from '@react-native-firebase/perf';
import Loading from '@totara/components/Loading';
import { config } from '@totara/lib';
import FontAwesome from '@totara/lib/fontAwesome';
import crashReportLogger from '@utils/crashlyticsLogger';
import { FeatureFlagsProvider } from '@utils/FeatureFlagsProvider';
import Jail<PERSON>onkey from 'jail-monkey-new';
import { Root } from 'native-base';
import deviceInfoModule from 'react-native-device-info';
import FlashMessage from 'react-native-flash-message';
import RNFS from 'react-native-fs';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as RNLocalize from 'react-native-localize';
import { OrientationLocker, PORTRAIT } from 'react-native-orientation-locker';
import RNRestart from 'react-native-restart';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { NotificationTray } from './components';
import { ThemeProvider } from './theme/ThemeProvider';
import RootContainer from './totara/RootContainer';
import { persistor, store } from './totara/store';
import { setMomentLocale } from './utils/localization';

if (Text.defaultProps == null) Text.defaultProps = {};
Text.defaultProps.allowFontScaling = false;

if (TextInput.defaultProps == null) TextInput.defaultProps = {};
TextInput.defaultProps.allowFontScaling = false;

const getSystemLanguage = () => {
  const locales = RNLocalize.getLocales();

  if (locales.length > 0) {
    // Primary language code, e.g., "en"
    const languageCode = locales[0].languageCode;

    // Optional: Region code, e.g., "US"
    const countryCode = locales[0].countryCode;

    // Full language tag, e.g., "en-US"
    const languageTag = locales[0].languageTag;

    return { languageCode, countryCode, languageTag };
  }

  return null; // Fallback if no locales are available
};

const clearAppCache = async () => {
  try {
    const cacheDir = RNFS.CachesDirectoryPath;
    const files = await RNFS.readDir(cacheDir);

    for (const file of files) {
      await RNFS.unlink(file.path);
    }
  } catch (err) {
    crashReportLogger(err, {
      component: 'App.js clearAppCache',
      additionalInfo: 'Failed to clear cache',
    });
    console.error(`Error clearing cache: ${err}`);
  }
};

const checkAppVersion = async () => {
  try {
    const currAppVersion = deviceInfoModule.getVersion();
    const cachedAppVersion = await AsyncStorage.getItem('app-version');
    if (!cachedAppVersion || cachedAppVersion !== currAppVersion) {
      await clearAppCache();
      AsyncStorage.setItem('app-version', currAppVersion);
    }
  } catch (err) {
    crashReportLogger(err, {
      component: 'App.js checkAppVersion',
      additionalInfo: 'Failed to check app version',
    });
    console.error(`Failed to get app version: ${err}`);
  }
};

const syncLanguageToAndroid = async () => {
  try {
    if (Platform.OS === 'android' && NativeModules.NotificationScheduler) {
      const savedLanguage = await AsyncStorage.getItem('currentLanguage');
      if (savedLanguage) {
        await NativeModules.NotificationScheduler.setAppLanguage(savedLanguage);
        console.log('Language synced to Android on app startup:', savedLanguage);

        // Update notification content with localized strings
        let title, body;
        if (savedLanguage === 'arabic') {
          title = 'تذكير بتسجيل الدخول';
          body = 'لا تنس تسجيل الدخول لمواصلة رحلة التعلم الخاصة بك!';
        } else {
          title = 'Sign In Reminder';
          body = 'Don\'t forget to sign in to continue your learning journey!';
        }

        await NativeModules.NotificationScheduler.updateNotificationContent(title, body);
        console.log('Notification content synced to Android on app startup:', { title, body });
      }
    }
  } catch (error) {
    console.warn('Failed to sync language to Android on startup:', error);
  }
};

const loadRTLSetting = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem('currentLanguage');
    await syncLanguageToAndroid();

    if (savedLanguage == null) {
      if (getSystemLanguage().languageCode == 'ar' && !I18nManager.isRTL) {
        await I18nManager.forceRTL(true);
        await I18nManager.allowRTL(true);
        await AsyncStorage.setItem('isRtlSet', 'true');
        RNRestart.Restart();
      }
    } else if (savedLanguage === 'arabic' && !I18nManager.isRTL) {
      await I18nManager.forceRTL(true);
      await I18nManager.allowRTL(true);
      await AsyncStorage.setItem('isRtlSet', 'true');
      RNRestart.Restart();
    } else if (savedLanguage !== 'arabic' && I18nManager.isRTL) {
      await I18nManager.forceRTL(false);
      await I18nManager.allowRTL(false);
      await AsyncStorage.setItem('isRtlSet', 'false');
      RNRestart.Restart();
    }
  } catch (e) {
    crashReportLogger(e, {
      component: 'App.js loadRTLSetting',
      additionalInfo: 'Failed to load the RTL setting',
    });
    console.error('Failed to load the RTL setting.', e);
  }
};

FontAwesome.init();
if (config.disableConsoleYellowBox) {
  LogBox.ignoreAllLogs();
}
LogBox.ignoreLogs(['new NativeEventEmitter']);
LogBox.ignoreAllLogs(true);

const isJailBroken = JailMonkey.isJailBroken() || JailMonkey.isOnExternalStorage();

const App = () => {
  if (isJailBroken && !__DEV__) {
    return (
      <SafeAreaProvider style={{ justifyContent: 'center', alignItems: 'center' }}>
        <Text>Our app is not supported on root devices.</Text>
      </SafeAreaProvider>
    );
  }

  useEffect(() => {
    loadRTLSetting();
    setMomentLocale();
    checkAppVersion();
    crashlytics().setCrashlyticsCollectionEnabled(true);
    perf().setPerformanceCollectionEnabled(true);
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1, backgroundColor: 'transparent' }}>
      <Root>
        <Provider store={store}>
          <PersistGate loading={<Loading />} persistor={persistor}>
            <OrientationLocker orientation={PORTRAIT} />
            <FeatureFlagsProvider>
              <ThemeProvider>
                <RootContainer />
                <FlashMessage MessageComponent={renderCustomContent} />
              </ThemeProvider>
            </FeatureFlagsProvider>
            <FlashMessage MessageComponent={renderCustomContent} />
          </PersistGate>
        </Provider>
      </Root>
    </GestureHandlerRootView>
  );
};

export default App;

const renderCustomContent = ({ message }) => <NotificationTray message={message} />;
