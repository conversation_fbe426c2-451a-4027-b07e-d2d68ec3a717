import { AnalyticsLogger } from './AnalyticsLogger.tsx';
import { FirebaseAnalyticsLogger } from './FirebaseAnalyticsLogger.tsx';

class AnalyticsService {
  private logger: AnalyticsLogger;

  constructor(logger: AnalyticsLogger) {
    this.logger = logger;
  }

  setLogger(logger: AnalyticsLogger): void {
    this.logger = logger;
  }

  logScreenView(screenName: string, screenClass: string): Promise<void> {
    return this.logger.logScreenView(screenName, screenClass);
  }

  logEvent(eventName: string, params?: Record<string, any>): Promise<void> {
    return this.logger.logEvent(eventName, params);
  }

  setUserId(userId: string): Promise<void> {
    return this.logger.setUserId(userId);
  }

  setUserProperty(propertyName: string, propertyValue: string): Promise<void> {
    return this.logger.setUserProperty(propertyName, propertyValue);
  }
}

export const analyticsService = new AnalyticsService(new FirebaseAnalyticsLogger());
