import analytics from '@react-native-firebase/analytics';
import { AnalyticsLogger } from './AnalyticsLogger.tsx';

const loggedEvents = new Map();

export class FirebaseAnalyticsLogger implements AnalyticsLogger {
  async logScreenView(screenName: string, screenClass: string): Promise<void> {
    await analytics().logScreenView({ screen_name: screenName, screen_class: screenClass });
    console.log(`Firebase logged screen view: ${screenName}`);
  }

  async logEvent(eventName: string, params?: Record<string, any>): Promise<void> {
    const eventKey = `${eventName}-${JSON.stringify(params || {})}`;
    const now = Date.now();
    const lastLogged = loggedEvents.get(eventKey);
    if (!lastLogged || (now - lastLogged > 3000)) {
      loggedEvents.set(eventKey, now);
      if (params) {
        await analytics().logEvent(eventName, params);
        console.log(`Firebase logged event: ${eventName}`, params);
      } else {
        await analytics().logEvent(eventName);
        console.log(`Firebase logged event: ${eventName}`);
      }

      for (const [key, timestamp] of loggedEvents.entries()) {
        if (now - timestamp > 60000) {
          loggedEvents.delete(key);
        }
      }
    } else {
      console.log(`Skipped duplicate event: ${eventName}`, params);
    }
  }

  async setUserId(userId: string): Promise<void> {
    await analytics().setUserId(userId);
    console.log(`Firebase set user ID: ${userId}`);
  }

  async setUserProperty(propertyName: string, propertyValue: string): Promise<void> {
    await analytics().setUserProperties({ [propertyName]: propertyValue });
    console.log(`Firebase set user property: ${propertyName}=${propertyValue}`);
  }
}
