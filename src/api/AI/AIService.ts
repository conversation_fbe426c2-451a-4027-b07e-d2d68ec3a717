import axios from 'axios';

// Create an Axios instance with default configuration
const AIService = axios.create({
  baseURL: '*******************************************/conv-ai-engine/rag_search',
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-tamm-search-key': '3f49dec6-847f-404a-83da-0add176a066a',
  },
});

// Request Interceptor to add the access token
AIService.interceptors.request.use(
  async (config) => {


    console.log('🚀 [Request]', {
      url: config.url,
      method: config.method,
      headers: config.headers,
      params: config.params,
      data: config.data,
    });

    return config;
  },
  (error) => {
    console.error('❌ [Request Error]', error);
    return Promise.reject(error);
  }
);

// Response Interceptor for handling errors globally
AIService.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {

      console.error('❌ [Response Error]', {
        status: error.response.status,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('❌ [Request Error]: No response received', error.request);
    } else {
      console.error('❌ [General Error]:', error.message);
    }


    return Promise.reject(error);
  }
);

export default AIService;
