import { TOMOUH_BASE_URL } from '@api/api_client.tsx';
import { getAppLocale } from '@utils/AppUtils.tsx';
import crashReportLogger from '@utils/crashlyticsLogger';
import axios from 'axios';

export function getAIUserProfile(profile) {
  const bio = profile?.bio;
  const education = JSON.stringify(profile?.education);
  const training = JSON.stringify(profile?.training);
  const workExperience = JSON.stringify(profile?.workExperience);
  const achievements = JSON.stringify(profile?.achievements);
  const skills = profile?.skills;
  const hobbies = profile?.hobbies;
  const interests = profile?.interests;
  const prompt = `
## USER PROFILE:
first name: ${profile?.firstName}
first name AR: ${profile?.firstNameAR}
last name: ${profile?.lastName}
last name AR: ${profile?.lastNameAR}
Bio: ${bio}
Job Title: ${profile?.jobTitle}
Education: ${education}
Training: ${training}
Work Experience: ${workExperience}
Skills: ${skills}
Achievements: ${achievements}
Hobbies: ${hobbies}
Interests: ${interests}
## END USER PROFILE
`;
  return prompt;
}

export async function chatWithAI(prompt, maxResults=10) {
  const data = JSON.stringify({
    prompt_name: 'gov-academy-course-query',
    output_schema: {
      properties: {
        text: {
          description:
            'text that should return to user, the text must be in the same language of the user',
          title: 'Text',
          type: 'string',
        },
        course_ids: {
          description:
            'List of `Id` of courses that were mentioned in the `text` field of the response',
          items: {
            type: 'string',
          },
          title: 'Course Ids',
          type: 'array',
        },
      },
      required: ['text', 'course_ids'],
      title: 'AgentResponse',
      type: 'object',
    },
    index_configs: [
      {
        name: 'gov-academy-courses',
        number_of_results: maxResults || 10,
        select_fields: [
          'Id',
          'Name',
          'Summary',
          'Type',
          'Skills',
          'Language',
          'Proficiency',
          'Duration',
          'Category',
          'CourseURL',
          'ImageURL',
        ],
        full_text_fields: ['FullText'],
        vector_fields: ['FullTextVector'],
      },
    ],
    messages: prompt,
  });

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: TOMOUH_BASE_URL + 'tamm/rag_search/',
    headers: {
      'Content-Type': 'application/json',
      'X-API-KEY': 'key-abc123',
    },
    data: data,
  };

  try {
    const response = await axios.request(config);
    return response.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'index.ts chatWithAI',
      url: config.url,
      additionalInfo: 'API Error',
    });
    throw error;
  }
}
export async function chatCompletionWithFunctionCalling(messages) {
  const tools = [
    {
      type: 'function',
      function: {
        name: 'searchCoursesRAG',
        description:
          "Search for top or recommended courses,skills, interests, and educational programs using user's profile or query and you MUST mention if user asks in arabic you must add in the query the response MUST be in arabic",
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description:
                "User's query or interest area to find suitable courses and must mention if user talks in arabic to translate",
            },
          },
          required: ['query'],
        },
      },
    },
  ];
  const data = JSON.stringify({
    stream: false,
    messages: messages,
    tools: tools,
    tool_choice: 'auto',
  });

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: TOMOUH_BASE_URL + 'tamm/chat/completions/',
    headers: {
      'X-API-KEY': 'key-def456',
      'Content-Type': 'application/json',
    },
    data,
  };

  const response = await axios.request(config);
  const result = response.data;

  const toolCall = result.choices[0]?.message?.tool_calls?.[0];
  if (toolCall?.function?.name === 'searchCoursesRAG') {
    const args = JSON.parse(toolCall.function.arguments);
    const query = args.query;
      return await chatWithAI([
      {
        role: "user",
        content: query
      }
    ],10);
  } else {
    return result;
  }
}

export async function generativeAI(
  prompt,
  desc,
  arabicReply: boolean = false,
  is_json: boolean = false,
) {
  if (arabicReply) {
    const locale = await getAppLocale();
    if (locale == 'ar') {
      prompt = prompt + ', your response MUST be in Arabic even the json values must be translated';
    }
  }
  const replyType = is_json ? 'json_object' : 'text';
  const data = JSON.stringify({
    messages: [
      { role: 'system', content: desc },
      { role: 'user', content: prompt },
    ],
    stream: false,
    response_format: { type: replyType },
  });
  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: TOMOUH_BASE_URL + 'tamm/chat/completions/',
    headers: {
      'X-API-KEY': 'key-def456',
      'Content-Type': 'application/json',
    },
    data: data,
  };
  try {
    const response = await axios.request(config);
    return response.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis generativeAI',
      url: config.url,
      additionalInfo: 'API error',
    });
  }
}
export async function getChatTitle(userPrompt) {
  const prompt =
    'Read and analyze user prompt below and write a professional title for the chat based on user prompt context only:\n' +
    userPrompt;
  const desc =
    'chat title based on provided data plain text without any markdown and it should be without user name or courses only his chat title based on his prompt';
  return await generativeAI(prompt, desc, true);
}
export async function getSuggestions(profile) {
  const prompt =
    'based on your knowledge and my profile below give me json list of 3 unique prompt suggestions my profile: ' +
    getAIUserProfile(profile);
  const desc =
    "prompt suggestions json array {prompts:[{label:xx,title:xx}]} title should be always based on the courses , users skills and profession and should be short as the example, example: [{label:'I am interested in',title:'Learning about python'}]";
  return await generativeAI(prompt, desc, true, true);
}
export async function getAITopPicsCourses(profile) {
  const prompt = [
    {
      role: 'user',
      content:
        'Based on the user profile, generate a list of top 3 courses that align with their skills and interests.\n' +
        getAIUserProfile(profile),
    },
  ];
  return await chatWithAI(prompt);
}
export async function getAIRecommendedCourses(profile) {
  const prompt = [
    {
      role: 'user',
      content:
        'Based on the user profile, generate a list of recommended courses that align with their skills and interests.\n' +
        getAIUserProfile(profile),
    },
  ];
  return await chatWithAI(prompt, 50);
}
export async function getEnhancedAIBio(profile) {
  const prompt =
    'Write my professional enhanced bio in a tone like am talking about my self based on the details below:\n' +
    getAIUserProfile(profile);
  const desc =
    'user enhanced bio based on his profile plain text without any markdown and it should be without user name or courses only his enhanced bio with enhanced text';
  return await generativeAI(prompt, desc, true);
}

export async function getEnhancedAIResponsibilities(workExperience) {
  const prompt =
    'Write my professional enhanced Responsibilities in a tone like am talking about my self based on the details below read it and analyze it before answering:\n' +
    workExperience;

  const desc =
    'user enhanced Responsibilities based on provided data plain text without any markdown and it should be without user name or courses only his enhanced responsibilities with enhanced text';
  return await generativeAI(prompt, desc, true);
}
