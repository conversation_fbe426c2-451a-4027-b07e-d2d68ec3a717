import crashReportLogger from '@utils/crashlyticsLogger';
import { customFetch } from './api_client_fetch';
import { TOMOUH_BASE_URL } from './api_client';

export interface APIFeature {
  code: string;
  description: string;
}

export interface APIFeatureFlagsResponse {
  data: {
    features: APIFeature[];
  };
}

export interface APIFeatureFlagsDirectResponse {
  features: APIFeature[];
}

export interface APIFeatureFlagsRequest {
  email_address: string;
}

const API_KEY = 'key-fxf456';

export const fetchAPIFeatureFlags = async (email: string): Promise<APIFeature[]> => {
  try {
    const url = `${TOMOUH_BASE_URL}features/get-features/`;
    const requestBody: APIFeatureFlagsRequest = {
      email_address: email,
    };

    const customHeaders = {
      'Content-Type': 'application/json',
      'X-API-KEY': API_KEY,
    };

    const response = await customFetch<APIFeatureFlagsResponse>(
      url,
      'POST',
      requestBody,
      {},
      customHeaders
    );

    if (response && typeof response === 'object' && 'features' in response) {
      const directResponse = response as APIFeatureFlagsDirectResponse;
      return directResponse.features || [];
    }
    
    if (response && typeof response === 'object' && 'data' in response) {
      const typedResponse = response as APIFeatureFlagsResponse;
      return typedResponse.data?.features || [];
    }

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'apiFeatureFlagsService fetchAPIFeatureFlags',
      additionalInfo: `Failed to fetch API feature flags for email: ${email}`,
    });
    return [];
  }
}; 