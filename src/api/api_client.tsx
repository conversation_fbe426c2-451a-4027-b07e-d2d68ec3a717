import crashReportLogger from '@utils/crashlyticsLogger';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

export const ENV: string = 'stg'; // prod, stg, dev

export const BASE_URL_PROD = 'https://admin.workspace.dge.gov.ae/';
export const BASE_URL_STG = 'https://admin.stg.workspace.dge.gov.ae/';
export const BASE_URL_DEV = 'https://admin.dev.workspace.dge.gov.ae/';

export const BASE_URL_SUPPORT_CENTER =
  ENV === 'prod'
    ? 'https://workspace.dge.gov.ae/app/v2'
    : 'https://stg.workspace.dge.gov.ae/app/v2';
export const TOMOUH_BASE_URL =
  ENV === 'prod' ? 'https://api.tomouh.gov.ae/' : 'https://stg.api.tomouh.gov.ae/';
export const SUPPORT_CENTER_AUTH_CREDENTIALS =
  ENV === 'prod'
    ? {
        emailAddress: '<EMAIL>',
        password: '2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG',
      }
    : {
        emailAddress: '<EMAIL>',
        password: '2y16gvJ9j2WQZPlJLneg6ExnEO.U4lCG.vOh.q9k3vU0ufFpdtuqSrbcG',
      };
export const SAMPLE_EMIRATES_ID = 'EMID00001';

export const BASE_URL: string =
  ENV === 'prod' ? BASE_URL_PROD : ENV === 'stg' ? BASE_URL_STG : BASE_URL_DEV;

const TOTARA_BASE_URL_PROD = 'https://learn.adsg.gov.ae/webservice/rest/server.php';
const TOTARA_BASE_URL_STG = 'https://adsgstaging.elearning.ae/webservice/rest/server.php';

export const TOTARA_AUTH_BASE_URL =
  ENV === 'prod' ? 'https://learn.adsg.gov.ae' : 'https://adsgstaging.elearning.ae';

export const TOTARA_BASE_URL: string = ENV === 'prod' ? TOTARA_BASE_URL_PROD : TOTARA_BASE_URL_STG;

const TOTARA_BASE_SITE_URL_PROD = 'https://learn.adsg.gov.ae/';
const TOTARA_BASE_SITE_URL_STG = 'https://adsgstaging.elearning.ae/';
export const TOTARA_SITE_URL: string =
  ENV === 'prod' ? TOTARA_BASE_SITE_URL_PROD : TOTARA_BASE_SITE_URL_STG;

export let BASIC_AUTH_CRED = {
  username: '<EMAIL>',
  password: 'Liferay@DGE',
  type: 'requestor',
  isRequestor: true,
  isApprover: false,
};

enum StatusCode {
  Unauthorized = 401,
  Forbidden = 403,
  TooManyRequests = 429,
  InternalServerError = 500,
}

const headers: Readonly<Record<string, string | boolean>> = {
  Accept: 'application/json',
  'Content-Type': 'application/json; charset=utf-8',
  'Access-Control-Allow-Credentials': true,
  'X-Requested-With': 'XMLHttpRequest',
};

// We can use the injectToken function to inject the JWT token through an interceptor
// We get the `accessToken` from the `localStorage` which is stored during the authentication process
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const injectToken: any = (config: AxiosRequestConfig): AxiosRequestConfig => {
  try {
    if (config != null && config.headers != null) {
      config.headers.Authorization =
        'BASIC ' + btoa(`${BASIC_AUTH_CRED.username}:${BASIC_AUTH_CRED.password}`);
    }
    return config;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'api_client injectToken',
      additionalInfo: 'Failed to inject token',
    });
    throw new Error(error + '');
  }
};

class Http {
  private instance: AxiosInstance | null = null;

  private get http(): AxiosInstance {
    return this.instance != null ? this.instance : this.initHttp();
  }

  initHttp() {
    const http = axios.create({
      baseURL: BASE_URL,
      headers,
      withCredentials: false,
    });

    http.interceptors.request.use(injectToken, (error) => Promise.reject(error));

    http.interceptors.response.use(
      (response) => response,
      (error) => {
        const { response } = error;
        return this.handleError(response);
      },
    );

    this.instance = http;
    return http;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  request<T = any, R = AxiosResponse<T>>(config: AxiosRequestConfig): Promise<R> {
    return this.http.request(config);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  get<T = any, R = AxiosResponse<T>>(url: string, config?: AxiosRequestConfig): Promise<R> {
    return this.http.get<T, R>(url, config);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  post<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.post<T, R>(url, data, config);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  patch<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.patch<T, R>(url, data, config);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  put<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return this.http.put<T, R>(url, data, config);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  delete<T = any, R = AxiosResponse<T>>(url: string, config?: AxiosRequestConfig): Promise<R> {
    return this.http.delete<T, R>(url, config);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  uploadFile<T = any, R = AxiosResponse<T>>(
    url: string,
    file: File,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    additionalData?: Record<string, any>,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.keys(additionalData).forEach((key) => {
        formData.append(key, additionalData[key]);
      });
    }

    const headersForFileUpload: Record<string, string | boolean> = {
      'Content-Type': 'multipart/form-data',
      'Access-Control-Allow-Credentials': true,
      'X-Requested-With': 'XMLHttpRequest',
    };

    const headersConfig = {
      ...headers,
      ...headersForFileUpload,
    };

    const options = {
      headers: headersConfig,
      withCredentials: false,
    };

    return this.request<T, R>({
      method: 'post',
      url,
      data: formData,
      ...config,
      ...options,
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  replaceUploadedFile<T = any, R = AxiosResponse<T>>(
    url: string,
    file: File,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    additionalData?: Record<string, any>,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.keys(additionalData).forEach((key) => {
        formData.append(key, additionalData[key]);
      });
    }

    const headersForFileUpload: Record<string, string | boolean> = {
      'Content-Type': 'multipart/form-data',
      'Access-Control-Allow-Credentials': true,
      'X-Requested-With': 'XMLHttpRequest',
    };

    const headersConfig = {
      ...headers,
      ...headersForFileUpload,
    };

    const options = {
      headers: headersConfig,
      withCredentials: false,
    };

    return this.request<T, R>({
      method: 'put',
      url,
      data: formData,
      ...config,
      ...options,
    });
  }

  // Handle global app errors
  // We can handle generic app errors depending on the status code
  private handleError(error: { status: never }) {
    const status = error?.status;

    switch (status) {
      case StatusCode.InternalServerError: {
        // Handle InternalServerError
        break;
      }
      case StatusCode.Forbidden: {
        // Handle Forbidden
        break;
      }
      case StatusCode.Unauthorized: {
        // Handle Unauthorized
        break;
      }
      case StatusCode.TooManyRequests: {
        // Handle TooManyRequests
        break;
      }
    }

    return Promise.reject(error);
  }
}

export const http = new Http();

export const setCredentials = (newCredentials: {
  username: string;
  password: string;
  type: string;
  isRequestor: boolean;
  isApprover: boolean;
}): void => {
  BASIC_AUTH_CRED = newCredentials;
};
