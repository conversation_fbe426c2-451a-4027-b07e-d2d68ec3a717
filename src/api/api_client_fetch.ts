/* eslint-disable @typescript-eslint/no-explicit-any */
import crashReportLogger from '@utils/crashlyticsLogger';

export const customFetch = async <T>(
  url: string,
  method: string = 'GET',
  body?: Record<string, any>,
  options: RequestInit = {},
  customHeaders: Record<string, string> = {},
) => {
  let response: Response | undefined;
  try {
    options.headers = { ...options.headers, ...customHeaders };
    options.method = method;
    if (body) {
      options.body = JSON.stringify(body);
    }
    response = await fetch(url, options);

    const data: T = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'api_client_fetch customFetch',
      url: url,
      additionalInfo: 'customFetch method failed',
      serverResponse: response,
    });
    return error;
  }
};

export const customFetchGET = async <T>(
  url: string,
  customHeaders: Record<string, string> = {},
  options: RequestInit = {},
): Promise<T> => {
  let response: Response | undefined;
  try {
    options.headers = { ...options.headers, ...customHeaders };
    options.method = 'GET';

    response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data: T = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'api_client_fetch customFetchGet',
      url: url,
      additionalInfo: 'customFetchGET method failed',
      serverResponse: response,
    });
    throw error;
  }
};

export const customFetchDELETE = async <T>(
  url: string,
  customHeaders: Record<string, string> = {},
  options: RequestInit = {},
): Promise<T> => {
  let response: Response | undefined;
  try {
    options.headers = { ...options.headers, ...customHeaders };
    options.method = 'DELETE';

    response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data: T = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'api_client_fetch customFetchDELETE',
      url: url,
      additionalInfo: 'customFetchDELETE method failed',
      serverResponse: response,
    });
    throw error;
  }
};
