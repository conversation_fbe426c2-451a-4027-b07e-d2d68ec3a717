import { AxiosResponse } from 'axios';

import { API_URL } from './urls';

import { authenticateUser } from '../interfaces/authenticateUser';

import { http } from '../api/api_client';

export const authenticateUserToWFM = async (accessCode?: string): Promise<UAEPassUserType> => {
  const config = {
    headers: {
      accept: 'application/json',
      'Content-Type': 'application/json',
      'x-csrf-token': 'adrsEjot',
    },
  };
  const data = {
    accessCode,
  };
  const response: AxiosResponse<UAEPassUserType> = await http.post(
    `${API_URL.authenticateUser.url}`,
    data,
    config,
  );
  return response.data;
};
