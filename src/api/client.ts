/* eslint-disable no-console */
import { from, of, Observable, firstValueFrom } from 'rxjs';
import { shareReplay, catchError, tap, finalize } from 'rxjs/operators';

type Params = unknown;

const cache = new Map<string, unknown>();
const pendingRequests = new Map<string, Observable<unknown>>();
const cacheTimeout = 1 * 60 * 1000; // 1 minute

const genCacheKey = ({ url, params }: { url: string; params: Params }) =>
  JSON.stringify({ url, params });

const fetchData = (url: string, params: Params): Observable<unknown> => {
  return from(
    fetch(url, params as RequestInit | undefined).then((res) => {
      if (!res.ok) throw new Error(`HTTP error ${res.status}`);
      return res.json();
    })
  );
};

export const request = (url: string, params: Params): Observable<unknown> => {
  const key = genCacheKey({ url, params });

  if (cache.has(key)) {
    return of(cache.get(key));
  }

  if (pendingRequests.has(key)) {
    return pendingRequests.get(key)!;
  }

  const request$ = fetchData(url, params).pipe(
    tap((data) => {
      cache.set(key, data);
      setTimeout(() => {
        cache.delete(key);
      }, cacheTimeout);
    }),
    catchError((err) => {
      throw err;
    }),
    finalize(() => {
      pendingRequests.delete(key);
    }),
    shareReplay({ bufferSize: 1, refCount: true })
  );

  pendingRequests.set(key, request$);
  return request$;
};

export const requestAsync = async (url: string, params: unknown) => {
  try {
    const result = await firstValueFrom(request(url, params));
    return result;
  } catch (error) {
    console.error(`[ERROR] `, JSON.stringify(error));
    throw error;
  }
};
