import { getCustomHeaders } from '../utils/constants';
import { BASE_URL } from './api_client';
import { customFetchGET } from './api_client_fetch';

export const getPickList = async (token, type) => {
  const url = `${BASE_URL}o/headless-admin-list-type/v1.0/list-type-definitions?filter=name eq '${type}'`;
  const headers = getCustomHeaders(token);

  const result = await customFetchGET<any>(url, headers);
  if (result?.items) {
    const response = result.items.filter((item) => item.name === type);

    let modifiedListEntries;
    if (response.length > 0) {
      modifiedListEntries = response[0].listTypeEntries.map((entry) => ({
        ...entry,
        isSelected: false,
        name: entry.name,
        label: entry.name,
        value: entry.key,
      }));
    }
    return modifiedListEntries;
  }
};

export const getCountriesAndEmirates = async (token) => {
  const url = `${BASE_URL}o/c/countries?pageSize=-1`;
  const headers = getCustomHeaders(token);
  const result = await customFetchGET(url, headers);
  let listOfEmirates;
  if (result?.items?.length > 0) {
    listOfEmirates = result.items.map((entry) => ({
      ...entry,
      isSelected: false,
      name: entry.emirates,
      label: entry.emirates,
      value: entry.emirates,
    }));
  }
  return listOfEmirates;
};
