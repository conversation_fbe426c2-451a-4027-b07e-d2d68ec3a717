/* eslint-disable @typescript-eslint/no-explicit-any */
import { I18nManager } from 'react-native';
import { GenerateCodeType } from '@interfaces/onBoardingInterfaces';
import { store } from '@totara/store';
import crashReportLogger from '@utils/crashlyticsLogger';
import axios from 'axios';
import { Toast } from 'native-base';
import { getProfileOfUser, getProfilePercentage } from '../totara/actions/getWFMProfileAction';
import { getCustomHeaders } from '../utils/constants';
import { BASE_URL } from './api_client';
import { customFetch, customFetchDELETE, customFetchGET } from './api_client_fetch';
import { API_URL } from './urls';

export const getFaqs = async (searchQuery?: string, token: string) => {
  let url: string = `${BASE_URL}${API_URL.getFaq.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);

    if (searchQuery) {
      url += '?search=' + searchQuery;
    }

    result = await customFetchGET<any>(url, headers);
    if (result?.items) {
      const itemsByCategory = {};

      result?.items?.forEach((item) => {
        const category = I18nManager.isRTL ? item?.category?.ar_SA : item?.category?.en_US;
        if (!itemsByCategory[category]) {
          itemsByCategory[category] = [];
        }
        itemsByCategory[category].push(item);
      });

      const finalArray = Object.keys(itemsByCategory).map((category) => ({
        title: category,
        items: itemsByCategory[category],
      }));

      return finalArray;
    }

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getFaqs',
      url: url,
      additionalInfo: `Failed to fetch data for Faq's`,
      serverResponse: result,
    });
    return [];
  }
};

export const getSearchFAQ = async (searchQuery: string, token: string) => {
  let url = `${BASE_URL}${API_URL.getSearchFaq.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    if (searchQuery) {
      url += '?search=' + searchQuery;
    }
    result = await customFetchGET<any>(url, headers);
    if (result?.items) {
      const itemsByCategory = {};

      result?.items?.forEach((item) => {
        const category = item.category;
        if (!itemsByCategory[category]) {
          itemsByCategory[category] = [];
        }
        itemsByCategory[category].push(item);
      });

      const finalArray = Object.keys(itemsByCategory).map((category) => ({
        title: category,
        items: itemsByCategory[category],
      }));

      return finalArray;
    }

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getSearchFaqs',
      url: url,
      additionalInfo: `Failed to get searched data for Faq's`,
      serverResponse: result,
    });
    return [];
  }
};

interface ContactUs {
  emiratesId: string;
  message: string;
  token?: any;
}
export const submitContactInfo = async (body?: ContactUs) => {
  const url = `${BASE_URL}${API_URL.contactUs.url}`;
  let result;
  try {
    const headers = getCustomHeaders(body?.token);

    delete body?.token;
    result = await customFetch<any>(url, 'POST', body, {}, headers);
    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests SubmitContactInfo',
      url: url,
      additionalInfo: 'Failed to submit contact info data',
      serverResponse: result,
    });
    return [];
  }
};

// Biometrics
export const sendPublicKeyToServer = async (
  publicKey: string,
  deviceId: string,
): Promise<boolean> => {
  const url = `${BASE_URL}${API_URL.mobileBiometricCreate.url}`;
  let result;
  try {
    const headers = getCustomHeaders(store.getState().getWFMProfile.response?.liferayaccesstoken);
    const body = {
      biometricId: publicKey,
      deviceId,
    };

    const axiosConfig = {
      headers,
    };

    result = await axios.post(url, body, axiosConfig);
    return result && result?.data && result?.data?.code == 200 ? true : false;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests sendPublicKeyToServer',
      url: url,
      additionalInfo: 'Failed to send public key',
      serverResponse: result,
    });
    return false;
  }
};

export const verifySignatureWithServer = async (
  email: string,
  challenge: string,
  signature: string,
  deviceId?: string,
): Promise<any | null> => {
  const url = `${BASE_URL}${API_URL.mobileBiometricVerify.url}`;
  let result;
  try {
    const body = {
      email,
      challenge,
      signature,
      deviceId,
    };

    const axiosConfig = {
      headers: { 'content-type': 'application/json' },
    };
    result = await axios.post(url, body, axiosConfig);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests verifySignatureWithServer',
      url: url,
      additionalInfo: 'failed to verify signature with server',
      serverResponse: result,
    });
    throw error;
  }
};
// Biometrics end //

export const getEndorsement = async (skillName: string, id: string, token) => {
  let url = `${BASE_URL}${API_URL.getEndorsement.url}`;
  let response;
  try {
    const headers = getCustomHeaders(token);

    url += `filter=skillName eq '${skillName}' and talentProfileId eq '${id}'`;
    let endorsements, profile;
    response = await customFetchGET<any>(url, headers);
    if (response?.items) {
      endorsements = await Promise.all(
        response?.items?.map(async (item) => {
          let endorserProfile = {};

          profile = await getProfileOfUser(token, `id eq '${item.endorsedUserId}'`);
          if (profile) {
            endorserProfile = profile;
          }
          return {
            ...item,
            picture: item.picture,
            endorserProfile,
          };
        }),
      );

      return {
        // totalCount: response.totalCount,
        endorsedByprofile: endorsements,
      };
    }

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getEndorsement',
      url: url,
      additionalInfo: 'Failed to fetch endorsement from server',
      serverResponse: response,
    });
    return [];
  }
};

export const onSubmitReferCourse = async ({ body, token }) => {
  const url = `${BASE_URL}${API_URL.referACourse.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);

    result = await customFetch<any>(url, 'POST', body, {}, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests onSubmitReferCourse',
      url: url,
      additionalInfo: 'Failed to refer a course',
      serverResponse: result,
    });
    return error;
  }
};

const ReferralURLs = {
  received: API_URL.receivedReferrals.url,
  sent: API_URL.sentReferrals.url,
};

export const getReferralHistory = async ({ type, token }) => {
  const url = `${BASE_URL}${ReferralURLs[type]}?userId=${global.liferayUser.id}&page=1&pageSize=100`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getReferralHistory',
      url: url,
      additionalInfo: 'Failed to fetch referral history',
      serverResponse: result,
    });
    return [];
  }
};

export const getReferralHistoryCount = async (token) => {
  const url = `${BASE_URL}${API_URL.referralCount.url}?userId=${global.liferayUser.id}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getReferralHistoryCount',
      url: url,
      additionalInfo: 'Failed to fetch referral history count',
      serverResponse: result,
    });
    return [];
  }
};

export const onReadReferralStatus = async ({ body, token }) => {
  const url = `${BASE_URL}${API_URL.readReferralStatus.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'POST', body, {}, headers);
    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests onReadReferralStatus',
      url: url,
      additionalInfo: 'Failed to fetch referral status',
      serverResponse: result,
    });
    return [];
  }
};

export const getUsernames = async (
  searchQuery?: string,
  type = 'fullname',
  token?: string,
  userId?: string,
) => {
  const url = `${BASE_URL}${API_URL.searchUserName.url}${searchQuery}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);

    const filteredUsers = result?.items?.filter((item) => item.id !== userId);
    if (filteredUsers?.length > 0) {
      const arr = filteredUsers?.map((it) => {
        return {
          name: it?.name,
          id: it?.id,
          picture: it?.picture,
          email: it?.email,
        };
      });
      return arr;
    }

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getUsernames',
      url: url,
      additionalInfo: 'Failed to fetch usernames from server',
      serverResponse: result,
    });
    return [];
  }
};

export const toggleEndorsementPublication = async (
  id,
  endorsedUserId,
  pubEndorsed,
  skillName,
  talentProfileId,
  token,
) => {
  const body = {
    endorsedUserId: endorsedUserId,
    pubEndorsed: pubEndorsed,
    skillName: skillName,
    talentProfileId: talentProfileId,
  };

  const url = `${BASE_URL}${API_URL.toggleEndorsement.url}/${id}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'PUT', body, {}, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests toggleEndorsementPublication',
      url: url,
      additionalInfo: 'Failed to fetch data from server',
      serverResponse: result,
    });
    return [];
  }
};

export const addRecommendation = async (data, token) => {
  const url = `${BASE_URL}${API_URL.addRecommendation.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'POST', data, {}, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests addRecommendation',
      url: url,
      additionalInfo: 'Failed to add reccommendation',
      serverResponse: result,
    });
    return [];
  }
};

export const updateRecommendation = async (data, id, token) => {
  //same api using as add
  const url = `${BASE_URL}${API_URL.updatePendingRecommendation.url}/${id}`;
  let result;

  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'PUT', data, {}, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests updateRecommendation',
      url: url,
      additionalInfo: 'Failed to updateRecommendation',
      serverResponse: result,
    });
    return [];
  }
};

export const getRecievedRequest = async (userId, token) => {
  const url = `${BASE_URL}${API_URL.recommendationListingUrl.url}?statusRecommend=true&askUserId=${userId}&pageSize=50&page=1`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getReceivedRequest',
      url: url,
      additionalInfo: 'Failed to fetch received request',
      serverResponse: result,
    });
    return [];
  }
};
export const getPendingRecommendations = async (userId, token) => {
  const url = `${BASE_URL}${API_URL.recommendationListingUrl.url}?statusRecommend=false&giverUserId=${userId}&pageSize=50&page=1`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getPendingRecommendations',
      url: url,
      additionalInfo: 'Failed to fetch pending recommendations',
      serverResponse: result,
    });
    return [];
  }
};
export const getSentRequest = async (userId, token) => {
  const url = `${BASE_URL}${API_URL.recommendationListingUrl.url}?statusRecommend=true&giverUserId=${userId}&pageSize=50&page=1`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getSentRequest',
      url: url,
      additionalInfo: 'Failed to fetch request',
      serverResponse: result,
    });
    return [];
  }
};

export const deleteRecommendation = async (id, token) => {
  //same api using as add
  const url = `${BASE_URL}${API_URL.getRecommendations.url}/${id}`;
  let result;

  try {
    const headers = getCustomHeaders(token);
    result = await customFetchDELETE<void>(url, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests deleteRecommendation',
      url: url,
      additionalInfo: 'Failed to delete recommendation',
      serverResponse: result,
    });
    return [];
  }
};

export const onSubmitRecommendTalent = async ({ body, token }) => {
  const url = `${BASE_URL}${API_URL.recommendatalents.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);

    result = await customFetch<any>(url, 'POST', body, {}, headers);
    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests onSubmitRecommendTalent',
      url: url,
      additionalInfo: 'Failed to submit recommend talent',
      serverResponse: result,
    });
    return [];
  }
};

export const getRolesPermission = async (token) => {
  const url = `${BASE_URL}${API_URL.getRolesPermission.url}${global.liferayUser.userIdonboarding}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetchGET<any>(url, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests getRolespermission',
      url: url,
      additionalInfo: 'Failed to fetch permission',
      serverResponse: result,
    });
    return [];
  }
};

export const getPercentageAndMissingSections = async (userId, token) => {
  if (userId) {
    const data = await getProfilePercentage(userId, token);
    if (data?.data?.missingSections) {
      const missingSections = data?.data?.missingSections
        ?.map((section) => {
          if (section === 'workExperience') {
            return { route: 'WorkExperienceList', title: 'Work Experience' };
          }
          if (section === 'education') {
            return { route: 'EducationList', title: section };
          }
          if (section === 'training') {
            return { route: 'TrainingList', title: 'Trainings & Certificates' };
          }
          if (section === 'skills') {
            return { route: 'SkillList', title: 'Skills' };
          }
          if (section === 'assessments' || section === 'performance') {
            return null;
          }
          if (section === 'bio') {
            return {
              route: 'EditFormTelentProfile',
              title: 'About',
              type: 'About',
            };
          }
          return null;
        })
        .filter((section) => section !== null); // Filter out null values

      return {
        missingSections,
        percentage: data?.data?.progressPercentage,
      };
    }
  }
};

export const updateFcmToken = async (body, token) => {
  const url = `${BASE_URL}${API_URL.updateToken.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'POST', body, {}, headers);

    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests updateFcmToken',
      url: url,
      additionalInfo: 'Failed to update FCM Token',
      serverResponse: result,
    });
    return [];
  }
};

export const addRecommendation_notifier = async (
  talentId,
  body: {
    emailTo: string;
    recommender: string;
    recommenderEmail: string;
    userToName: string;
  },
  token,
) => {
  const url = `${BASE_URL}${API_URL.addRecommendationNotify.url}/${talentId}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'POST', body, {}, headers);

    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests addRecommendation_notifier',
      url: url,
      additionalInfo: 'Failed to add recommendation_notifier',
      serverResponse: result,
    });
    return [];
  }
};
export const recommendationRequestNotify = async (
  body: {
    personWhoRequest: string;
    personWhoRequestEmail: string;
    recommender: string;
    recommenderEmail: string;
  },
  token,
) => {
  const url = `${BASE_URL}${API_URL.recommendationRequestNotify.url}`;
  let result;
  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<any>(url, 'POST', body, {}, headers);

    if (result) return result;

    return [];
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests recommendationRequestNotify',
      url: url,
      additionalInfo: 'Failed request',
      serverResponse: result,
    });
    return [];
  }
};

export const sendCode = async (token, body) => {
  const url = BASE_URL + API_URL.generateCode.url;
  let result;

  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<GenerateCodeType>(url, 'POST', body, {}, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests sendCode',
      url: url,
      additionalInfo: 'Failed request',
      serverResponse: result,
    });
    Toast.show({
      text: JSON.stringify(error),
      textStyle: { textAlign: 'left' },
    });
  }
};

export const verifyOtp = async (token, body) => {
  const url = BASE_URL + API_URL.verifyCode.url;
  let result;

  try {
    const headers = getCustomHeaders(token);
    result = await customFetch<GenerateCodeType>(url, 'POST', body, {}, headers);
    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests verifyOtp',
      url: url,
      additionalInfo: 'Failed to verify Otp',
      serverResponse: result,
    });
    Toast.show({
      text: JSON.stringify(error),
      textStyle: { textAlign: 'left' },
    });
  }
};

export const requestEmailLoginOtpCode = async (email: string, type: number = 2) => {
  const url = `${BASE_URL}${API_URL.loginOtpCode.url}?source=ga-mobile-app`;
  let result;
  try {
    const headers = getCustomHeaders();
    const body = {
      code: 0,
      email,
      isAlternateLogin: true,
      type,
    };
    result = await customFetch<{ message: string; status: string }>(url, 'POST', body, {}, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests requestEmailLoginOtpCode',
      url: url,
      additionalInfo: 'Failed request for email login otp code',
      serverResponse: result,
    });
    Toast.show({
      text: JSON.stringify(error),
      textStyle: { textAlign: 'left' },
    });
  }
};

export const verifyEmailLoginOtpCode = async (email: string, code: number, type: number = 2) => {
  const url = `${BASE_URL}${API_URL.loginOtpCode.url}?source=ga-mobile-app`;
  let result;
  try {
    const headers = getCustomHeaders();
    const body = {
      code,
      email,
      isAlternateLogin: true,
      type,
    };
    result = await customFetch<any>(url, 'POST', body, {}, headers);

    return result;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'server_requests verifyEmailLoginOtpCode',
      url: url,
      additionalInfo: 'Failed to verifyLoginOtpCode',
      serverResponse: result,
    });
    Toast.show({
      text: JSON.stringify(error),
      textStyle: { textAlign: 'left' },
    });
  }
};
