/* eslint-disable no-console */

/* eslint-disable @typescript-eslint/no-explicit-any */
import { I18nManager } from 'react-native';
import { COURSES_PER_PAGE, PERSONAL_LISTS_PER_PAGE } from '@screens/myLearningPathways/constants';
import crashReportLogger from '@utils/crashlyticsLogger';
import { trackHttpRequestMetrics } from '@utils/trackHttpRequestMetrics';
import axios, { AxiosResponse } from 'axios';
import { Toast } from 'native-base';
import i18n from '../../i18n';
import { CourseDetailsTypes, UserHasLineManagerResponse } from '../../src/models/CourseType';
import { AnnouncementModel, TrendingCourseModel } from '../models';
import CourseProgress from '../models/CourseProgress';
import HomeScreenType from '../models/HomeScreenType';
import { getStreaks } from '../totara/actions/streakActions';
import { TOTARA_BASE_URL } from './api_client';
import { requestAsync } from './client';
import constants from './constants';

const requestTracker = {};

export const getCoursesProgress = async ({ apiToken }) => {
  if (!requestTracker[constants.LEARNING_DATA]) {
    requestTracker[constants.LEARNING_DATA] = true;
    const requestData = {
      wstoken: apiToken,
      wsfunction: constants.LEARNING_DATA,
      moodlewsrestformat: 'json',
      language: I18nManager.isRTL ? 'ar' : 'en',
    };

    let response: Response | undefined;
    try {
      response = await fetch(TOTARA_BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: Object.keys(requestData)
          .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
          .join('&'),
      });

      if (!response.ok) {
        requestTracker[constants.LEARNING_DATA] = false;
        return;
      }
      const responseData = (await response.json()) as CourseProgress;
      requestTracker[constants.LEARNING_DATA] = false;
      return responseData;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'totaraApis getCoursesProgress',
        url: TOTARA_BASE_URL,
        additionalInfo: 'Failed to fetch courses progress',
        serverResponse: response,
      });
      requestTracker[constants.LEARNING_DATA] = false;
      console.error('getCoursesProgress API call error:', error);
    } finally {
      if (response)
        trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getCoursesProgress');
    }
  }
};

export const getProfileData = async ({ apiToken }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.Profile,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    const response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getProfileData',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetchprofile data',
      serverResponse: response,
    });
    console.error('getProfileData API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getProfileData');
  }
};

export const getHomeScreenData = async ({ apiToken, userId }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.HOME_SCREEN,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = (await response.json()) as HomeScreenType;
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getHomeScreenData',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch home screen data',
      serverResponse: response,
    });
    console.error(' getHomeScreenData API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getHomeScreenData');
  }
};

export const addToFavorite = async ({ apiToken, userId, courseid, status }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.SAVE_FOR_LATER,
    moodlewsrestformat: 'json',
    courseid,
    status: status ? 1 : 0,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      // throw new Error("Network response was not ok");
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis addToFavorite',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to add to favorite',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis addToFavorite');
  }
};

export const getPersonalLists = async ({ apiToken, page = 0 }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_PERSONAL_LISTS,
    moodlewsrestformat: 'json',
    page,
    perpage: PERSONAL_LISTS_PER_PAGE,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getPersonalList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch personal list',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getPersonalLists');
  }
};

export const createPersonalList = async ({
  apiToken,
  name,
}: {
  apiToken: string;
  name: string;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.CREATE_PERSONAL_LIST,
    moodlewsrestformat: 'json',
    name,
    action: 'add',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis createPersonalList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to create personal list',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis createPersonalList');
  }
};

export const updatePersonalList = async ({
  apiToken,
  id,
  name,
}: {
  apiToken: string;
  id: number;
  name: string;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.CREATE_PERSONAL_LIST,
    moodlewsrestformat: 'json',
    id,
    name,
    action: 'update',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis updatePersonal list',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to update personal list',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis updatePersonalList');
  }
};

export const removePersonalList = async ({ apiToken, id }: { apiToken: string; id: number }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.REMOVE_PERSONAL_LISTS,
    moodlewsrestformat: 'json',
    id,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis removePersonalList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to remove personal list',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis removePersonalList');
  }
};

export const addCourseToPersonalList = async ({
  apiToken,
  id,
  coursesIds,
}: {
  apiToken: string;
  id: number;
  coursesIds: number[];
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.UPDATE_COURSE_STATUS_IN_PERSONAL_LIST,
    moodlewsrestformat: 'json',
    personallsitid: id,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key])}`)
        .join('&')
        .concat(
          coursesIds.map((id, i) => `&list[${i}][courseid]=${id}&list[${i}][status]=${1}`).join(''),
        ),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis addCourseToPersonalList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to add course to personal list',
      serverResponse: response,
    });
    console.error('API call error:', error);
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis addCourseToPersonalList',
      );
  }
};

export const getCoursesByPersonalListId = async ({
  apiToken,
  id,
  page = 0,
}: {
  apiToken: string;
  id: number;
  page?: number;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_COURSES_BY_PERSONAL_LIST_ID,
    moodlewsrestformat: 'json',
    id,
    page,
    perpage: COURSES_PER_PAGE,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(requestData[key])}`)
        .join('&'),
    });

    if (!response.ok) {
      console.error('Invalid Network response');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCorsesByPersonalListId',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch courses by personal list id',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis getCoursesByPersonalListId',
      );
  }
};

export const removeCourseFromPersonalList = async ({
  id,
  apiToken,
  coursesIds,
}: {
  id: number;
  apiToken: string;
  coursesIds: number[];
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.UPDATE_COURSE_STATUS_IN_PERSONAL_LIST,
    moodlewsrestformat: 'json',
    personallsitid: id,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&')
        .concat(
          coursesIds.map((id, i) => `&list[${i}][courseid]=${id}&list[${i}][status]=${0}`).join(''),
        ),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis removeCourseFromPersonalList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to remove course from personal list',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis removeCourseFromPpersonalList',
      );
  }
};

export const validatePersonalListName = async ({
  apiToken,
  name,
}: {
  apiToken: string;
  name: string;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.VALIDATE_PERSONAL_LIST_NAME,
    moodlewsrestformat: 'json',
    name,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis validatePersonalListName',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to validate personal list name',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis validatePerrsonalListName',
      );
  }
};

export type SearchedCourse = {
  courseid: number;
  contains_scorms: boolean;
  location: string;
  title: string;
  image: string;
  description: string;
  duration: number;
  lessons: number;
  coursetype: string;
  language: string;
  category: {
    id: number;
    name: string;
  };
  points: number;
  providername: string;
  providerlogo: string;
  rating: string;
  isenrolled: boolean;
  reviews: Array<{
    id: string;
    comment: string;
    userid: string;
    username: string;
    courseid: string;
    rating: string;
    created_at: string;
  }>;
  proficiency: null | string;
  product: null | string;
  getstarted: null | string;
  errormessage: null | string;
};

export type SearchedCoursesResponseT = {
  internalcourses: SearchedCourse[];
  librarycourses: SearchedCourse[];
};

const formatCreatorFilter = (provider: string[]) => {
  let creator = '[';
  if (Array.isArray(provider)) {
    provider.forEach((el, i) => {
      creator = `${creator}"${el}"`;
      if (i !== provider.length - 1) creator = `${creator},`;
    });
  }
  creator = `${creator}]`;
  return creator;
};

export const getSearchedCourses = async ({
  apiToken,
  text,
  product,
  learningtype,
  categoryid,
  duration,
  provider,
  page = 1,
  signal,
}) => {

  const creator = formatCreatorFilter(provider);

  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.SEARCH_LEARNING_DATA,
    moodlewsrestformat: 'json',
    keyword: text.trim(),

    language: I18nManager.isRTL ? 'ar' : 'en',
    ...(product && { product }),
    ...(learningtype && { learningtype }),
    ...(categoryid && { category: categoryid }),
    ...(provider && { creator }),
    ...(duration && { courseduration: duration }),
    page: page,
    perpage: 10,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
      signal: signal,
    });
    if (!response.ok) {
      return;
    }
    const responseData: SearchedCoursesResponseT = await response.json();

    return responseData;
  } catch (error) {
    if (error.name !== 'AbortError') {
      crashReportLogger(error as Error, {
        component: 'totaraApis',
        url: TOTARA_BASE_URL,
        additionalInfo: 'Failed to feth searched courses',
        serverResponse: response,
      });
    }
    throw error;
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getSearchedCourses');
  }
};

export type SearchKeyword = {
  keyword: string;
  count?: string;
  time?: string;
};

export type SearchDataResponseT = {
  popular_search: SearchKeyword[];
  recent_search: SearchKeyword[];
};

export const getPopularAndRecentSearch = async ({ apiToken }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.SEARCH_POPULAR_AND_RECENT,
    moodlewsrestformat: 'json',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    const responseData: SearchDataResponseT = await response.json();

    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getPopularAndRecentSearch',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch popular and recent courses',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis getPopularAndRecentSearch',
      );
  }
};

export const onClearRecent = async ({ apiToken }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.CLEAR_RECENT_SEARCHES,
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onClearRecent',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to clear recent courses',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onClearRecent');
  }
};

export const courseFilters = async ({ apiToken }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.COURSE_FILTERS,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis courseFilters',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch course filters',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response) {
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis courseFilters');
    }
  }
};

export const fetchPreferences = async (apiToken) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: 'wstotara_get_interests',
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) return;

    const responseData = await response.json();
    if (responseData.length > 0) {
      const modifiedListEntries = responseData?.map((entry) => ({
        ...entry,
        isSelected: false,
      }));
      return modifiedListEntries;
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis fetchPreferences',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch preferences',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis fetchPreferences');
  }
};

export const getTrendingCourses = async ({ apiToken, page, pageSize }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.TRENDING_COURSES,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
    page,
    perpage: pageSize,
  };

  let response: Response | undefined = undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = (await response.json()) as TrendingCourseModel;
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getTrendingCourses',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch trending courses',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getTrendingCourses');
  }
};

export const getCoursesYouMightBeInterestedIn = async ({ apiToken }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.COURSES_YOU_MIGHT_BE_INTERESTED_IN,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
    // page,
    // perpage: pageSize,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = (await response.json()) as TrendingCourseModel;
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCoursesYouMightBeInterestedIn',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch courses you might be interested',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis getCoursesYouMightBeInterestedIn',
      );
  }
};

export const getAnnouncements = async ({ apiToken }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.ANNOUNCEMENTS,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = (await response.json()) as AnnouncementModel;
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getAnnouncements',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch Announcements',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getAnnoucements');
    }
  }
};

export const getNewReleases = async ({ apiToken, userId, page = 1, perpage = 5, category }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.NEW_RELEASES,
    moodlewsrestformat: 'json',
    // userid: userId,
    page: page,
    perpage: perpage,
    language: I18nManager.isRTL ? 'ar' : 'en',
    ...(category && { category }),
  };

  let response: Response | undefined = undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return { records: [] };
    }
    const responseData = (await response.json()) as TrendingCourseModel;
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getNewReleases',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch new Releases',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getNewReleases');
    }
  }
};

export const updateTotoraProfile = async (apiToken, userid, data) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: 'wstotara_user_profileupdate',
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  const updatedObj = Object.assign(requestData, data);

  let response: Response | undefined;
  try {
    response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(updatedObj)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (response.status == 200) {
      const responseData = await response.json();
      return responseData;
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis updateTotaraProfile',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to update Totara profile',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis updateTotaraProfile');
  }
};

export const getNotificationList = async (apiToken) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_NOTIFICATIONS_LIST,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (response.status == 200) {
      const responseData = await response.json();
      return responseData;
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getNotificationList',
      url: apiUrl,
      additionalInfo: 'Failed to fetch notification list',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis getNotifictionList');
  }
};

export const markNotificationAsRead = async (apiToken, userid, notificationid) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.READ_NOTIFICATION,
    moodlewsrestformat: 'json',
    notificationid,
  };

  let response: Response | undefined;
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (response.status == 200) {
      const responseData = await response.json();
      return responseData;
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis markNotificationAsRead',
      url: apiUrl,
      additionalInfo: 'Failed to fetch mark notification as read',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis markNootificationAsRead');
    }
  }
};

export const updateLearningGoal = (apiToken, userid, goal, callback) => async (dispatch) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: 'wstotara_manage_user_daily_goals',
    moodlewsrestformat: 'json',
    goal,
  };

  let response: Response | undefined;
  try {
    const response = await axios.get(apiUrl, { params: requestData });
    if (response?.data?.success) {
      new Promise((resolve, reject) => {
        dispatch(getStreaks(userid, apiToken));
        resolve('success');
      })
        .then(() => {
          Toast.show({
            text: i18n.t('Your goal has been updated successfully'),
            textStyle: { textAlign: 'left' },
          });
          return 'success';
        })
        .then(() => {
          callback();
        });
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis updateLearningGoal',
      url: apiUrl,
      additionalInfo: 'Failed to update learning goal',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
    callback();
  } finally {
    if (response)
      trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis updateLearningGoal');
  }
};

export const courseDetails = async ({ apiToken, courseId, userId }) => {
  const apiUrl = TOTARA_BASE_URL;
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.COURSE_DETAILS,
    moodlewsrestformat: 'json',
    courseid: courseId,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }

    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis courseDetails',
      url: apiUrl,
      additionalInfo: 'Failed to fetch course details',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis corseDetails');
  }
};

export const getLatestCourseDetails = async ({ apiToken, courseId, userId }) => {
  const apiUrl = TOTARA_BASE_URL;
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_LATEST_COURSE_DETAILS,
    moodlewsrestformat: 'json',
    courseid: courseId,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }

    const responseData = await response.json();
    return responseData as CourseDetailsTypes;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getLatestCourseDetails',
      url: apiUrl,
      additionalInfo: 'Failed to fetch latest course details',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis getLatestCuurseDetails');
  }
};

export const getMultiSessionCourseDetails = async ({ apiToken, courseId }) => {
  const apiUrl = `${TOTARA_BASE_URL}`;
  const url = `${apiUrl}?wstoken=${apiToken}&wsfunction=wstotara_course_details&moodlewsrestformat=json&courseid=${courseId}`;

  let response: Response | undefined;
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    if (!response.ok) {
      return;
    }

    const responseData = await response.json();
    return responseData as CourseDetailsTypes;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getMultiSessionCourseDetails',
      url: apiUrl,
      additionalInfo: 'Failed to fetch multi session course details',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(url, 'GET', response, 'totaraApis getMultiSessionCourseDetails');
  }
};

export const getLearningUnder30Mints = async ({
  apiToken,
  page,
  perpage,
}: {
  apiToken: string;
  page?: number;
  perpage?: number;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.LEARNING_UNDER_30_MINTS,
    moodlewsrestformat: 'json',
    durationto: 0.5,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  if (page) {
    requestData.page = page;
  }

  if (perpage) {
    requestData.perpage = perpage;
  }

  let response: Response | undefined = undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = await response.json();

    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getLearningUnder30Mins',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch learning',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(
      TOTARA_BASE_URL,
      'POST',
      response,
      'totaraApis getLearniingUnder30Minutes',
    );
  }
};

export const requireCourse = async ({
  apiToken,
  courseId,
  slotId,
}: {
  apiToken: string;
  courseId: number;
  slotId: number;
}) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.BOOKING_SLOT,
    moodlewsrestformat: 'json',
    courseid: courseId,
    slotid: slotId,
    edit: 0,
    previous_slot_id: 0,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = await response.json();

    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'requireCourse',
      additionalInfo: 'Failed to requireCourse',
      serverResponse: response,
    });
  } finally {
    trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis requireCourse');
  }
};

export const checkIfUserHasLineManager = async ({
  apiToken,
}: {
  apiToken: string;
}): Promise<UserHasLineManagerResponse | undefined> => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.CHECK_LINE_MANAGER,
    moodlewsrestformat: 'json',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      Toast.show({
        text: 'Failed to fetch line manager information.',
        textStyle: { textAlign: 'left' },
        duration: 5000,
      });
      return;
    }
    const responseData: Promise<UserHasLineManagerResponse> = await response.json();

    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis checkIfUserHasLineManager',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch data',
      serverResponse: response,
    });
    Toast.show({
      text: 'Failed to fetch line manager information.',
      textStyle: { textAlign: 'left' },
      duration: 5000,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis checkIfUserHasLineManager',
      );
  }
};

export const getCalendarDayWiseEvents = async (
  userid,
  apiToken,
  date,
  month?: number,
  year?: string | number,
  signal?: AbortSignal,
) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: 'wstotara_get_upcoming_events',
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',

    // userid,
  };
  if (date) {
    requestData['date'] = date;
  } else {
    delete requestData?.date;
  }

  if (year) {
    requestData['year'] = year;
  } else {
    delete requestData?.year;
  }

  if (month) {
    requestData['month'] = month;
  } else {
    delete requestData?.month;
  }

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(apiUrl, { 
      params: requestData,
      signal: signal 
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCalendarDayWiseEvents',
      url: apiUrl,
      additionalInfo: 'Failed to fetch calendar data',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(apiUrl, 'GET', response, 'totaraApis getClendarDayWiseEvents');
    }
  }
};

export const getStaticPromoBanner = async ({ apiToken }) => {
  const apiUrl = TOTARA_BASE_URL;
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_STATIC_PROMO_BANNER,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    const responseData = await response.json();

    return responseData as any;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getStaticPromoBanner',
      url: apiUrl,
      additionalInfo: 'Failed to fetch Static promo banner',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis getStaticPromoBanner');
    }
  }
};

export const onBookSlot = async (requestParams) => {
  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });

    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getStaticPromoBanner',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch banner',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onBookSlot');
  }
};

export const onCancelSlot = async ({ apiToken, slotid, courseid }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.CANCEL_BOOKING_SLOT,
    moodlewsrestformat: 'json',
    // userid,
    slotid,
    courseid,
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onCancelSlot',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to cancel slot',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onCancelSlot');
  }
};

type BadgesParams = {
  apiToken: string;
  userid: string;
};

export const getBadges = async ({ apiToken, userid }: BadgesParams) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.BADGES,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',

    // userid,
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getBadges',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch badges',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response) trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getBadges');
  }
};

export const getRealTimeCourses = async (apiToken) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.getRealTimeCourses,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getRealTimeCourses',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch real time courses',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response) {
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getRealTimeCourses');
    }
  }
};

type SubmitRatingType = {
  apiToken: string;
  userid: string;
  courseId: string;
  rating: number;
};

export const onSubmitRating = async ({ apiToken, userid, courseId, rating }: SubmitRatingType) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.RATING,
    moodlewsrestformat: 'json',
    // userid,
    courseid: courseId,
    rating: rating,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onSubmitRating',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to submit rating',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onSubmitRating');
  }
};

export const onSubmitRatingCoach = async ({ apiToken, coachId, rating }: any) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.RATE_COACH,
    moodlewsrestformat: 'json',
    coach_id: coachId,
    rating_value: rating,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    const response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onSubmitRatingCoach',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to submit rating coach',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onSubmitRatingCoach');
  }
};

export const checkCoachRating = async ({ apiToken, coachId }: any) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.RATE_COACH,
    moodlewsrestformat: 'json',
    coach_id: coachId,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis checkCoachRating',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch coach rating',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response) {
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis checkCoachRating');
    }
  }
};

export const fetchLearningPathways = async (
  apiToken,
  userId,
  lang = I18nManager.isRTL ? 'ar' : 'en',
  requiredlearningfilter = 'all',
  recommendedlearningfilter = 'all',
  personallearningfilter = 'all',
  tab,
  page,
) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_LEARNING_PATHWAYS,
    moodlewsrestformat: 'json',
    language: lang,
    requiredlearningfilter: requiredlearningfilter,
    recommendedlearningfilter: recommendedlearningfilter,
    personallearningfilter: personallearningfilter,
    tab,
    page,
    perpage: 10,
  };

  let response: Response | undefined;
  try {
    response = await requestAsync(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response) {
      return;
    }
    return response;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis fetchLearningPathways',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch learning pathways',
      serverResponse: response,
    });
  } finally {
    if (response) {
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis fetchLearningPathways',
      );
    }
  }
};

export const getProgramDetails = async ({ programid, apiToken, userid }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.PROGRAM_DETAILS,
    moodlewsrestformat: 'json',
    // userid: userid,
    programid: programid,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getProgramDetails',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch program details',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getProgramDDetails');
  }
};

export const getSubInterest = async (categories, apiToken) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.GET_SUB_INTEREST,
    moodlewsrestformat: 'json',
    category: categories,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getSubinterest',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch subinterest',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getSubInterest');
  }
};

export const getMentorList = async ({ apiToken, userid, tab }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.GET_MENTORS_LIST,
    moodlewsrestformat: 'json',
    // language: I18nManager.isRTL ? 'ar' : 'en',

    // id: userid,
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getMenthorList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch menthor list',
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getMentorList');
  }
};

export const getCoachList = async ({ apiToken, coachspecialization, name }: any) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.GET_COACHES_LIST,
    moodlewsrestformat: 'json',
    coachspecialization: coachspecialization,
    name,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCoachList',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch coach list',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getCoachList');
  }
};

export const getMentorSlotsDetails = async ({ apiToken, mentorId, tab }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: tab == '1' ? constants.GET_MENTORS_SLOTS_LIST : constants.GET_COACHES_SLOT_LIST,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  if (tab == '1') {
    requestParams['mentor_id'] = mentorId;
  } else {
    requestParams['coach_id'] = mentorId;
  }

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });
    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getMentorlotsDetails',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch mentors details',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getMentorSlotDetails');
  }
};

export const onBookMentorSession = async ({ apiToken, userid, slot_id, tab }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: tab == '1' ? constants.BOOK_MENTORS_SLOT : constants.BOOK_COACHES_SLOT,
    moodlewsrestformat: 'json',
    // userid: userid,
    slot_id: slot_id,
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });

    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis oBookMentorSession',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to book mentor session',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onBookMentorSession');
  }
};

export const getCoachDetails = async ({ apiToken, coachId, tab }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.GET_COACH_DETAILS,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };
  requestParams['coach_id'] = coachId;

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });

    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCoachDetails',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to get coach details',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis getCoachDetails');
  }
};

export const onProgramEnrol = async ({ apiToken, programid }) => {
  const requestParams = {
    wstoken: apiToken,
    wsfunction: constants.ENROLL_PROGRAM,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
    programid: programid,
  };

  let response: AxiosResponse | undefined;
  try {
    response = await axios.get(TOTARA_BASE_URL, {
      params: requestParams,
    });

    return response?.data;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onProgramEnroll',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to enroll program',
      serverResponse: response,
    });
    Toast.show({ text: 'Something went wrong!', textStyle: { textAlign: 'left' } });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'GET', response, 'totaraApis onProgramEnrol');
  }
};

export const fetchSkills = async (apiToken) => {
  const apiUrl = TOTARA_BASE_URL;

  const requestData = {
    wstoken: apiToken,
    wsfunction: 'wstotara_get_skills',
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
  };

  let response: Response | undefined;
  try {
    response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis fetchSkills',
      url: apiUrl,
      additionalInfo: 'Failed to fetch skills',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis fetchSkills');
  }
};

export const getLatestEarnedBadge = async ({ apiToken }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_LATEST_EARNED_BADGE,
    moodlewsrestformat: 'json',
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });

    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getLatestEarnedBadge',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch badge',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getLatestEarnedBadge');
  }
};

export const onSeenEarnedBadge = async ({ apiToken, badgeid }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.SEEN_LATEST_EARNED_BADGE,
    moodlewsrestformat: 'json',
    badgeid: badgeid,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onSeenEarnedBadge',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch badge',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis onSeenEarnedBadge');
  }
};

export const getCurrentLevel_Discipline = async ({ body, successCallback }) => {
  const apiUrl = TOTARA_BASE_URL;
  const requestData = {
    wsfunction: constants.GET_LEVEL_AND_DISCIPLINE,
    moodlewsrestformat: 'json',
    // language: I18nManager.isRTL ? 'ar' : 'en',
    ...body,
  };

  const params = new URLSearchParams(requestData).toString();

  let response: AxiosResponse | undefined;
  try {
    response = await axios.post(`${apiUrl}?${params}`);
    // console.log("response", response);

    if (response?.data) successCallback && successCallback(response.data);
    return response;
  } catch (e) {
    crashReportLogger(e as Error, {
      component: 'totaraApis getCurrentLevel_Discipline',
      url: apiUrl,
      additionalInfo: 'Failed to fetch current discipline',
      serverResponse: response,
    });
    return e;
  } finally {
    if (response)
      trackHttpRequestMetrics(
        TOTARA_BASE_URL,
        'POST',
        response,
        'totaraApis getCurrentLevel_Discipline',
      );
  }
};

export const getTopPicksData = async ({ apiToken, tabindex, page, pageSize }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_TOP_PICK_DATA,
    moodlewsrestformat: 'json',
    language: I18nManager.isRTL ? 'ar' : 'en',
    page,
    perpage: pageSize,
  };

  if (tabindex) {
    requestData['tabindex'] = tabindex;
  } else {
    if (requestData?.tabindex) {
      delete requestData['tabindex'];
    }
  }

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getTopPicksData',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch data',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getTopPicksData');
  }
};

export const getCourseType = async ({ apiToken, id }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.GET_COURSE_TYPE,
    moodlewsrestformat: 'json',
    course_id: id,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis getCourseType',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch course type',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis getCourseType');
  }
};

export const onSaveQRAttendance = async ({ apiToken, data }) => {
  const requestData = {
    wstoken: apiToken,
    wsfunction: constants.SAVE_QR_ATTENDANCE,
    moodlewsrestformat: 'json',
    courseid: data?.courseid,
    sessionid: data?.sessionid,
    seminarid: data?.seminarid,
  };

  let response: Response | undefined;
  try {
    response = await fetch(TOTARA_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: Object.keys(requestData)
        .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
        .join('&'),
    });
    if (!response.ok) {
      return;
    }
    const responseData = await response.json();
    return responseData;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'totaraApis onSaveQRAttendance',
      url: TOTARA_BASE_URL,
      additionalInfo: 'Failed to fetch data',
      serverResponse: response,
    });
  } finally {
    if (response)
      trackHttpRequestMetrics(TOTARA_BASE_URL, 'POST', response, 'totaraApis onSaveQRAttenance');
  }
};
