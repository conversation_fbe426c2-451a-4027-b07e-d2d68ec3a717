import { store } from '../totara/store';

const version = 'v1.0';

export const API_URL = {
  authenticateUser: {
    url: 'o/adsg-authentication/v1.0/authenticate',
  },
  generateCode: {
    url: 'o/adsg-smpp-verification/v1.0/generate-code',
  },
  verifyCode: {
    url: 'o/adsg-smpp-verification/v1.0/verify-code',
  },
  loginOtpCode: {
    url: 'o/adsg-authentication/v1.0/authenticate-by-email'
  },
  submitOnboarding: {
    url: 'o/adsg-talent-profile/v1.0/talent',
  },
  getUserDetails: {
    url: 'o/c/userpersonaldetailses',
  },
  getUserDetailsUpdated: {
    url: 'o/adsg-profiles-search-results/v1.0/search',
  },
  updateUserDetailsUpdated: {
    url: 'o/adsg-profiles-search-results/v1.0/updateUser',
  },
  documentUpload: {
    url: 'o/adsg-document-upload/v1.0/obs-document/',
  },
  getHobbies: {
    url: "o/headless-admin-list-type/v1.0/list-type-definitions?filter=name eq 'hobbies'",
  },
  getCountiesList: {
    url: 'o/c/countries/',
  },
  submitWorkExperience: {
    url: 'o/c/userpersonaldetailses', // seems to not be in use
  },
  getForApproval: {
    url: 'o/c/talentprofileapprovaldetails',
  },
  getBase64Url: {
    url: 'o/adsg-document-upload/v1.0/obs-document',
  },
  addLanguage: {
    url: 'o/c/languageses/',
  },
  getObsDoc: {
    url: 'o/adsg-document-upload/v1.0/obs-document-url',
  },
  getProfilePercentage: {
    url: 'o/adsg-talent-team/v1.0/profile-progress',
  },
  getFaq: {
    url: 'o/adsg-faq-headless/v1.0/faq?',
  },
  getSearchFaq: {
    url: 'o/adsg-faq-headless/v1.0/faq',
  },
  contactUs: {
    url: 'o/adsg-contact-support-rest/v1.0/contact-support',
  },
  getEndorsement: {
    url: 'o/c/skillsendorsementses?',
  },
  referACourse: {
    url: 'o/adsg-courses/v1.0/courses/referred/course',
  },
  receivedReferrals: {
    url: 'o/adsg-courses/v1.0/courses/referred/received',
  },
  sentReferrals: {
    url: 'o/adsg-courses/v1.0/courses/referred/sent',
  },
  referralCount: {
    url: 'o/adsg-courses/v1.0/courses/referred/unread-count',
  },
  readReferralStatus: {
    url: 'o/adsg-courses/v1.0/courses/referred/update-read-status',
  },
  username: {
    url: 'o/c/userpersonaldetailses/?filter=startswith', // seems to not be in use
  },
  toggleEndorsement: {
    url: 'o/c/skillsendorsementses',
  },
  addRecommendation: {
    url: 'o/adsg-talent-recommendations/v1.0/add/recommendation',
  },
  getRecommendations: {
    url: 'o/c/recommendationses',
  },
  recommendatalents: {
    url: 'o/c/recommendatalents',
  },
  getRolesPermission: {
    url: 'o/adsg-talent-team/v1.0/roles-permissions/',
  },
  updateToken: {
    url: `o/adsg-mobile/${version}/mobile/register-device`,
  },
  searchUserName: {
    url: 'o/adsg-profiles-search-results/v1.0/profile-search?keyword=',
  },
  addRecommendationNotify: {
    url: `o/adsg-talent-profile/${version}/talent/recommendation/`,
  },
  recommendationRequestNotify: {
    url: `o/adsg-talent-profile/${version}/talent/recommendation-request/`,
  },
  principlesYou: {
    results: `o/adsg-principle-rest/${version}/assessment/results/`,
    addUser: `o/adsg-principle-rest/${version}/assessment/addUser/`,
    questions: `o/adsg-principle-rest/${version}/assessment/questions/`,
    answers: `o/adsg-principle-rest/${version}/assessment/answers/`,
    generatePdf: `o/adsg-principle-rest/${version}/assessment/pdf`,
    status: `o/adsg-principle-rest/${version}/assessment/status`
  },
  deleteWorkExperience: {
    url: `o/c/workexperiences`
  },
  AddAndEditWorkExperience: {
    url: `o/c/workexperiences`
  },
  recommendationListingUrl: {
    url: '/o/adsg-talent-recommendations/v1.0/recommendations/list'
  },
  updatePendingRecommendation: {
    url: '/o/adsg-talent-recommendations/v1.0/update/recommendation'
  },
  mobileBiometricCreate: {
    url: 'o/adsg-mobile-biometric/v1.0/create',
  },
  mobileBiometricVerify: {
    url: 'o/adsg-authentication/v1.0/biometric',
  },
};

export const BEARER_TOKEN = store.getState().getWFMProfile.response?.liferayaccesstoken;
