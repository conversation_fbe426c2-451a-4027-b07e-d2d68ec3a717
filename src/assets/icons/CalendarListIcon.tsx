import React from 'react';
import Svg, { Circle, Line, Rect } from 'react-native-svg';

interface CalendarListIconProps {
  width?: number;
  height?: number;
  isDarkMode?: boolean;
}

const CalendarListIcon: React.FC<CalendarListIconProps> = ({ 
  width = 48, 
  height = 48, 
  isDarkMode = false 
}) => {
  const circleColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const lineColor = isDarkMode ?  '#1E1E1E':'#FFFFFF';
  const backgroundColor = isDarkMode ? '#FFFFFF' : '#1E1E1E';

  return (
    <Svg width={width} height={height} viewBox="0 0 48 48" fill="none">
      <Rect width="48" height="48" rx="24" fill={backgroundColor} />
      <Circle cx="14" cy="17" r="1.33333" fill={circleColor} stroke={circleColor} strokeWidth="1.33333" />
      <Circle cx="14" cy="24" r="1.33333" fill={circleColor} stroke={circleColor} strokeWidth="1.33333" />
      <Circle cx="14" cy="31" r="1.33333" fill={circleColor} stroke={circleColor} strokeWidth="1.33333" />
      <Line x1="20.1809" y1="17.3984" x2="31.5099" y2="17.3984" stroke={lineColor} strokeWidth="1.5" />
      <Line x1="20.1809" y1="23.9414" x2="31.5099" y2="23.9414" stroke={lineColor} strokeWidth="1.5" />
      <Line x1="20.1809" y1="30.8652" x2="31.5099" y2="30.8652" stroke={lineColor} strokeWidth="1.5" />
    </Svg>
  );
};

export default CalendarListIcon; 