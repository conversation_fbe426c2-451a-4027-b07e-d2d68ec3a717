import React from 'react';
import { Svg, Path } from 'react-native-svg';

interface ResetFilterIconProps {
  width?: number;
  height?: number;
  color?: string;
  stroke?: string;
  style?: any;
}

const ResetFilterIcon: React.FC<ResetFilterIconProps> = ({ 
  width = 20, 
  height = 20, 
  color = 'black', 
  stroke,
  style 
}) => {
  const strokeColor = stroke || color;
  
  return (
    <Svg 
      width={width} 
      height={height} 
      viewBox="0 0 20 20" 
      fill="none" 
      style={style}
    >
      <Path 
        d="M4.6562 6.04167C5.81449 3.92445 7.97941 2.5 10.459 2.5C13.2704 2.5 15.6773 4.33119 16.671 6.92708
M6.6908 6.92708H3.34131V3.38542M16.1931 13.125C15.0348 15.2422 12.8699 16.6667 10.3903 16.6667C7.57892 16.6667 5.17203 14.8355 4.17825 12.2396M14.1585 12.2396H17.508V15.7813"
        stroke={strokeColor} 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ResetFilterIcon; 