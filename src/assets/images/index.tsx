import { ImageURISource } from 'react-native';

const Icons: Record<string, ImageURISource> = {
  hanshake: require('./icons/handshake.png'),
  Isolation_Mode: require('./icons/Isolation_Mode.png'),
  view_in_ar: require('./icons/view_in_ar.png'),
  chevronRight: require('./icons/chevron-right.png'),
  twoStars: require('./icons/twoStars.png'),
  degree: require('./icons/degree.png'),
  location: require('./icons/location.png'),
  edit: require('./icons/edit.png'),
  calender: require('./icons/calender.png'),
  ratingStar: require('./icons/ratingStar.png'),
  ratingStartEmpty: require('./icons/ratingStartEmpty.png'),
  threeDots: require('./icons/3dots.png'),
  watch: require('./icons/watch.png'),
  whiteHeart: require('./icons/whiteHeart.png'),
  Rectangle1: require('./Rectangle1.png'),
  Rectangle2: require('./Rectangle2.png'),
  Rectangle3: require('./Rectangle3.png'),
  officeimage: require('./office.jpeg'),
};

export default Icons;
