import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Platform,
  ScrollView,
  Image, KeyboardAvoidingView, I18nManager, SafeAreaView
} from 'react-native';
import {chatCompletionWithFunctionCalling,  getAIUserProfile, getChatTitle, getSuggestions} from "@api/AI";
import Markdown from 'react-native-markdown-display';
import {useSelector} from "react-redux";
import {useSession} from "@totara/core";
import {getLatestCourseDetails} from "@api/totaraApis.tsx";
import {navigateToCourseDetail} from "@utils/constants.tsx";
import LottieView from "lottie-react-native";
import GradientText from "@components/GradientText.tsx";
import RadialGradientSVG from "@components/RadialGradientSVG.tsx";
import {<PERSON><PERSON>, Header, TextView} from "@components";
import {v4 as uuidv4} from 'uuid';
import {
  addChatSession,
  loadAIChatMessage,
  loadChatMessage,
  saveAIChatMessage,
  saveChatMessage
} from "@utils/chatSessionsHelper.tsx";
import {goBack} from "@navigation/navigationService.tsx";
import {useTranslation} from "react-i18next";
import fonts from "@utils/fonts.tsx";
import {useTheme} from "@theme/ThemeProvider.tsx";
import {RootState} from "@totara/reducers";

const AIChat = ({navigation, route}) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [typingText, setTypingText] = useState('');
  const [fullResponse, setFullResponse] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [pendingCourses, setPendingCourses] = useState(null);
  const [aiChatMsgs, setAiChatMsgs] = useState<{ role: string; content: string; }[]>([]);
  const [suggestions, setSuggestions] = useState([]);
  const flatListRef = useRef(null);
  const [sessionId, setSessionId] = useState(route?.params?.sessionId || null);
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const user = useSelector((state: RootState) => state?.getWFMProfile?.response);
  const [activeCoursePages, setActiveCoursePages] = useState({});
  const typingSpeed = 0.2;
  const chunkSize = 10;
  useEffect(() => {
    navigation.setOptions({tabBarStyle: {display: 'none'}});

    return () => {
      navigation.setOptions({tabBarStyle: undefined});
    };

  }, [navigation]);
  useEffect(() => {
    if (route?.params?.sessionId) {
      setSessionId(route.params.sessionId);
    }else{
      getSuggestions(talentProfile).then((res) => {
        setSuggestions(JSON.parse(res.choices[0].message.content)?.prompts || [])
      })
    }
  }, [route?.params?.sessionId]);
  useEffect(() => {
    const loadExistingChat = async () => {
      if (sessionId) {
        const storedMessages = await loadChatMessage(sessionId);
        if (storedMessages) {
          setMessages(storedMessages);
        }
        const storedAIMessages = await loadAIChatMessage(sessionId);
        if (storedAIMessages) {
          setAiChatMsgs(storedAIMessages);
        }
      }
    };
    loadExistingChat();
  }, [sessionId]);

  const {apiToken} = useSession();
  useEffect(() => {
    resetAIChat()
  }, [talentProfile]);
  const resetAIChat = () => {
    setAiChatMsgs([{
      role: "system",
      content: "You're AI Assistant for DGE, you should help the talents by answering there questions professionally, " +
        " You should personalize your messages based on the current user and make sure to direct the responses to " +
        "the user like try to say his first name while you answering the prompts and be always formal realistic " +
        "and give your best response ever.\n" +
        "the current user is: " + getAIUserProfile(talentProfile)
    }]);
  }
  // Fast typing effect for bot messages
  useEffect(() => {
    if (isTyping && fullResponse) {
      if (typingText.length < fullResponse.length) {
        const timeout = setTimeout(() => {
          // Type multiple characters at once to speed up the effect
          const nextIndex = Math.min(typingText.length + chunkSize, fullResponse.length);
          setTypingText(fullResponse.substring(0, nextIndex));
        }, typingSpeed);
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({animated: true});
        }, 100);
        return () => clearTimeout(timeout);
      } else {
        setIsTyping(false);

        // After typing is complete, update the real message
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === prevMessages.length ? {...msg, text: fullResponse} : msg
          )
        );
        // Add courses message if it exists after the typing is complete
        if (pendingCourses) {
          setTimeout(() => {
            const botCoursesReply = {
              id: messages.length + 1,
              data: pendingCourses,
              sender: 'assistant',
              type: "courses"
            };
            const updatedMessagesWithBot = [...messages, botCoursesReply];
            setMessages(updatedMessagesWithBot);
            saveChatMessage(sessionId, updatedMessagesWithBot);
            setPendingCourses(null);
          }, 200);
        }
      }
    }
  }, [typingText, fullResponse, isTyping, pendingCourses, messages.length]);
  const sendMessage = async (text=null) => {
    const userPrompt = (text?.length)? text : inputText.trim();

    if (userPrompt.trim().length === 0) return;
    let currentSessionId = sessionId;

    if (!sessionId) {
      // Generate new sessionId & add session entry
      currentSessionId = uuidv4();
      setSessionId(currentSessionId);
      getChatTitle(userPrompt.trim()).then(async (res)=>{
        const newSession = {
          id: currentSessionId,
          title: res.choices[0].message.content || userPrompt.trim(),
          createdAt: Date.now(),
        };
        await addChatSession(newSession);
      })

    }
    const newMessage = {
      id: messages.length + 1,
      text: userPrompt,
      sender: 'user',
      type: "text"
    };
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);

    setAiChatMsgs((prevMessages) => [...prevMessages, {
      "role": "user",
      "content": userPrompt
    }]);
    setInputText('');
    setIsLoading(true);
    await saveChatMessage(currentSessionId, updatedMessages);

    chatCompletionWithFunctionCalling([...aiChatMsgs, {
      "role": "user",
      "content": userPrompt
    }])
      .then((response) => {
        // Add empty message first that will be filled by typing effect1213
        const assistantTextMsg = (response?.choices?.[0]?.message?.content || response?.data?.agent_response?.text)
          ?.replace(/https:\/\/learn\.adsg\.gov\.ae\/course\/view\.php\?id=(\d+)/g, 'https://learn.adsg.gov.ae/local/wstotara/redirect.php?courseid/$1');

        const botReply = {
          id: messages.length + 2,
          text: assistantTextMsg,  // Initially empty, will be filled by typing effect
          sender: 'assistant',
          type: "text"
        };


        // Start typing effect
        setFullResponse(assistantTextMsg);
        setTypingText('');
        setIsTyping(true);

        // Store courses for later display after typing is complete
        let assistantMsg = {
          content: assistantTextMsg,
          role: 'assistant'
        }
        if (response.data?.search_results['gov-academy-courses']?.length > 0) {
          setPendingCourses(response.data.search_results['gov-academy-courses']);
        }
        const updatedAiMsgs = [...aiChatMsgs, assistantMsg];
        const updatedMessagesWithBot = [...updatedMessages, botReply];
        setAiChatMsgs(updatedAiMsgs);
        setMessages(updatedMessagesWithBot);
        saveChatMessage(currentSessionId, updatedMessagesWithBot);
        saveAIChatMessage(currentSessionId, updatedAiMsgs);
      })
      .catch(error => console.error("Error fetching AI response:", error))
      .finally(() => setIsLoading(false));

    // Scroll to bottom when sending message

  };

  const navigateToCourseDetails = async (courseId, aiCourse) => {
    const course = await getLatestCourseDetails({
      courseId: courseId,
      apiToken,
      userId: talentProfile?.id,
    });
    if (course) {
      navigateToCourseDetail({...course, courseid: course?.courseId, type: "course"}, navigation, false);
    }
  };


  // Auto-scroll when messages update
  useEffect(() => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({animated: true});
    }, 100);
  }, [messages]);

  // Custom styles for Markdown component
  const getMarkdownStyles = (sender) => {
    const baseColor = theme.textColor;
    return {
      body: {
        color: baseColor,
      },
      paragraph: {
        color: baseColor,
      },
      text: {
        color: baseColor,
      },
      strong: {
        color: baseColor,
        fontWeight: 'bold',
      },
      link: {
        color: sender === 'user' ? '#8cb4ff' : '#4EA1FF',
      },
      list_item: {
        color: baseColor,
      },
      code_block: {
        backgroundColor: isDarkMode ? '#333' : '#f0f0f0',
        color: baseColor,
        padding: 8,
        borderRadius: 4,
        fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
      },
      code_inline: {
        backgroundColor: isDarkMode ? '#333' : '#f0f0f0',
        color: baseColor,
        fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
      },
    };
  };
  const handleSuggestionClick = (text) => {
    sendMessage(text);
  };
  const openHistory = () => {
    navigation.navigate("AIChatHistory")
  }
  const newChat = () => {
    resetAIChat()
    setPendingCourses(null)
    setMessages([])
    setSessionId(null)
  }
  const renderMessageItem = ({item, index}) => {
    const isShortMessage = item.text && item.text.length < 30;
    const displayText = (isTyping && item.id === messages.length && item.sender === 'assistant')
      ? typingText
      : item.text;

    const sender = item.sender;
    const previousSender = index > 0 ? messages[index - 1]?.sender : null;
    const shouldShowAvatar = previousSender !== sender;

    return (
      <>
        {shouldShowAvatar && (
          sender === 'user' ? (
            <Avatar
              editable={false}
              containerStyles={{
                width: 30,
                height: 30,
                borderWidth:0,
                marginEnd: 2,
                alignSelf: 'flex-end',

              }}
            />
          ) : (
            <LottieView
              source={require('assets/lottie/transparent.json')}
              autoPlay
              loop
              style={{
                width: 30,
                height: 30,
                marginStart: 10,
                marginBottom: 10
              }}
            />
          )
        )}


        <View
          style={[
            styles.messageContainer,
            item.sender === 'user' ? styles.userMessage : styles.botMessage,
            // Ensure user messages are always aligned to the right
            item.sender === 'user' && {alignSelf: 'flex-end'},
            // Adjust width for short messages
            isShortMessage && {maxWidth: 'auto'},
            isDarkMode && {backgroundColor: theme.bgShade5},
          ]}
        >
          <View style={styles.textWrapper}>
            {item.type === "text" && (
              <Markdown
                style={getMarkdownStyles(item.sender)}
              >
                {displayText}
              </Markdown>
            )}
            {item.type === "courses" && (
              <View style={styles.courseContainer}>
                <Text style={[styles.courseTitle,{color: theme.textColor}]}>{t('Recommended courses')}</Text>

                {/* Horizontal ScrollView for courses with pagination dots */}
                <View>
                  <ScrollView
                    horizontal
                    pagingEnabled
                    snapToInterval={300} // assumes card width + margin
                    decelerationRate="fast"
                    showsHorizontalScrollIndicator={false}
                    onScroll={(e) => {
                      const offsetX = e.nativeEvent.contentOffset.x;
                      const newIndex = Math.round(offsetX / 300); // Adjust if card width/margin changes

                      setActiveCoursePages((prev) => ({
                        ...prev,
                        [item.id]: newIndex
                      }));
                    }}
                    scrollEventThrottle={16}
                    contentContainerStyle={styles.courseScrollContainer}
                  >
                    {item.data.map((course, index) => (
                      <TouchableOpacity
                        key={index}
                        style={[styles.recommendationCard,{backgroundColor: theme.white}]}
                        onPress={() => navigateToCourseDetails(course.CourseURL.split('id=')[1], course)}
                      >
                        {course.ImageURL && (
                          <Image
                            source={{ uri: course.ImageURL }}
                            style={styles.courseImageRounded}
                            resizeMode="cover"
                          />
                        )}

                        <View style={styles.courseContent}>
                          {course.Type && (
                            <View style={[styles.courseBadge,{backgroundColor: theme.white},isDarkMode && {borderWidth:1,borderColor: theme.bgLightDark}]}>
                              <Text style={[styles.courseBadgeText,{color: theme.textColor}]}>{course.Type}</Text>
                            </View>
                          )}

                          {course.Name && (
                            <Text style={[styles.recommendationTitle,{color: theme.textColor}]}>{course.Name}</Text>
                          )}

                          <View style={styles.courseMeta}>
                            {course.Duration && (
                              <Text style={[styles.courseMetaText,{color: theme.textColor}]}>⏱ {course.Duration}</Text>
                            )}
                            {course.Proficiency && (
                              <Text style={[styles.courseMetaText,{color: theme.textColor}]}>• {course.Proficiency}</Text>
                            )}
                          </View>

                          {course.Summary && (
                            <Text style={[styles.courseSummary,{color: theme.textColor}]} numberOfLines={2}>
                              {course.Summary.replace(/\[.*?\]/g, '')}
                            </Text>
                          )}

                          {course.Skills && (
                            <View style={[styles.courseSkillTag,{backgroundColor: theme.bgShade5}]}>
                              <Text style={[styles.courseSkillText,{color: theme.textColor}]}>
                                {course.Skills.split(',')[0]}
                              </Text>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>

                  {/* Pagination dots */}
                  <View style={styles.paginationContainer}>
                    {item.data.map((_, index) => (
                      <View
                        key={index}
                        style={[
                          styles.paginationDot,
                          index === (activeCoursePages[item.id] || 0) && styles.activePaginationDot,
                        ]}
                      />
                    ))}
                  </View>
                </View>
              </View>
            )}
          </View>

        </View>
      </>
    );
  };

  return (
    <SafeAreaView  style={{flex: 1}}>


      <View style={[styles.container, {backgroundColor: theme.backgroundColor}]}>
        <RadialGradientSVG/>
        <Header text={t("AI Assistant")} onBackPress={goBack} rightComponent={
          <View style={{flexDirection: 'row', gap: 5, alignItems: 'center'}}>
            <TouchableOpacity onPress={newChat} style={{padding: 10}}>
            <Image source={require('../../assets/icons/new_chat.png')} style={[{width: 19, height: 19}, {tintColor: theme.textColor}]}/>
          </TouchableOpacity>
            <TouchableOpacity onPress={openHistory} style={{padding: 10}}>
              <Image source={require('../../assets/images/list.png')} style={[{width: 15, height: 15}, {tintColor: theme.textColor}]}/>
            </TouchableOpacity>

          </View>

        } transparent={true}/>
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderMessageItem}
          keyboardShouldPersistTaps="always"
          contentInsetAdjustmentBehavior="automatic"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <LottieView
                source={require('assets/lottie/transparent.json')}
                autoPlay
                loop
                style={{width: 150, height: 150,marginBottom:50}}
              />
              <TextView style={{
                fontWeight: "bold",
                fontSize: 20,
                color: theme.textColor,
                textAlign: "center",
                fontFamily: fonts.semiBold,
              }}>{t("Hi")} {I18nManager.isRTL ? (user?.firstnameAR + "،" || user?.firstnameEN) : (user?.firstnameEN || '') + ","}</TextView>
              {//
                (I18nManager.isRTL)?
                  <TextView style={{
                    fontSize: 24,
                    fontWeight: 'bold',
                    textAlign: 'center',
                    color: (isDarkMode)?'#48a5ff':'#0F7ee9'
                  }}>
                    {t("How can I help you today?")}
                  </TextView>
                  :
                  <GradientText
                    text={t("How can I help you today?")}
                    style={{
                      fontSize: 24,
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  />
              }

            </View>
          }
        />
        {/* Loading indicator */}
        {isLoading && !isTyping && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>{t("AIProcessing")}</Text>
          </View>
        )}

        {/* Input bar */}
        {messages.length === 0 && suggestions.length > 0 && (
          <View style={styles.suggestionsContainer}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{ paddingHorizontal: 10 }}
            >
              {suggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.suggestionCard,{backgroundColor: theme.backgroundColor},(isDarkMode)?{borderColor: theme.borderBackground}:{borderColor: theme.coolGrey100}]}
                  onPress={() => handleSuggestionClick(`${suggestion.label} ${suggestion.title}`)}
                >
                  <Text style={[styles.suggestionTitle, { color: theme.textColor,textAlign:"auto" }]}>{suggestion.label}</Text>
                  <Text style={[styles.suggestionSubtitle, { color: theme.textColor,textAlign:"auto" }]}>{suggestion.title}</Text>

                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
        <KeyboardAvoidingView

          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
        <View style={[styles.inputContainer, {backgroundColor: theme.backgroundColor,borderWidth:1},(isDarkMode)?{borderColor: theme.borderBackground}:{borderColor: theme.coolGrey100}]}>
          <TextInput
            style={[styles.textInput,{ textAlign: I18nManager.isRTL ? 'right' : 'left',
              writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',color: theme.textColor}]}
            value={inputText}
            onChangeText={setInputText}
            placeholder={t("Type a message...")}
            placeholderTextColor="#aaa"
            multiline={true}
            maxHeight={100}
          />
          <TouchableOpacity
            style={[]}
            onPress={sendMessage}
            disabled={inputText.trim().length === 0 || isLoading || isTyping}
          >
            <Image source={(isDarkMode)?require("assets/images/send_dark.png"):require("assets/images/send.png")} style={[{width:30, height:30},{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }]} resizeMode="contain" />
          </TouchableOpacity>
        </View>
        </KeyboardAvoidingView>
      </View>

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF'
  },
  chatContainer: {
    flexGrow: 1,
    padding: 15,
    paddingBottom: 10,
  },
  emptyContainer: {
    flex: 1,
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  messageContainer: {
    maxWidth: '75%',
    padding: 12,
    paddingVertical: 5,
    borderRadius: 18,
    marginBottom: 10,

    overflow: 'hidden',
    // Add shadow for iOS
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // Add elevation for Android
    elevation: 1,

  },
  textWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',

  },
  userMessage: {
    backgroundColor: '#CBD9E7',
    borderTopEndRadius: 4,
    marginTop: 5,
    marginHorizontal:10

  },
  botMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#EFF5FB',
    borderTopStartRadius: 4,
    marginHorizontal:10
  },
  courseContainer: {
    width: '100%',
  },
  courseTitle: {
    fontWeight: 'bold',
    marginBottom: 12,
    fontSize: 16,
    color: '#333',
  },
  courseScrollContainer: {
    paddingBottom: 5,
  },
  courseItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    width: 280,
  },
  courseBody: {
    padding: 10,
  },
  courseHeader: {

    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  courseName: {
    fontWeight: 'bold',
    fontSize: 15,
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  courseDuration: {
    fontSize: 13,
    color: '#666',
    fontWeight: '500',
  },
  courseDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseType: {
    fontSize: 13,
    color: '#666',
    marginRight: 10,
  },
  proficiencyTag: {
    backgroundColor: '#e8f4fc',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  proficiencyText: {
    color: '#0366d6',
    fontSize: 12,
    fontWeight: '500',
  },
  courseSummary: {
    fontSize: 13,
    color: '#555',
    lineHeight: 18,
    marginBottom: 10,
  },
  courseFooter: {
    marginTop: 5,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 6,
  },
  skillText: {
    color: '#666',
    fontSize: 12,
  },
  moreSkillsTag: {
    backgroundColor: '#e0e0e0',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginBottom: 6,
  },
  moreSkillsText: {
    color: '#555',
    fontSize: 12,
  },
  loadingContainer: {
    padding: 10,
    marginLeft: 15,
    marginRight: 15,
    marginBottom: 10,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomLeftRadius: 4,
    // Add shadow for iOS
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // Add elevation for Android
    elevation: 1,
  },
  loadingText: {
    color: '#000hi',
    fontSize: 18,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:"center",
    padding: 5,
    backgroundColor: '#fff',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25
  },
  textInput: {
    flex: 1,
    padding: 20,
  },
  sendButton: {
    marginLeft: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#221F1F',
    borderRadius: 25,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: 'bold'
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  activePaginationDot: {
    backgroundColor: '#666',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  courseImage: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    marginBottom: 10,
  },

  topRight: {
    top: 0,
    right: 0,
  },
  bottomLeft: {
    position: 'absolute',
    bottom: -100,
    left: -100,
  },
  suggestionsContainer: {
    marginBottom: 10,
    paddingTop: 5,
  },

  suggestionCard: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#f1f1f1',
    elevation: 1,

    minWidth: 160,
  },

  suggestionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },

  suggestionSubtitle: {
    fontSize: 13,
    color: '#888',
    marginTop: 4,
  },
  recommendationCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    marginRight: 16,
    overflow: 'hidden',
    width: 290,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },

  courseImageRounded: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },

  courseContent: {
    padding: 14,
  },

  courseBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 8,
  },

  courseBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#111',
    letterSpacing: 0.5,
  },

  recommendationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111',
    marginBottom: 6,
  },

  courseMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  courseMetaText: {
    fontSize: 13,
    color: '#555',
    marginRight: 10,
  },

  courseSummary: {
    fontSize: 13,
    color: '#666',
    marginBottom: 12,
    lineHeight: 18,
  },

  courseSkillTag: {
    alignSelf: 'flex-start',
    backgroundColor: '#E6EFFB',
    paddingHorizontal: 12,
    paddingVertical: 5,
    borderRadius: 20,
  },

  courseSkillText: {
    fontSize: 13,
    color: '#3C74BA',
    fontWeight: '500',
  },

});

export default AIChat;
