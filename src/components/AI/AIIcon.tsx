import {navigate} from "@navigation/navigationService.tsx";
import Lottie<PERSON>ie<PERSON> from "lottie-react-native";
import {Pressable, ViewStyle} from "react-native";
import React from "react";

interface Props {
  style?: ViewStyle;
}


const AIIcon = ({ style = { marginStart: 16 } }: Props) => {
  return (
    <Pressable style={style} onPress={()=>navigate('AIAssistant')}>
      <LottieView
        source={require('assets/lottie/transparent.json')}
        autoPlay
        loop
        style={{ width: 26, height: 26}}

      />
    </Pressable>
  );
}
export default AIIcon
