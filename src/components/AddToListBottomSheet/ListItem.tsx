import FastImage from 'react-native-fast-image';
import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text } from 'react-native';
import { TextView } from '@components';
import { I18nManager } from 'react-native';
import { lineHeight } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

const ListItem = ({ onPress, image, text, textStyle, iconStyle, style, key }) => {
  const { isDarkMode } = useTheme();
  return (
    <Pressable key={key} style={[styles.container, style]} onPress={onPress}>
      <Image
        source={image}
        style={[styles.icon, iconStyle, isDarkMode && { tintColor: Colors.activeStateDark }]}
      />
      <TextView style={[styles.text, textStyle, isDarkMode && { color: Colors.activeStateDark }]} text={text} />
    </Pressable>
  );
};

export default ListItem;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: isPad ? 80 : 60,
    paddingHorizontal: 20,
    marginBottom: Platform.OS == 'android' ? 10 : 0,
    marginTop: Platform.OS == 'android' ? 5 : 0,
  },
  text: {
    fontFamily: fonts.regular,
    fontSize: fontSize.semiMedium1,
    color: Colors.black,
    marginStart: isPad ? 18 : 12,
  },

  icon: { width: 24, height: 24, resizeMode: 'contain' },
});
