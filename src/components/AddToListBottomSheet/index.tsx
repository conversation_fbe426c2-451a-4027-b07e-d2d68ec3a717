import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import ListItem from './ListItem';
import Colors from '../../theme/Colors';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../totara/reducers';
import { setBottomSheetVisible } from '../../totara/reducers/addToListBottomSheetReducer';
import { navigate } from '../../navigation/navigationService';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import BottomSheet from '../bottomSheet';
import Share from 'react-native-share';
import { TOTARA_AUTH_BASE_URL } from '@api/api_client';
import { AssetsImages, LabelConfig } from '@theme';
import { useTranslation } from 'react-i18next';

import RNFetchBlob from 'react-native-blob-util';

const AddToListBottomSheet = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { visible, content } = useSelector((state: RootState) => state.addToListBottomSheetReducer);
  const { referCourse } = useSelector((state: RootState) => state.referCourseReducer);

  const _handleOnPress = (item) => {
    item.onPress && item.onPress();
    setBottomSheetVisible(dispatch, false);
    item.navigationScreenName && setTimeout(() => navigate(item.navigationScreenName), 50);
  };

  const onSharePress = () => {
    setBottomSheetVisible(dispatch, false);

    setTimeout(() => {
      let url = TOTARA_AUTH_BASE_URL + '/local/wstotara/redirect.php?';
      if (referCourse?.isProgram) {
        url += `networkid/${referCourse.courseid}`;
      } else {
        url += `courseid/${referCourse.courseid}`;
      }

      RNFetchBlob.fetch('GET', `${referCourse.image}`)
        .then((resp) => {
          let base64image = resp.data;
          Share.open({
            title: 'Share',
            subject: '',
            message:
              'Check out this course at My Learning app.\n' +
              referCourse.name +
              '\n\nLink:\n' +
              url,
            url: 'data:image/png;base64,' + base64image,
          });
        })
        .catch((err) => { });
    }, 2000);
  };

  const ProgramList = [
    {
      image: AssetsImages.share,
      text: t(LabelConfig.global.ShareACourse),
      navigationScreenName: 'ShareCourse',
    },
  ];

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={{ justifyContent: 'flex-start' }}
      visiblity={visible}
      setVisibility={() => setBottomSheetVisible(dispatch, false)}
    >
      <View style={styles.body}>
        {referCourse?.isProgram
          ? ProgramList?.map((item, index) => {
            return (
              <ListItem
                key={`${item.image}-${item.text}-${index}`}
                style={styles.dropDownContainer}
                textStyle={styles.textStyle}
                iconStyle={styles.iconStyle}
                image={item.image}
                text={item.text}
                onPress={() => {
                  if (item.navigationScreenName == 'ShareCourse') {
                    onSharePress();
                    return;
                  }

                  _handleOnPress(item);
                }}
              />
            );
          })
          : content?.map((item, index) => {
            return (
              <>
                <ListItem
                  key={`${item.image}-${item.text}-${index}`}
                  style={[
                    styles.dropDownContainer,
                    item?.navigationScreenName == 'ShareCourse' &&
                    Platform.OS == 'android' && { marginTop: 10 },
                  ]}
                  textStyle={styles.textStyle}
                  iconStyle={styles.iconStyle}
                  image={item.image}
                  text={item.text}
                  onPress={() => {
                    if (item.navigationScreenName == 'ShareCourse') {
                      onSharePress();
                      return;
                    }
                    _handleOnPress(item);
                  }}
                />
                {index !== content.length - 1 && <View style={styles.line} />}
              </>
            );
          })}
      </View>
    </BottomSheet>
  );
};

export default AddToListBottomSheet;

const styles = StyleSheet.create({
  line: { borderBottomWidth: 0.5, borderBottomColor: Colors.grayline },
  bottomSheet: { paddingHorizontal: 0 },
  body: { paddingTop: 0, paddingBottom: 20 },
  dropDownContainer: {
    height: isPad ? 90 : Platform.OS === 'ios' ? 60 : 35,
  },
  textStyle: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xxlarge : fontSize.semiMedium1,
    color: Colors.black,
  },
  iconStyle: {
    width: isPad ? 35 : 22,
    height: isPad ? 35 : 22,
    resizeMode: 'contain',
  },
});
