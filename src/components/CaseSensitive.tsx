import React from 'react';
import { Image, StyleSheet, View, ViewStyle } from 'react-native';
import { TextView } from './index';
import AssetsImages from '../theme/AssetsImages';
import fonts from '../utils/fonts';
import fontSize, { isPad } from '../utils/fontSize';

type CaseSensitiveProps = {
  style?: ViewStyle;
};

const CaseSensitive = ({ style }: CaseSensitiveProps) => {
  return (
    <View style={[styles.infoContainer, style]}>
      <Image source={AssetsImages.info} style={styles.info} />
      <TextView style={styles.completeYourProfile} text="The search field is case sensitive" />
    </View>
  );
};

export default CaseSensitive;

const styles = StyleSheet.create({
  info: {
    width: isPad ? 20 : 15,
    height: isPad ? 20 : 15,
    resizeMode: 'contain',
    marginRight: 5,
  },

  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginStart: 20,
    marginTop: 5,
    // alignSelf: 'flex-end',
  },

  completeYourProfile: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: 'rgba(14, 121, 219, 1)',
    textAlign: 'left'
  },
});
