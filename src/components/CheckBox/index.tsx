import React, { useState } from 'react';
import { Image, Pressable, StyleSheet, Text } from 'react-native';
import { AssetsImages } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import commonStyle from '@utils/commonStyle';

type CheckBoxypes = {
  defaultValue: boolean;
  isDisabled?: boolean;
  label?: string;
  onChange?: (val: boolean) => void;
};

const CheckBox = ({ defaultValue, isDisabled, label, onChange }: CheckBoxypes) => {
  const { styles } = useStyle();
  const [isChecked, setChecked] = useState<boolean>(defaultValue);

  const onStateChange = (): void => {
    const newVal = !isChecked;
    setChecked(newVal);
    onChange?.(newVal);
  }

  return (
    <Pressable
      style={styles.container}
      disabled={isDisabled}
      onPress={onStateChange}
    >
      <Image
        style={styles.checkbox}
        source={
          isChecked
            ? AssetsImages.selectedCheckBox
            : AssetsImages.uncheck
        }
      />
      <Text style={styles.text}>{label}</Text>
    </Pressable>
   
  );
};

export default CheckBox;

const useStyle = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: 8,
      alignItems: 'center',
    },
    text: {
      fontSize: 14,
      color: theme.thunder100,
      fontWeight: '400',
      flexWrap: 'wrap',
      flexShrink: 1,
      ...commonStyle.textDirection,
    },
    checkbox: {
      width: 20,
      height: 20,
      resizeMode: 'contain',
      borderRadius: 5,
    },
  });

  return { styles, theme, isDarkMode };
}


