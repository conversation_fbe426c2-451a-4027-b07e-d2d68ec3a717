import { TOTARA_SITE_URL } from '@api/api_client';
import { TextView } from '@components';
import { AssetsImages, Icons } from '@theme';
import { IconsType } from '@theme/Icons';
import { Toast } from 'native-base';
import React, { useEffect, useState } from 'react';
import { Alert, Image, Modal, Platform, StyleSheet, View } from 'react-native';
import { IconButton } from 'react-native-paper';
import { PERMISSIONS, RESULTS, check, openSettings, request } from 'react-native-permissions';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
  useCameraPermission,
} from 'react-native-vision-camera';

const QRScanner = ({ onClose }) => {
  const device = useCameraDevice('back');
  const { hasPermission, requestPermission } = useCameraPermission();
  const [enableOnCodeScanned, setEnableOnCodeScanned] = useState(true);

  const requestCameraPermission = async () => {
    const cameraPermission =
      Platform.OS === 'ios' ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;

    const result = await check(cameraPermission);

    switch (result) {
      case RESULTS.UNAVAILABLE:
        Toast.show({
          text: 'Camera is not available on this device.',
          textStyle: { textAlign: 'left' },
        });
        break;
      case RESULTS.DENIED:
        const requestResult = await request(cameraPermission);
        if (requestResult !== RESULTS.GRANTED) {
          Toast.show({
            text: 'Camera access is required for this feature.',
            textStyle: { textAlign: 'left' },
          });
        }
        break;
      case RESULTS.LIMITED:
        Toast.show({
          text: 'Camera permission is limited. Please check your device settings.',
          textStyle: { textAlign: 'left' },
        });
        break;
      case RESULTS.GRANTED:
        break;
      case RESULTS.BLOCKED:
        Alert.alert(
          'Permission Blocked',
          'Camera access is blocked. Please enable it in the settings.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => {
                onClose(undefined);
              },
            },
            {
              text: 'Open Settings',
              onPress: async () => {
                try {
                  await openSettings();
                } catch (error) {
                  Alert.alert('Error', 'Unable to open settings.');
                }
              },
            },
          ],
        );
        break;
      default:
        Alert.alert('Error', 'Unexpected permission status.');
    }
  };

  useEffect(() => {
    if (!hasPermission) {
      requestPermission();
      // requestCameraPermission();
    }
  }, []);

  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],
    onCodeScanned: (codes) => {
      if (enableOnCodeScanned && codes && codes[0]?.type == 'qr') {
        console.log(`Scanned ${JSON.stringify(codes)} ${codes.length} codes!`);
        const qrValue = codes[0]?.value;
        if (qrValue && qrValue?.includes(TOTARA_SITE_URL)) {
          const splitValues = qrValue
            .split('?')[1] // Extract the query string part
            .split('&') // Split by '&' into key-value pairs
            .reduce((acc, item) => {
              const [key, value] = item.split('/'); // Split by '/'
              acc[key] = value;
              return acc;
            }, {});
          setEnableOnCodeScanned(false);
          onClose(splitValues);
        } else {
          setEnableOnCodeScanned(false);
          onClose(undefined);

          Toast.show({
            text: 'Invalid QR Code',
            textStyle: { textAlign: 'left' },
            duration: 5000,
          });
        }
      }
    },
  });

  if (!hasPermission) return null;
  if (device == null) return null;

  return (
    <Modal
      visible={true}
      onRequestClose={() => {
        onClose(undefined);
      }}
    >
      <Camera
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        codeScanner={codeScanner}
      />
      <View style={styles.qrFrameView}>
        <Image source={AssetsImages.qrframe} style={styles.qrFrame} />
      </View>

      <View style={styles.txtFind}>
        <TextView style={styles.txtFindText} text="Find a code to scan" />
      </View>

      <IconButton
        icon={() => {
          return <Icons color={'black'} type={IconsType.Entypo} name={'cross'} size={20} />;
        }}
        size={18}
        style={styles.crossContainer}
        onPress={() => {
          onClose(undefined);
        }}
      />
    </Modal>
  );
};

export default QRScanner;

const styles = StyleSheet.create({
  qrFrame: {
    width: 220,
    height: 220,
    tintColor: 'black',
    resizeMode: 'contain',
  },
  qrFrameView: {
    position: 'absolute',
    top: 0,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -95,
    right: 0,
    bottom: 0,
    left: 0,
  },
  crossStyle: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginHorizontal: 10,
    tintColor: 'black',
  },
  crossContainer: {
    backgroundColor: 'white',
    position: 'absolute',
    top: 80,
    right: 20,
  },
  txtFind: { position: 'absolute', top: 90, left: 0, right: 0 },
  txtFindText: { textAlign: 'center', fontSize: 14, color: 'white' },
});
