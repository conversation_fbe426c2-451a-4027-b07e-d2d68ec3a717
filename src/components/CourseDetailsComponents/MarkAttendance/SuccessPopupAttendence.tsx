import { Button, TextView } from '@components';
import {AssetsImages, Colors, fontSize, fonts, LabelConfig} from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';

const SuccessPopupAttendence = ({ onClose, requiresManagerApproval = false }) => {
  const { theme, isDarkMode } = useTheme();

  return (
    <View style={[styles.successBottom, { backgroundColor: theme }]}>
      {requiresManagerApproval ? null : (
      <FastImage
        resizeMode={FastImage.resizeMode.contain}
        style={{ height: 80, width: 80, marginLeft: -15 }}
        source={isDarkMode ? AssetsImages.tickGreenDark : AssetsImages.greenTick}
      />)}

      <TextView
        text={LabelConfig.physicalCourse.attendanceSuccessMessage}
        style={[styles.bTheading, isDarkMode && { color: theme.textColor }]}
      />

      <Button
        style={[
          styles.btn,
          {
            backgroundColor: isDarkMode ? Colors.activeStateDark : 'black',
          },
        ]}
        textStyle={[
          {
            color: isDarkMode ? 'black' : 'white',
          },
        ]}
        type="solid"
        text="Close"
        onPress={onClose}
      />
    </View>
  );
};

export default SuccessPopupAttendence;

const styles = StyleSheet.create({
  successBottom: {
    paddingVertical: 10,
    paddingHorizontal: 25,
    paddingBottom: 30,
    backgroundColor: '#fff',
  },
  btn: { width: '100%', marginTop: 20 },
  mainContainer: {
    flex: 1,
  },
  container: {
    backgroundColor: Colors.white,
    flex: 1,
  },
  timeSlot: {
    position: 'absolute',
    bottom: Platform.OS == 'ios' ? 120 : 90,
    left: 0,
    right: 0,
  },

  bTheading: {
    fontSize: fontSize.xlarge,
    fontFamily: fonts.medium,
    marginTop: 5,
    marginLeft: 5,
    textAlign: 'left',
  },
  bTsub: {
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    color: Colors.black,
    marginTop: 5,
    marginLeft: 5,
    textAlign: 'left',
  },
  marginBottom: {
    marginBottom: Platform.OS == 'android' ? 10 : 0,
  },
  timeSlotStyle: { marginHorizontal: 0, borderRadius: 15, marginTop: 20 },
});
