/* eslint-disable react/no-children-prop */
import React, { useState } from 'react';
import { onSaveQRAttendance } from '@api/totaraApis';
import { Button } from '@components';
import BottomSheet from '@components/bottomSheet';
import useToggleState from '@hooks/useToggleState';
import { AssetsImages, Colors, LabelConfig } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { useSession } from '@totara/core';
import crashReportLogger from '@utils/crashlyticsLogger';
import { Toast } from 'native-base';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import QRScanner from './QRScanner';
import SuccessPopupAttendence from './SuccessPopupAttendence';

const MarkAttendance = ({ tabBarHeight, route }) => {
  const safeAreaContainer = useSafeAreaInsets();
  const { isDarkMode, theme } = useTheme();
  const [successPopupEnable, setSuccessPopupEnable] = useToggleState();
  const [enableQRScanner, setEnableQRScanner] = useToggleState();
  const [loading, setLoading] = useState(false);
  const { apiToken } = useSession();

  const onPress = () => setEnableQRScanner(true);

  const onScannedDone = async (data) => {
    try {
      setLoading(true);
      const res = await onSaveQRAttendance({ apiToken, data });
      setLoading(false);
      if (!res?.success) {
        Toast.show({
          text: res?.message,
          textStyle: { textAlign: 'left' },
          duration: 5000,
        });
      } else {
        setSuccessPopupEnable(true);
      }
    } catch (e) {
      crashReportLogger(e as Error, {
        component: 'index.tsx onScannedDone',
        additionalInfo: 'API Error',
      });
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        containerStyle={{ paddingBottom: -safeAreaContainer.bottom + tabBarHeight }}
        style={{
          backgroundColor: isDarkMode ? Colors.activeStateDark : 'black',
        }}
        textStyle={[
          {
            color: isDarkMode ? 'black' : 'white',
          },
        ]}
        withBg
        icon={loading ? undefined : AssetsImages.qr}
        iconStyle={{ marginRight: 7, tintColor: isDarkMode ? 'black' : 'white' }}
        type="solid"
        isLoading={loading}
        onPress={onPress}
        text={LabelConfig.physicalCourse.markMyAttendance}
      />

      <BottomSheet
        visiblity={successPopupEnable}
        setVisibility={setSuccessPopupEnable}
      >
        <SuccessPopupAttendence onClose={() => setSuccessPopupEnable(false)} />
      </BottomSheet>

      {enableQRScanner && (
        <QRScanner
          onClose={(scannedData) => {
            if (scannedData) {
              onScannedDone(scannedData);
            }
            setEnableQRScanner(false);
          }}
        />
      )}
    </>
  );
};

export default MarkAttendance;
