import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useTheme } from '@theme/ThemeProvider';
import BottomSheet from '@components/bottomSheet';
import AssetsImages from '@theme/AssetsImages';
import { Button, TextView } from '@components/common';
import fontSize, { isPad } from '@utils/fontSize';
import { fonts } from '@theme';
import { useTranslation } from 'react-i18next';

interface ReviewSuccessPopupProps {
  isVisible: boolean;
  setVisible: (val: boolean) => void;
  onClose: () => void;
}

const ReviewSuccessPopup = ({
  isVisible,
  setVisible,
  onClose,
}: ReviewSuccessPopupProps) => {
  const { isDarkMode, styles } = useStyles();
    const { t } = useTranslation();

  const handleClose = () => {
    setVisible(false);
    onClose();
  }

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      visiblity={isVisible}
      setVisibility={handleClose}
    >
      <View>
        <View style={styles.body}>
          <FastImage
            resizeMode={FastImage.resizeMode.contain}
            style={styles.tickImage}
            source={isDarkMode ? AssetsImages.tickGreenDark : AssetsImages.greenTick}
          />
          <View style={styles.textContainer}>
            <TextView
              style={styles.text}
              text={t("Thank you for your review!")}
            />
            <TextView
              style={styles.textDescription}
              text={`${t('Your feedback helps us improve.')}\n${t('Keep learning and growing with us!')}`}
            />
          </View>
          
          <Button
            onPress={handleClose}
            text='Close'
            style={{width: '100%'}}
          />
        </View>
      </View>
    </BottomSheet>
  );
};

export default ReviewSuccessPopup;

const useStyles = () => {
  const { isDarkMode, theme } = useTheme();

  const styles = StyleSheet.create({
    bottomSheet: { paddingHorizontal: 0 },
    body: {
      paddingTop: 10,
      paddingHorizontal: 20,
      marginTop: 30,
      paddingBottom: 50,
      gap: 30,
    },
    image: {
      width: 68,
      height: 68,
      resizeMode: 'contain',
    },
    tickImage: {
      width: 80,
      height: 80,
      resizeMode: 'contain',
      marginLeft: -15,
      marginTop: isPad ? -20 : -30,
      borderRadius: 50,
    },
    text: {
      fontSize: Platform.OS == 'android' ? fontSize.xxxxlarge : fontSize.xxxxxlarge,
      color: theme.thunder100,
      fontFamily: fonts.medium,
      letterSpacing: -0.5,
      marginTop: -10,
      textAlign: 'left',
    },
    textDescription: {
      fontSize: Platform.OS == 'android' ? fontSize.medium : fontSize.large,
      fontFamily: fonts.regular,
      color: theme.thunder80,
      letterSpacing: -0.5,
      textAlign: 'left',
    },
    textContainer: {
      flexDirection: 'column',
      gap: 16,
    }
  });

  return { styles, isDarkMode, theme };
}
