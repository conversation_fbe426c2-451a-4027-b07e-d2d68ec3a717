import React from 'react';
import {
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import { useNavigation } from '@hooks';
import { CertificateType } from '@models/CourseType';


type WriteReviewFooterProps = {
  courseid: string;
  userProfileId: number;
  certificates: CertificateType[] | null;
};

const WriteReviewFooter = ({ courseid, userProfileId, certificates }: WriteReviewFooterProps) => {
  const { styles, isDarkMode, theme } = useStyles();
  const navigation = useNavigation();
  const { t } = useTranslation();

  const certificate = certificates ? certificates[0] : null;

  return (
    <View
      style={styles.footer}
    >
      {certificate && (
        <Pressable
          onPress={() => {
            navigation.navigate('ViewCertificates', {
              downloadLink: certificate?.link,
              isInApp: true,
              fileName: certificate?.name,
            });
          }}
          style={styles.certificateBttn}
          >
          <Text
            style={styles.certificateButtonText}
          >
            {t('View certificates')}
          </Text>
        </Pressable>
      )}
      <Pressable
        onPress={() => {
          navigation.navigate('WriteReview', {
            courseid,
            userid: userProfileId,
          });
        }}
        style={[
          styles.reviewBttn,
          { backgroundColor: theme.thunder100 },
        ]}
      >
        <Text
          style={[
            styles.reviewBttnText,
            {
              color: isDarkMode ? 'black' : 'white',
            },
          ]}
        >
          {t('Write a review')}
      </Text>
    </Pressable>
  </View>
)};

export default React.memo(WriteReviewFooter);

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    reviewBttn: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.backgroundColor,
    },
    certificateBttn: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.transparent,
      borderWidth: 1,
      borderColor: theme.thunder100,
    },
    reviewBttnText: {
      color: theme.thunder100,
      fontSize: 16,
    },
    certificateButtonText: {
      color: theme.thunder100,
      fontSize: 16,
    },
    footer: {
      flexDirection: 'row',
      gap: 16,
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      ...(!isDarkMode && {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 5,
      }),
      backgroundColor: isDarkMode ? theme.bgDark : theme.backgroundColor,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
    },
  });

  return { styles, theme, isDarkMode }
}


