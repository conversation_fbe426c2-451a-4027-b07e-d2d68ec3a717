import React, { useRef, useEffect } from 'react';
import {
  Text,
  View,
  Animated,
  Dimensions,
  StyleSheet,
} from 'react-native';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';
import fonts from '@utils/fonts.tsx';

const { width: screenWidth } = Dimensions.get('window');

const isRTLText = (text = '') => {
  const rtlCharReg = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/;
  return rtlCharReg.test(text);
};

const GradientText = ({ text, style = {} }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const rtl = isRTLText(text);

  useEffect(() => {
   !rtl && Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 4000,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  // Safer offset for RTL/LTR that avoids completely exiting
  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: rtl
      ? [0, -screenWidth * 1.2]
      : [-screenWidth * 1.2, 0],
  });

  return (
    <MaskedView
      style={styles.maskedView}
      maskElement={
        <View style={styles.center}>
          <Text
            numberOfLines={1}
            adjustsFontSizeToFit
            style={[
              {
                fontSize: 24,
                fontWeight: 'bold',
                textAlign: rtl ? 'right' : 'left',
                writingDirection: rtl ? 'rtl' : 'ltr',
                fontFamily: fonts.semiBold,
                backgroundColor: 'transparent',
              },
              style,
            ]}
          >
            {text}
          </Text>
        </View>
      }
    >
      <Animated.View
        style={[
          styles.animatedGradient,
          {
            transform: [{ translateX }],
            height: style?.fontSize ? style.fontSize * 1.6 : 60,
          },
        ]}
      >
        <LinearGradient
          colors={['#0F7EE9', '#00CFFF', '#743CBE', '#00CFFF', '#0F7EE9']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            width: screenWidth * 3,
            height: '100%',
          }}
        />
      </Animated.View>
    </MaskedView>
  );
};

const styles = StyleSheet.create({
  maskedView: {
    width: '100%',
    alignSelf: 'center',
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedGradient: {
    flexDirection: 'row',
  },
});

export default GradientText;
