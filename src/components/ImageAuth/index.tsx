import { <PERSON>uffer } from 'buffer';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleProp, View, ViewStyle } from 'react-native';
import AssetsImages from '@theme/AssetsImages';
import { getCustomHeaders } from '@utils/constants';
import crashReportLogger from '@utils/crashlyticsLogger';
import { trackHttpRequestMetrics } from '@utils/trackHttpRequestMetrics';
import axios, { AxiosResponse } from 'axios';
import FastImage from 'react-native-fast-image';
import { useSelector } from 'react-redux';

interface ImageAuthProps {
  uri?: string;
  style?: StyleProp<ViewStyle>;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  defaultImage?: string;
  loaderSize?: string;
}

const ImageAuth = ({
  uri,
  style,
  resizeMode,
  defaultImage = AssetsImages.default,
  loaderSize,
}: ImageAuthProps) => {
  const [image, setImage] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);

  const getImageUrl = async () => {
    if (uri) {
      let response: AxiosResponse | undefined;
      try {
        setLoading(true);
        setError(false);
        response = await axios(uri, {
          method: 'GET',
          headers: getCustomHeaders(token),
          responseType: 'arraybuffer',
        });
        const base64String = Buffer.from(response?.data, 'binary').toString('base64');
        const imageUrl = `data:image/png;base64,${base64String}`;
        setImage(imageUrl);
      } catch (e) {
        crashReportLogger(e as Error, {
          component: 'ImageAuth getImageUrl',
          url: uri,
          additionalInfo: 'Failed to fetch image url',
        });
        setError(true);
      } finally {
        setLoading(false);
        if (response) trackHttpRequestMetrics(uri, 'GET', response, 'ImageAuth getImageUrl');
      }
    } else {
      setLoading(false);
    }
  };

  useEffect(() => {
    getImageUrl();
  }, [uri]);

  return (
    <View style={[style, { justifyContent: 'center', alignItems: 'center' }]}>
      {!loading && (!image || error || image == undefined) && (
        <FastImage source={AssetsImages.soulvite} style={style} resizeMode={resizeMode} />
      )}
      {!loading && image && (
        <FastImage source={{ uri: image }} style={style} resizeMode={resizeMode} />
      )}
      {loading && (
        <ActivityIndicator
          color="black"
          size={loaderSize ? loaderSize : 'large'}
          style={{ position: 'absolute', zIndex: 1 }}
        />
      )}
    </View>
  );
};

export default ImageAuth;
