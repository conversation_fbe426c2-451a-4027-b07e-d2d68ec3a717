// Import necessary modules
import React from 'react';
import { I18nManager, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { Colors, fonts } from '../theme';
import { TextView } from './common';
import { fontFamily } from '../utils/AppUtils';
import { IconButton } from 'react-native-paper';
import Icons, { IconsType } from '../theme/Icons';
import { useTheme } from '../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';

interface IndicatorProps {
  step: number;
  bgColor: string;
  activeIndex: number;
  onBackPress?: () => void;
  style?: StyleProp<ViewStyle>;
}
const Indicator: React.FC<IndicatorProps> = ({ step, activeIndex, onBackPress, style }) => {
  const steps = Array.from({ length: step });
  const { theme, isDarkMode } = useTheme();

  const { t } = useTranslation();

  const renderLines = () => {
    return steps?.map((step, index) => {
      const backgroundColor =
        activeIndex > index
          ? isDarkMode
            ? theme.bgCreamColor.backgroundColor
            : 'black'
          : isDarkMode
            ? theme.cardBg
            : Colors.lightGray;
      return (
        <View
          key={index}
          style={[
            styles.line,
            {
              backgroundColor,
            },
          ]}
        />
      );
    });
  };

  return (
    <View style={[styles.container, style]}>
      {activeIndex != 1 && onBackPress && (
        <IconButton
          icon={() => {
            return (
              <Icons color={'black'} type={IconsType.Ionicons} name={'chevron-back'} size={25} />
            );
          }}
          size={17}
          onPress={onBackPress}
        />
      )}
      <View style={styles.outerContainer}>
        <View style={styles.subContainer}>{renderLines()}</View>
      </View>
      <TextView
        text={I18nManager.isRTL ? `${steps.length} ${t('of')} ${activeIndex} ${t('Step')}` : `${t('Step')} ${activeIndex} ${t('of')} ${steps.length}`}
        style={[styles.text, isDarkMode && { color: theme.textColor }]}
      />
    </View>
  );
};

// Styles for the Indicator component
const styles = StyleSheet.create({
  container: {
    marginTop: 15,
  },
  subContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  line: {
    height: 3,
    borderRadius: 20,
    backgroundColor: Colors.blue,
    flex: 1,
    marginHorizontal: 5,
  },
  text: {
    fontSize: 12,
    marginTop: 15,
    marginHorizontal: 18,
    fontFamily: fonts.medium,
    textTransform: 'uppercase',
    textAlign: 'left'
    
  },
  outerContainer: {
    marginHorizontal: 9,
    marginTop: 15,
  },
});

export default Indicator;
