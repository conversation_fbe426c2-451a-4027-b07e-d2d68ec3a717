import Colors from '../../theme/Colors';
import AssetsImages from '../../theme/AssetsImages';
import React, { useEffect, useState } from 'react';
import { Image, Modal, Platform, StyleSheet, Text, View } from 'react-native';
import fontSize from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import Button from '../../components/common/ButtonPrimary';
import { useNetInfo } from '@react-native-community/netinfo';

const InternetInfo = () => {
  const { isConnected, isInternetReachable } = useNetInfo();

  const [delayInternetPopup, setDelayInternetPopup] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setDelayInternetPopup(false);
    }, 3000);
  }, []);

  if (delayInternetPopup) {
    return null;
  }

  return (
    <Modal visible={false}>
      <View style={styles.container}>
        <Image source={AssetsImages.Isolation_Mode} style={styles.image} />
        <Text style={styles.heading}>You're currently offline</Text>
        <Text style={styles.text}>
          It looks like you're offline.{'\n'}Check your internet connection and try again.
        </Text>

        <Button
          style={{ marginTop: 30, marginHorizontal: 30 }}
          label={'Go to Downloads'}
          onPress={() => {}}
        />

        <Text style={styles.tryAgainText}>Please try again</Text>
      </View>
    </Modal>
  );
};

export default InternetInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    justifyContent: 'center',
  },

  heading: {
    fontSize: fontSize.xlarge,
    color: Colors.black,
    fontWeight: '600',
    fontFamily: fonts.medium,
    letterSpacing: -0.2,
    textAlign: 'center',
  },

  text: {
    marginTop: Platform.OS == 'ios' ? 10 : 0,
    textAlign: 'center',
    fontSize: fontSize.small,
    color: Colors.black,
    fontFamily: fonts.regular,
    letterSpacing: -0.2,
  },

  image: {
    alignSelf: 'center',
    width: 70,
    height: 60,
    resizeMode: 'contain',
  },

  tryAgainText: {
    color: '#0058DA',
    textAlign: 'center',
    marginTop: Platform.OS == 'ios' ? 10 : 5,
    fontSize: fontSize.small,
    fontWeight: '500',
    fontFamily: fonts.medium,
  },
});
