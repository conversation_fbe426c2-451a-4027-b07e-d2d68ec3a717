import React, { useCallback, useMemo, useRef } from 'react';
import BottomSheets from '../bottomSheet';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
  NativeEventSubscription,
  Platform,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { AssetsImages, Colors, Dimen, LabelConfig, fontSize, fonts } from '@theme';
import { Spacer, TextView } from '@components';
import { isPad } from '../../utils/fontSize';
import { I18nManager } from 'react-native';
import { useTranslation } from 'react-i18next';
import { lineHeight } from '@utils/constants';
import { navigate } from '@navigation/navigationService';
import { useTheme } from '@theme/ThemeProvider';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetFlatList,
  BottomSheetModal,
  BottomSheetModalProps,
} from '@gorhom/bottom-sheet';

type BadgeCriteriaProps = {
  selectedBadge: Badge;
  earnedBadgeCriteria: boolean;
  onTouchOutSide: () => void;
};
const { earnBadge, complete, requiredGrade, completeDate, or, and } = LabelConfig.badges;

const BadgeCriteria = ({
  earnedBadgeCriteria,
  selectedBadge,
  onTouchOutSide,
}: BadgeCriteriaProps) => {
  const { t } = useTranslation();
  const { isDarkMode, theme } = useTheme();
  const sheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['95%'], []);
  const { handleSheetPositionChange } = useBottomSheetBackHandler(sheetRef);

  return (
    <BottomSheet
      ref={sheetRef}
      index={0}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
      enablePanDownToClose={true}
      onChange={handleSheetPositionChange}
      onClose={() => onTouchOutSide()}
      backdropComponent={(backdropProps) => (
        <BottomSheetBackdrop {...backdropProps} enableTouchThrough={true} disappearsOnIndex={-1} />
      )}
      handleIndicatorStyle={{
        backgroundColor: isDarkMode ? theme.bgLightDark : Colors.darkBg,
      }}
      handleStyle={{
        backgroundColor: isDarkMode ? Colors.darkBg : 'white',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        borderWidth: 0,
      }}
      style={{
        borderWidth: 0,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        backgroundColor: isDarkMode ? Colors.darkBg : 'white',
      }}
    >
      <View
        style={[
          styles.body,
          {
            backgroundColor: isDarkMode ? Colors.darkBg : 'white',
            borderTopLeftRadius: isDarkMode ? 0 : 20,
            borderTopRightRadius: isDarkMode ? 0 : 20,
          },
        ]}
      >
        <View
          style={[styles.circleContainer, isDarkMode && { backgroundColor: theme.bgLightDark }]}
        >
          <FastImage
            source={
              selectedBadge?.image_url
                ? { uri: selectedBadge?.image_url }
                : AssetsImages.Isolation_Mode
            }
            style={styles.userBadgeImage}
          />
        </View>

        <TextView
          text={selectedBadge.name?.toLocaleUpperCase()}
          numberOfLines={2}
          style={[styles.title, isDarkMode && { color: theme.textColor }]}
        />

        <View
          style={[
            styles.criteriaContainer,
            { flex: 1 },
            isDarkMode && { borderColor: theme.borderBackground },
          ]}
        >
          <TextView
            text={earnedBadgeCriteria ? "You've Achieved This Badge!" : earnBadge}
            style={[styles.txtCriteria, isDarkMode && { color: theme.textColor }]}
          />

          <BottomSheetFlatList
            data={selectedBadge?.award_criteria}
            style={styles.flatlist}
            contentContainerStyle={styles.contentContainer}
            keyExtractor={(_, i) => i.toString()}
            showsVerticalScrollIndicator={false}
            renderItem={({ item: it, index }) => {
              return (
                <>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TextView
                      text={`${it.title}`}
                      style={[
                        styles.mainTitle,
                        { fontFamily: fonts.medium, fontSize: 16 },
                        isDarkMode && { color: theme.textColor },
                      ]}
                    />
                  </View>

                  <FlatList
                    data={it?.criteria}
                    renderItem={({ item, index: indx }: any) => {
                      return (
                        <View key={indx} style={styles.criteriaItem}>
                          <View style={styles.countContainer}>
                            <TextView text={(indx + 1).toString()} style={styles.txtCount} />
                          </View>

                          <TouchableOpacity
                            activeOpacity={1}
                            style={styles.col}
                            onPress={() => {
                              onTouchOutSide();
                              if (item?.type == 'program' && item?.onboarding_program) {
                                navigate('NewJoinerPrograms', { id: item.id });
                              } else if (
                                (item?.type == 'course' && item?.course_type == 'One Day') ||
                                item?.course_type_detail == 'Self-Paced' || item?.course_type == 'Blended'
                              ) {
                                navigate('CourseDetail', { courseid: item.id });
                              } else if (
                                item?.type == 'course' &&
                                item?.course_type == 'Multi Day' &&
                                item?.course_type_detail != 'Self-Paced'
                              ) {
                                navigate('MultipleSession', { courseid: item?.id });
                              } else if (item?.type == 'program') {
                                navigate('ProgramDetails', {
                                  id: item?.id,
                                });
                              }
                            }}
                          >
                            <TextView
                              text={item?.title}
                              style={[styles.txtComplete, isDarkMode && { color: theme.textColor }]}
                            />

                            {item?.grade && (
                              <TextView
                                text={`${t(requiredGrade)}: ${item?.grade}`}
                                style={[
                                  styles.txtRequired,
                                  isDarkMode && { color: theme.textColor },
                                ]}
                              />
                            )}
                            {item?.date && (
                              <TextView
                                text={`${t(completeDate)}: ${item?.date ? item?.date : 'NIL'}`}
                                style={[styles.txtDate, isDarkMode && { color: '#C7C7C7' }]}
                              />
                            )}
                          </TouchableOpacity>
                        </View>
                      );
                    }}
                    ItemSeparatorComponent={() => {
                      return (
                        <View style={[styles.row, styles.itemSeparator]}>
                          <View
                            style={[
                              styles.line,
                              isDarkMode && { borderBottomColor: theme.borderBackground },
                            ]}
                          />
                          <View
                            style={[
                              styles.circleOR,
                              it?.operator !== 'Any' && { width: 50, padding: 0, height: 25 },
                              isDarkMode && { borderColor: theme.borderBackground },
                            ]}
                          >
                            <TextView
                              text={it?.operator == 'Any' ? or : and}
                              style={[styles.or, isDarkMode && { color: theme.textColor }]}
                            />
                          </View>
                          <View
                            style={[
                              styles.line,
                              isDarkMode && { borderBottomColor: theme.borderBackground },
                            ]}
                          />
                        </View>
                      );
                    }}
                    keyExtractor={(_, i) => i.toString()}
                  />
                  <Spacer height={10} />
                </>
              );
            }}
            ItemSeparatorComponent={(item) => {
              return (
                <View style={[styles.row, styles.itemSeparator]}>
                  <View
                    style={[
                      styles.line,
                      isDarkMode && { borderBottomColor: theme.borderBackground },
                    ]}
                  />
                  <View
                    style={[
                      styles.circleOR,
                      item?.leadingItem?.award_criteria_operator !== 'Any' && {
                        width: 50,
                        padding: 0,
                        height: 25,
                      },
                      isDarkMode && { borderColor: theme.borderBackground },
                    ]}
                  >
                    <TextView
                      text={item?.leadingItem?.award_criteria_operator == 'Any' ? or : and}
                      style={[styles.or, isDarkMode && { color: theme.textColor }]}
                    />
                  </View>
                  <View
                    style={[
                      styles.line,
                      isDarkMode && { borderBottomColor: theme.borderBackground },
                    ]}
                  />
                </View>
              );
            }}
          />
        </View>
      </View>
    </BottomSheet>
  );

  return (
    <BottomSheets
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={styles.sheetStyle}
      visiblity={true}
      setVisibility={onTouchOutSide}
      children={
        <View style={styles.body}>
          <View style={styles.circleContainer}>
            <FastImage
              source={
                selectedBadge?.image_url
                  ? { uri: selectedBadge?.image_url }
                  : AssetsImages.Isolation_Mode
              }
              style={styles.userBadgeImage}
            />
          </View>

          <TextView
            text={selectedBadge.name?.toLocaleUpperCase()}
            numberOfLines={2}
            style={[styles.title, isDarkMode && { color: theme.textColor }]}
          />

          <View style={styles.criteriaContainer}>
            <TextView
              text={earnedBadgeCriteria ? "You've Achieved This Badge!" : earnBadge}
              style={[styles.txtCriteria, isDarkMode && { color: theme.textColor }]}
            />

            <FlatList
              data={selectedBadge?.award_criteria}
              style={styles.flatlist}
              contentContainerStyle={styles.contentContainer}
              nestedScrollEnabled
              showsVerticalScrollIndicator={false}
              renderItem={({ item: it, index }) => {
                return (
                  <>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <TextView
                        text={`${t(complete)}: `}
                        style={[styles.mainTitle, isDarkMode && { color: theme.textColor }]}
                      />
                      <TextView
                        text={`“${it.title}”`}
                        style={[
                          styles.mainTitle,
                          { fontFamily: fonts.medium },
                          isDarkMode && { color: theme.textColor },
                        ]}
                      />
                    </View>

                    <FlatList
                      data={it?.criteria}
                      renderItem={({ item, index: indx }: any) => {
                        return (
                          <View key={indx} style={styles.criteriaItem}>
                            <View style={styles.countContainer}>
                              <TextView text={(indx + 1).toString()} style={styles.txtCount} />
                            </View>

                            <TouchableOpacity
                              activeOpacity={1}
                              style={styles.col}
                              onPress={() => {
                                onTouchOutSide();
                                if (item?.type == 'program' && item?.onboarding_program) {
                                  navigate('NewJoinerPrograms', { id: item.id });
                                } else if (
                                  item?.type == 'course' &&
                                  item?.course_type == 'One Day'
                                ) {
                                  navigate('CourseDetail', { courseid: item.id });
                                } else if (
                                  item?.type == 'course' &&
                                  item?.course_type == 'Multi Day'
                                ) {
                                  navigate('MultipleSession', { courseid: item?.id });
                                } else if (item.type != 'course')
                                  navigate('ProgramDetails', {
                                    id: item?.id,
                                  });
                              }}
                            >
                              <TextView
                                text={item?.title}
                                style={[
                                  styles.txtComplete,
                                  isDarkMode && { color: theme.textColor },
                                ]}
                              />

                              <TextView
                                text={`${t(requiredGrade)} ${item?.grade ? item?.grade : '0'}%`}
                                style={[
                                  styles.txtRequired,
                                  isDarkMode && { color: theme.textColor },
                                ]}
                              />
                              {item?.date && (
                                <TextView
                                  text={`${t(completeDate)}: ${item?.date ? item?.date : 'NIL'}`}
                                  style={[styles.txtDate, isDarkMode && { color: theme.textColor }]}
                                />
                              )}
                            </TouchableOpacity>
                          </View>
                        );
                      }}
                      ItemSeparatorComponent={() => {
                        return (
                          <View style={[styles.row, styles.itemSeparator]}>
                            <View
                              style={[
                                styles.line,
                                isDarkMode && { borderBottomColor: theme.textColor },
                              ]}
                            />
                            <View
                              style={[
                                styles.circleOR,
                                it?.operator !== 'Any' && { width: 50, padding: 0, height: 25 },
                              ]}
                            >
                              <TextView
                                text={it?.operator == 'Any' ? or : and}
                                style={[styles.or, isDarkMode && { color: theme.textColor }]}
                              />
                            </View>
                            <View
                              style={[
                                styles.line,
                                isDarkMode && { borderBottomColor: theme.textColor },
                              ]}
                            />
                          </View>
                        );
                      }}
                    />
                  </>
                );
              }}
              ItemSeparatorComponent={() => {
                return <View style={[styles.line, styles.marginVertical]} />;
              }}
            />
          </View>
        </View>
      }
    />
  );
};

export default BadgeCriteria;

export const useBottomSheetBackHandler = (
  bottomSheetRef: React.RefObject<BottomSheetModal | null>,
) => {
  const backHandlerSubscriptionRef = useRef<NativeEventSubscription | null>(null);
  const handleSheetPositionChange = useCallback<NonNullable<BottomSheetModalProps['onChange']>>(
    (index) => {
      const isBottomSheetVisible = index >= 0;
      if (isBottomSheetVisible && !backHandlerSubscriptionRef.current) {
        // setup the back handler if the bottom sheet is right in front of the user
        backHandlerSubscriptionRef.current = BackHandler.addEventListener(
          'hardwareBackPress',
          () => {
            bottomSheetRef.current?.close();
            return true;
          },
        );
      } else if (!isBottomSheetVisible) {
        backHandlerSubscriptionRef.current?.remove();
        backHandlerSubscriptionRef.current = null;
      }
    },
    [bottomSheetRef, backHandlerSubscriptionRef],
  );
  return { handleSheetPositionChange };
};

const styles = StyleSheet.create({
  line: {
    borderBottomWidth: 1,
    height: 1,
    flex: 1,
    borderBottomColor: Colors.grayline,
  },
  body: { paddingTop: 0, paddingBottom: 20, flex: 1 },
  circleContainer: {
    marginTop: 25,
    borderRadius: 80 / 2,
    height: 80,
    width: 80,
    backgroundColor: '#E5E9EB',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  title: {
    marginHorizontal: 30,
    marginBottom: 20,
    textAlign: 'center',
    marginTop: 15,
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
  },
  criteriaContainer: {
    marginTop: 10,
    borderRadius: 15,
    overflow: 'hidden',
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: Colors.darkgray,
    paddingHorizontal: 24,
  },
  txtCriteria: {
    marginTop: 25,
    fontSize: fontSize.medium,
    marginBottom: 15,
    textAlign: 'left',
    lineHeight: lineHeight(fontSize.medium),
  },
  criteriaItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  countContainer: {
    height: 18,
    width: 18,
    backgroundColor: Colors.skyBlue,
    borderRadius: 20 / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  txtCount: {
    fontSize: fontSize.mini,
    color: Colors.white,
    textAlign: 'center',
    marginTop: Platform.OS == 'ios' ? 0 : -2,
  },
  txtComplete: {
    marginStart: 10,
    fontSize: fontSize.medium,
    textAlign: 'left',
    lineHeight: 21,
  },
  txtRequired: {
    marginStart: 10,
    marginTop: 5,
    fontFamily: fonts.medium,
    fontSize: fontSize.small,
    textAlign: 'left',
    lineHeight: lineHeight(fontSize.small),
  },
  txtDate: {
    marginLeft: 10,
    marginTop: 10,
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    textAlign: 'left',
    lineHeight: lineHeight(fontSize.small),
  },
  flatlist: {
    maxHeight: Dimen.height / 2,
    flex: 1,
  },
  row: { flexDirection: 'row', alignItems: 'flex-start' },
  circleOR: {
    borderWidth: 1,
    borderColor: Colors.grayline,
    height: 33,
    width: 33,
    borderRadius: 33 / 2,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  or: {
    fontSize: isPad ? fontSize.small : I18nManager.isRTL ? fontSize.xlarge : fontSize.mini,
    color: Colors.dark_g,
    lineHeight: lineHeight(
      isPad ? fontSize.small : I18nManager.isRTL ? fontSize.xlarge : fontSize.mini,
    ),
  },
  userBadgeImage: {
    width: Dimen.width / 9,
    height: Dimen.width / 9,
    resizeMode: 'contain',
  },
  sheetStyle: {
    justifyContent: 'flex-start',
    paddingBottom: 15,
  },
  col: { paddingEnd: 25, marginTop: Platform.OS == 'ios' ? -3 : -3 },
  itemSeparator: {
    alignItems: 'center',
    marginVertical: 7,
  },
  txtComplt: {
    fontSize: fontSize.medium,
    marginTop: 0,
    textAlign: 'left',
  },
  contentContainer: { paddingBottom: 20 },
  mainTitle: {
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    marginBottom: 15,
    textAlign: 'left',
  },
  marginVertical: { marginVertical: 15 },
});
