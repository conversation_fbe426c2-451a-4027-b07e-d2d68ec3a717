import AssetsImages from '../../theme/AssetsImages';
import Colors from '../../theme/Colors';
import React from 'react';
import { Pressable, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { SubItem, TextView } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { useTheme } from '../../theme/ThemeProvider';

type AvailableSeatsType = {
  seats: string;
  dateTime: string;
  onPress?: () => void;
  label: string;
  containerStyle?: StyleProp<ViewStyle>;
};

const AvailableSeats = ({
  containerStyle,
  seats,
  dateTime,
  onPress,
  label,
}: AvailableSeatsType) => {
  const { theme, isDarkMode } = useTheme();

  return (
    <Pressable
      style={[styles.container, containerStyle, isDarkMode && { backgroundColor: theme.cardBg }]}
      onPress={onPress}
    >
      <View style={styles.row}>
        <SubItem
          icon={AssetsImages.calRound}
          label={`${seats} ${label}`}
          textStyle={styles.txt}
          iconStyle={[styles.image, isDarkMode && { tintColor: Colors.white }]}
        />
        {dateTime && (
          <TextView
            text={dateTime}
            style={[styles.dayTimeTxt, isDarkMode && { color: theme.textColor }]}
          />
        )}
      </View>
    </Pressable>
  );
};

export default AvailableSeats;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.cream,
    marginHorizontal: 20,
    borderRadius: 10,
    marginTop: 10,
    paddingVertical: 7,
    paddingHorizontal: 10,
  },
  image: {
    width: 35,
    height: 35,
    resizeMode: 'contain',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txt: {
    marginLeft: 3,
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    color: Colors.black,
    fontFamily: fonts.regular,
  },

  dayTimeTxt: {
    marginStart: -10,
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    color: Colors.black,
    fontFamily: fonts.medium,
  },
});
