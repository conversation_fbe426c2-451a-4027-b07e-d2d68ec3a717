import AssetsImages from '@theme/AssetsImages';
import Colors from '@theme/Colors';
import React from 'react';
import {
  I18nManager,
  Platform,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { Spacer, SubItem } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import Icons, { IconsType } from '../../theme/Icons';
import moment from 'moment';
import CompletedIcon from './CompletedIcon';
import { useTranslation } from 'react-i18next';
import { DAYS, MONTHS } from '@utils/AppUtils';
import { useTheme } from '@theme/ThemeProvider';
import { LOCALE } from '@utils/localization';

type CohortSlot = {
  startdatetime: string;
  enddatetime: string;
  time?: string;
  name: string;
  isCompleted?: boolean;
  isPending: boolean;
  style?: StyleProp<ViewStyle>;
  isRequested?: boolean;
  isEditable?: boolean;
  hideHeading?: boolean;
  onMorePress?: () => void;
};

const formatDateToArabic = (date) => {
  const formattedDate = moment(date).format('ddd, DD MMM YYYY');
  const [day, dayNumberMonthYear] = formattedDate?.split(', ');
  const [dayNumber, month, year] = dayNumberMonthYear?.split(' ');

  const arabicDay = DAYS[day.substring(0, 3)];
  const arabicMonth = MONTHS[month];

  return `${arabicDay}, ${dayNumber} ${arabicMonth} ${year}`;
};

const CohortSlot = ({
  name,
  time,
  isCompleted,
  isPending,
  startdatetime,
  enddatetime,
  style,
  isEditable,
  hideHeading,
  onMorePress,
}: CohortSlot) => {
  const { styles, theme, isDarkMode } = useStyles();
  const { t } = useTranslation();

  const startDate = startdatetime ? moment.utc(startdatetime).locale(LOCALE).format('DD MMM') : null;
  const endDate = enddatetime ? moment.utc(enddatetime).locale(LOCALE).format('DD MMM') : null;
  const isOneDaySeminar = startDate === endDate;

  const genDateTime = (): string => {
    if (isOneDaySeminar && startDate) {
      if (startDate) {
        return `${startDate}, ${time ?? ''}`;
      }
      return time ?? "";
    } else {
      if (startDate && endDate) {
        return `${startDate} - ${endDate}`
      }
      return time ?? "";
    }
  }

  const requestedLabel = t(isPending ? 'Requested cohort' : 'Your cohort details');

  return (
    <View
      style={[
        [styles.container, isCompleted && { backgroundColor: Colors.completeBox }],
        style,
        isDarkMode && { backgroundColor: theme.bgDark },
      ]}
    >
      <View style={[styles.row, isEditable && styles.rowBetween, hideHeading && styles.hidden]}>
        <View>
          <SubItem
            customView={
                isCompleted ? (
                <CompletedIcon style={{ marginLeft: 3, marginRight: 5 }} />
                ) : undefined
            }
            isLogo
            icon={AssetsImages.timeslote}
            label={
                isCompleted
                ? t('You have attended this course on')
                : `${t(requestedLabel)}:`
            }
            textStyle={styles.txt}
            iconStyle={[styles.image, { width: 30, height: 30 }]}
          />
        </View>
        {isEditable && !isCompleted && (
          <TouchableOpacity style={styles.eplisis} onPress={onMorePress}>
            <Icons
              color={isDarkMode ? 'white' : Colors.black}
              type={IconsType.Ionicons}
              name={'ellipsis-vertical'}
              size={25}
            />
          </TouchableOpacity>
        )}
        </View>

        {!hideHeading && (
          <Spacer height={15} />
        )}

        <SubItem
          icon={AssetsImages.clock}
          label={name}
          textStyle={styles.dayTimeTxt}
          iconStyle={[styles.icon, isDarkMode && { tintColor: 'white' }]}
        />
        <Spacer height={10} />
        <SubItem
          icon={AssetsImages.cal}
          label={genDateTime()}
          textStyle={styles.dayTimeTxt}
          iconStyle={[styles.icon, isDarkMode && { tintColor: 'white' }]}
        />
    </View>
  );
};

export default CohortSlot;

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    heading: {
      marginLeft: 5,
      fontSize: isPad ? fontSize.medium : 14,
    },
    container: {
      backgroundColor: Colors.cream,
      // height: 140,
      // height: Platform.OS == "android" ? 150 : 140,
      justifyContent: 'space-between',
      marginHorizontal: 20,
      borderRadius: 20,
      marginTop: 10,
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    image: {
      width: 35,
      height: 35,
      resizeMode: 'contain',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    txt: {
      marginLeft: 5,
      fontSize: 18,
      color: theme.thunder100,
      fontFamily: fonts.regular,
      fontWeight: '700',
    },
  
    dayTimeTxt: {
      marginLeft: 10,
      fontSize: fontSize.small,
      color: Colors.black,
      fontFamily: fonts.regular,
    },
  
    icon: {
      width: 18,
      height: 18,
      marginLeft: 7,
      resizeMode: 'contain',
      tintColor: Colors.black,
      marginTop: Platform.OS == 'android' ? 3 : 1,
    },
  
    eplisis: {
      marginRight: -5,
    },
  
    rowBetween: { justifyContent: 'space-between' },
    hidden: { display: 'none' },
    greenTick: {
      width: 30,
      height: 30,
      backgroundColor: Colors.green,
      borderRadius: 30 / 2,
      marginTop: 5,
      marginBottom: 5,
      alignSelf: 'center',
      justifyContent: 'center',
      alignItems: 'center',
    },
    greenTickImage: {
      width: 15,
      height: 15,
      tintColor: Colors.white,
      resizeMode: 'contain',
    },
  });

  return { styles, theme, isDarkMode };
}
