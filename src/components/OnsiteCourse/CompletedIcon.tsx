import AssetsImages from '../../theme/AssetsImages';
import Colors from '../../theme/Colors';
import React from 'react';
import { Image, ImageStyle, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

type CompletedIconType = {
  style?: StyleProp<ViewStyle>;
  imageStyle?: StyleProp<ImageStyle>;
};

const CompletedIcon = ({ style, imageStyle }: CompletedIconType) => {
  return (
    <View style={[styles.greenTick, style]}>
      <Image style={[styles.greenTickImage, imageStyle]} source={AssetsImages.tick_course} />
    </View>
  );
};

export default CompletedIcon;

const styles = StyleSheet.create({
  greenTick: {
    width: 30,
    height: 30,
    backgroundColor: Colors.green,
    borderRadius: 30 / 2,
    marginTop: 5,
    marginBottom: 5,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  greenTickImage: {
    width: 15,
    height: 15,
    tintColor: Colors.white,
    resizeMode: 'contain',
  },
});
