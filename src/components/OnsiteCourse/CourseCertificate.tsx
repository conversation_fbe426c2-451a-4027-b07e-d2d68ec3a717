import React from 'react';
import { Platform, Pressable, StyleSheet, View } from 'react-native';
import { CourseHeading, TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import Colors from '../../theme/Colors';
import { useNavigation } from '@react-navigation/native';
import { LabelConfig } from '@theme';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';

const CourseCertificate = ({ certificate }) => {
  const { GetCertificate, ViewCertificate } = LabelConfig.courseDetail;
  const navigation = useNavigation();
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();
  return (
    <View style={styles.padding}>
      <CourseHeading title={t(GetCertificate)} />

      <Pressable
        style={[styles.container, isDarkMode && { backgroundColor: theme.bgLightDark }]}
        onPress={() => {
          navigation.navigate('ViewCertificates', {
            downloadLink: certificate?.link,
            isInApp: true,
            fileName: certificate?.name,
          });
        }}
      >
        <TextView
          text={t(ViewCertificate)}
          style={[styles.viewCertificate, isDarkMode && { color: 'white' }]}
        />
      </Pressable>
    </View>
  );
};

export default CourseCertificate;

const styles = StyleSheet.create({
  viewCertificate: {
    fontFamily: fonts.regular,
    fontSize: isPad
      ? fontSize.semiMedium
      : Platform.OS == 'ios'
        ? fontSize.semiSmall
        : fontSize.semiLarge,
    textAlign: 'left',
  },
  padding: { paddingBottom: 15, alignItems: 'flex-start' },
  image: {
    width: 130,
    height: 100,
  },

  container: {
    borderRadius: 10,
    borderColor: Colors.lgray,
    paddingVertical: 15,
    paddingHorizontal: 20,
    marginTop: 20,
    backgroundColor: Colors.certificateColor,
    alignSelf: 'stretch',
  },

  textStyle: {
    fontSize: fontSize.semiSmall,
    color: Colors.black,
    fontFamily: fonts.regular,
    marginLeft: 5,
  },
  iconStyle: {
    width: 35,
    height: 35,
    resizeMode: 'contain',
  },
});
