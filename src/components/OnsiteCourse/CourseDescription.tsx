import React, { useState } from 'react';
import { CourseHeading, RenderCustomHtml, TextView } from '../index';
import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { Platform, Pressable, StyleProp, StyleSheet, Text, TextStyle, View } from 'react-native';
import { containsHTML, lineHeightForAndroidLTR } from '../../utils/constants';
import RenderHTML from 'react-native-render-html';
import Dimen from '../../theme/Dimen';
import { LabelConfig } from '@theme';
import { useTheme } from '../../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';

type CourseDescriptionType = {
  description: string;
  headingStyle?: StyleProp<TextStyle>;
  showHeading?: boolean;
};

const CourseDescription = ({
  description,
  showHeading = true,
  headingStyle,
}: CourseDescriptionType) => {
  const { theme, isDarkMode } = useTheme();
  const [isContentFullView, setContentFullView] = useState(false);
  const { Description, ViewLess, ViewMore } = LabelConfig.courseDetail;

  return (
    <>
      {showHeading && <CourseHeading title={Description} textStyle={headingStyle} />}

      {description ? (
        <RenderCustomHtml
          limit={180}
          html={description}
        />
      ) : null}
    </>
  );
};

export default CourseDescription;

const styles = StyleSheet.create({
  desViewMore: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginLeft: 20,
    color: Colors.skyBlue,
  },
  descText: {
    marginTop: 15,
    color: Colors.ldark,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    fontFamily: fonts.regular,
    textAlign: 'left',
  },
  descriptionText: {
    color: '#656464',
    marginBottom: 20,
    paddingHorizontal: 22,
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.large : fontSize.small,
    marginTop: 10,
    textAlign: 'left',
  },

  viewMoreTextStyle: {
    color: Colors.skyBlue,
    fontFamily: fonts.medium,
    fontSize: fontSize.small,
    textAlign: 'left',
  },
  toggleDescriptionText: { color: 'rgba(61, 145, 226, 1)', alignSelf: 'flex-end' },
});
