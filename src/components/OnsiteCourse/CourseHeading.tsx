import React from 'react';
import { TextView } from '../index';
import fontSize from '@utils/fontSize';
import { StyleProp, StyleSheet, TextStyle } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';

type CourseHeading = { title: string; textStyle?: StyleProp<TextStyle> };

const CourseHeading = ({ title, textStyle }: CourseHeading) => {
  const { theme, isDarkMode } = useTheme();
  return (
    <TextView
      text={title}
      style={[styles.text, textStyle, isDarkMode && { color: theme.textColor }]}
    />
  );
};

export default CourseHeading;

const styles = StyleSheet.create({
  text: { marginTop: 20, fontSize: fontSize.xlarge, textAlign: 'left' },
});
