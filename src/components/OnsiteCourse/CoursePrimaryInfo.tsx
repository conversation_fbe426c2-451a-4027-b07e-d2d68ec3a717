import React, { useCallback } from 'react';
import Colors from '../../theme/Colors';
import { StyleSheet, View } from 'react-native';
import FastImage, { Source } from 'react-native-fast-image';
import { Spacer, SubItem, TextView } from '../../components';
import fontSize, { isPad } from '../../utils/fontSize';
import AssetsImages from '../../theme/AssetsImages';
import Dimen from '../../theme/Dimen';
import { useTheme } from '@theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { lineHeightForAndroidLTRXLarge } from '@utils/constants';
import { formatNumbers } from '@utils/AppUtils';
import { handleTimeFormat } from '@utils/localization';

type CoursePrimaryInfo = {
  title: string;
  source: Source | number;
  status: string;
  duration: string;
  rating: string;
  activity: string;
  agenda_count: string;
  completed_count: string;
  academy: string;
  courseMode: string;
  academyUrl?: string | null;
  courseType: string | null;
  newJoiner?: boolean | undefined;
  isExternalCourse: boolean;
};

const CoursePrimaryInfo = ({
  isExternalCourse,
  newJoiner,
  activity,
  courseType,
  title,
  source,
  status,
  duration,
  agenda_count,
  completed_count,
  rating,
  courseMode,
  academy,
  academyUrl,
}: CoursePrimaryInfo) => {
  const { t } = useTranslation();
  const isCourseCompleted = status === 'Complete';
  const statusText = status?.toUpperCase().includes('DAY')
    ? `${t('DUE IN')} ${status}`
    : isCourseCompleted
      ? t('COMPLETED')
      : status == 'Not yet started'
        ? t('Not yet started')
        : status;
  const { theme, isDarkMode } = useTheme();
  const CourseView = useCallback(() => {
    return (
      <>
        <FastImage source={source} style={styles.image} />

        <View style={[styles.subContainer, { backgroundColor: theme.backgroundColor }]}>
          {statusText != '' && (
            <View style={styles.dueConRow}>
              <View
                style={[
                  styles.dueCon,
                  isCourseCompleted && { backgroundColor: Colors.green },
                  isDarkMode && { backgroundColor: theme.bgLightDark },
                ]}
              >
                <TextView
                  type="h6"
                  style={[styles.dueText, { color: Colors.white }]}
                  text={statusText}
                />
              </View>
            </View>
          )}
          {courseType && (
            <View style={[styles.dueConRow, { marginLeft: 5 }]}>
              <View
                style={[
                  styles.dueCon,
                  [styles.outline, { borderColor: theme.borderBackground }],
                  isDarkMode && { backgroundColor: 'transparent' },
                ]}
              >
                <TextView
                  type="h6"
                  style={[styles.dueText, { color: isDarkMode ? theme.textColor : Colors.black }]}
                  text={t(`${courseType}`)}
                />
              </View>
            </View>
          )}
        </View>

        <TextView
          style={[
            styles.title,
            lineHeightForAndroidLTRXLarge,
            isDarkMode && {
              color: theme.textColor,
            },
          ]}
          text={title}
        />

        <View style={styles.row}>
          {duration && (
            <SubItem
              label={handleTimeFormat(duration) ?? ''}
              icon={AssetsImages.clock}
              textStyle={{ maxWidth: 100, marginRight: 5 }}
              iconStyle={[isDarkMode && { tintColor: 'white' }]}
            />
          )}

          <SubItem
            label={
              courseType == 'Instructor-led'
                ? `${formatNumbers(agenda_count ?? '0')} ${t('Agenda')}`
                : `${formatNumbers(activity ?? '0')} ${Number(activity) > 1 ? t('activities') : t('activity')}`
            }
            icon={AssetsImages.agenda}
            textStyle={{ maxWidth: 100, marginRight: 5 }}
            iconStyle={[isDarkMode && { tintColor: 'white' }]}
          />
          {rating && Number(rating) > 0 ? (
            <SubItem label={rating} icon={AssetsImages.ratingStar} iconStyle={styles.rating} />
          ) : null}
        </View>

        <SubItem
          label={`${formatNumbers(completed_count ? completed_count : '0')} ${t('people completed this course')}`}
          icon={AssetsImages.people}
          iconStyle={[isDarkMode && { tintColor: 'white' }]}
        />
      </>
    );
  }, [activity, status]);

  const NewJoinerView = useCallback(() => {
    return (
      <>
        <>
          <TextView
            style={[styles.title, { marginTop: 0 }, isDarkMode && { color: theme.textColor }]}
            text={title?.trim()}
          />

          <View style={[styles.subContainer, { marginTop: 10 }]}>
            {courseType && (
              <>
                <View style={styles.dueConRow}>
                  <View style={[styles.dueCon, styles.outline]}>
                    <TextView
                      type="h6"
                      style={[styles.dueText, { color: Colors.black }]}
                      text={courseMode}
                    />
                  </View>
                </View>
                <View style={[styles.dueConRow, { marginLeft: 5 }]}>
                  <View style={[styles.dueCon, styles.outline]}>
                    <TextView
                      type="h6"
                      style={[styles.dueText, { color: Colors.black }]}
                      text={t(`${courseType}`)}
                    />
                  </View>
                </View>
              </>
            )}
          </View>

          <View style={[styles.row, { marginTop: 15 }]}>
            <SubItem
              label={`${activity} ${t('activities')}`}
              icon={AssetsImages.activity}
              textStyle={{ maxWidth: 100 }}
              iconStyle={[isDarkMode && { tintColor: 'white' }]}
            />
          </View>
        </>
      </>
    );
  }, [activity, courseMode, status]);

  const dgeTitleLogo = academy || academyUrl ? false : true;

  return (
    <View style={styles.container}>
      {newJoiner ? NewJoinerView() : CourseView()}
      <Spacer height={newJoiner ? 10 : 20} />

      <SubItem
        isLogo
        label={dgeTitleLogo ? 'Gov Academy' : academy}
        icon={dgeTitleLogo ? AssetsImages.dgeLogo1 : academyUrl ? { uri: academyUrl } : undefined}
        iconStyle={[styles.academyIcon, isDarkMode && { borderWidth: 0.5, borderColor: 'white' }]}
        textStyle={styles.academy}
      />

      <Spacer height={5} />
    </View>
  );
};

export default CoursePrimaryInfo;

const styles = StyleSheet.create({
  subContainer: { flexDirection: 'row', alignItems: 'center', marginTop: 30 },
  container: {
    paddingHorizontal: 20,
    // flexGrow: 1,
  },
  image: {
    width: '100%',
    alignSelf: 'center',
    height: Dimen.height / 3.7,
    resizeMode: 'cover',
    borderRadius: 25,
    overflow: 'hidden',
  },
  dueText: {
    color: Colors.white,
    padding: 2,
    paddingHorizontal: 8,
    letterSpacing: 0.2,
  },
  dueCon: {
    backgroundColor: Colors.skyBlue,
    flexDirection: 'row',
    borderRadius: 18,
    paddingVertical: 1.5,
  },
  dueConRow: {
    flexDirection: 'row',
  },

  title: {
    color: Colors.black,
    marginTop: 10,
    fontSize: fontSize.xxxxlarge,
    letterSpacing: -1,
    textAlign: 'left',
  },

  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 8,
    marginTop: 10,
  },
  rating: {
    width: 13,
    marginTop: -2,
    height: 13,
  },
  academy: {
    fontSize: isPad ? fontSize.large : fontSize.medium,
    color: Colors.black,
  },

  academyIcon: {
    width: isPad ? 35 : 28,
    height: isPad ? 35 : 28,
    borderRadius: 28 / 2,
    resizeMode: 'cover',
  },
  outline: {
    backgroundColor: Colors.white,
    borderWidth: 0.9,
    borderColor: Colors.lightGray,
    paddingHorizontal: 5,
  },
});
