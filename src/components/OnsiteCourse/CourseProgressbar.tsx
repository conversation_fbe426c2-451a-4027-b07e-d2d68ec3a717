import Colors from '../../theme/Colors';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { TextView } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import { useTranslation } from 'react-i18next';
import { convertNumbersToArabicNumerals } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

const CourseProgressbar = ({ progress }) => {
  const { t } = useTranslation();
  const { isDarkMode, theme } = useTheme();

  return (
    <View style={styles.main}>
      <View style={styles.row}>
        <TextView
          text={'Overall progress'}
          style={[styles.text, isDarkMode && { color: 'white' }]}
        />
        <TextView
          text={`${convertNumbersToArabicNumerals(progress)}% ${t('completed')}`}
          style={[styles.text, isDarkMode && { color: 'white' }]}
        />
      </View>

      <View style={[styles.container1, isDarkMode && { backgroundColor: theme.bgLightDark }]}>
        <LinearGradient
          colors={isDarkMode ? [Colors.progressBarDarkColor, Colors.progressBarDarkColor] : ['#7ed321', '#c8e30b']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.progress, { width: `${progress}%` }]}
        />
        <View style={[styles.dot, { left: `${progress}%`, marginStart: -6 }]} />
      </View>
    </View>
  );
};

export default CourseProgressbar;

const styles = StyleSheet.create({
  text: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.medium,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  main: { marginBottom: -7 },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 5,
    backgroundColor: Colors.white,
    position: 'absolute',
    top: '100%',
    transform: [{ translateY: -5 }],
  },
  container1: {
    marginTop: 10,
    marginBottom: 10,
    height: 7,
    width: '100%',
    alignSelf: 'center',
    backgroundColor: '#e0e0e0',
    borderRadius: 10,
    overflow: 'hidden',
  },
  progress: {
    height: '100%',
    borderRadius: 10,
  },
});
