import React, { useEffect, useState } from 'react';
import BottomSheet from '../bottomSheet';
import ListItem from '../AddToListBottomSheet/ListItem';
import { Platform, StyleSheet, Text, View } from 'react-native';
import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import AssetsImages from '../../theme/AssetsImages';
import fonts from '../../utils/fonts';
import { useTranslation } from 'react-i18next';
import { commonStyle } from '@utils';
import { useTheme } from '@theme/ThemeProvider';
import { Button } from '@components';

const EditTimeSlotPopup = ({
  onTouchOutSide,
  onEditSlot,
  onCancelSlot,
  requiresManagerApproval,
  managerApprovalStatus,
  isSlotChangeable,
  isVisible,
  secondaryButtonText = 'Cancel',
  primaryButtonText = 'Confirm',
  headingText = 'This is heading text.',
  subheadingText = 'This is subheading text.',
  onSecondaryButtonClick = () => {},
  onPrimaryButtonClick,
  subheadingStyle,
  showOnlyPrimaryButton = false,
  childSlot = null,
}) => {
  const { t } = useTranslation();
  const { styles } = useStyles();
  const [cancelSlotModal, setCancelSlotModal] = useState<boolean>(false);

  useEffect(() => {
    if (!isVisible) {
      setCancelSlotModal(false);
    }
  }, [isVisible]);

  const onCancelCallback = (): void => {
    setCancelSlotModal(true);
    onCancelSlot();
  }

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={{ justifyContent: 'flex-start', paddingBottom: 15 }}
      visiblity={isVisible}
      setVisibility={onTouchOutSide}
      // eslint-disable-next-line react/no-children-prop
      children={
        !cancelSlotModal ? ( <View style={styles.body}>
          {(isSlotChangeable) ? (
          <>
            <ListItem
              style={styles.dropDownContainer}
              textStyle={styles.textStyle}
              iconStyle={styles.iconStyle}
              image={requiresManagerApproval ? AssetsImages.pencilSquaredIcon : AssetsImages.ic_b_edit}
              text={requiresManagerApproval ? t('Change slot') : t('Edit Slot')}
              onPress={onEditSlot}
              />
              <View style={styles.line} />
            </>
          ) : null}
          <ListItem
            style={styles.dropDownContainer}
            textStyle={styles.textStyle}
            iconStyle={styles.iconStyle}
            image={requiresManagerApproval ? AssetsImages.cross_x : AssetsImages.ic_b_cancel}
            text={requiresManagerApproval ? t('Cancel request') : t('Cancel Slot')}
            onPress={onCancelCallback}
          />
        </View>
        ) : (
          <View style={[styles.sheetContainer]}>
            <Text style={[styles.heading, styles.headingConfirm]}>{t(headingText)}</Text>
    
            <Text style={[styles.subheading, subheadingStyle ? subheadingStyle : {}]}>
              {t(subheadingText)}
            </Text>
    
            {childSlot}
    
            <View style={styles.buttonsContainer}>
              {showOnlyPrimaryButton ? null : (
                <Button
                  text={t(secondaryButtonText)}
                  type="fill"
                  onPress={() => {
                    onSecondaryButtonClick();
                  }}
                  style={[styles.cancelButton]}
                  textStyle={[styles.cancelButtonText]}
                />
              )}
    
              <Button
                text={t(primaryButtonText)}
                type="fill"
                onPress={() => {
                  onPrimaryButtonClick();
                }}
                style={[styles.button]}
                textStyle={[styles.buttonText]}
              />
            </View>
          </View>
        )
      }
    />
  );
};

export default EditTimeSlotPopup;

const useStyles = () => {
  const { theme } = useTheme();
  
  const styles = StyleSheet.create({
    line: { borderBottomWidth: 0.5, borderBottomColor: Colors.grayline },
    bottomSheet: { paddingHorizontal: 0 },
    body: { paddingTop: 0 },
    dropDownContainer: {
      height: isPad ? 90 : Platform.OS === 'ios' ? 50 : 45,
      marginTop: Platform.OS == "ios" ? 0 : 7,
      marginBottom: Platform.OS == "ios" ? 0 : 3,
  
    },
    textStyle: {
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.xxlarge : fontSize.semiMedium,
      color: Colors.black,
    },
    iconStyle: {
      width: isPad ? 35 : 20,
      height: isPad ? 35 : 20,
      resizeMode: 'contain',
    },
    sheetContainer: {
      paddingHorizontal: 20,
      paddingBottom: 30,
      paddingTop: 30,
    },
    heading: {
      fontWeight: 700,
      fontSize: 28,
      lineHeight: 33,
      color: theme.thunder100,
      ...commonStyle.textDirection,
    },
    headingConfirm: {},
    subheading: {
      marginTop: 24,
      marginBottom: 40,
      fontWeight: 400,
      fontSize: 16,
      lineHeight: 20,
      color: theme.thunder80,
      ...commonStyle.textDirection,
    },
  
    buttonsContainer: {
      flexDirection: 'row',
      gap: 16,
    },
    cancelButton: {
      height: 56,
      flex: 1,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      borderColor: theme.thunder100,
      backgroundColor: theme.white,
      ...commonStyle.textDirection,
    },
    cancelButtonText: {
      ...commonStyle.textDirection,
      color: theme.thunder100,
    },
    button: {
      height: 56,
      flex: 1,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      borderTopWidth: 0,
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      backgroundColor: theme.thunder100,
      ...commonStyle.textDirection,
    },
    buttonText: {
      ...commonStyle.textDirection,
      color: theme.white,
    },
    submitButton: {
      height: 56,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      borderTopWidth: 0,
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      ...commonStyle.textDirection,
    },
    submitButtonText: {
      color: theme.white,
    },
  });

  return { styles, theme }
}


