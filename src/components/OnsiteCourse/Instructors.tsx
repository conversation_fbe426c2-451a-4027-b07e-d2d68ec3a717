import React from 'react';
import { FlatList, Platform, StyleSheet, View } from 'react-native';
import { CourseHeading, SeparatorLine, TextView } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import Colors from '../../theme/Colors';
import fonts from '@utils/fonts';
import { InstructorsType } from '../../../src/models/CourseType';
import FastImage from 'react-native-fast-image';
import { LabelConfig } from '@theme';
import { useTheme } from '@theme/ThemeProvider';

type InstructorProps = {
  instructors: InstructorsType[];
};

const Instructors = ({ instructors }: InstructorProps) => {
  const { Instructors } = LabelConfig.courseDetail;
  return (
    <View>
      <CourseHeading title={Instructors} />
      <View>
        <FlatList
          scrollEnabled={false}
          data={instructors}
          renderItem={({ item }) => <InstructorsItem item={item} />}
          ItemSeparatorComponent={() => {
            return <SeparatorLine style={styles.flex} />;
          }}
        />
      </View>
    </View>
  );
};

export default Instructors;

export const InstructorsItem = ({ item }: { item: InstructorsType }) => {
  if (!item?.name) return null;

  const { isDarkMode } = useTheme();

  return (
    <>
      <View style={styles.itemContainer}>
        {/* {item?.image && <FastImage source={{ uri: item?.image }} style={styles.image} />} */}
        <View>
          <TextView style={[styles.title, isDarkMode && { color: 'white' }]} text={item.name} />
          {item.designation && (
            <TextView
              text={item.designation}
              style={[styles.des, isDarkMode && { color: 'white' }]}
            />
          )}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  flex: { flex: 1 },
  itemContainer: {
    marginTop: 15,
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 15,
  },
  image: {
    width: isPad ? 50 : 45,
    height: isPad ? 50 : 45,
    borderRadius: 45 / 2,
    resizeMode: 'contain',
  },
  des: {
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.ldark,
    fontFamily: fonts.regular,
    marginTop: Platform.OS == 'ios' ? 2 : 0,
  },
  row: {
    marginLeft: 0,
  },
  title: {
    fontSize: isPad ? fontSize.large : fontSize.semiMedium,
    color: Colors.black,
    fontFamily: fonts.medium,
  },
});
