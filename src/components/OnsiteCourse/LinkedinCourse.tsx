import Colors from '../../theme/Colors';
import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import { Button, TextView } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { IconButton } from 'react-native-paper';
import Icons, { IconsType } from '../../theme/Icons';
import { LabelConfig } from '@theme';
import {
  lineHeightForAndroidLTRMed,
  lineHeightForAndroidLTRSmall,
} from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

const LinkedinCourse = ({ isEnrolled, title, onPress, hideBtn }) => {
  const { StartCourse, LinkedinDes } = LabelConfig.courseDetail;
  const { styles, isDarkMode, theme } = useStyles();

  return (
    <View
      style={[
        styles.container,
        {
          borderColor: theme.borderBackground,
        },
      ]}
    >
      <TextView
        text={title}
        style={[styles.text, lineHeightForAndroidLTRMed, isDarkMode && { color: theme.textColor }]}
      />
      <TextView
        text={LinkedinDes}
        style={[
          styles.des,
          lineHeightForAndroidLTRSmall,
          isDarkMode && { color: theme.textColor, opacity: 0.7 },
        ]}
      />
      {!hideBtn && (
        <Button
          style={[
            styles.buttonStyle,
            !isEnrolled && {
              backgroundColor: isDarkMode ? Colors.inActiveStateDarkBtn : 'rgba(199, 199, 199, 1)',
              borderWidth: 0,
            },
          ]}
          textStyle={styles.buttonTextStyle}
          onPress={onPress}
          isDisabled={!isEnrolled}
          type="transparent"
          text={StartCourse}
        />
      )}

      {isEnrolled && (
        <IconButton
          icon={() => {
            return (
              <Icons
                color={isDarkMode ? 'white' : 'black'}
                type={IconsType.Octicons}
                name={'link-external'}
                size={18}
              />
            );
          }}
          size={17}
          style={styles.iconBtn}
          onPress={onPress}
        />
      )}
    </View>
  );
};

export default LinkedinCourse;

const useStyles = () => {
  const { isDarkMode, theme } = useTheme();

  const styles = StyleSheet.create({
    iconBtn: { position: 'absolute', top: Platform.OS == 'ios' ? 2 : 5, right: 10 },
    container: {
      borderWidth: 1,
      borderRadius: 15,
      borderColor: Colors.lgray,
      paddingVertical: 13,
      paddingHorizontal: 15,
      marginTop: 10,
    },
    text: {
      fontSize: isPad ? fontSize.large : fontSize.medium,
      fontFamily: fonts.medium,
      marginRight: 20,
      textAlign: 'left',
      lineHeight: 20,
    },

    des: {
      fontSize: isPad ? fontSize.medium : fontSize.semiMini,
      marginTop: 5,
      color: Colors.ldark,
      textAlign: 'left',
      lineHeight: 20,
    },

    btn: {
      marginTop: 20,
      marginBottom: 0,
      width: '100%',
    },
    row: {
      alignItems: 'flex-start',
      flexDirection: 'row',
    },
    buttonTextStyle: {
      fontFamily: fonts.medium,
      textTransform: 'uppercase',
      fontSize: fontSize.medium,
      color: theme.thunder80,
    },
    buttonStyle: {
      borderRadius: 12,
      height: 50,
      backgroundColor: 'transparent',
      borderColor: theme.thunder80,
    },
  });

  return { styles, isDarkMode, theme };
}
