import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { TextView } from '../index';
import { AssetsImages } from '@theme';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { useTheme } from '@theme/ThemeProvider';
import { SlotEvent, SlotEventSession } from '@models/CourseType';
import { hexToRGBa } from '@utils/customTheme';

type DateItem = {
  day?: string;
  date?: string;
  month?: string;
  events?: SlotEvent[];
  time?: string;
  index?: number;
  selectedSlot: SlotEventSession | null;
  setSelectedSlot: (slot: (SlotEventSession & {eventId: number})) => void;
};

const MultiDaySlotItem = ({
  day,
  date,
  month,
  events,
  index,
  selectedSlot,
  setSelectedSlot,
}: DateItem) => {
  const isHeadingVisible = index === 0;
  const { t } = useTranslation();
  const { isDarkMode, theme, styles } = useStyles();

  const mappedEvents = events?.flatMap((e) => e.sessions.map((s) => ({...s, eventId: e.id})));

  function formatTime(timeString: string): string {
    const formattedTime = moment(timeString, 'h:mm A').format('ha');
    return formattedTime.toLowerCase();
  }

  const onSeminarPress = async (session: (SlotEventSession & {eventId: number})) => {
    setSelectedSlot(session);
  }

  const selectedIndexEvent = mappedEvents?.findIndex((e) => e.id === selectedSlot?.id);
  const isSelected = (selectedIndexEvent ?? -1) > -1;

  return (
    <View style={[styles.dateItem, isHeadingVisible && styles.marginTop]}>
      <View style={styles.dateColumn}>
        <TextView
          style={[
            styles.dayText,
            isHeadingVisible && styles.heading,
            isDarkMode && { color: theme.textColor },
          ]}
          text={day || ''}
        />
        <TextView
          style={[styles.dateText, isDarkMode && { color: theme.textColor }]}
          text={date || ''}
        />
        <TextView
          style={[styles.dayText, isDarkMode && { color: theme.textColor }]}
          text={month?.toString() || ''}
        />
      </View>
      <View style={styles.lineContainer}>
        <View style={[styles.circle, isSelected && [styles.selectedSlot, isDarkMode && { borderColor: Colors.progressBarDarkColor }]]}>
          {isSelected && <View style={[styles.selectedSlotCircle, isDarkMode && { backgroundColor: Colors.progressBarDarkColor }]} />}
        </View>
        <View style={[styles.line, { backgroundColor: theme.borderBackground }]} />
      </View>
      <View style={styles.slotsColumn}>
        {mappedEvents?.map((session) => (
          <Pressable
             style={[
               [styles.slotContainer, isDarkMode && { backgroundColor: theme.bgDark }],
               selectedSlot?.id === session.id && {
                 backgroundColor: isDarkMode ? theme.bgLightDark : Colors.black,
               },
             ]}
             onPress={() => onSeminarPress(session)}
           >
            <View style={styles.slotInnerRowContainer}>
              <Text
                style={selectedSlot?.id === session.id ? styles.selectedSlotHeading : styles.slotHeading}
              >
                {session.session}
              </Text>
              <Text style={selectedSlot?.id === session.id ? styles.selectedSlotHeading : styles.slotHeading}>
                {`${formatTime(session.starttime)} - ${formatTime(session.endtime)}`}
              </Text>
            </View>
            <View style={styles.locationContainer}>
              {!session.location?.join(", ") && (
                <Image
                  source={AssetsImages.team}
                  resizeMode="contain"
                  style={styles.icon}
                />
              )}
              <Text style={selectedSlot?.id === session.id ? styles.selectedSlotSubheading : styles.slotSubheading}>
                {session.location?.join(", ") || t("Via Microsoft teams")}
              </Text>
            </View>
            
           </Pressable>
        ))}
      </View>
    </View>
  );
};

export default MultiDaySlotItem;

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    dateItem: {
      flexDirection: 'row',
      paddingHorizontal: 20,
  
      alignItems: 'flex-start',
    },
    dateColumn: {
      width: 45,
      alignItems: 'center',
    },
    lineContainer: {
      width: 30,
      alignItems: 'center',
      position: 'relative',
      marginBottom: -5,
    },
    circle: {
      width: 13,
      height: 13,
      marginTop: 3,
      borderRadius: 13 / 2,
      borderColor: Colors.lgray,
      borderWidth: 2,
      position: 'absolute',
      top: 0,
    },
    line: {
      width: 2,
      backgroundColor: '#ddd',
      flex: 1,
      marginTop: 15,
    },
    slotsColumn: {
      flex: 1,
      paddingLeft: 10,
      paddingBottom: 50,
    },
    dayText: {
      fontFamily: fonts.regular,
      color: Colors.ldark,
      fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    },
    dateText: {
      marginVertical: Platform.OS == 'ios' ? -2 : -5,
      fontFamily: fonts.medium,
      color: Colors.black,
      fontSize: isPad ? fontSize.xxxxlarge : fontSize.xxxxxlarge,
    },
    slotContainer: {
      paddingVertical: 7,
      paddingHorizontal: 20,
      backgroundColor: Colors.slotBg,
      borderWidth: 1,
      borderColor: Colors.inputBackground,
      borderRadius: 5,
      marginVertical: 5,
      gap: 8,
    },
    slotText: {
      fontSize: isPad ? fontSize.medium : fontSize.xxmini,
      fontFamily: fonts.medium,
      letterSpacing: -0.2,
      color: Colors.black,
    },
    timeText: {
      fontSize: isPad ? fontSize.medium : fontSize.xxmini,
      fontFamily: fonts.medium,
      letterSpacing: -0.2,
      color: Colors.black,
    },
  
    heading: {
      fontSize: fontSize.medium,
      marginVertical: 0,
      fontFamily: fonts.medium,
      color: Colors.black,
      marginTop: 10,
    },
    marginTop: { marginTop: 10 },
    selectedSlot: {
      borderColor: Colors.green,
      justifyContent: 'center',
      alignItems: 'center',
    },
    selectedSlotCircle: {
      height: 6.5,
      width: 6.5,
      backgroundColor: Colors.green,
      alignSelf: 'center',
      justifyContent: 'center',
      borderRadius: 6.5 / 2,
    },
    slotInnerRowContainer: {
      flex: 1,
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    slotHeading: {
      fontSize: 16,
      color: theme.thunder100,
    },
    slotSubheading: {
      fontSize: 14,
      color: theme.thunder80,
    },
    selectedSlotHeading: {
      fontSize: 16,
      color: theme.white,
    },
    selectedSlotSubheading: {
      fontSize: 14,
      color: hexToRGBa(theme.white, 0.8),
    },
    locationContainer: {
      flexDirection: 'row',
    },
    icon: {
      width: isPad ? 20 : 15,
      height: isPad ? 20 : 15,
      marginRight: 5,
      resizeMode: 'contain',
    },
  });

  return { theme, isDarkMode, styles };
}
