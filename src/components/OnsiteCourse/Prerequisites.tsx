import React from 'react';
import { StyleSheet } from 'react-native';
import { CourseHeading, Spacer, SubItem } from '../index';
import AssetsImages from '../../theme/AssetsImages';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import Colors from '../../theme/Colors';
import { LabelConfig } from '@theme';

const Prerequisites = ({ prerequisites }) => {
  const { Prerequisites } = LabelConfig.courseDetail;

  return (
    <>
      <CourseHeading title={Prerequisites} />

      {prerequisites?.map((it) => (
        <>
          <Spacer height={15} />
          <SubItem
            icon={AssetsImages.tick}
            label={it?.title}
            textStyle={styles.textStyle}
            iconStyle={styles.iconStyle}
          />
        </>
      ))}
    </>
  );
};

export default Prerequisites;

const styles = StyleSheet.create({
  textStyle: {
    fontSize: isPad ? fontSize.semiLarge : fontSize.medium,
    color: Colors.black,
    fontFamily: fonts.regular,
    marginLeft: 5,
  },
  iconStyle: {
    width: 35,
    height: 35,
    resizeMode: 'contain',
  },
});
