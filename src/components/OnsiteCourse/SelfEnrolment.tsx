import React, { useState } from 'react';
import { Platform, StyleSheet } from 'react-native';
import { useMutation } from '@apollo/client';
import { Colors, fonts, fontSize, LabelConfig } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { lineHeight } from '@utils/constants';
import crashReportLogger from '@utils/crashlyticsLogger';
import AnalyticsConstants from 'analytics/AnalyticsConstants';
import { analyticsService } from 'analytics/AnalyticsService';
import { isEmpty } from 'lodash';
import { Toast } from 'native-base';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { selfEnrolmentMutation } from '../../totara/features/enrolment/api';
import { EnrolmentOption } from '../../totara/types/EnrolmentOption';
import { Button } from '../index';

const SelfEnrolmentWidget = ({
  status,
  enrolRefreshing,
  setEnrolRefreshing,
  courseId,
  isEnrolled,
  enrolment: { id, passwordRequired },
  onEnroll,
  onSuccess,
}: {
  status: string;
  isEnrolled: boolean;
  enrolRefreshing: boolean;
  courseId: string;
  enrolment: EnrolmentOption;
  onEnroll: () => void;
  setEnrolRefreshing: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess: () => void;
}) => {
  const { Enroll } = LabelConfig.courseDetail;
  const [selfEnrol] = useMutation(selfEnrolmentMutation);
  const safeArea = useSafeAreaInsets();
  const { isDarkMode } = useTheme();
  const { t } = useTranslation();

  const [enrolmentKeyState, setEnrolmentKeyState] = useState({
    key: undefined,
    isNotValid: false,
  });

  const onEnrolMeTap = async () => {
    if (isEnrolled) {
      onSuccess();
      return;
    }

    setEnrolRefreshing(true);
    onEnroll();
    const missingEnrolmentKey = passwordRequired && isEmpty(enrolmentKeyState?.key);
    if (missingEnrolmentKey) {
      setEnrolmentKeyState({
        ...enrolmentKeyState,
        isNotValid: true,
      });
      return;
    }

    try {
      const response = await selfEnrol({
        variables: {
          input: {
            courseid: courseId,
            instanceid: id,
            password: enrolmentKeyState.key,
          },
        },
      });

      if (!response?.data.mobile_findlearning_enrolment_result?.success) {
        Toast.show({
          text: response?.data.mobile_findlearning_enrolment_result?.msgKey,
          textStyle: { textAlign: 'left' },
        });
      } else {
        await analyticsService.logEvent(AnalyticsConstants.COURSE_ENROLLED, { courseId });
      }
      setEnrolRefreshing(false);
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'SelfEnrolementWidget onEnrollMeTap',
        additionalInfo: 'Mutation error',
      });
      setEnrolRefreshing(false);
    }
  };
  return (
    <Button
      style={[
        // { marginBottom: 15 },
        {
          backgroundColor: isDarkMode ? Colors.activeStateDark : 'black',
        },
      ]}
      onPress={onEnrolMeTap}
      isLoading={enrolRefreshing}
      type="solid"
      withBg
      text={isEnrolled ? t(status == 'Not Yet Started' ? 'Start course' : 'Resume course') : Enroll}
      textStyle={[
        styles.buttonTextStyle,
        {
          color: isDarkMode ? 'black' : 'white',
        },
      ]}
      containerStyle={{ paddingBottom: -safeArea.bottom + 15 }}
    />
  );
};

export default SelfEnrolmentWidget;

const styles = StyleSheet.create({
  btnText: {
    padding: 0,
    paddingTop: Platform.OS == 'ios' ? 0 : 5,
  },
  buttonTextStyle: {
    fontFamily: fonts.medium,
    lineHeight: lineHeight(fontSize.mini),
    fontSize: fontSize.medium,
    padding: 0,
    paddingTop: Platform.OS == 'ios' ? 0 : 5,
  },
});
