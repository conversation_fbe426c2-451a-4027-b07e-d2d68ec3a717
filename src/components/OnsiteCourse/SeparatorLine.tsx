import Colors from '@theme/Colors';
import { useTheme } from '@theme/ThemeProvider';
import React from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

type SeparatorLineType = {
  style?: StyleProp<ViewStyle>;
};

const SeparatorLine = ({ style }: SeparatorLineType) => {
  const { theme } = useTheme();
  return <View style={[styles.view, style, {
    backgroundColor: theme.borderBackground
  }]} />;
};

export default SeparatorLine;

const styles = StyleSheet.create({
  view: {
    height: 1,
    backgroundColor: Colors.borderGray,
  },
});
