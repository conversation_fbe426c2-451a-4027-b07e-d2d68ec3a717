import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import React from 'react';
import { I18nManager, Platform, Pressable, StyleSheet, View } from 'react-native';
import { SubItem, TextView } from '../index';
import { AssetsImages } from '@theme';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { convertNumbersToArabicNumerals } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';
import { getTimeWithSystemTZ, formatNumbers } from '@utils/AppUtils';

type DateItem = {
  id?: string;
  day?: string;
  date?: string;
  month?: string;
  slots?: string | [];
  time?: string;
  index?: number;
  isDisabled?: boolean;
  isDateSelected?: boolean;
  isSelectedSlot?: boolean;
  hideDate?: boolean;
  onPress?: (item?: any) => void;
  selectedSlot?: { id: string };
};

const SlotDateItem = ({
  day,
  date,
  month,
  time,
  slots,
  index,
  hideDate = false,
  isDisabled = false,
  isDateSelected,
  onPress,
  isSelectedSlot,
  selectedSlot,
}: DateItem) => {
  const isHeadingVisible = index == -1 ?? false;
  const { t } = useTranslation();
  const { isDarkMode, theme } = useTheme();

  return (
    <View style={[styles.dateItem, isHeadingVisible && styles.marginTop, isDisabled && styles.disabled]}>
      <View style={[styles.dateColumn, hideDate && {opacity: 0}]}>
        <TextView
          style={[
            styles.dayText,
            isHeadingVisible && styles.heading,
            isDarkMode && { color: theme.textColor },
          ]}
          text={day || ''}
        />
        <TextView
          style={[styles.dateText, isDarkMode && { color: theme.textColor }]}
          text={I18nManager.isRTL ? formatNumbers(date || '') : date || ''}
        />
        <TextView
          style={[styles.dayText, isDarkMode && { color: theme.textColor }]}
          text={month?.toString() || ''}
        />
      </View>

      <View style={styles.lineContainer}>
        {!isHeadingVisible && (
          <View style={!hideDate
            ? [styles.circle, (isSelectedSlot || isDateSelected) && [styles.selectedSlot, isDarkMode && { borderColor: Colors.progressBarDarkColor }]]
            : [styles.circleLine, isDarkMode && { backgroundColor: Colors.progressBarDarkColor }]
          }>
            {(isSelectedSlot || isDateSelected) && !hideDate && <View style={[styles.selectedSlotCircle, isDarkMode && { backgroundColor: Colors.progressBarDarkColor }]} />}
          </View>
        )}
        <View style={[styles.line, { backgroundColor: theme.borderBackground }]} />
      </View>
      <View style={[styles.slotsColumn, !hideDate && styles.separator]}>
        {isHeadingVisible ? (
          <TextView
            style={[
              styles.dateText,
              styles.heading,
              { textAlign: 'left' },
              isDarkMode && { color: theme.textColor },
            ]}
            text={'Available time slot'}
          />
        ) : typeof slots == 'string' ? (
          <Slot {...{ isSelectedSlot, onPress, slots, time, meeting_link: undefined, isDisabled }} />
        ) : (
          <>
            {slots?.map((s: any, i) => (
              <Slot
                {...{
                  isSelectedSlot: s.slot_id == selectedSlot?.id,
                  onPress: () => {
                    onPress && onPress(s);
                  },
                  slots: t('Session') + ' ' + convertNumbersToArabicNumerals(i + 1),
                  time: getTimeWithSystemTZ(s?.time_from, 'HH', 'mma') + ' - ' + getTimeWithSystemTZ(s.time_to, 'HH', 'mma'),
                  meeting_link: s.meeting_link,
                  location: s.location,
                  isDisabled
                }}
              />
            ))}
          </>
        )}
      </View>
    </View>
  );
};

export default SlotDateItem;

export const Slot = ({
  isSelectedSlot,
  onPress,
  slots,
  time,
  meeting_link,
  location,
  style,
  slot_date,
  isDisabled = false,
}) => {
  const { isDarkMode, theme } = useTheme();

  return (
    <Pressable
      style={[
        [styles.slotContainer, isDarkMode && { backgroundColor: theme.bgDark }],
        isSelectedSlot && {
          backgroundColor: isDarkMode ? theme.bgLightDark : Colors.black,
        },
        style,
      ]}
      disabled={isDisabled}
      onPress={onPress}
    >
      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <TextView
          style={[[styles.slotText, isDarkMode && { color: theme.textColor }], isSelectedSlot && { color: Colors.white }]}
          text={slots || 'Event'}
        />
        <TextView
          style={[[styles.timeText, isDarkMode && { color: Colors.dateColorForDark }], isSelectedSlot && { color: Colors.white }]}
          text={time || ''}
        />
      </View>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <View>
          {meeting_link && (
            <SubItem
              isLogo
              icon={AssetsImages.team}
              label={'Via Microsoft teams'}
              containerStyle={{ marginStart: 0, marginTop: 5 }}
              textStyle={isSelectedSlot && { color: Colors.white }}
            />
          )}

          {location && (
            <SubItem
              icon={AssetsImages.location}
              label={location ? location : 'meeting_link'}
              containerStyle={{ marginStart: -2, marginTop: 5 }}
              textStyle={isSelectedSlot && { color: Colors.white }}
              iconStyle={isSelectedSlot && { tintColor: Colors.white }}
            />
          )}
        </View>
        {slot_date ? (
          <TextView
            style={[
              [styles.timeText, isDarkMode && { color: Colors.dateColorForDark }],
              isSelectedSlot && { color: Colors.white },
              { letterSpacing: undefined, marginTop: 5 },
            ]}
            text={slot_date || ''}
          />
        ) : null}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  dateItem: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  dateColumn: {
    width: 45,
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
  lineContainer: {
    width: 30,
    alignItems: 'center',
    position: 'relative',
    marginBottom: -5,
  },
  circle: {
    width: 13,
    height: 13,
    marginTop: 3,
    borderRadius: 13 / 2,
    borderColor: Colors.lgray,
    borderWidth: 2,
    position: 'absolute',
    top: 0,
  },
  circleLine: {
    width: 2,
    height: 15,
    backgroundColor: Colors.lgray,
    position: 'absolute',
  },
  line: {
    width: 2,
    backgroundColor: '#ddd',
    flex: 1,
    marginTop: 15,
  },
  separator: {paddingTop: 0},
  slotsColumn: {
    flex: 1,
    paddingLeft: 10,
    paddingBottom: 30,
  },
  dayText: {
    fontFamily: fonts.regular,
    color: Colors.ldark,
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
  },
  dateText: {
    marginVertical: Platform.OS == 'ios' ? -2 : -5,
    fontFamily: fonts.medium,
    color: Colors.black,
    fontSize: isPad ? fontSize.xxxxlarge : fontSize.xxxxxlarge,
  },
  slotContainer: {
    paddingVertical: 7,
    paddingHorizontal: 20,
    backgroundColor: Colors.slotBg,
    borderWidth: 1,
    borderColor: Colors.inputBackground,
    borderRadius: 5,
    marginVertical: 5,
  },
  slotText: {
    fontSize: isPad ? fontSize.medium : fontSize.xxmini,
    fontFamily: fonts.medium,
    letterSpacing: -0.2,
    color: Colors.black,
  },
  timeText: {
    fontSize: isPad ? fontSize.medium : fontSize.xxmini,
    fontFamily: fonts.medium,
    letterSpacing: -0.2,
    color: Colors.black,
  },

  heading: {
    fontSize: fontSize.medium,
    marginVertical: 0,
    fontFamily: fonts.medium,
    color: Colors.black,
    marginTop: 10,
  },
  marginTop: { marginTop: 10 },
  selectedSlot: {
    borderColor: Colors.green,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedSlotCircle: {
    height: 6.5,
    width: 6.5,
    backgroundColor: Colors.green,
    alignSelf: 'center',
    justifyContent: 'center',
    borderRadius: 6.5 / 2,
  },
});
