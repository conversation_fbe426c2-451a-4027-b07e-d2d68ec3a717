import React from 'react';
import { Image, ImageStyle, Platform, StyleProp, TextStyle } from 'react-native';
import { StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { useTheme } from '../../theme/ThemeProvider';

type SubItemType = {
  icon?: number;
  label?: string;
  heading?: string;
  iconStyle?: StyleProp<ImageStyle>;
  textStyle?: StyleProp<TextStyle>;
  headingStyle?: StyleProp<TextStyle>;
  containerStyle?: StyleProp<TextStyle>;
  children?: JSX.Element;
  customView?: JSX.Element;
  isLogo?: boolean;
};

const SubItem = ({
  customView,
  icon,
  label,
  heading,
  iconStyle,
  textStyle,
  headingStyle,
  containerStyle,
  children,
  numberOfLines,
  isLogo,
}: SubItemType) => {
  const { theme, isDarkMode } = useTheme();

  return (
    <View style={[styles.row, containerStyle]}>
      {customView
        ? customView
        : icon && (
            <Image
              source={icon}
              resizeMode="contain"
              style={[
                styles.icon,
                iconStyle,
                isDarkMode && !isLogo && { tintColor: Colors.dateColorForDark },
              ]}
            />
          )}
      <View>
        {heading && (
          <TextView
            text={heading}
            style={[styles.label, headingStyle, isDarkMode && { color: theme.textColor }]}
          />
        )}
        {label && (
          <TextView
            numberOfLines={numberOfLines ? numberOfLines : 2}
            text={label}
            style={[styles.label, textStyle, isDarkMode && { color: Colors.dateColorForDark }]}
          />
        )}
        {children && children}
      </View>
    </View>
  );
};

export default SubItem;

const styles = StyleSheet.create({
  icon: {
    width: isPad ? 20 : 15,
    height: isPad ? 20 : 15,
    marginRight: 5,
    resizeMode: 'contain',
  },
  row: {
    marginRight: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: fontSize.h7,
    textAlign: 'left',
    color: Colors.lightBlackText,
    fontFamily: fonts.thin,
  },
});
