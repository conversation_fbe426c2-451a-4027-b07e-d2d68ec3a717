import AssetsImages from '@theme/AssetsImages';
import Colors from '@theme/Colors';
import React from 'react';
import {
  Platform,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { Spacer, SubItem } from '../index';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import Icons, { IconsType } from '../../theme/Icons';
import moment from 'moment';
import CompletedIcon from './CompletedIcon';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import { IS_ARABIC, LOCALE } from '@utils/localization';

type TimeSlot = {
  isCompleted?: boolean;
  date: string;
  time: string;
  selected?: boolean;
  style?: StyleProp<ViewStyle>;
  type?: 'simple' | 'cohort';
  isCohort?: boolean;
  isRequested?: boolean;
  isEditable?: boolean;
  onMorePress?: () => void;
};

const TimeSlot = ({
  isCompleted,
  date,
  time,
  selected = false,
  style,
  type,
  isEditable,
  isRequested = false,
  endDate,
  isCohort = type === 'cohort',
  onMorePress,
}: TimeSlot) => {
  const { styles, theme, isDarkMode } = useStyles();
  const { t } = useTranslation();

  if (endDate && endDate != '' && type != 'simple') {
    endDate = moment(endDate).locale(LOCALE).format('ddd, DD MMM YYYY');
  }

  const requestedLabel = isRequested ? t(isCohort ? 'Requested cohort' : 'Requested slot') : null;

  const format = 'ddd DD MMM YYYY';
  const startDate = moment(date ?? "", format).locale(LOCALE);

  const formattedDayInWeek = startDate.format('dddd');
  const formattedDayInWeekShort = startDate.format('ddd');
  const formattedDay = startDate.format('DD');
  const formattedMonth = startDate.format('MMMM');
  const formattedYear = startDate.format('YYYY');
  const formattedStart = `${formattedDayInWeek}, ${formattedDay} ${formattedMonth} ${formattedYear}`;
  const formattedStartShort = IS_ARABIC
    ? `${formattedDayInWeekShort}, ${formattedDay} ${formattedMonth} ${formattedYear}`
    : date
  
  const cleaned = time.replace(/"/g, '').trim();
  const [startTimeEng, endTimeEng] = cleaned.split('-').map(str => str.trim());

  const timeFormatted = `${startTimeEng} - ${endTimeEng}`;

  return (
    <View
      style={[
        [styles.container, isCompleted && { backgroundColor: Colors.completeBox }],
        style,
        isDarkMode && { backgroundColor: theme.bgDark },
      ]}
    >
      {selected ? (
        <SubItem
          isLogo
          icon={AssetsImages.timeslote}
          heading={t('Selected slot:')}
          label={`${formattedStartShort}\n${timeFormatted}`}
          textStyle={styles.txt}
          headingStyle={styles.heading}
          iconStyle={[styles.image, { height: 50, width: 50, marginLeft: 10 }]}
        />
      ) : type == 'simple' ? (
        <>
          <SubItem
            icon={AssetsImages.cal}
            label={date}
            textStyle={styles.dayTimeTxt}
            iconStyle={[styles.icon]}
          />
          <Spacer height={5} />
          <SubItem
            icon={AssetsImages.clock}
            label={time}
            textStyle={styles.dayTimeTxt}
            iconStyle={styles.icon}
          />
        </>
      ) : (
        <>
          <View style={[styles.row, isEditable && styles.rowBetween]}>
            <View>
              <SubItem
                customView={
                  isCompleted ? (
                    <CompletedIcon style={{ marginLeft: 3, marginRight: 5 }} />
                  ) : undefined
                }
                isLogo
                icon={AssetsImages.timeslote}
                label={
                  isCompleted
                    ? t('You have attended this course on')
                    : requestedLabel || ((type === 'cohort' && isCohort)
                      ? t('Your Cohort Details')
                      : t('Your slot details'))
                }
                textStyle={styles.txt}
                iconStyle={[styles.image, { width: 30, height: 30 }]}
              />
            </View>

            {isEditable && !isCompleted && (
              <TouchableOpacity style={styles.eplisis} onPress={onMorePress}>
                <Icons
                  color={isDarkMode ? 'white' : Colors.black}
                  type={IconsType.Ionicons}
                  name={'ellipsis-vertical'}
                  size={25}
                />
              </TouchableOpacity>
            )}
          </View>

          <Spacer height={15} />

          <SubItem
            icon={AssetsImages.cal}
            label={formattedStart}
            textStyle={styles.dayTimeTxt}
            iconStyle={[styles.icon, isDarkMode && { tintColor: 'white' }]}
          />
          <Spacer height={10} />
          <SubItem
            icon={AssetsImages.clock}
            label={timeFormatted}
            textStyle={styles.dayTimeTxt}
            iconStyle={[styles.icon, isDarkMode && { tintColor: 'white' }]}
          />
        </>
      )}
    </View>
  );
};

export default TimeSlot;

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    heading: {
      marginLeft: 5,
      fontSize: isPad ? fontSize.medium : 14,
    },
    container: {
      backgroundColor: Colors.cream,
      // height: 140,
      // height: Platform.OS == "android" ? 150 : 140,
      justifyContent: 'space-between',
      marginHorizontal: 20,
      borderRadius: 20,
      marginTop: 10,
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    image: {
      width: 35,
      height: 35,
      resizeMode: 'contain',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    txt: {
      marginLeft: 5,
      marginTop: 8,
      fontSize: 18,
      color: theme.thunder100,
      fontFamily: fonts.regular,
    },
  
    dayTimeTxt: {
      marginLeft: 10,
      fontSize: fontSize.small,
      color: Colors.black,
      fontFamily: fonts.regular,
    },
  
    icon: {
      width: 18,
      height: 18,
      marginLeft: 7,
      resizeMode: 'contain',
      tintColor: Colors.black,
      marginTop: Platform.OS == 'android' ? 3 : 1,
    },
  
    eplisis: {
      marginRight: -5,
    },
  
    rowBetween: { justifyContent: 'space-between' },
  
    greenTick: {
      width: 30,
      height: 30,
      backgroundColor: Colors.green,
      borderRadius: 30 / 2,
      marginTop: 5,
      marginBottom: 5,
      alignSelf: 'center',
      justifyContent: 'center',
      alignItems: 'center',
    },
    greenTickImage: {
      width: 15,
      height: 15,
      tintColor: Colors.white,
      resizeMode: 'contain',
    },
  });

  return { styles, theme, isDarkMode };
}


