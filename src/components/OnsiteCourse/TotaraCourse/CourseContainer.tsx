import Colors from '../../../theme/Colors';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { IconButton } from 'react-native-paper';
import Icons, { IconsType } from '../../../theme/Icons';

type CourseContainer = {
  children: JSX.Element;
  btnLink?: string;
  isEnrolled?: boolean;
  onPress?: () => void;
};

const CourseContainer = ({ isEnrolled, children, btnLink, onPress }: CourseContainer) => (
  <View style={styles.container}>
    {children}
    {btnLink && isEnrolled && (
      <IconButton
        icon={() => {
          return (
            <Icons color={'black'} type={IconsType.Octicons} name={'link-external'} size={18} />
          );
        }}
        size={17}
        style={styles.btn}
        onPress={onPress}
      />
    )}
  </View>
);

export default CourseContainer;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 15,
    borderColor: Colors.lgray,
    paddingVertical: 13,
    paddingHorizontal: 15,
    marginTop: 10,
  },
  btn: { position: 'absolute', top: 5, right: 0 },
});
