import Colors from '../../../theme/Colors';
import React from 'react';
import { I18nManager, StyleSheet, View } from 'react-native';
import { TextView } from '../../index';
import fontSize, { isPad } from '../../../utils/fontSize';
import { IconButton } from 'react-native-paper';
import Icons, { IconsType } from '../../../theme/Icons';
import { useTheme } from '@theme/ThemeProvider';
import { commonStyle } from '@utils';

const Week = ({ isEnrolled, name, onPress }) => {
  const { isDarkMode, theme } = useTheme();

  return (
    <View style={styles.weekContainer}>
      <TextView
        numberOfLines={20}
        text={name}
        style={[styles.des, isDarkMode && { color: theme.textColor }]}
      />
      <IconButton
        icon={() => {
          return (
            <Icons
              color={isDarkMode ? 'white' : 'black'}
              type={IconsType.Octicons}
              name={'link-external'}
              size={15}
              style={styles.rtlIcon}
            />
          );
        }}
        size={17}
        disabled={!isEnrolled}
        onPress={onPress}
      />
    </View>
  );
};

export default Week;

const styles = StyleSheet.create({
  des: {
    color: Colors.ldark,
    marginTop: 0,
    fontSize: isPad ? fontSize.medium : fontSize.xxmini,
    marginLeft: 3,
    flex: 1,
    ...commonStyle.textDirection,
  },

  weekContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '98%',
    paddingLeft: 10,
    backgroundColor: Colors.slotBg,
    borderWidth: 1,
    borderColor: Colors.inputBackground,
    borderRadius: 5,
    marginTop: 5,
    minHeight: 35,
  },

  rtlIcon: {
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
  },
});
