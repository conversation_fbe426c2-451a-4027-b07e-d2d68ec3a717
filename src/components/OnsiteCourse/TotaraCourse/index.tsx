import Colors from '../../../theme/Colors';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { SubItem, TextView } from '../../index';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import AssetsImages from '../../../theme/AssetsImages';
import CourseContainer from './CourseContainer';

const TotaraCourse = ({ isEnrolled, index, title, des, onPress }) => {
  return (
    <>
      <CourseContainer
        btnLink={'Link_here'}
        onPress={onPress}
        isEnrolled={isEnrolled}
        children={
          <SubItem
            icon={AssetsImages.calRound}
            customView={
              <View style={styles.iconContainer}>
                <TextView style={styles.sessionCount} type="h3" text={index.toString()} />
              </View>
            }
            label={des}
            heading={title}
            containerStyle={styles.containerStyle}
            iconStyle={styles.icon}
            headingStyle={styles.text}
            textStyle={[styles.des]}
          />
        }
      />
    </>
  );
};

export default TotaraCourse;

const styles = StyleSheet.create({
  containerStyle: { alignItems: 'flex-start', paddingRight: 50 },
  iconContainer: {
    marginEnd: 10,
    borderRadius: isPad ? 60 : 40,
    width: isPad ? 60 : 40,
    height: isPad ? 60 : 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.creamColor,
  },

  sessionCount: {
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.large : fontSize.medium,
  },

  icon: { width: isPad ? 50 : 40, height: isPad ? 50 : 40 },

  text: {
    fontSize: isPad ? fontSize.large : fontSize.semiSmall,
    fontFamily: fonts.medium,
    color: Colors.black,
  },

  des: {
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    marginTop: 3,
    color: Colors.ldark,
  },
});
