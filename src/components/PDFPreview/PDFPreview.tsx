import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import Colors from '@theme/Colors.tsx';
import { useTheme } from '@theme/ThemeProvider.tsx';
import Pdf from 'react-native-pdf';

type Props = {
  url: string;
};

const PDFPreview = ({ url }: Props) => {
  const { isDarkMode } = useTheme();

  return (
    <View pointerEvents={'none'} style={styles.container}>
      <Pdf
        style={styles.pdfView}
        source={{ uri: url }}
        singlePage
        renderActivityIndicator={() => (
          <ActivityIndicator
            color={isDarkMode ? Colors.white : Colors.black}
            size={'large'}
          />
        )}
        onError={(error) => console.warn('PDF Error:', error)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  loader: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  pdfView: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default PDFPreview;
