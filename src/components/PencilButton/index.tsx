import { hitSlop } from '../../utils/constants';
import AssetsImages from '../../theme/AssetsImages';
import React from 'react';
import {
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  Image,
  StyleProp,
  ViewStyle,
  View,
} from 'react-native';
import { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';

type PencilButtonType = {
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  bgColor?: string;
};

export default ({ onPress, style, bgColor }: PencilButtonType) => {
  const { theme, isDarkMode } = useTheme()
  return (
    <TouchableOpacity onPress={onPress}>
      <View
        style={[
          {
            width: isPad ? 40 : 34,
            height: isPad ? 40 : 34,
            borderRadius: (isPad ? 40 : 34) / 2,
            borderWidth: 1,
            borderColor: theme.borderBackground,
            justifyContent: 'center',
            alignItems: 'center',
          },
          style,

        ]}
      >
        <Image
          style={{
            width: 22,
            height: 22,
            marginBottom: 2,
            marginTop: 0,
            marginLeft: 1,
            marginRight: 2,
            tintColor: theme.textColor
          }}
          source={AssetsImages.edit}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  editIcon: {
    width: 35,
    height: 35,
    marginTop: -50,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
