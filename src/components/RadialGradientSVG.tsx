// @components/RadialGradientSVG.tsx
import React from 'react';
import { StyleSheet } from 'react-native';
import Svg, { Defs, RadialGradient, Rect, Stop } from 'react-native-svg';

const RadialGradientSVG = () => (
  <Svg
    height="100%"
    width="100%"
    style={StyleSheet.absoluteFill}
    pointerEvents="none"
  >
    <Defs>
      {/* Top Right Gradient */}
      <RadialGradient
        id="topRight"
        cx="100%" cy="-20%"
        rx="50%" ry="50%"
        fx="100%" fy="-20%"
        gradientUnits="userSpaceOnUse"
      >
        <Stop offset="0%" stopColor="#D0DCE8" stopOpacity="0.82" />
        <Stop offset="50%" stopColor="#C6D5E5" stopOpacity="0.5" />
        <Stop offset="100%" stopColor="#FFFFFF" stopOpacity="0" />
      </RadialGradient>

      {/* Bottom Left Gradient */}
      <RadialGradient
        id="bottomLeft"
        cx="-20%" cy="100%"
        rx="50%" ry="50%"
        fx="-20%" fy="100%"
        gradientUnits="userSpaceOnUse"
      >
        <Stop offset="0%" stopColor="#D0DCE8" stopOpacity="0.82" />
        <Stop offset="50%" stopColor="#C6D5E5" stopOpacity="0.5" />
        <Stop offset="100%" stopColor="#FFFFFF" stopOpacity="0" />
      </RadialGradient>
    </Defs>

    {/* First layer */}
    <Rect width="100%" height="100%" fill="url(#topRight)" />
    {/* Second layer */}
    <Rect width="100%" height="100%" fill="url(#bottomLeft)" />
  </Svg>
);

export default RadialGradientSVG;
