import Colors from '@theme/Colors';
import React from 'react';
import { Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { TextView } from '../../components';
import { SearchKeyword } from '@api/totaraApis.tsx';
import { fonts, fontSize } from '@utils';
import { useTheme } from '@theme/ThemeProvider';

type Props = {
  data: Array<SearchKeyword>;
  onItemPress: (search: string) => void;
};

const PopularSearches = ({ data, onItemPress }: Props) => {
  const { theme } = useTheme();

  return (
    <ScrollView
      nestedScrollEnabled
      style={styles.scrollViewStyle}
      showsHorizontalScrollIndicator={false}
    >
      <View style={styles.container}>
        {data?.map((res) => {
          return (
            <Pressable
              onPress={() => onItemPress(res.keyword)}
              style={[styles.tabStyle, {
                borderColor: theme.borderBackground
              }]}
              key={res?.id}
            >
              <TextView style={[styles.tabText, { color: theme.textColor }]} text={res?.keyword} />
            </Pressable>
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  tabStyle: {
    borderWidth: 1,
    borderColor: Colors.borderGray,
    borderRadius: 20,
    marginEnd: 7.5,
    marginBottom: 10,
  },
  tabText: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontFamily: fonts.medium,
    fontSize: fontSize.xmini,
    color: Colors.black,
  },
  scrollViewStyle: {
    marginVertical: 20,
    marginStart: 24,
    flexGrow: 1,
  },
});

export default PopularSearches;
