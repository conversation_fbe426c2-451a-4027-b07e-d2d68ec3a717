import AssetsImages from '@theme/AssetsImages';
import Colors from '@theme/Colors';
import React from 'react';
import { FlatList, Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import { TextView } from '../../components';
import { SearchKeyword } from '@api/totaraApis.tsx';
import { useTheme } from '@theme/ThemeProvider';

type Props = {
  data: Array<SearchKeyword>;
  onItemPress: (search: string) => void;
};

const RecentSearches = ({ data, onItemPress }: Props) => {
  const { theme } = useTheme();
  return (
    <View>
      <View style={[styles.itemBorder, {
        borderColor: theme.borderBackground
      }]} />

      <FlatList
        data={data}
        renderItem={({ item, index }) => {
          return (
            <View key={index}>
              <RenderItem onPress={onItemPress} item={item} />
            </View>
          );
        }}
      />
    </View>
  );
};

const RenderItem = ({ item, onPress }) => {
  const { theme } = useTheme();

  return (
    <>
      <TouchableOpacity onPress={() => onPress(item.keyword)} style={styles.listContainer}>
        <Image source={AssetsImages.clockForward} style={styles.imageStyle} />
        <TextView style={[styles.textStyle, { color: theme.textColor }]} text={item.keyword} />
      </TouchableOpacity>
      <View style={[styles.itemBorder, {
        borderColor: theme.borderBackground
      }]} />
    </>
  );
};

export default RecentSearches;

const styles = StyleSheet.create({
  itemBorder: {
    borderTopWidth: 1,
    marginVertical: 15,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageStyle: {
    width: 20,
    height: 20,
  },
  textStyle: { paddingHorizontal: 8 },
});
