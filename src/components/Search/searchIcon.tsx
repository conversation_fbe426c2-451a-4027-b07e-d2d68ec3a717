import React from 'react';
import { I18nManager, Pressable, StyleSheet, TextInput, TouchableOpacity, View, ViewStyle } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import Icons, { IconsType } from '@theme/Icons';
import { useNavigation } from '@hooks';
import { Colors, fonts } from '@theme';
import { useTranslation } from 'react-i18next';
import { EScreenContextTypes } from '@screens/Communities';

type Props = {
  style?: ViewStyle;
  size?: number;
  variant?: 'icon' | 'input';
  text?: string;
  searchNetwork?: boolean;
};

const SearchIcon = ({
  style,
  size = 22,
  variant = 'icon',
  text,
  searchNetwork = false,
}: Props) => {
  const { isDarkMode, theme, styles } = useStyles();
  const navigation = useNavigation();
  const { t } = useTranslation();

  const handleCourseNavigation = () => navigation?.navigate('Search');

  const handleCommunitiesNavigation = () => navigation?.navigate('MyNetworkCommunities', {
    screenContext: EScreenContextTypes.ALL_COMMUNITIES,
  });

  return variant === 'icon' ? (
    <Pressable
      onPress={searchNetwork ? handleCommunitiesNavigation : handleCourseNavigation}
      style={style ? {...style, ...styles.iconStyle} : styles.iconStyle}
    >
      <Icons
        type={IconsType.AntDesign}
        name={'search1'}
        size={size}
        color={theme.textColor}
      />
    </Pressable>
  ) : (
    <Pressable style={styles.searchContainer} onPress={searchNetwork ? handleCommunitiesNavigation : handleCourseNavigation}>
      <View
        style={[styles.searchView, isDarkMode && { borderColor: 'rgba(255, 255, 255, 0.2)' }]}
        pointerEvents="none"
      >
        <TextInput
          textAlign={I18nManager.isRTL ? 'right' : 'left'}
          placeholderTextColor={theme.thinder40}
          style={styles.input}
          placeholder={t(text ?? 'What do you want to learn?')}
          pointerEvents="none"
        />
        <TouchableOpacity>
          <Icons
            type={IconsType.AntDesign}
            name={'search1'}
            size={22}
            style={{ color: isDarkMode ? Colors.activeStateDark : '#000000' }}
          />
        </TouchableOpacity>
      </View>
    </Pressable>
  );
};

export default SearchIcon;

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    searchContainer: {
      paddingHorizontal: 20,
      backgroundColor: 'transparent',
      overflow: 'hidden',
    },
    iconStyle: {
      marginTop: 2,
    },
    searchView: {
      borderWidth: 1,
      height: 55,
      borderRadius: 15,
      borderColor: 'rgba(0, 0, 0, 0.35)',
      marginBottom: 24,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      padding: 20,
      paddingVertical: 5,
      backgroundColor: theme.white,
    },
    input: {
      flex: 1,
      color: theme.thunder100,
      fontFamily: fonts.medium,
    },
  });

  return { theme, isDarkMode, styles };
}
