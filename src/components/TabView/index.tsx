import React, { ReactNode, useState, useEffect } from 'react';
import { Animated, Dimensions, Text, TouchableOpacity, View } from 'react-native';
import useStyles from './styles';
import { useTranslation } from 'react-i18next';
import { I18nManager } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';

type CustomTabBarStyles = {
  borderBottomWidth?: number;
};

type Props = {
  tabs: string[];
  children: ReactNode;
  activeTab: number;
  onChangeActiveTab: (x: number) => void;
  customTabBarStyles?: CustomTabBarStyles;
  disabled?: boolean;
};

const CustomTabView = ({
  tabs,
  children,
  activeTab,
  onChangeActiveTab,
  customTabBarStyles,
  disabled = false,
}: Props) => {
  const { styles, theme } = useStyles({ tabsLength: tabs.length });
  const { t } = useTranslation();
  const { isDarkMode } = useTheme();


  const handleTabPress = (index: number) => {
    onChangeActiveTab(index);
  };

  const containerStyle = {
    ...styles.tabBar,
    flexDirection: 'row',
    ...customTabBarStyles,
    borderBottomColor: theme.borderBackground,
  };

  return (
    <View style={styles.tabViewContainer}>
      <View style={containerStyle}>
        {tabs.map((tab, index) => {
          return (
            <TouchableOpacity
              key={index}
              style={[styles.tab, { borderBottomWidth: 2, borderColor: activeTab === index ? theme.thunder100 : 'transparent' }]}
              onPress={() => handleTabPress(index)}
              disabled={disabled}
            >
              <Text style={[styles.tabText, activeTab === index && [styles.activeTabText]]}>
                {t(tab)}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
      <View style={styles.tabContent}>{children}</View>
    </View>
  );
};

export default CustomTabView;
