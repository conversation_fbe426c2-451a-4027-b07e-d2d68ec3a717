import { Dimensions, StyleSheet } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import fonts from '@utils/fonts';
import { fontSize } from '@theme';

const { width } = Dimensions.get('window');

const useStyles = ({ tabsLength = 3 }: { tabsLength?: number }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    tabViewContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: 20,
      backgroundColor: 'transparent',
    },
    tabBar: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      width: '100%',
      borderBottomWidth: 1,
      borderBottomColor: theme.textColor,
      position: 'relative',
    },
    tab: {
      paddingVertical: 15,
      width: width / tabsLength,
      alignItems: 'center',
    },
    tabText: {
      fontSize: fontSize.medium,
      fontFamily: fonts.regular,
      color: theme.textColor,
      zIndex: 1,
    },
    activeTabText: {
      color: theme.textColor,
      fontFamily: fonts.semiBold
    },
    underline: {
      height: 2,
      backgroundColor: theme.textColor,
      position: 'absolute',
      bottom: 0,
    },
    tabContent: {
      width: '100%',
      backgroundColor: theme.backgroundColor,
      paddingBottom: 70,
    },
  });

  return { styles, theme };
};

export default useStyles;
