import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ImageBackground,
  Platform,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { Avatar, TextView } from '../index';
import BottomSheet from '../bottomSheet';
import { AssetsImages, Colors, Dimen, fonts, fontSize } from '../../../src/theme';
import { updateProfile, updateWFMField } from '../../totara/actions/getWFMProfileAction';
import commonStyle from '../../utils/commonStyle';
import {
  cameraList,
  pickImage,
  pickImageFromCamera,
} from '../../utils/constants';
import { RootState } from '@totara/reducers';
import { useTheme } from '@theme/ThemeProvider';

interface UserAvatarProps {
  isEditable?: boolean;
  isAbsolute?: boolean;
}

const UserAvatar = ({
  isEditable = true,
  isAbsolute = true,
}: UserAvatarProps) => {
  const dispatch = useDispatch();
  const { styles, theme } = useStyles();
  const profile = useSelector((state: RootState) => state?.getWFMProfile);
  const [, setFileName] = useState('');
  const navigation = useNavigation();
  const [imageOptions, setImageOptions] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [uri, setUri] = useState('');
  const data = useSelector((state: RootState) => state.getWFMProfile.talentProfile);
  const token = useSelector((state: RootState) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const talentProfile = useSelector((state: RootState) => state?.getWFMProfile.talentProfile);
  const userProfile = useSelector((state: RootState) => state?.getWFMProfile);


  const onChange = (fields) => {
    if (profile?.talentProfile?.id !== undefined) {
      dispatch(
        updateProfile(
          profile?.talentProfile?.id,
          fields,
          profile?.response?.liferayaccesstoken,
          navigation,
        ),
      );
    }
  };

  const toggleVisibility = async () => setImageOptions(!imageOptions);

  const navigateToFun = (item) => () => {
    toggleVisibility();
    setTimeout(() => {
      if (item.id === '1') navigation.navigate('ImageViewer', { uri });
      if (item.id == '2') openCameraCropper();
      if (item.id == '3') openGallery();
    }, 500);
  };

  const openGallery = async () => {
    setLoading(true);
    const response = await pickImage(token, getFile, 'profilepicture', data?.response?.idn);
    getFile(response);
  };

  const openCameraCropper = async () => {
    setLoading(true);
    const response = await pickImageFromCamera(
      token,
      getFile,
      'profilepicture',
      data?.response?.idn,
    );
    getFile(response);
  };

  const getFile = (data) => {
    if (data?.fileName !== talentProfile?.picture) {
      onChange && onChange({ picture: data?.fileName, noSuccessMessage: true });
    }
    setFileName(data?.fileName);
    dispatch(updateWFMField({ key: 'imageUrl', value: data?.base64 }));
    setLoading(false);
  };

  useEffect(() => {
    if (userProfile?.response?.imageUrl) {
      setUri(userProfile?.response?.imageUrl);
    }
  }, [userProfile?.response?.imageUrl]);

  return (
    <>
      <TouchableOpacity
        style={[styles.avatarContainer, isAbsolute && styles.avatarContainerWithOffset]}
        disabled={isEditable}
        onPress={navigateToFun({id: '1'})}
      >
        <ImageBackground style={styles.linearGradient} source={AssetsImages.avatarBorder}>
          <Avatar
            containerStyles={styles.avatarBorder}
            onChange={(url) => onChange({ picture: url, noSuccessMessage: true })}
            editable={false}
          />
        </ImageBackground>
        {isEditable && (
          <TouchableOpacity onPress={toggleVisibility} style={styles.editContainer}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator style={styles.loading} color={theme.textColor} size={'small'} />
              </View>
            ) : (
              <Image style={{ width: 30, height: 30, zIndex: 1 }} source={AssetsImages.addImage} />
            )}
          </TouchableOpacity>
          )}
      </TouchableOpacity>

      {isEditable && (
        <BottomSheet
          visiblity={imageOptions}
          setVisibility={setImageOptions}
          children={
            <View style={styles.sheetContainer}>
              {cameraList?.map((item, index) => {
                if (index === 0) {
                  if (!uri) {
                    return null
                  }
                  else {
                    return (
                      <TouchableOpacity
                        onPress={navigateToFun(item)}
                        key={item?.title}
                        style={[styles.missingContainer, {
                          borderBottomColor: theme.borderBackground
                        }]}
                      >
                        <View style={commonStyle.rowAlign}>
                          <Image source={item.icon} style={[styles.icon, { tintColor: theme.tintColor }]} />
                          <TextView style={[styles.missingSectionText, { color: theme.textColor }]} text={item?.title} />
                        </View>
                      </TouchableOpacity>
                    );
                  }
                }
                return (
                  <TouchableOpacity
                    onPress={navigateToFun(item)}
                    key={item?.title}
                    style={[styles.missingContainer, {
                      borderBottomColor: theme.borderBackground
                    }]}
                  >
                    <View style={commonStyle.rowAlign}>
                      <Image source={item.icon} style={[styles.icon, { tintColor: theme.tintColor }]} />
                      <TextView style={[styles.missingSectionText, { color: theme.textColor }]} text={item?.title} />
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          }
        />
      )}
    </>
  );
};

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    avatarContainer: {
      overflow: 'visible',
      width: 120,
    },
    avatarContainerWithOffset: {
      top: Platform.OS == 'ios' ? -Dimen.height * 0.19 : -Dimen.height * 0.2,
      bottom: 0,
      left: 20,
    },
    missingSections: {
      backgroundColor: isDarkMode ? theme.bgDark : 'transparent',
      paddingHorizontal: 20,
      paddingBottom: 15,
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatarBorder: {},
    linearGradient: {
      borderRadius: 70,
      width: 115,
      height: 115,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 0,
    },
    editContainer: {
      position: 'absolute',
      borderColor: Colors.borderGray,
      right: 0,
      bottom: 11,
      zIndex: 1000000000,
    },
    missingSectionText: {
      textTransform: 'capitalize',
      fontFamily: fonts.medium,
      fontSize: fontSize.semiMedium,
      marginVertical: 10,
    },
    icon: {
      width: 20,
      height: 20,
      resizeMode: 'contain',
      marginRight: 10,
      marginTop: 2,
    },
    sheetContainer: {
      paddingHorizontal: 20,
      paddingBottom: 60,
    },
    missingContainer: {
      borderBottomColor: '#f5f5f5',
      paddingTop: 15,
      borderBottomWidth: 1,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    loadingContainer: {
      borderColor: theme.textColor,
      borderWidth: 1,
      borderRadius: 20,
    },
    loading: {
      padding: 2,
    },
  });

  return { styles, theme, isDarkMode };
};


export default UserAvatar;
