import { Text, View } from 'native-base';
import React from 'react';
import { I18nManager, Image, Pressable, StyleSheet, TouchableOpacity } from 'react-native';
import { TextView } from '../common';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import { AssetsImages, Colors, Icons } from '../../theme';
import { IconsType } from '@theme/Icons';
import { useNavigation } from '@hooks';
import SubItem from '@components/OnsiteCourse/SubItem';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import { FavItem } from '@components/learningHome';
import FastImage from 'react-native-fast-image';
import {
  convertNumbersToArabicNumerals,
  isOnlyNumbers,
  lineHeightForAndroidLTR, navigateToCourseDetail,
  widgetPadding,
} from '@utils/constants';
import {getLatestCourseDetails} from "@api/totaraApis.tsx";
import {useSelector} from "react-redux";
import {useSession} from "@totara/core";

const AICoursesListItem = ({ image, title, onPressMore, item }) => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const isProgram = item?.programid ? true : false;
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const { apiToken } = useSession();
  const onCourseDetail = async () => {
    const course  = await getLatestCourseDetails({
      courseId: item?.courseid,
      apiToken,
      userId: talentProfile?.id,
    });
    if (course) {
      navigateToCourseDetail({...course,courseid: course?.courseId,type: "course"}, navigation, false);
    }
  };

  const logo = item?.providerlogo ? { uri: item?.providerlogo } : undefined;
  const durationExtension = item?.duration
    ? `${Number(item?.duration) > 1 ? t('hours') : t('hour')}`
    : undefined;

  const countActivites = item?.lessons ? item?.lessons : item?.course_count;
  const isItemTypeProgram = item?.component_type === "Program";
  const { isDarkMode } = useTheme()
  return (
    <Pressable onPress={onCourseDetail} style={styles.coursesItem}>
      <View style={styles.leftContainer}>
        <FastImage
          source={
            image
              ? {
                uri: image,
                priority: FastImage.priority.high,
              }
              : AssetsImages.placeHolderCourseImage
          }
          style={styles.image}
          resizeMode="cover"
        />
      </View>
      <View style={styles.centerContainer}>
        <View>
          <TextView
            numberOfLines={2}
            style={[
              {
                paddingBottom: 5,
                textAlign: 'left',
                fontFamily: fonts.medium,
                fontSize: fontSize.h4,
              },
              lineHeightForAndroidLTR,
              { color: theme.textColor }
            ]}
            text={title}
          />
        </View>
        <View style={{ flexDirection: 'row', marginTop: 8 }}>
          {durationExtension && (
            <SubItem
              label={
                item?.duration && !isOnlyNumbers(item?.duration)
                  ? item?.duration
                  : `${convertNumbersToArabicNumerals(item?.duration)} ${durationExtension}`
              }
              icon={AssetsImages.clock}
              textStyle={{ maxWidth: 100 }}
            />
          )}

          {countActivites > 0 ? (
            <SubItem
              label={`${convertNumbersToArabicNumerals(countActivites)} ${Number(countActivites) > 1 ? t((isItemTypeProgram ? 'courses' : 'activities')) : t(isItemTypeProgram ? 'course' : 'activity')}`}
              icon={AssetsImages.agenda}
              textStyle={{ maxWidth: 100 }}
            />
          ) : null}
          {item?.rating && item?.rating > 0 ? (
            <View style={styles.clockTime}>
              <Image source={AssetsImages.ratingStar} style={styles.star} />
              <TextView
                numberOfLines={1}
                style={[styles.ratingText, { color: theme.textColor }]}
                type="h5"
                text={convertNumbersToArabicNumerals(item?.rating)}
              />
            </View>
          ) : null}
        </View>
        {item?.creator ? (
          <SubItem
            label={item?.creator}
            textStyle={{ marginTop: 8 }}
          />
        ) : null}
      </View>
      {!isProgram && (
        <View style={styles.rightContainer}>
          <FavItem
            style={styles.favItem}
            color={theme.textColor}
            courseid={item?.courseid ? item?.courseid : item.id}
          />
          <TouchableOpacity onPress={() => onPressMore && onPressMore()}>
            <Icons
              color={isDarkMode ? 'white' : Colors.black}
              size={18}
              type={IconsType.Entypo}
              style={styles.viewMore}
              name={'dots-three-vertical'}
            />
          </TouchableOpacity>
        </View>
      )}
    </Pressable>
  );
};
export default AICoursesListItem;

const styles = StyleSheet.create({
  coursesItem: {
    flex: 1,
    marginVertical: 15,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  image: {
    borderRadius: 16,
    width: 73,
    height: 73,
  },
  favItem: {
    top: -8,
  },
  leftContainer: {
    marginEnd: widgetPadding,
  },
  centerContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 1
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    top: I18nManager.isRTL ? 8 : 0
  },
  ratingText: {
    color: Colors.ldark,
    fontSize: isPad ? fontSize.medium : fontSize.xxmini,
    fontFamily: fonts.regular,
  },
  clockTime: {
    marginRight: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  clock: {
    width: isPad ? 20 : 15,
    height: isPad ? 20 : 15,
    marginRight: 5,
  },
  provider: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  providerImage: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  providerText: {
    fontFamily: fonts.medium,
    color: Colors.black,
    fontSize: fontSize.xmini,
    textAlign: 'center',
  },
  star: {
    width: 13,
    marginTop: -2,
    height: 13,
    marginRight: 5,
  },
  threeDotStyle: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  viewMore: {
    resizeMode: 'contain',
  },
  companyLogo: {
    width: 15,
    height: 15,
    borderRadius: 5,
    marginRight: 5,
    backgroundColor: '#f5f5f5',
  },
});
