import Colors from '@theme/Colors';
import React from 'react';
import { View, Image, StyleSheet } from 'react-native';

const AvatarOverlap = ({ images }) => {
  return (
    <View style={styles.container}>
      {images.map((avatar, index) => (
        <Image
          key={index}
          source={avatar}
          style={[
            styles.avatar,
            index !== 0 && {
              marginLeft: -18,
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: Colors.black,
  },
  overlap: {
    marginLeft: -25,
  },
});

export default AvatarOverlap;
