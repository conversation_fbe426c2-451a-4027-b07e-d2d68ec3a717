import React, { memo, ReactNode } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import Modal from 'react-native-modal';
import { Colors } from '../../theme';
import { useDispatch } from 'react-redux';
import { isSubmitSuccess, setLoading } from '../../totara/actions/getWFMProfileAction';
import { useTheme } from '@theme/ThemeProvider';

type BottomSheetProps = {
  visibility?: boolean;
  backButton?: boolean;
  setVisibility?: (visibility: boolean) => void;
  children?: ReactNode;
  isModal?: boolean;
  visiblity?: boolean;
  customStyles?: ViewStyle;
  backdropColor?: string;
  isIPad?: boolean;
  noSwipe?: boolean;
};

const BottomSheet = ({
  visiblity,
  setVisibility,
  children,
  customStyles,
  backdropColor,
  isIPad,
  noSwipe,
}: BottomSheetProps) => {
  const dispatch = useDispatch();
  const handleVisibility = () => setVisibility && setVisibility(false);
  const { theme, isDarkMode } = useTheme();

  const closeModal = () => {
    dispatch(isSubmitSuccess(false));
    dispatch(setLoading(false));
  };

  const renderContent = () => {
    return (
      <View
        style={[
          styles.sheet,
          customStyles,
          {
            width: isIPad ? '50%' : '100%',
            borderRadius: isIPad ? 30 : 0,
            backgroundColor: isDarkMode ? theme.backgroundColor : 'white',
          },
        ]}
      >
        <View
          style={[styles.dock, isDarkMode && { backgroundColor: theme.bgLightDark, opacity: 1 }]}
        />
        {children}
      </View>
    );
  };

  return (
    <View>
      <Modal
        avoidKeyboard
        onSwipeComplete={() => {
          handleVisibility();
          closeModal();
        }}
        backdropColor={backdropColor ? backdropColor : '#232323'}
        onBackButtonPress={() => {
          handleVisibility();
          closeModal();
        }}
        backdropTransitionOutTiming={0}
        onBackdropPress={handleVisibility}
        isVisible={visiblity ?? false}
        animationInTiming={0.5}
        animationOutTiming={0.5}
        style={[
          styles.view,
          {
            justifyContent: isIPad ? 'center' : 'flex-end',
          },
        ]}
        {...(!noSwipe ? { swipeDirection: 'down' } : {})}
      >
        {renderContent()}
      </Modal>
    </View>
  );
};

export default memo(BottomSheet);

const styles = StyleSheet.create({
  sheet: {
    borderRadius: 0,
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
    justifyContent: 'center',
    alignSelf: 'center',
    width: '100%',
  },
  view: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  dock: {
    height: 6,
    width: 60,
    backgroundColor: Colors.black,
    marginVertical: 10,
    borderRadius: 10,
    opacity: 0.1,
    alignSelf: 'center',
  },
});
