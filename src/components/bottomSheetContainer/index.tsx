import Colors from '../../theme/Colors';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Pressable, View } from 'react-native';
import { StyleSheet } from 'react-native';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
// import BottomSheet from "reanimated-bottom-sheet";

const BottomSheetContainer = ({ index, onCloses, children, snapPoints, style }) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [disabled, setDisable] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      // bottomSheetRef?.current?.snapTo(index ? index : 1);
      setDisable(false);
    }, 100);
  }, [index]);

  const points = useMemo(() => snapPoints ?? ['70%', '90%'], [snapPoints]);

  return (
    <>
      <Pressable disabled={disabled} onPress={onCloses} style={styles.darkBg} />
      <BottomSheet
        enablePanDownToClose
        ref={bottomSheetRef}
        index={0}
        handleIndicatorStyle={{ width: 70, backgroundColor: Colors.darkblue }}
        snapPoints={points}
        onClose={onCloses}
      >
        <View style={[styles.mainCon, style]}>{children}</View>
      </BottomSheet>
    </>
  );
};

export default BottomSheetContainer;

const styles = StyleSheet.create({
  darkBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },

  mainCon: {
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    paddingHorizontal: 22,
    // flex: 1,
    backgroundColor: Colors.white,
    height: '100%',
  },

  line: {
    alignSelf: 'center',
    marginTop: 15,
    borderRadius: 12,
    height: 3,
    width: 70,
    backgroundColor: Colors.darkblue,
  },
});
