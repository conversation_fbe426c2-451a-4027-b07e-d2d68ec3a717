import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import ChevronDown from './ChevronDown';

const Accordion = ({
  header,
  content,
  isOpen = false,
  toggleOpen,
  customStyle = {},
  headerStyle = {},
  contentStyle = {},
}) => {
  const { theme } = useTheme();
  const styles = useStyles(theme);

  return (
    <View
      style={[styles.itemContainer, customStyle]}
    >
      <View style={[styles.componentHeader, headerStyle]}>
        {header}

        <ChevronDown isOpen={isOpen} toggleOpen={toggleOpen} />
      </View>

      <View style={[{ height: isOpen ? 'auto' : 0, overflow: 'hidden' }, contentStyle]}>
        {content}
      </View>
    </View>
  );
};

const useStyles = (theme) => {
  return StyleSheet.create({
    itemContainer: {
      backgroundColor: theme.white
    },
    componentHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  });
};

export default Accordion;
