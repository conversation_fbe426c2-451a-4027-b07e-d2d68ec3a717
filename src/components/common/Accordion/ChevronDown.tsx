import React from 'react';
import { Image, StyleSheet, TouchableOpacity } from 'react-native';
import { AssetsImages } from '@theme';
import { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';

const ChevronDown = ({ isOpen, toggleOpen }) => {
  const { theme, isDarkMode } = useTheme();
  const styles = useStyles(theme, isDarkMode);

  return (
    <TouchableOpacity style={styles.itemContainer} onPress={toggleOpen}>
      <Image
        style={[styles.collapseImg, { transform: [{ scaleY: isOpen ? -1 : 1 }] }]}
        source={AssetsImages.collapseBtn}
      />
    </TouchableOpacity>
  );
};

const useStyles = (theme, isDarkMode) => {
  return StyleSheet.create({
    itemContainer: {
      width: isPad ? 40 : 34,
      height: isPad ? 40 : 34,
      borderRadius: (isPad ? 40 : 34) / 2,
      borderWidth: 1,
      borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.25)' : theme.borderBackground,
      justifyContent: 'center',
      alignItems: 'center',
      marginStart: 15,
      marginRight: 10,
    },
    collapseImg: {
      width: 18,
      height: 18,
      tintColor: theme.textColor,
    },
  });
};

export default ChevronDown;
