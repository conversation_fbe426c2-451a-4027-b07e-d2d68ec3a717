import AssetsImages from '../../theme/AssetsImages';
import React from 'react';
import { StyleSheet, TouchableOpacity, Image, StyleProp, ViewStyle, View } from 'react-native';
import { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';

type addButtonType = {
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  bgColor?: string;
};

export default ({ onPress, style, bgColor }: addButtonType) => {
  const { theme } = useTheme()
  return (
    <TouchableOpacity onPress={onPress}>
      <View style={[styles.border, style, { borderColor: theme.borderColor }]}>
        <Image style={[styles.addBtn, { tintColor: theme.textColor, }]} source={AssetsImages.addBtn} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  editIcon: {
    width: 35,
    height: 35,
    marginTop: -50,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addBtn: {
    width: 22,
    height: 22,
    marginBottom: 2,
    marginTop: 0,
    marginLeft: 1,
    marginRight: 2,
  },
  border: {
    width: isPad ? 40 : 34,
    height: isPad ? 40 : 34,
    borderRadius: (isPad ? 40 : 34) / 2,
    borderWidth: 0.5,
    borderColor: 'rgba(30, 30, 30, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
