import React from 'react';
import { Image, Pressable, StyleSheet, View, I18nManager, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { LineHeight } from '@utils';
import { hitSlop, lineHeightForAndroidLTRMed, widgetPadding } from '@utils/constants';
import { AssetsImages, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { TextView } from '.';

type Prop = {
  isBack?: boolean;
  title?: string;
  subTitle?: string;
  setVisibility?: () => void;
  onBackPress?: () => void;
  centerContainer?: StyleProp<ViewStyle>;
  mainTitleStyle?: StyleProp<ViewStyle | TextStyle>;
  subTitleStyle?: StyleProp<ViewStyle | TextStyle>;
  skip?: boolean;
  onSkipPress?: () => void;
};

const AuthHeader = ({
  isBack,
  title,
  subTitle,
  setVisibility,
  onBackPress,
  centerContainer,
  mainTitleStyle,
  subTitleStyle,
  skip,
  onSkipPress,
}: Prop) => {
  const navigation = useNavigation();
  const styles = useStyles({ mainTitleStyle, subTitleStyle, lineHeightForAndroidLTRMed });

  const goBack = () =>
    onBackPress ? onBackPress() : setVisibility ? setVisibility() : navigation.goBack();

  return (
    <View style={styles.container}>
      <View style={centerContainer}>
        <View style={styles.skipBtnContainer}>
          {isBack && (
            <Pressable style={styles.backBtn} hitSlop={hitSlop} onPress={goBack}>
              <Image style={styles.backImg} source={AssetsImages.back} />
            </Pressable>
          )}
          {skip && (
            <Pressable onPress={onSkipPress}>
              <TextView style={styles.skipBtn} text="Skip" />
            </Pressable>
          )}
        </View>
        <TextView style={styles.authHeaderTitle} text={title} />
        {subTitle && <TextView style={[styles.authHeaderSubTitle]} text={subTitle} />}
      </View>
    </View>
  );
};

const useStyles = (props) => {
  const { theme, isDarkMode } = useTheme();

  return StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      marginTop: 15,
    },
    skipBtnContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backBtn: {
      marginBottom: widgetPadding,
    },
    backImg: {
      width: 15,
      height: 15,
      resizeMode: 'contain',
      transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
      tintColor: isDarkMode ? '#fff' : '',
    },
    authHeaderTitle: {
      fontFamily: fonts.semiBold,
      color: theme.textColor,
      fontSize: fontSize.h1,
      textAlign: 'left',
      lineHeight: LineHeight.h1_LH,
      ...props.mainTitleStyle,
    },
    authHeaderSubTitle: {
      marginTop: 20,
      fontFamily: fonts.thin,
      color: theme.textColor,
      fontSize: fontSize.small,
      textAlign: 'left',
      ...props.subTitleStyle,
      ...props.lineHeightForAndroidLTRMed,
    },
    skipBtn: {
      color: theme.textColor,
      fontFamily: fonts.semiBold,
      fontSize: fontSize.medium,
    },
  });
};

export default AuthHeader;
