import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { AssetsImages, Colors } from "../../theme";
import { updateWFMField } from "../../totara/actions/getWFMProfileAction";
import { pickImage } from "../../utils/constants";
import ImageAuth from "../ImageAuth/ImageAuth";
import FastImage from "react-native-fast-image";

type props = {
  containerStyles?: ViewStyle;
  onChange?: (url: Record<string, string>) => void;
  loading?: boolean;
  url?: string;
  editable?: boolean;
};

const Avatar = ({
  containerStyles,
  editable = true,
  onChange,
  loading,
  url,
}: props) => {
  const [uri, setUri] = useState("");
  const [isLoading, setLoading] = useState(false);
  const [fileName, setFileName] = useState("");
  const dispatch = useDispatch();
  const token = useSelector(
    (state) => state?.getWFMProfile?.response?.liferayaccesstoken
  );
  const talentProfile = useSelector(
    (state) => state?.getWFMProfile?.talentProfile
  );
  const data = useSelector((state) => state?.getWFMProfile);

  useEffect(() => {
    if (data?.response?.imageUrl) {
      setUri(data?.response?.imageUrl);
    }
  }, [data?.response?.imageUrl]);

  const openGallery = async () => {
    setLoading(true);
    const response = await pickImage(
      token,
      getFile,
      "profilepicture",
      data?.response?.idn
    );

    getFile(response);
  };

  const getFile = (data) => {
    if (data?.fileName !== talentProfile?.picture) {
      onChange && onChange(data?.fileName);
    }
    setFileName(data?.fileName);
    dispatch(updateWFMField({ key: "imageUrl", value: data?.base64 }));
    setLoading(false);
  };
  return (
    <View style={[styles.vatarImageView, containerStyles]}>
      {uri ?
        <ImageAuth
          uri={uri}
          style={[styles.vatarImage, containerStyles]}
          defaultImage={AssetsImages.user}
        /> :
        <FastImage
          defaultSource={AssetsImages.soulvite}
          style={[styles.vatarImage, containerStyles]}
          source={AssetsImages.soulvite}
        />}

      {editable && (
        <>
          <TouchableOpacity style={styles.editContainer} onPress={openGallery}>
            {isLoading ? (
              <ActivityIndicator color="white" size={"small"} />
            ) : (
              <Image
                style={{ width: 20, height: 20 }}
                source={AssetsImages.cameraWhite}
              />
            )}
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  vatarImage: {
    width: 105,
    height: 105,
    borderRadius: 55,
  },
  vatarImageView: {
    width: 110,
    height: 110,
    // backgroundColor: "rgba(30, 30, 30, 0.7)",
    borderRadius: 100,
    borderWidth: 4,
    borderColor: "white",
    alignItems: "center",
    justifyContent: "center",
  },
  editContainer: {
    position: "absolute",
    borderWidth: 1,
    borderRadius: 50,
    borderColor: Colors.borderGray,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(30, 30, 30, 0.7)",
    padding: 5,
    zIndex: 1000000000,
  },
});

export default Avatar;