import React, { memo } from 'react';
import {
  StyleProp,
  TextStyle,
  Pressable,
  StyleSheet,
  Image,
  ColorSchemeName,
  ImageStyle,
  ActivityIndicator,
  View,
  ViewStyle,
  Platform,
} from 'react-native';
import { Colors } from '../../theme';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import TextView from './TextView';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@theme/ThemeProvider';
import { I18nManager } from 'react-native';
import { lineHeight } from '@utils/constants';

type ButtonProps = {
  type?: 'transparent' | 'fill' | 'solid' | 'outline';
  style?: any;
  onPress?(): void;
  text?: string;
  numberOfLines?: number;
  icon?: any;
  iconStyle?: StyleProp<ImageStyle>;
  textStyle?: StyleProp<TextStyle>;
  testID?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
  withBg?: boolean;
  loadingColor?: string;
  containerStyle?: StyleProp<ViewStyle>;
  isOnBoarding?: boolean;
};

const InnerButton = ({
  withBg = false,
  style,
  onPress,
  text,
  type = 'transparent',
  icon,
  iconStyle,
  textStyle,
  testID,
  isLoading,
  isDisabled,
  loadingColor,
  isOnBoarding,
}: ButtonProps) => {
  const { theme, isDarkMode } = useTheme();
  const safeArea = useSafeAreaInsets();
  const styleByType = getStyleByType(type, theme, isDarkMode);
  const textStyleByType = getStyleBasedOnButton(type, isDarkMode, theme);

  return (
    <Pressable
      disabled={isLoading || Boolean(isDisabled)}
      testID={testID}
      onPress={onPress}
      style={[
        styleByType,
        { backgroundColor: isDarkMode ? '#F5F5F5' : '#1E1E1E' },
        isDisabled && {
          backgroundColor: !isOnBoarding && isDarkMode ? '#787878' : '#C7C7C7',
        },
        style,
      ]}
    >
      {icon && <Image style={[iconStyle, styles.iconStyle]} source={icon} />}
      {isLoading ? (
        <ActivityIndicator
          size={'small'}
          color={loadingColor ? loadingColor : isDarkMode ? 'black' : '#F5F5F5'}
        />
      ) : (
        <TextView
          text={text || ''}
          style={[
            textStyleByType,
            Platform.OS == 'android' &&
              withBg && {
                paddingBottom: 3,
              },
            { marginTop: I18nManager.isRTL && Platform.OS == 'ios' ? -2 : 0 },
            Platform.OS == 'android' && { marginTop: -4 },
            { color: isDarkMode ? '#1E1E1E' : '#F5F5F5' },
            isDisabled && {
              color: !isOnBoarding && isDarkMode ? Colors.black : 'rgba(120, 120, 120, 1)s',
            },
            textStyle,
          ]}
        />
      )}
    </Pressable>
  );
};

const Button = ({
  withBg = false,
  type = 'transparent',
  containerStyle,
  isLoading = false,
  ...props
}: ButtonProps) => {
  const { theme, isDarkMode } = useTheme();
  const safeArea = useSafeAreaInsets();

  if (withBg) {
    return (
      <View
        style={
          withBg
            ? [
                styles.roundContainer,
                { paddingBottom: safeArea.bottom > 0 ? safeArea.bottom : 10 },
                containerStyle,
                isDarkMode && { backgroundColor: theme.bgDark },
              ]
            : {}
        }
      >
        <InnerButton {...props} isLoading={isLoading} withBg={withBg} type={type} containerStyle={containerStyle} />
      </View>
    );
  }

  return <InnerButton {...props} isLoading={isLoading} withBg={withBg} type={type} containerStyle={containerStyle} />;
};

const getStyleByType = (
  type: string,
  theme: ColorSchemeName,
  isDarkMode: boolean,
): StyleProp<TextStyle> => {
  if (theme == null) {
    return styles.transparent;
  }

  switch (type) {
    case 'transparent':
      return {
        color: 'black',
        ...styles.transparent,
        borderColor: isDarkMode ? '#fff' : Colors.light.black,
      };
    case 'fill':
      return {
        color: 'black',
        ...styles.fill,
        backgroundColor: isDarkMode ? theme.bgLightDark : 'black',
      };
    case 'error':
      return styles.transparent;
    case 'solid':
      return {
        ...styles.solid,
        backgroundColor: isDarkMode ? theme.bgLightDark : 'black',
      };
    case 'outline':
      return styles.outline;
    default:
      return { ...styles.transparent, borderColor: isDarkMode ? '#fff' : Colors.light.black };
  }
};

const getStyleBasedOnButton = (
  type: string,
  isDarkMode: boolean,
  theme: ColorSchemeName,
): StyleProp<TextStyle> => {
  if (theme == null) {
    return styles.title;
  }

  switch (type) {
    case 'transparent':
      return { ...styles.title };
    case 'fill':
      return { ...styles.fillTitle };
    case 'error':
      return styles.fillTitle;
    case 'solid':
      return {
        ...styles.solidText,
        // color: isDarkMode ? 'black' : 'white',
      };
    case 'outline':
      return styles.outlineText;
    default:
      return styles.transparent;
  }
};

export default Button;

const styles = StyleSheet.create({
  transparent: {
    fontSize: fontSize.large,
    fontFamily: fonts.medium,
    borderColor: Colors.light.black,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    justifyContent: 'center',
    marginTop: 30,
    width: '90%',
    alignSelf: 'center',
    height: 50,
    marginBottom: 10,
  },
  fill: {
    fontSize: fontSize.large,
    fontFamily: fonts.medium,
    backgroundColor: Colors.light.black,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    justifyContent: 'center',
    marginTop: 30,
    width: '90%',
    alignSelf: 'center',
    height: 50,
    marginBottom: 10,
  },
  iconStyle: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  title: {
    color: 'black',
    fontFamily: fonts.regular,
    padding: 14,
    fontSize: isPad ? fontSize.xlarge : fontSize.large,
    lineHeight: lineHeight(fontSize.semiMedium1),
  },
  fillTitle: {
    color: 'white',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xlarge : fontSize.large,
  },

  roundContainer: {
    paddingTop: 15,
    backgroundColor: Colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: 'center',

    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    paddingLeft: 20,
    paddingRight: 20,
  },
  solid: {
    backgroundColor: Colors.black,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: isPad ? 60 : 10,
    width: '100%',
    justifyContent: 'center',
    alignSelf: 'center',
    height: isPad ? 80 : 45,
  },

  outline: {
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.black,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: isPad ? 60 : 10,
    width: '100%',
    justifyContent: 'center',
    alignSelf: 'center',
    height: isPad ? 80 : 45,
  },

  solidText: {
    fontSize: fontSize.semiMedium,
    fontFamily: fonts.medium,
    color: Colors.white,
    paddingTop: Platform.OS == 'ios' ? 0 : 5,
    lineHeight: lineHeight(fontSize.semiMedium),
  },
  outlineText: {
    fontSize: fontSize.semiMedium,
    fontFamily: fonts.medium,
    color: Colors.black,
    paddingTop: Platform.OS == 'ios' ? 0 : 5,
    lineHeight: lineHeight(fontSize.semiMedium),
  },
});
