import { Colors } from '../../theme';
import React from 'react';
import { Pressable, StyleProp, StyleSheet, ViewStyle } from 'react-native';
import { TextView } from '.';
import { fontFamily } from '../../utils/AppUtils';
import fonts from '../../utils/fonts';

type ButtonTypes = {
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  onPress: () => void;
  label: string;
};

const Button = ({ label, disabled = false, style, onPress }: ButtonTypes) => {
  return (
    <Pressable
      onPress={onPress}
      style={[styles.btn, disabled && { backgroundColor: '#bdc3c7' }, style]}
      disabled={disabled}
    >
      <TextView text={label} style={styles.text} />
    </Pressable>
  );
};

export default Button;

const styles = StyleSheet.create({
  btn: {
    borderRadius: 64,
    height: 52,
    backgroundColor: Colors.black,
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  text: {
    color: 'white',
    textAlign: 'center',
    fontFamily: fonts.regular,
    fontSize: 16,
  },
});
