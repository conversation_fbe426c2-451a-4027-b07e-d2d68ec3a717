import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BottomSheet, Button } from '@components';
import { useTheme } from '@theme/ThemeProvider';
import { commonStyle } from '@utils';
import { useTranslation } from 'react-i18next';
import { SafeAreaProvider } from 'react-native-safe-area-context';

const ConfirmationBottomSheet = ({
  showBottomSheet,
  setShowBottomSheet,
  secondaryButtonText = 'Cancel',
  primaryButtonText = 'Confirm',
  headingText = 'This is heading text.',
  subheadingText = 'This is subheading text.',
  onSecondaryButtonClick = () => {},
  onPrimaryButtonClick,
  subheadingStyle,
  showOnlyPrimaryButton = false,
  childSlot = null,
}) => {
  const styles = useStyles();
  const { t } = useTranslation();

  return (
    <BottomSheet
      visiblity={showBottomSheet}
      setVisibility={() => setShowBottomSheet(!showBottomSheet)}
    >
      <View style={[styles.sheetContainer]}>
        <Text style={[styles.heading, styles.headingConfirm]}>{t(headingText)}</Text>

        <Text style={[styles.subheading, subheadingStyle ? subheadingStyle : {}]}>
          {t(subheadingText)}
        </Text>

        {childSlot}

        <View style={styles.buttonsContainer}>
          {showOnlyPrimaryButton ? null : (
            <Button
              text={t(secondaryButtonText)}
              type="fill"
              onPress={() => {
                onSecondaryButtonClick();
              }}
              style={[styles.cancelButton]}
              textStyle={[styles.cancelButtonText]}
            />
          )}

          <Button
            text={t(primaryButtonText)}
            type="fill"
            onPress={() => {
              onPrimaryButtonClick();
            }}
            style={[styles.button]}
            textStyle={[styles.buttonText]}
          />
        </View>
      </View>
    </BottomSheet>
  );
};

const useStyles = () => {
  const { theme } = useTheme();

  return StyleSheet.create({
    bottomSheet: {zIndex: 90},
    sheetContainer: {
      paddingHorizontal: 20,
      paddingBottom: 30,
      paddingTop: 30,
    },
    heading: {
      fontWeight: 700,
      fontSize: 28,
      lineHeight: 33,
      color: theme.thunder100,
      ...commonStyle.textDirection,
    },
    headingConfirm: {},
    subheading: {
      marginTop: 24,
      marginBottom: 40,
      fontWeight: 400,
      fontSize: 16,
      lineHeight: 20,
      color: theme.thunder80,
      ...commonStyle.textDirection,
    },

    buttonsContainer: {
      flexDirection: 'row',
      gap: 16,
    },
    cancelButton: {
      height: 56,
      flex: 1,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      borderColor: theme.thunder100,
      backgroundColor: theme.white,
      ...commonStyle.textDirection,
    },
    cancelButtonText: {
      ...commonStyle.textDirection,
      color: theme.thunder100,
    },
    button: {
      height: 56,
      flex: 1,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      borderTopWidth: 0,
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      backgroundColor: theme.thunder100,
      ...commonStyle.textDirection,
    },
    buttonText: {
      ...commonStyle.textDirection,
      color: theme.white,
    },
    submitButton: {
      height: 56,
      borderWidth: 1,
      marginTop: 0,
      margin: 0,
      padding: 0,
      width: '100%',
      borderTopWidth: 0,
      fontSize: 18,
      lineHeight: 22,
      fontWeight: '400',
      ...commonStyle.textDirection,
    },
    submitButtonText: {
      color: theme.white,
    },
  });
};

export default ConfirmationBottomSheet;
