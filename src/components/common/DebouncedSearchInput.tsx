
import React, { useCallback, useState } from 'react';
import { I18nManager, Pressable, StyleSheet, TextInput, View, ViewStyle } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import Icons, { IconsType } from '@theme/Icons';
import { debounce } from 'lodash';
import { hexToRGBa } from '@utils/customTheme';

type DebouncedSearchInputProps = {
  onSearch: (text: string) => void;
  value: string;
  setValue: (val: string) => void;
  disabled?: boolean;
  placeholder?: string;
  containerStyle?: any;
};

const DebouncedSearchInput = ({
  onSearch,
  value,
  setValue,
  disabled = false,
  placeholder = '',
  containerStyle,
}: DebouncedSearchInputProps) => {
  const { theme, styles } = useStyles();
  const { t } = useTranslation();

  const handler = useCallback(debounce(onSearch, 500), [])

  const onType = (text: string): void => {
    setValue(text);
    if (text?.length > 2) {
      handler(text);
    }
  }

  const onClean = (): void => {
    setValue('');
    handler('');
  }

  return (
    <View style={[styles.searchContainer, containerStyle]}>
      <View style={styles.searchView}>
        <TextInput
          value={value}
          textAlign={I18nManager.isRTL ? 'right' : 'left'}
          placeholderTextColor={theme.placeholderText}
          onChangeText={onType}
          style={styles.input}
          placeholder={t(placeholder)}
        />
        <Pressable
          onPress={onClean}
          disabled={disabled || !value}
        >
          {!value ? <Icons type={IconsType.AntDesign} name={'search1'} size={22} color={theme.textColor} /> :
            <Icons type={IconsType.Ionicons} name="close" size={22} color={theme.textColor} />}
        </Pressable>
      </View>
    </View>
  );
};

export default DebouncedSearchInput;

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    input: {
      flex: 1,
      writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
      color: theme.textColor,
    },
    searchView: {
      borderWidth: 1,
      height: 55,
      borderRadius: 12,
      marginBottom: 24,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      padding: 20,
      paddingVertical: 5,
      borderColor: hexToRGBa(theme.thunder100, 0.3),
    },
    searchContainer: {
      backgroundColor: theme.backgroundColor,
      paddingHorizontal: 24,
      paddingTop: 24,
      borderTopLeftRadius: 30,
    },
  });

  return { styles, theme, isDarkMode }
}
