import React, { useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ImageSourcePropType,
  Platform,
  Pressable,
  StyleProp,
  StyleSheet,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { TextView } from '.';
import { AssetsImages, Colors, Icons } from '../../theme';
import { fontFamily } from '../../utils/AppUtils';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import { AndroidLayerType } from 'react-native-webview/lib/WebViewTypes';
import { IconsType } from '../../theme/Icons';
import { IconButton } from 'react-native-paper';
import { useTheme } from '@theme/ThemeProvider';
import { I18nManager } from 'react-native';
import { useCommonThemeStyles } from '@utils';

type DropdownComponentProps = {
  label?: string;
  labelStyle?: StyleProp<TextStyle>;
  dropdownListStyle?: StyleProp<TextStyle>;
  icon?: any;
  data: any[];
  placeholder?: string;
  dropdownPosition?: 'top' | 'auto' | 'bottom' | undefined;
  style?: StyleProp<ViewStyle>;
  getSelectedValue: any;
  value?: string;
  iconStyle?: ViewStyle;
  placeholderStyle?: StyleProp<TextStyle>;
  search?: boolean;
  searchPlaceholder?: string;
  type?: string;
  loading?: boolean;
  disable?: boolean;
  rightDoubleIcon?: boolean;
  selectedTextStyle?: StyleProp<TextStyle>;
  inputSearchStyle?: StyleProp<TextStyle>;
  containerStyle?: StyleProp<ViewStyle>;
  selectedList?: any;
  onClickOfSelectedPreferences?: any;
  errorText?: string;
  required?: boolean;
  renderRightIcon?: any
};

type DropdownItem = {
  label: string;
  value: any;
};

const renderDoubleRightIcon = () => (
  <View style={{ position: 'relative', height: '100%', justifyContent: 'space-between' }}>
    <Image source={AssetsImages.doubleArrow} style={[styles.doubleDropdownImage, { top: isPad ? 8 : Platform.OS == 'ios' ? 2 : 5 }]} />
  </View>
);

const DropdownComponent: React.FC<DropdownComponentProps> = ({
  label,
  labelStyle,
  icon,
  data,
  placeholder,
  getSelectedValue,
  dropdownPosition,
  style,
  value: defaultValue,
  iconStyle,
  disable,
  dropdownListStyle,
  type,
  search = false,
  searchPlaceholder,
  loading,
  rightDoubleIcon,
  placeholderStyle,
  selectedTextStyle,
  inputSearchStyle,
  containerStyle,
  selectedList,
  onClickOfSelectedPreferences,
  errorText,
  required,
  ...props
}) => {
  const onRemovePreferences = (e) => () => {
    onClickOfSelectedPreferences(e);
  };

  const renderLeftIcon = (icon: ImageSourcePropType) => () => {
    if (selectedList?.length) {
      return (
        <View style={{ flexDirection: 'row' }}>
          {selectedList?.slice(selectedList?.length - 1).map((res) => (
            <View style={[styles.preferedMode, isDarkMode && { backgroundColor: theme.bgLightDark }]}>
              <TextView style={[styles.preferredMode, { color: theme.textColor }]} text={res} />
              <Pressable onPress={onRemovePreferences(res)}>
                <Image source={AssetsImages.cross} style={[styles.cross, isDarkMode && { tintColor: Colors.activeStateDark }]} />
              </Pressable>
            </View>
          ))}

          {selectedList?.length > 1 && (
            <View style={[styles.preferedMode, isDarkMode && { backgroundColor: theme.bgLightDark }]}>
              <TextView style={[styles.preferredMode, { color: theme.textColor }]} text={'+' + (selectedList?.length - 1)} />
            </View>
          )}
        </View>
      );
    }
    return <Image source={icon} style={[styles.flagImage, iconStyle, isDarkMode && { tintColor: Colors.activeStateDark }]} />;
  };

  const { theme, isDarkMode } = useTheme();

  const renderRightIcon = () => {
    if (!data?.length && loading) return <ActivityIndicator size={'small'} color={Colors.black} />;
    return <Image source={AssetsImages.dropdown} style={[styles.dropdownImage, isDarkMode && {
      tintColor: Colors.activeStateDark
    }]} />;
  };

  const [value, setValue] = useState<any>(defaultValue);
  const [focus, setIsFocus] = useState(false);

  const handleFocus = () => setIsFocus(!focus);
  const handleBlur = () => setIsFocus(false);
  const { themeStyle } = useCommonThemeStyles();

  const handleDropdownChange = (item: DropdownItem) => {
    setValue(item.value);
    setIsFocus(false);
    getSelectedValue(
      type == 'maritalStatus' || type == 'Passport Type' || type == 'relation' || type == 'label'
        ? item.label
        : type == 'label'
          ? item.name
          : item.value,
    );
  };

  const renderItem = item => {
    return (
      <View style={styles.item}>
        <TextView style={[styles.textItem, {
          color: theme.textColor
        }]} text={item.label} />
        {selectedList?.includes(item?.label) && <Image source={AssetsImages.tick_mark_green} style={styles.tick_mark_green} />}
      </View>
    );
  };

  return (
    <>
      <View style={styles.mainContainer}>
        {label && <TextView text={label} style={[styles.text, labelStyle, { color: theme.textColor }]} />}
        {(required && label) && <TextView text={"*"} style={[styles.required, isDarkMode && { color: Colors.notificationRedForDark }]} />}
      </View>

      <View style={[styles.container, containerStyle, themeStyle.darkBackgroundColor]}>
        <Dropdown
          itemTextStyle={[styles.itemTextStyle, { color: theme.borderBackground }]}
          showsVerticalScrollIndicator
          containerStyle={[{ borderRadius: 5 }, dropdownListStyle, themeStyle.darkBackgroundColor, { borderColor: theme.borderBackground }]}
          disable={disable}
          value={selectedList?.length ? '' : disable ? searchPlaceholder : value}
          style={[styles.dropdown, style, { backgroundColor: disable ? (isDarkMode ? theme.bgLightDark : Colors.disabledBtn) : theme.backgroundColor }, { borderColor: theme.borderBackground }]}
          placeholderStyle={[styles.placeholderStyle, placeholderStyle, { color: theme.textColor }]}
          selectedTextStyle={[styles.selectedTextStyle, selectedTextStyle, { textAlign: 'left', color: label == 'Preferred mode of learning' ? 'transparent' : theme.textColor }]}//{ color: !isDarkMode ? Colors.black : theme.borderColor }
          inputSearchStyle={[styles.inputSearchStyle, inputSearchStyle, { color: theme.textColor, borderColor: theme.borderBackground, borderRadius: 5, fontSize: fontSize.small }]}
          iconStyle={styles.iconStyle}
          data={data || []}
          searchPlaceholder={searchPlaceholder}
          search={search}
          labelField="label"
          valueField="value"
          placeholder={placeholder}
          onFocus={handleFocus}
          onBlur={handleBlur}
          activeColor={isDarkMode ? '#000' : '#fff'}
          dropdownPosition={dropdownPosition}
          onChange={handleDropdownChange}
          renderRightIcon={!I18nManager.isRTL ? (rightDoubleIcon ? renderDoubleRightIcon : renderRightIcon) : ((icon || selectedList?.length) && renderLeftIcon(icon))}
          renderLeftIcon={!I18nManager.isRTL ? ((icon || selectedList?.length) && renderLeftIcon(icon)) : (rightDoubleIcon ? renderDoubleRightIcon : renderRightIcon)}
          {...props}
          renderItem={renderItem}
        />
      </View>
    </>
  );
};

export default DropdownComponent;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
  },
  dropdown: {
    height: isPad ? 65 : 50,
    borderColor: Colors.borderGray,
    marginTop: 8,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 18,
    paddingHorizontal: 10,
    fontFamily: fonts.medium,
  },
  itemTextStyle: {
    fontFamily: fonts.regular,
    color: Colors.black,
    fontSize: isPad ? fontSize.large : fontSize.small,

  },
  icon: {
    marginRight: 5,
  },
  placeholderStyle: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
    color: Colors.black,
    fontFamily: fonts.medium,
    textAlign: 'left'
  },
  selectedTextStyle: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
    fontFamily: fonts.regular,
    color: Colors.black,
    marginRight: 40,
  },
  iconStyle: {
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
    resizeMode: 'contain',
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
  dropdownImage: {
    width: isPad ? 20 : 20,
    height: isPad ? 30 : 20,
    position: 'absolute',
    right: isPad ? 8 : 0,
    marginHorizontal: 5,
  },

  doubleDropdownImage: {
    height: isPad ? 20 : 15,
    position: 'absolute',
    right: isPad ? 8 : 2,
    resizeMode: 'contain'
  },
  flagImage: {
    width: 22,
    height: 22,
    marginRight: 15,
    resizeMode: 'contain',
  },
  text: {
    marginLeft: 18,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
    color: Colors.black,
    fontFamily: fonts.regular,
  },
  cross: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
    tintColor: 'black',
  },
  preferedMode: {
    backgroundColor: 'rgba(242, 246, 249, 1)',
    flexDirection: 'row',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
    marginRight: 10,
  },
  preferredMode: {
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    marginRight: 10,
    paddingVertical: 8,
  },
  required: {
    color: 'red',
    top: 2,
    left: 2
  },
  mainContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  item: {
    padding: 17,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textItem: {
    fontSize: fontSize.small,
    fontFamily: fonts.regular,
    marginRight: 10

  },
  tick_mark_green: {
    width: 18,
    height: 18,
  },
  errorText: {
    color: "red",
    marginHorizontal: 20,
    fontSize: isPad ? fontSize.large : fontSize.mini,
    fontFamily: fonts.medium,
    marginTop: -6,
    marginBottom: 12
  },
});
