import React, {useState} from 'react';
import {TextView} from '.';
import {AssetsImages, Colors} from '../../theme';
import {
  Image,
  ImageStyle,
  StyleProp,
  StyleSheet,
  TextInput,
  TextStyle,
  View,
  ImageSourcePropType,
  ViewStyle,
  Pressable,
  Platform,
  NativeSyntheticEvent,
  TextInputFocusEventData,
  ReturnKeyType,
  I18nManager, TouchableOpacity,
} from 'react-native';
import fontSize, {isPad} from '../../utils/fontSize';
import fonts from '../../utils/fonts';
// import { CountryPicker } from "react-native-country-codes-picker";
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
// import DateTimePicker from '@react-native-community/datetimepicker';
import CountryPicker from 'react-native-country-picker-modal';
import {ActivityIndicator, IconButton} from 'react-native-paper';
import {useTheme} from '../../theme/ThemeProvider';
import {useTranslation} from 'react-i18next';
import LottieView from "lottie-react-native";

type EditTextProps = {
  label?: string;
  placeholder?: string;
  labelStyle?: StyleProp<TextStyle>;
  value?: string;
  onChange?: (text: string) => void;
  image?: ImageSourcePropType;
  rightIcon?: ImageSourcePropType;
  inputViewStyle?: StyleProp<ViewStyle>;
  customInputStyles?: StyleProp<TextStyle>;
  helperText?: string;
  onPressHelper?: () => void;
  imageTestId?: string;
  iconStyle?: StyleProp<ImageStyle>;
  placeholderTextColor?: string;
  disable?: boolean;
  getCountryCode?: any;
  codeCountry?: string;
  editable?: boolean;
  keyboardType?: 'numeric' | 'email-address';
  errorText?: string;
  isPreffered?: boolean;
  notint?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  isPhoneInput?: boolean;
  rightIconLoading?: boolean;
  renderLeftComponent?: ReactElement;
  onSubmit?: () => void;
  isCalender?: boolean;
  maxLength?: number;
  returnKeyType?: ReturnKeyType;
  minimumDate?: any;
  maximumDate?: any;
  blurOnSubmit?: any;
  helperTextColor?: any;
  textAlignVertical?: any;
  rightIconOnPress?: () => void;
  onWriteWithAI?: () => Promise<{
    data: {
      agent_response: {
        text: string;
      };
    };
  }>;
  onFocused?: (e: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  required?: boolean;
  showHelpText?: boolean;
  rightIconStyles?: any
  onBoarding?: boolean;
  onBlur?: () => void;
};

const EditTextWithAI = ({
                          onFocused,
                          rightIconLoading,
                          label,
                          rightIconOnPress,
                          labelStyle,
                          placeholder = '',
                          image,
                          inputViewStyle,
                          customInputStyles,
                          helperText,
                          rightIcon,
                          onPressHelper,
                          imageTestId,
                          value,
                          onChange,
                          iconStyle,
                          placeholderTextColor,
                          disable,
                          getCountryCode,
                          codeCountry,
                          editable,
                          keyboardType,
                          errorText,
                          isPreffered,
                          notint,
                          multiline,
                          numberOfLines,
                          isPhoneInput,
                          renderLeftComponent,
                          onSubmit,
                          isCalender,
                          maxLength,
                          returnKeyType,
                          minimumDate,
                          maximumDate,
                          textAlignVertical,
                          blurOnSubmit,
                          required,
                          showHelpText,
                          helperTextColor,
                          rightIconStyles,
                          onBoarding,
                          isPassword,
                          onWriteWithAI,
                          onBlur,
                          ...props
                        }: EditTextProps) => {
  const {theme, isDarkMode} = useTheme();
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState(codeCountry);
  const [openCalander, setOpenCalander] = useState(false);
  const [isAILoading, setAILoading] = useState(false);
  const [currentText, setCurrentText] = useState(value);
  const [originalText, setOriginalText] = useState(value);
  const [isCurrentTextByAI, setIsCurrentTextByAI] = useState(false);
  const onHandlePicker = () => {
    editable && setShow(true);
  };
  const {t} = useTranslation();
  const onWriteWithAIClicked = () => {
    if (!onWriteWithAI) return

    setAILoading(true)
    onWriteWithAI()
      .then((response) => {
        const ai_rep = response.choices[0].message.content
        setCurrentText(ai_rep)

        setIsCurrentTextByAI(true)
        setAILoading(false)
        onChange(ai_rep)
      })
  }
  const revertToOriginal = () => {
    setCurrentText(originalText)
    setIsCurrentTextByAI(false)
  }

  return (
    <View>

      <View style={styles.preferredView}>
        <View style={styles.container}>
          {label && (
            <TextView text={label} style={[styles.text, labelStyle, (isDarkMode) && {color: theme.textColor}]}/>
          )}
          {required && label &&
            <TextView text={'*'} style={[styles.required, isDarkMode && {color: Colors.notificationRedForDark}]}/>}
        </View>

        {isPreffered && (
          <View style={styles.row}>
            <Image source={AssetsImages.preferred} style={styles.tick}/>
            <TextView text={'Preferred'} style={[styles.preferredText]}/>
          </View>
        )}
      </View>

      <Pressable
        onPress={() => isCalender && setOpenCalander(true)}
        style={[
          styles.textinputContainer,
          inputViewStyle,
          {
            borderColor: !!errorText ? 'red' :
              onBoarding ? Colors.onBoardingEditTextBorder :
                theme.borderBackground,
          },
          disable && {
            backgroundColor: isDarkMode ? theme.bgLightDark : Colors.inputBackground,
          },
        ]}
      >

        {image && (
          <Pressable onPress={() => isCalender && setOpenCalander(true)}>
            <Image
              testID={imageTestId}
              source={image ? image : AssetsImages.userprofile}
              style={[
                styles.icon,
                !notint && {tintColor: disable ? 'grey' : isDarkMode ? 'white' : 'black'},
                iconStyle,
                (label != 'Facebook' && isDarkMode) && {tintColor: theme.textColor},
                // { backgroundColor: 'black' }
              ]}
            />
          </Pressable>
        )}

        {renderLeftComponent && renderLeftComponent()}

        {codeCountry ? (
          <Pressable onPress={onHandlePicker} style={styles.phoneCode}>
            <TextView type="h4" text={`+${countryCode}`} style={{color: theme.textColor}}/>
            <Image
              source={AssetsImages.dropdown}
              style={[styles.dropdDown, isDarkMode && {tintColor: theme.textColor}]}
            />
            <View style={[styles.line, {backgroundColor: theme.textColor}]}/>
          </Pressable>
        ) : null}
        <TextInput
          secureTextEntry={isPassword}
          keyboardAppearance={isDarkMode ? 'dark' : 'light'}
          textAlign={I18nManager.isRTL ? 'right' : 'left'}
          onPressIn={() => (disable ? null : isCalender && setOpenCalander(true))}
          returnKeyType={returnKeyType || 'done'}
          maxLength={maxLength}
          onSubmitEditing={onSubmit}
          // scrollEnabled={multiline ? true : false}
          blurOnSubmit={blurOnSubmit}
          keyboardType={keyboardType}
          onFocus={(e) => onFocused && onFocused(e)}
          onBlur={onBlur}
          editable={!disable && editable}
          numberOfLines={numberOfLines}
          multiline={multiline}
          placeholderTextColor={
            onBoarding ? Colors.placeholderColor :
              !disable
                ? theme.placeholderText
                : placeholderTextColor
                  ? placeholderTextColor
                  : Colors.placeholderColor
          }
          placeholder={t(placeholder) || ''}
          style={[styles.input, {
            flex: 1,
            paddingVertical: 10,
          }, isDarkMode && {color: Colors.activeStateDark}, customInputStyles, onBoarding && {
            color: theme.textColor
          }]}
          onChangeText={(v) => {
            setCurrentText(v)
            onChange && onChange(v)
          }}
          value={currentText}
          textAlignVertical={textAlignVertical}
          {...props}
        />

        {rightIcon &&
          (rightIconLoading ? (
            <ActivityIndicator size={18} color={theme.textColor} style={{marginRight: 20}}/>
          ) : (
            <IconButton
              icon={() => {
                return (
                  <Image
                    source={rightIcon}
                    style={[
                      styles.rightIcon,
                      // { top: Platform.OS == "ios" ? 0 : -10 },
                      rightIconOnPress && {opacity: value != '' ? 1 : 0.5},
                      isDarkMode && {tintColor: theme.textColor},
                      rightIconStyles
                    ]}
                  />
                );
              }}
              size={20}
              style={{position: 'absolute', top: 2, right: 5}}
              onPress={
                rightIconOnPress ? rightIconOnPress : () => isCalender && setOpenCalander(true)
              }
            />
            // <Pressable
            //   style={{
            //     marginTop: Platform.OS == "ios" ? -20 : 0,
            //     marginRight: 5,
            //   }}
            //   onPress={
            //     rightIconOnPress
            //       ? rightIconOnPress
            //       : () => isCalender && setOpenCalander(true)
            //   }
            // >
            //   <Image
            //     source={rightIcon}
            //     style={[
            //       styles.rightIcon,
            //       { top: Platform.OS == "ios" ? 0 : -10 },
            //       rightIconOnPress && { opacity: value != "" ? 1 : 0.5 },
            //     ]}
            //   />
            // </Pressable>
          ))}
        <View style={{
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          // marginTop: 30,
          flexDirection: 'row',
          alignSelf: 'center',
          alignItems: 'center',
          flex: 0.5,
          gap: 2,
          width: '90%'
        }}>
          {(isAILoading) ?
            <View
              onPress={onWriteWithAIClicked}
              style={{
                flexDirection: 'row',
                alignSelf: 'center',
                alignItems: 'center',
                flex: 1,
                gap: 2,
              }}>

              <LottieView
                source={require('assets/lottie/ai-loading.json')}
                autoPlay
                loop
                style={{width: 20, height: 20}}
                colorFilters={[
                  {
                    keypath: "Dot 1.Ellipse 1.Fill 1",
                    color: theme.textColor  // White or "#000000" for black
                  },
                  {
                    keypath: "Dot 2.Ellipse 1.Fill 1",
                    color:theme.textColor
                  },
                  {
                    keypath: "Dot 3.Ellipse 1.Fill 1",
                    color: theme.textColor
                  }
                ]}
              />
              <TextView style={{fontSize: 12, marginHorizontal: 5,color: theme.textColor}}>{t('generatingResultForYou')}</TextView>
            </View>


            :
            (isCurrentTextByAI) ?
              <>
                <View style={{
                  flexDirection: 'row',
                  justifyContent: "space-between"
                }}>
                  <TouchableOpacity
                    onPress={onWriteWithAIClicked}
                    style={{
                      flexDirection: 'row',
                      justifyContent: "flex-start",
                      alignItems: 'center',
                      flex: 1,
                      gap: 2,
                    }}>

                    <Image
                      source={require('assets/icons/arrow-refresh.png')}
                      style={{width: 20, height: 20,tintColor:theme.textColor}}
                      resizeMode="contain"
                    />
                    <TextView
                      style={{fontSize: 12, marginHorizontal: 5, color: theme.textColor}}>{t('writeAgain')}</TextView>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={revertToOriginal}
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'flex-end',
                      alignItems: 'center',
                      justifyContent: "flex-end",
                      flex: 1,
                      gap: 2,
                    }}>

                    <Image
                      source={require('assets/icons/revert.png')}
                      style={{width: 15, height: 15,tintColor:theme.textColor}}
                      resizeMode="contain"
                    />
                    <TextView
                      style={{fontSize: 12, marginHorizontal: 5, color: theme.textColor}}>{t('Revert')}</TextView>
                  </TouchableOpacity>
                </View>

              </> :
              <TouchableOpacity
                onPress={onWriteWithAIClicked}
                style={{
                  flexDirection: 'row',
                  alignSelf: 'center',
                  alignItems: 'center',
                  flex: 1,
                  gap: 2,
                }}>

                <Image
                  source={require('assets/icons/write-with-ai.png')}
                  style={{width: 15, height: 15}}
                  resizeMode="contain"
                />
                <TextView
                  style={{fontSize: 12, marginHorizontal: 5, color: theme.textColor}}>{(currentText && !isCurrentTextByAI)?t("Enhance with AI"):t('writeWithAI')}</TextView>
              </TouchableOpacity>
          }


        </View>
      </Pressable>
      {helperText ? (
        <Pressable onPress={onPressHelper}>
          <TextView
            text={helperText}
            style={[
              styles.helperText,
              {color: theme.textColor},
              helperTextColor && {color: helperTextColor, textAlign: 'left'},
            ]}
          />
        </Pressable>
      ) : null}
      {!!errorText ? <TextView text={errorText} style={styles.errorText}/> : null}

      {show ? (
        <CountryPicker
          onClose={() => setShow(false)}
          onSelect={(country) => {
            if (country?.cca2 === 'AQ') {
              setCountryCode('672'?.replace('+', ''));
              getCountryCode && getCountryCode('672'?.replace('+', ''));
              setShow(false);
            } else {
              setCountryCode(country?.callingCode[0]?.replace('+', ''));
              getCountryCode && getCountryCode(country?.callingCode[0]?.replace('+', ''));
              setShow(false);
            }
          }}
          withCallingCode={true}
          withAlphaFilter={true}
          visible={show}
        />
      ) : null}

      <DatePicker

        theme={isDarkMode ? "dark" : "light"}
        textColor={(isDarkMode && Platform.OS == 'ios') ? 'white' : 'black'}
        mode="date"
        modal
        minimumDate={minimumDate && new Date(minimumDate)}
        maximumDate={maximumDate ? maximumDate : !minimumDate && new Date()}
        open={openCalander}
        date={new Date()}
        onConfirm={(date) => {
          setOpenCalander(false);
          onChange && onChange(moment(date).format('YYYY-MM-DD'));
        }}
        onCancel={() => {
          setOpenCalander(false);
        }}
      />

    </View>
  );
};

export default EditTextWithAI;

const styles = StyleSheet.create({
  input: {
    paddingLeft: 10,
    fontFamily: fonts.medium,
    color: Colors.black,
    width: '90%',
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
    height: 100,
    textAlignVertical: 'top',
    writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
    // textAlign: "left"
  },
  icon: {
    marginLeft: 10,
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
  rightIcon: {
    width: isPad ? 25 : 20,
    height: isPad ? 25 : 20,
    resizeMode: 'contain',
    // position: "absolute",
    // right: 10,
  },
  textinputContainer: {
    marginTop: 5,
    borderWidth: 1,
    borderRadius: 8,
    marginHorizontal: 18,
    borderColor: Colors.borderGray,

    flexDirection: 'column',
    height: isPad ? 65 : 50,
    // alignItems: 'center',
  },

  text: {
    marginLeft: 18,
    fontSize: fontSize.h4, //h5
    color: Colors.black,
    fontFamily: fonts.regular,
    textAlign: 'left',
    marginBottom: 8,
  },
  helperText: {
    color: Colors.helperText,
    marginHorizontal: 20,
    marginTop: 5,
    fontSize: isPad ? fontSize.large : fontSize.small,
    fontFamily: fonts.medium,
    textAlign: 'right',
  },
  preferredText: {
    color: Colors.black,
    fontSize: isPad ? fontSize.large : fontSize.medium,
    fontFamily: fonts.regular,
    textAlign: 'left',
  },
  errorText: {
    color: 'red',
    marginHorizontal: 20,
    fontSize: isPad ? fontSize.large : fontSize.mini,
    fontFamily: fonts.medium,
    textAlign: 'left',
    opacity: 0.8,
    position: 'absolute',
    bottom: -12,
    right: 0,
  },
  required: {

    // top: 2,
    left: 2,

    fontSize: fontSize.h5, //h5
    color: 'red',
    fontFamily: fonts.regular,
    textAlign: 'left',
    marginBottom: 8
  },
  dropdDown: {
    width: 20,
    height: 20,
  },
  phoneCode: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingStart: 10,
    height: '100%',
  },
  line: {
    height: 20,
    width: 1,
    backgroundColor: Colors.black,
    opacity: 0.2,
    marginHorizontal: 5,
  },
  preferredView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tick: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginHorizontal: 10,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginHorizontal: 20,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
