import { Image, ImageStyle, StyleSheet, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native";
import React from "react";
import { Colors, fonts, fontSize } from "@theme";
import TextView from "../TextView";
import { useTheme } from "@theme/ThemeProvider";
import { widgetPadding } from "@utils/constants";
import { ImageProps } from "react-native";


type props = {
    heading:string;
    description:string;
    icon:ImageProps;
    containerStyles?:any;
    onPress?:() => void;
    buttonContainerStyle?:ViewStyle;
    buttonText?:string;
    buttonTextStyle?:TextStyle;
    imageStyle?:ImageStyle,
    headingStyle?:TextStyle;
    descriptionStyle?:TextStyle;
    type?:"reversed" | "default";
    buttonType?:'filled' | 'default'


}
const GlobalEmptyStateComponent = ({
    heading, 
    description, 
    icon, 
    containerStyles, 
    onPress, 
    buttonText, 
    imageStyle, 
    headingStyle, 
    descriptionStyle, 
    type = "default", 
    buttonTextStyle, 
    buttonContainerStyle,
    buttonType = "default", 
}:props)=>{
    const { theme, isDarkMode } = useTheme();
    if(type === "reversed"){
        return (
            <View style={[styles.container, containerStyles]}>
                <TextView style={[styles.heading, headingStyle, {color:theme.textColor}]} text={heading}/>
                <TextView style={[styles.description, descriptionStyle, {color:theme.textColor}, onPress && { marginBottom:widgetPadding * 3}]} text={description}/>
                <Image source={icon} style={[styles.image, imageStyle]}/>
            </View>
        )
    }
    else{
        return (
            <View style={[styles.container, containerStyles]}>
                <Image source={icon} style={[styles.image, imageStyle]}/>
                <TextView style={[styles.heading, headingStyle, {color:theme.textColor}]} text={heading}/>
                <TextView style={[styles.description, onPress && { marginBottom:widgetPadding * 2}, descriptionStyle, {color:theme.textColor}]} text={description}/>
                {onPress ? <Button type={buttonType} containerStyles={[buttonContainerStyle, {borderColor:isDarkMode ? "#fff" : '#000'}]} textStyle={buttonTextStyle} text={buttonText || ''} onPress={onPress} /> : null}
            </View>
        )
    }
    
}



const styles = StyleSheet.create({
    container:{
        width:'100%',
        height:'100%',
        justifyContent:'center',
        alignItems:'center',
        paddingHorizontal:widgetPadding * 2,
    },
    image:{
        resizeMode:'contain',
        width:180,
        height:150,
        marginBottom:widgetPadding * 1.5
    },
    heading:{
        fontSize:fontSize.h2,
        fontFamily:fonts.semiBold,
        textAlign:'center',
        marginBottom:widgetPadding,
        textTransform:'capitalize'
    },
    description:{
        fontSize:fontSize.h4,
        fontFamily:fonts.regular,
        textAlign:'center',
        width:'84%'
    }
})



const buttonStyles = StyleSheet.create({
    container:{
        width:'100%',
        height:56,
        borderWidth:1,
        borderColor:"rgba(0, 0, 0, 1)",
        borderRadius:12,
        justifyContent:'center',
        alignItems:'center',
    },
    filledStyleDark:{
        backgroundColor:"rgba(245, 245, 245, 1)"
    },
    filledStyleLight:{
        backgroundColor:"rgba(30, 30, 30, 1)"
    },
    text:{
        fontSize:fontSize.h4,
        fontFamily:fonts.regular,
    },
    filledTextStyleLight:{
        color:"rgba(251, 252, 253, 1)"
    },
    filledTextStyleDark:{
        color:"#000"
    },
})


type buttonProps = {
    onPress?:() => void;
    text:string;
    containerStyles?:any;
    textStyle?:TextStyle;
    type?:'filled' | 'default'
}


const Button = ({onPress, text, containerStyles, textStyle, type = 'default'}:buttonProps)=>{
    const { theme, isDarkMode } = useTheme();
   return (
    <TouchableOpacity onPress={onPress} style={[buttonStyles.container, type === "filled" && (isDarkMode ? buttonStyles.filledStyleDark : buttonStyles.filledStyleLight), containerStyles]}>
        <TextView style={[buttonStyles.text, textStyle, {color:theme.textColor}, type === "filled" && (isDarkMode ? buttonStyles.filledTextStyleDark : buttonStyles.filledTextStyleLight)]} text={text}/>
    </TouchableOpacity>
   )
}

export default GlobalEmptyStateComponent;