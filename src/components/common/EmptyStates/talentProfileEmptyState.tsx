import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { AssetsImages, Colors, fonts, fontSize } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import LinearGradient from 'react-native-linear-gradient';

type EmptyStateForDataProfileProps = {
  onPress: () => void;
  hideButton?: boolean;
};

const EmptyStateForDataProfile = ({ onPress, hideButton }: EmptyStateForDataProfileProps) => {
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();
  const linearColor = isDarkMode
    ? [theme.bgLightDark, theme.bgDark]
    : ['rgba(217, 217, 217, 0.7)', 'rgba(255, 255, 255, 1)'];

  return (
    <View style={styles.container}>
      <LinearGradient style={styles.circle} colors={linearColor}>
        <Image
          style={styles.addDataBtn}
          source={isDarkMode ? AssetsImages.addDataDark : AssetsImages.addDataLight}
        />
      </LinearGradient>
      <Text style={[styles.mainText, isDarkMode && { color: 'white' }]}>
        {t('No data available')}
      </Text>
      <Text style={[styles.detail, isDarkMode && { color: 'rgba(255, 255, 255, 1)' }]}>
        {t('You do not have any details added')}
      </Text>
      {!hideButton && (
        <Pressable
          onPress={onPress}
          style={[styles.addDetailBtn, isDarkMode && { backgroundColor: Colors.white }]}
        >
          <Image
            source={AssetsImages.plus}
            style={[styles.addBtn, isDarkMode && { tintColor: 'black' }]}
          />
          <Text style={[styles.addDetailText, isDarkMode && { color: Colors.black }]}>
            {t('Add details')}
          </Text>
        </Pressable>
      )}
    </View>
  );
};

export default EmptyStateForDataProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    marginTop: 30,
  },
  circle: {
    width: 120,
    height: 120,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addDataBtn: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
  },
  mainText: {
    fontFamily: fonts.medium,
    marginTop: 10,
    fontSize: fontSize.h5,
  },
  detail: {
    fontFamily: fonts.regular,
    marginTop: 10,
    fontSize: fontSize.h6,
    color: 'rgba(30, 30, 30, 1)',
  },
  addBtn: {
    width: 18,
    height: 18,
    tintColor: 'white',
    marginRight: 5,
  },
  addDetailBtn: {
    height: 45,
    backgroundColor: Colors.black,
    marginTop: 20,
    flexDirection: 'row',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  addDetailText: {
    fontFamily: fonts.medium,
    color: 'white',
    fontSize: fontSize.h6,
  },
});
