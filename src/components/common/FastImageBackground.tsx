import React from 'react';
import FastImage from 'react-native-fast-image';
import {
  View,
  TouchableOpacity,
  FlatList,
  ScrollView,
  SafeAreaView,
  TextInput,
  Image,
  Pressable,
  Alert,
  StyleSheet,
} from 'react-native';

const FastImageBackground = ({ children, source, resizeMode, containerStyles, onLayout, key }) => {
  return (
    <View key={key} onLayout={onLayout} style={containerStyles}>
      <FastImage resizeMode={resizeMode || 'cover'} style={styles.image} source={source} />
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});

export default FastImageBackground;
