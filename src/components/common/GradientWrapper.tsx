import React from 'react';
import { ImageBackground, Platform, StatusBar, StyleSheet } from 'react-native';
import { flipIconStyles } from '@utils/constants';
import { AssetsImages, Dimen } from '@theme';
import { useTheme } from '@theme/ThemeProvider';

interface GradientView {
  children?: JSX.Element;
}
 
const GradientView = ({ children }: GradientView) => {
  const { isDarkMode } = useTheme();
  const gradient = isDarkMode ? AssetsImages.darkMode_new : AssetsImages.lightMode_new;

  return (
    <ImageBackground source={gradient} style={[styles.container, flipIconStyles]}>
      <StatusBar translucent backgroundColor={'transparent'} />
      {children}
    </ImageBackground>
  );
};

export default GradientView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    backgroundColor: 'transparent',
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
  topGradient: {
    width: Dimen.width,
    height: Platform.OS == 'android' ? Dimen.height * 0.65 : Dimen.height * 0.6,
    top: 0,
    position: 'absolute',
  },
  bottomGradient: {
    width: Dimen.width,
    height: Dimen.height * 0.6,
    position: 'absolute',
    bottom: 0,
  },
});
