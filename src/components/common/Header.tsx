import React from 'react';
import {
  I18nManager,
  Image,
  Pressable,
  StatusBar,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { AssetsImages, Colors, Icons } from '../../theme';
import { IconsType } from '../../theme/Icons';
import { TextView } from '.';
import { IconButton } from 'react-native-paper';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import { FavItem } from '@components';
import { isIOS } from '@totara/lib/tools';

type HeaderProps = {
  onBackPress?: () => void;
  onSettingIconPress?: () => void;
  onMorePress?: () => void;
  text?: string;
  numberOfTextLines?: number;
  customStyles?: StyleProp<ViewStyle>;
  onFilterPress?: () => void;
  IconColor?: string;
  customBackIconStyles?: StyleProp<ViewStyle>;
  customRightIconStyles?: StyleProp<ViewStyle>;
  textStyles?: StyleProp<TextStyle>;
  showBackBtn?: boolean;
  themeColor?: string;
  courseid?: string;
  rightComponent?: JSX.Element;
  showCross?: boolean;
  backtint?: boolean;
  onSearchPress?: () => void;
  isCategory?: boolean;
  transparent?: boolean;
};

const Header = ({
  onBackPress,
  text,
  numberOfTextLines,
  onFilterPress,
  customStyles,
  onSettingIconPress,
  onMorePress,
  customRightIconStyles,
  showBackBtn,
  themeColor,
  rightComponent,
  showCross,
  onSearchPress,
  isCategory = false,
  courseid,
  textStyles,
  showShadow,
  transparent = false,
  backBtnContainerStyle
}: HeaderProps) => {
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();

  const BackIcon = ({ onPress, color }) => {
    if (showCross) {
      return (
        <Pressable onPress={onPress}>
          <Image
            source={AssetsImages.cross}
            style={[styles.crossStyle, { tintColor: color }, styles.rtlIcon]}
          />
        </Pressable>
      );
    }
    return (
      <IconButton
        style={[styles.rtlIcon, backBtnContainerStyle]}
        icon={() => {
          return <Icons color={color} type={IconsType.Ionicons} name={'chevron-back'} size={25} />;
        }}
        size={17}
        onPress={onPress}
      />
    );
  };

  const SettingIcon = ({ onPress, color }) => {
    return (
      <TouchableOpacity style={customRightIconStyles} onPress={onPress}>
        <Icons color={color} type={IconsType.Feather} name={'settings'} size={25} />
      </TouchableOpacity>
    );
  };

  const EllipsisBtn = ({ onPress, color }) => {
    return (
      <TouchableOpacity style={{ marginHorizontal: 5 }} onPress={onPress}>
        <Icons color={color} type={IconsType.Ionicons} name={'ellipsis-vertical'} size={25} />
      </TouchableOpacity>
    );
  };

  const SearchBtn = ({ onPress, color }) => {
    return (
      <TouchableOpacity style={{ marginHorizontal: 5 }} onPress={onPress}>
        <Icons color={color} type={IconsType.AntDesign} name={'search1'} size={25} />
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.backgroundColor,
        },
        customStyles,
        transparent && { backgroundColor: 'transparent' },
      ]}
    >
      <StatusBar
        backgroundColor={isDarkMode ? theme.backgroundColor : "white"}
        barStyle={isDarkMode ? "light-content" : "dark-content"} />
      {(onBackPress || showBackBtn) && (
        <BackIcon color={themeColor ? themeColor : theme.textColor} onPress={onBackPress} />
      )}

      {text ? (
        <TextView
          style={[
            styles.text,
            textStyles,
            { color: themeColor ? themeColor : theme.textColor, flex: 1 },
          ]}
          numberOfLines={numberOfTextLines}
          type={'h1'}
          text={t(text)}
        />
      ) : (
        <View style={{ flex: 1 }} />
      )}

      {onFilterPress && (
        <IconButton
          color={themeColor ? themeColor : theme.textColor}
          style={{ marginRight: 7 }}
          icon={() => {
            return isCategory ? (
              <Icons type={IconsType.Ionicons} name={'filter'} size={22} color={theme.textColor} />
            ) : (
              <Image
                source={AssetsImages.filter}
                style={{ height: 25, width: 25, resizeMode: 'contain', tintColor: theme.textColor }}
              />
            );
          }}
          size={17}
          onPress={onFilterPress}
        />
      )}
      {onSettingIconPress && (
        <SettingIcon
          color={themeColor ? themeColor : theme.textColor}
          onPress={onSettingIconPress}
        />
      )}

      {courseid && <FavItem color={isDarkMode ? Colors.white : Colors.black} courseid={courseid} />}
      {onMorePress && (
        <EllipsisBtn color={themeColor ? themeColor : theme.textColor} onPress={onMorePress} />
      )}

      {onSearchPress && (
        <SearchBtn color={themeColor ? themeColor : theme.textColor} onPress={onSearchPress} />
      )}
      {rightComponent}

      {showShadow && <Image style={styles.bgDot} source={AssetsImages.streakbgdot} />}
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'red',
    alignItems: 'center',
    paddingHorizontal: 10,
    minHeight: 50,
  },
  bgDot: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 600,
    height: 200,
    zIndex: -1,
  },
  text: {
    fontSize: fontSize.tg_PgHeading,
    marginStart: 5,
    fontFamily: fonts.semiBold,
    textAlign: 'left',
    ...(isIOS && {
      letterSpacing: -0.5,
    })
  },
  crossStyle: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginHorizontal: 10,
  },
  rtlIcon: {
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
  },
});
