import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { } from 'react-native';
import { TextView } from '.';
import { Dimen, Icons, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { widgetPadding } from '@utils/constants';
import { IconsType } from '@theme/Icons';
import AIIcon from "@components/AI/AIIcon.tsx";
import { useFeatureFlags } from '@utils/FeatureFlagsProvider';
import SearchIcon from '@components/Search/searchIcon';

const LandingPageHeader = ({ header, rightIcon, onRightIconPress, showSearch = false }) => {
  const { theme } = useTheme();
  const { featureFlags } = useFeatureFlags();

  return (
    <View style={[styles.container, rightIcon && { marginBottom: widgetPadding, marginTop: 24, paddingHorizontal: 20, }]}>
        <TextView style={[styles.pageTitleStyle, { color: theme.textColor }, rightIcon && { marginBottom: 0, marginTop: 0, paddingHorizontal: 0, }]} text={header} />
      <View style={styles.rightIcon}>
        {showSearch && <SearchIcon />}
        {rightIcon ? <Pressable onPress={onRightIconPress && onRightIconPress}>
          <Icons color={theme.textColor} type={IconsType.AntDesign} name={rightIcon} size={25}/>
        </Pressable> : null}
        {featureFlags.aiFeature &&<AIIcon/>}
      </View>
    </View>
  )
}

export default LandingPageHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: Dimen.width,
    justifyContent: 'space-between'
  },
  pageTitleStyle: {
    paddingHorizontal: 20,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.tg_PgHeading,
    textAlign: 'left',
    textTransform: 'capitalize',
    letterSpacing: -0.5,
    marginTop: 24,
    marginBottom: widgetPadding
  },
  rightIcon:{
    flexDirection: 'row',
    flex: 1, alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: 16,
    gap: 6,
  }
});
