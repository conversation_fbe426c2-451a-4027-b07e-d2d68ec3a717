import { AssetsImages, Dimen } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { flipIconStyles } from '@utils/constants';
import React from 'react';
import { Image, Platform, StatusBar, StyleSheet, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

const LightGradient = () => {
    const { isDarkMode } = useTheme();

    return (
        <View style={styles.container}>
            <StatusBar translucent backgroundColor={'transparent'} />

            {!isDarkMode ? <>
                <Image style={[{
                    width: '100%',
                    height: '100%',
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                }, flipIconStyles]} source={AssetsImages.lightMode_new} />
            </> :
                <>
                    <Image style={[{
                        width: '100%',
                        height: '100%',
                    }, flipIconStyles]} source={AssetsImages.darkMode_new} />
                    {/* <Image style={styles.topGradient} source={AssetsImages.darkTopViewGradietn} /> */}

                    {/* <LinearGradient
                        start={{ x: -1.1, y: -0.8 }}
                        end={{ x: 0.1, y: 0.9 }}

                        colors={['rgba(39, 57, 68, 1)', 'rgba(49, 68, 64, 1)', 'rgba(0, 0, 0, 1)']}
                        style={{ position: 'absolute', width: '100%', height: '50%', transform: [{ scaleX: -1 }] }} >
                    </LinearGradient>
                    <LinearGradient
                        start={{ x: -1.3, y: 1.9 }}
                        end={{ x: 0.3, y: 0.1 }}

                        colors={['rgba(39, 57, 68, 1)', 'rgba(49, 68, 64, 1)', 'rgba(0, 0, 0, 1)']}
                        style={{ position: 'absolute', width: '100%', height: '50%', bottom: 0 }}>

                    </LinearGradient> */}
                </>}

        </View>
    )
}

export default LightGradient;


const styles = StyleSheet.create({
    container: {
        flex: 1,
        position: 'absolute',
        top: 0,
        backgroundColor: 'transparent',
        left: 0,
        right: 0,
        bottom: 0
    },
    topGradient: {
        width: Dimen.width,
        height: Platform.OS == 'android' ? Dimen.height * 0.65 : Dimen.height * 0.6,
        top: 0,
        position: 'absolute'
    },
    bottomGradient: {
        width: Dimen.width,
        height: Dimen.height * 0.6,
        position: 'absolute',
        bottom: 0
    },
})