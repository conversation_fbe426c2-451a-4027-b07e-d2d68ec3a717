import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Colors from '../../theme/Colors';
import Dimen from '../../theme/Dimen';
import { useTheme } from '@theme/ThemeProvider';

const Loader = ({ isOnBoarding = false }) => {
  const { isDarkMode, theme } = useTheme();
  return (
    <View style={[styles.loader, (!isOnBoarding && isDarkMode) && { backgroundColor: theme.backgroundColor }]}>
      <ActivityIndicator color={(!isOnBoarding && isDarkMode) ? Colors.white : Colors.blue} size={'large'} />
    </View>
  );
};

export default Loader;

const styles = StyleSheet.create({
  loader: {
    position: 'absolute',
    right: 0,
    left: 0,
    bottom: 0,
    top: 0,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: **********,
    height: Dimen.height,
    width: Dimen.width,
  },
});
