import { Colors } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { RootState } from '@totara/reducers';
import React from 'react';
import { Modal, View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';

const LoaderModal = ({}) => {
  const { visible } = useSelector((state: RootState) => state.modalLoaderReducer);
  const { isDarkMode, theme } = useTheme();

  if (!visible) null;

  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}} // Prevent dismissing with back button
    >
      <View style={styles.overlay}>
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={isDarkMode ? Colors.white : Colors.blue} />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  loaderContainer: {
    width: 150,
    height: 150,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
  },
});

export default LoaderModal;
