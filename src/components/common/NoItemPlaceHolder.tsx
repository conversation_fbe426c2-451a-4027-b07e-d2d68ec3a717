import React from 'react';
import { TextView } from "@components";
import { AssetsImages, Colors, fonts, fontSize } from "@theme";
import { StyleSheet } from "react-native";
import { Image, View } from "react-native";
import FastImage from "react-native-fast-image";
import { lineHeightForAndroidLTRMini, lineHeightForAndroidLTRSmall } from '@utils/constants';

const NoItemPlaceHolder = ({ heading, subHeading }) => {
    return (
        <FastImage style={styles.background} source={AssetsImages.personalGradient}>
            <View style={styles.textSection}>
                <TextView style={[styles.heading, lineHeightForAndroidLTRSmall]} text={heading} />
                <TextView style={[styles.noCoursesAdded, lineHeightForAndroidLTRMini]} text={subHeading} />
            </View>
            <View style={styles.mountainView}>
                <Image source={AssetsImages.mountain} style={styles.mountain} />
            </View>
        </FastImage>
    )
}

const styles = StyleSheet.create({
    background: {
        width: '100%',
        borderColor: Colors.borderGray,
        borderWidth: 0.5,
        alignSelf: 'center',
        borderRadius: 15,
        flexDirection: 'row',
    },
    heading: {
        color: Colors.black,
        fontSize: fontSize.medium,
        fontFamily: fonts.medium,
        marginTop: 5,
        textAlign: 'left'
    },
    discoverLearning: {
        color: Colors.black,
        fontSize: fontSize.xxmini,
        fontFamily: fonts.thin,
        marginHorizontal: 20,
        marginTop: 15,
        width: '60%',
    },
    mountain: {
        alignSelf: 'center',
        resizeMode: 'contain',
        width: 160,
        height: 160,
        position: 'absolute',
        bottom: -32,
        left: -20,
    },
    mountainView: {
        flex: 1,
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
    },
    textSection: {
        flex: 1.3,
        padding: 10,
    },
    noCoursesAdded: {
        fontFamily: fonts.thin,
        fontSize: fontSize.xmini,
        color: 'black',
        marginTop: 30,
        textAlign: 'left'
    },
})

export default NoItemPlaceHolder;