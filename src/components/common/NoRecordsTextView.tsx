import React from 'react';
import { TextStyle, View, ViewStyle } from 'react-native';
import { TextView } from '../index';
import { StyleSheet } from 'react-native';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import Colors from '../../theme/Colors';
import { useTheme } from '@theme/ThemeProvider';

type props = {
  containerStyles?: ViewStyle;
  textStyle?: TextStyle;
  text?: string
}

const NoRecordsTextView = ({ containerStyles, textStyle, text }: props) => {
  const { theme } = useTheme()
  return (
    <View style={[styles.container, containerStyles]}>
      <TextView style={[styles.text, textStyle, { color: theme.textColor }]} text={text ? text : 'No records found!'} />
    </View>
  );
};

export default NoRecordsTextView;

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  text: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    fontFamily: fonts.medium,
    color: Colors.black,
    textAlign: "left"
  },
});
