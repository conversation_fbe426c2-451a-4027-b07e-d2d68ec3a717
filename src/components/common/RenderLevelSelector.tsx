import { AssetsImages, Colors, fontSize, fonts } from '@theme';
import React from 'react';
import { Platform, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '.';
import { useTheme } from '@theme/ThemeProvider';
import { Image } from 'react-native';
import { widgetPadding } from '@utils/constants';

const RenderLevelSelector = ({ res, getSelectedValue, value, style }) => {
    const { isDarkMode, theme } = useTheme();

    const textColor = isDarkMode && { color: theme.textColor }

    const passSelectedValue = () => getSelectedValue(res)
    return (
        <Pressable onPress={passSelectedValue}
            style={[styles.selectorView, { backgroundColor: value === res?.level ? (!isDarkMode ? 'rgba(190, 207, 225, 1)' : 'rgba(94, 112, 128, 1)') : isDarkMode ? 'rgba(58, 62, 65, 1)' : 'rgba(190, 208, 225, 0.35)' }, style]}>
            <View style={styles.levelView}>
                <TextView style={[styles.level, textColor]} text={res?.level} />
                {res?.detail ? <TextView style={[styles.detail, isDarkMode && { color: 'rgba(187, 187, 187, 1)' }]} text={res?.detail} /> : null}
            </View>
            {value === res?.level && <Pressable onPress={passSelectedValue}
                style={[styles.border, isDarkMode && { borderColor: Colors.borderGray }]}>
                <Image source={AssetsImages.levelTick} style={styles.tick} />
            </Pressable>}
        </Pressable>
    )
}

export default RenderLevelSelector;

const styles = StyleSheet.create({
    selectorView: {
        // backgroundColor: 'rgba(232, 238, 245)',
        marginHorizontal: 20,
        borderRadius: 10,
        paddingHorizontal: 15,
        marginBottom: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    border: {
        backgroundColor: 'white',
        width: 35,
        height: 35,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',

    },
    selectedITem: {
        width: 14,
        height: 14,
        backgroundColor: Colors.black,
        borderRadius: 10
    },
    levelView: {
        marginHorizontal: 8,
        paddingVertical: Platform.OS == 'android' ? 18 : 20,
    },
    detail: {
        fontFamily: fonts.regular,
        marginTop: Platform.OS == 'ios' ? widgetPadding : 10,
        fontSize: fontSize.h6,
        color: Colors.buttonBlackColor,
        textAlign:'left'
    },
    level: {
        fontFamily: fonts.semiBold,
        color: 'black',
        fontSize: fontSize.h5,
        textAlign: 'left'

    },
    tick: {
        tintColor: 'black',
        width: 15,
        height: 15,
        resizeMode: 'contain'
    }
})