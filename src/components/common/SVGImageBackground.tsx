import { Dimen } from '@theme';
import { View } from 'react-native';
import React from 'react';


const SVGImageBackground = ({ Image, children, style }) => {
    return <View style={style}>
        <View style={{
            position: "absolute",
            zIndex: -1,

        }}>
            <Image viewBox="0 22 100 70" width={Dimen.width} height={Dimen.height} />
        </View>
        {children}
    </View>
}

export default SVGImageBackground;