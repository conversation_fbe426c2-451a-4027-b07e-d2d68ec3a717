import Icons, { IconsType } from '../../theme/Icons';
import React from 'react';
import { I18nManager, Pressable, StyleSheet, TextInput, View, ViewStyle } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import { useTranslation } from 'react-i18next';

type SearchInputType = {
  searchQuery: string;
  setSearchQuery?: (text: string) => void;
  touchable?: boolean;
  placeholder?: string;
  mainViewStyle?: ViewStyle
  searchViewStyle?: ViewStyle
};

const SearchInput = ({
  searchQuery,
  setSearchQuery,
  touchable = true,
  placeholder = 'What do you want to learn?',
  mainViewStyle,
  searchViewStyle
}: SearchInputType) => {
  const { theme, isDarkMode } = useTheme();
  const { t } = useTranslation();

  const onClean = () => {
    setSearchQuery('')
  }

  return (
    <View style={[styles.searchContainer, { backgroundColor: theme.backgroundColor }, mainViewStyle]}>
      <View style={[styles.searchView, { borderColor: theme.borderBackground }, searchViewStyle]}>
        <TextInput
          textAlign={I18nManager.isRTL ? 'right' : 'left'}
          placeholderTextColor={theme.placeholderText}
          onChangeText={(text: string) => {
            setSearchQuery && setSearchQuery(text);
          }}

          style={[styles.input, { color: theme.textColor }]}
          value={searchQuery}
          placeholder={t(placeholder)}
        />
        <Pressable onPress={onClean} disabled={searchQuery == ''}>
          {(searchQuery == '' || searchQuery == undefined) ? <Icons type={IconsType.AntDesign} name={'search1'} size={22} color={theme.textColor} /> :
            <Icons type={IconsType.Ionicons} name="close" size={22} color={theme.textColor} />}
        </Pressable>
      </View>
    </View>
  );
};

export default SearchInput;

const styles = StyleSheet.create({
  input: {
    flex: 1,
    writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
  },
  searchView: {
    borderWidth: 1,
    height: 55,
    borderRadius: 12,
    marginBottom: 24,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    padding: 20,
    paddingVertical: 5,

  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingTop: 24,
    borderTopLeftRadius: 30,
  },
});
