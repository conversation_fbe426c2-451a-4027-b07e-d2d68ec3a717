import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import Colors from '../../theme/Colors';

type SeapatorProps = {
  mv?: number;
  mb?: number;
  mt?: number;
};

const Seperator = ({ mv, mb, mt }: SeapatorProps) => {
  const { theme } = useTheme();
  return (
    <View
      style={[
        styles.seprator,
        {
          marginVertical: mv,
          marginBottom: mb,
          marginTop: mt,
          backgroundColor: theme.borderBackground,
        },
      ]}
    />
  );
};

const styles = StyleSheet.create({
  seprator: {
    width: '100%',
    height: 1,
    backgroundColor: Colors.grayline,
  },
});

export default Seperator;
