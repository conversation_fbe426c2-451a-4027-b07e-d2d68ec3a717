import React, { ReactNode, useEffect, useState } from 'react';
import {
  ScrollView,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { TextView } from '.';
import { Colors, fontSize } from '../../theme';
import fonts from '../../utils/fonts';
import { isPad } from '@utils/fontSize';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';

interface TabViewProps {
  components: ReactNode[];
  buttons: string[];
  tabLabelContainerStyles?: ViewStyle;
  tabLabelButtonStyles?: ViewStyle;
  tabLabelStyle?: TextStyle;
  selectedTabStyles?: ViewStyle;
  tabContainerStyle?: StyleProp<ViewStyle>;
  isScrollable?: boolean;
  activeIndex?: number;
  setActiveIndex: React.Dispatch<React.SetStateAction<number>>;
}

const TabView: React.FC<TabViewProps> = ({
  components,
  buttons,
  tabLabelContainerStyles,
  tabLabelButtonStyles,
  tabLabelStyle,
  selectedTabStyles,
  tabContainerStyle,
  isScrollable = false,
  setActiveIndex,
  activeIndex,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const { theme, isDarkMode } = useTheme();

  useEffect(() => {
    setActiveTab(activeIndex);
  }, [activeIndex]);

  const setTabsIndex = (index: number) => () => {
    setActiveTab(index);
    setActiveIndex(index);
  };
  const { t } = useTranslation();

  const renderTabs = () => {
    return buttons.map((button: string, index: number) => (
      <TouchableOpacity
        activeOpacity={0.5}
        onPress={setTabsIndex(index)}
        style={[
          styles.button,
          tabLabelButtonStyles,
          index === activeTab
            ? isDarkMode
              ? {
                borderBottomWidth: 1,
                borderBottomColor: Colors.white,
              }
              : [styles.borderBottom, { borderBottomColor: theme.borderBackground }]
            : {},
        ]}
        key={index}
      >
        <TextView
          style={[
            styles.tabs,
            tabLabelStyle,
            index === activeTab && (selectedTabStyles ? selectedTabStyles : styles.selectedTab),
            isDarkMode && { color: 'gray' },
            index === activeTab && [
              isDarkMode && { color: Colors.activeStateDark, opacity: 1 },
            ],
          ]}
          type="h3"
          text={t(button)}
        />
      </TouchableOpacity>
    ));
  };

  return (
    <View
      style={[
        styles.container,
        tabContainerStyle,
        isDarkMode && { backgroundColor: theme.backgroundColor },
      ]}
    >
      <View style={[styles.tabContainer, tabLabelContainerStyles, {
        borderBottomColor: theme.borderBackground
      }]}>
        {isScrollable ? (
          <ScrollView
            style={{ alignSelf: 'flex-start' }}
            showsHorizontalScrollIndicator={false}
            horizontal
          >
            {renderTabs()}
          </ScrollView>
        ) : (
          renderTabs()
        )}
      </View>
      {components[activeTab] && components[activeTab]}
    </View>
  );
};

export default TabView;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
  },
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.borderGray,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.black,
  },
  tabs: {
    textTransform: 'uppercase',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.large : 12,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'center',
  },
  selectedTab: {
    fontFamily: fonts.medium,
    color: 'black',
  },
});
