import React, { memo, useState } from 'react';
import {
  Animated,
  ColorSchemeName,
  I18nManager,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
} from 'react-native';
import { useColorScheme } from '../../hooks';
import { Colors, fontSize } from '../../theme';
import { LabelConfig } from '../../theme/labelConfig';
import { useTranslation } from 'react-i18next';
import fonts from '@utils/fonts';
import { useTheme } from '@theme/ThemeProvider';

type TextViewProps = {
  type?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p1' | 'p2' | 'p3' | 'p4' | 'p5' | 'p6';
  style?: StyleProp<TextStyle>;
  onPress?(): void;
  onViewCallback?(): void;
  text?: string;
  numberOfLines?: number;
  icon?: string;
  iconStyle?: StyleProp<TextStyle>;
  animated?: any;
  isViewAll?: boolean;
  testID?: string;
  ellipsizeMode?: 'clip' | 'head' | 'middle' | 'tail';
  viewMoreTextStyle?: StyleProp<TextStyle>;
  children?: any;
  isTranslated?: boolean;
};

// const useFontFamily = (fontFamily: string, text: string) => {
//   const { t, i18n } = useTranslation();

//   if (!fontFamily || !text) {
//     return null;
//   }

//   if (!i18n.exists(text.toString())) {
//     return fontFamily;
//   }

//   // return

//   const length = fontFamily?.split('-')?.length;
//   if (fontFamily?.split('-')?.[length - 1] == 'Regular') {
//     return ArabicFonts.regular;
//   } else if (fontFamily?.split('-')?.[length - 1] == 'Bold') {
//     return ArabicFonts.bold;
//   } else if (fontFamily?.split('-')?.[length - 1] == 'LightItalic') {
//     return ArabicFonts.light;
//   } else if (fontFamily?.split('-')?.[length - 1] == 'Light') {
//     return ArabicFonts.regular;
//   } else if (fontFamily?.split('-')?.[length - 1] == 'Medium') {
//     return ArabicFonts.medium;
//   } else if (fontFamily?.split('-')?.[length - 1] == 'Black') {
//     return ArabicFonts.bold;
//   } else {
//     ArabicFonts.regular;
//   }
// };

const TextView = ({
  type = 'h3',
  style,
  onPress,
  text,
  numberOfLines,
  animated,
  isViewAll,
  testID,
  ellipsizeMode,
  viewMoreTextStyle,
  children,
  isTranslated = false,
  onViewCallback,
}: TextViewProps) => {
  const theme = useColorScheme();
  let styleByType = getStyleByType(type, theme);
  let styleByTypeForViewAll = getStyleByType('h4', theme);
  const { t } = useTranslation();
  const [descriptionViewAll, setDescriptionViewAll] = useState(false);
  const onViewAll = () => {
    setDescriptionViewAll(!descriptionViewAll);
    onViewCallback && onViewCallback();
  };
  const customTheme = useTheme();

  const ellipes = descriptionViewAll ? ' ' : '...';

  const truncatedText =
    numberOfLines && isViewAll
      ? text?.slice(0, descriptionViewAll ? Number.POSITIVE_INFINITY : numberOfLines) + ellipes
      : text;

  if (animated) {
    return (
      <Animated.Text
        testID={testID}
        numberOfLines={descriptionViewAll ? undefined : numberOfLines}
        onPress={onPress}
        style={[styleByType, style]}
      >
        {truncatedText}{' '}
        {isViewAll && (
          <Pressable onPress={onViewAll}>
            <Text
              onPress={onViewAll}
              style={[
                styles.viewAllText,
                styleByTypeForViewAll,
                viewMoreTextStyle,
                { color: customTheme.textColor },
              ]}
            >
              {' '}
              {!descriptionViewAll ? LabelConfig.global.ViewMore : LabelConfig.global.ViewLess}
            </Text>
          </Pressable>
        )}
        {children}
      </Animated.Text>
    );
  }

  return (
    <Text
      testID={testID}
      numberOfLines={descriptionViewAll ? undefined : numberOfLines}
      onPress={onPress}
      ellipsizeMode={ellipsizeMode}
      style={[styleByType, style]}
    >
      {isTranslated ? text : t(truncatedText)}
      {isViewAll && (
        <Text
          onPress={onViewAll}
          style={[
            styleByTypeForViewAll,
            styles.viewAllText,
            viewMoreTextStyle,
            { color: 'rgba(61, 145, 226, 1)' },
          ]}
        >
          {!descriptionViewAll ? t(LabelConfig.global.ViewMore) : t(LabelConfig.global.ViewLess)}
        </Text>
      )}
      {children}
    </Text>
  );
};

let getStyleByType = (type: string, theme: ColorSchemeName): StyleProp<TextStyle> => {
  if (theme == null) {
    return styles.h3;
  }

  switch (type) {
    case 'h6':
      return { color: Colors.black, ...styles.h6 };
    case 'h5':
      return { color: Colors.black, ...styles.h5 };
    case 'h4':
      return { color: Colors.black, ...styles.h4 };
    case 'h3':
      return { color: Colors.black, ...styles.h3 };
    case 'h2':
      return { color: Colors.black, ...styles.h2 };
    case 'h1':
      return { color: Colors.black, ...styles.h1 };
    case 'error':
      return styles.error;
    default:
      return styles.h3;
  }
};

const styles = StyleSheet.create({
  viewAllText: {
    color: Colors.black,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.small,
  },
  h1: {
    fontSize: 22,
    fontFamily: fonts.medium,
  },

  h2: {
    fontSize: 18,
    fontFamily: fonts.medium,
  },

  h3: {
    fontSize: 16,
    fontFamily: fonts.medium,
  },

  h4: {
    fontSize: 14,
    fontFamily: fonts.medium,
  },

  h5: {
    fontSize: 12,
    fontFamily: fonts.medium,
  },

  h6: {
    fontSize: 10,
    fontFamily: fonts.medium,
  },

  error: {},
  p1: {
    fontSize: 18,
    fontFamily: fonts.medium,
  },

  p2: {
    fontSize: 16,
    fontFamily: fonts.medium,
  },

  p3: {
    fontSize: 14,
    fontFamily: fonts.medium,
  },

  p4: {
    fontSize: 12,
    fontFamily: fonts.medium,
  },

  p5: {
    fontSize: 10,
    fontFamily: fonts.medium,
  },

  p6: {
    fontSize: 8,
    fontFamily: fonts.medium,
  },
});

export default memo(TextView);
