import { Colors } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import React from 'react';
import { View } from 'react-native';

const ThemeBackground = ({ children }) => {
    const { isDarkMode, theme } = useTheme();
    const backgroundColor = isDarkMode ? theme.backgroundColor : Colors.landingBackgroundLightColor
    return (
        <View style={[{ flex: 1 }, { backgroundColor }]}>
            {children}
        </View>
    )
}
export default ThemeBackground;