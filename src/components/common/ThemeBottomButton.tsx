import { useTheme } from "@theme/ThemeProvider";
import React from "react";
import { Platform, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { Button } from ".";
import { Colors, fontSize, fonts, useCommonThemeStyles } from "@utils";

type props = {
    isLoading?: boolean;
    onPress: () => void;
    heading: string;
    isDisabled?: boolean;
    style?: StyleProp<ViewStyle>;
}
const ThemedBottomButton = ({ isLoading, onPress, heading, isDisabled, style }: props) => {
    const { theme, isDarkMode } = useTheme();
    const { themeStyle } = useCommonThemeStyles()
    return (
        <View style={[styles.bottomView, themeStyle.backgroundOverlayColor, style]}>
            <Button
                isDisabled={isDisabled}
                style={{
                    backgroundColor: isDisabled ? (isDarkMode ? theme.bgLightDark : Colors.disabledBtn) :
                        isDarkMode ?
                            Colors.activeStateDark : 'black'
                }}
                isLoading={isLoading}
                onPress={onPress}
                text={heading}
                type="fill"
                textStyle={[styles.textStyle, {
                    color: isDisabled ? Colors.activeStateDark : isDarkMode ? 'black' : 'white'
                }]}
            />
        </View>
    )
}

export default ThemedBottomButton;


const styles = StyleSheet.create({
    bottomView: {
        bottom: Platform.OS == 'ios' ? 0 : 0,
        position: 'absolute',
        width: '100%',
        height: 100,
        borderTopWidth: 2,
        borderTopColor: '#f5f5f5',
        backgroundColor: 'white',
        paddingBottom: Platform.OS == 'android' ? 10 : 0,
        zIndex: 999,
        elevation: 10,
    },
    textStyle: {
        fontFamily: fonts.medium,
        fontSize: fontSize.medium,
        color: 'white'

    }
})
