import React from 'react';
import Icons, { IconsType } from '@theme/Icons';
import { IconButton } from 'react-native-paper';
import { I18nManager } from 'react-native';

const BackIcon = ({ onPress, color }) => {
  return (
    <IconButton
      style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }}
      icon={() => {
        return <Icons color={color} type={IconsType.Ionicons} name={'chevron-back'} size={18} />;
      }}
      size={17}
      onPress={onPress}
    />
  );
};

export default BackIcon;
