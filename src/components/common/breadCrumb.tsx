import React from 'react';
import { Platform, Pressable, StyleSheet, View, ViewStyle } from 'react-native';
import { Dimen, Icons } from '../../../src/theme';
import { TextView } from '.';
import { LabelConfig } from '../../theme/labelConfig';
import fontSize from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { IconsType } from '../../theme/Icons';
import { IconButton } from 'react-native-paper';
import { useTheme } from '@theme/ThemeProvider';
import { widgetPadding } from '@utils/constants';

type props = {
  text: string;
  showViewAll?: boolean;
  showViewAllIcon?: boolean;
  length?: number;
  onPress?: () => void;
  onAddPress?: () => void;
  addIcon?: boolean;
  customContainerStyles?: ViewStyle | ViewStyle[];
  customHeadingStyles?: ViewStyle;
  numberOfLines?: number;
};

const BreadCrumb = ({
  text,
  showViewAll,
  showViewAllIcon = true,
  length,
  onPress,
  addIcon,
  customContainerStyles,
  onAddPress,
  customHeadingStyles,
  numberOfLines = 2,
}: props) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, customContainerStyles]}>
      <View style={styles.titleContainer}>
        <TextView
          numberOfLines={numberOfLines}
          style={[styles.contentHeading, { color: theme.textColor }, customHeadingStyles]}
          type="h1"
          text={text}
        />
        {addIcon && (
          <IconButton
            onPress={onAddPress}
            style={{
              backgroundColor: theme.textColor,
            }}
            icon={() => {
              return (
                <Icons
                  type={IconsType.AntDesign}
                  name={'plus'}
                  size={14}
                />
              );
            }}
            size={Platform.OS == 'ios' ? 14 : 15}
          />
        )}
      </View>
      {length && showViewAll ? (
        <Pressable
          style={styles.viewAllBtnContainer}
          testID="viewAllBtn"
          onPress={length && onPress ? onPress : null}
        >
          <TextView
            style={[styles.viewAll, { color: theme.textColor }]}
            type="p2"
            text={`${LabelConfig.home.viewAll}`}
          />
        </Pressable>
      ) : null}
    </View>
  );
};
export default BreadCrumb;

const styles = StyleSheet.create({
  container: {
    width: Dimen.width,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: widgetPadding,
    marginBottom: widgetPadding,
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentHeading: {
    fontSize: fontSize.h3,
    fontFamily: fonts.semiBold,
    textTransform: 'capitalize',
    textAlign:'left'
  },
  viewAllBtnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAll: {
    fontSize: fontSize.h6,
    fontFamily: fonts.regular,
    textDecorationLine: 'underline',
    color: 'rgba(120, 120, 120, 1)',
  }
});
