import React, { useState } from 'react';
import { Platform, StyleSheet, Text, View } from 'react-native';
import Modal from 'react-native-modal';
import TextView from './TextView';
import { Colors, fonts, fontSize } from '../../utils';
import Button from './Button';
import BottomSheet from '../bottomSheet';
import { I18nManager } from 'react-native';
import { useTranslation } from 'react-i18next';
import { lineHeight } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

function ConfirmationModal({
  visibility,
  setVisibility,
  onDelete,
  type = '',
  title = '',
}) {
  const { t } = useTranslation();

  const label = t(`Are you sure you want to delete this ${t(type)}`)
  const { theme, isDarkMode } = useTheme()
  return (
    <View style={{ flex: 1 }}>
      <Modal
        backdropTransitionInTiming={0}
        backdropTransitionOutTiming={0}
        style={style.view}
        onBackdropPress={setVisibility}
        isVisible={visibility}
      >
        <View style={[style.modal, { backgroundColor: theme.backgroundColor }]}>
          <View style={[style.dock, isDarkMode && { backgroundColor: theme.bgLightDark, opacity: 1 }]} />
          <TextView style={[style.text, { color: theme.textColor }]} text={label} />

          <TextView style={[style.selectedSkillName, { color: theme.textColor }]} text={`"${title}"`} />

          <View style={style.butonRow}>
            <Button
              textStyle={[style.textStyle, { color: 'black' }]}
              onPress={setVisibility}
              type="transparent"
              text="Cancel"
              style={[style.btnStyle, { backgroundColor: 'white', borderColor: 'black', borderWidth: 1 }, isDarkMode && { backgroundColor: Colors.activeStateDark }]}

            />
            <Button
              textStyle={style.textStyle}
              onPress={onDelete}
              type="fill"
              text="Yes"
              style={style.btnStyle}
            />
          </View>

        </View>
      </Modal>
    </View>
  );
}

export default ConfirmationModal;

const style = StyleSheet.create({
  butonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  modal: {
    backgroundColor: 'white',
    borderRadius: 5,
    paddingVertical: 20,
    borderTopRightRadius: 30,
    borderTopLeftRadius: 30,
    justifyContent: 'center',
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: 20,
  },
  btnStyle: {
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 10,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  view: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  dock: {
    height: 6,
    width: 60,
    backgroundColor: Colors.black,
    marginVertical: 10,
    borderRadius: 10,
    opacity: 0.1,
    alignSelf: 'center',
    position: 'absolute',
    top: 0,
    bottom: 20,
  },
  text: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: fontSize.xlarge,
    marginBottom: 10,
    marginTop: 30,
    marginEnd: 20,
    textAlign: 'left'
  },
  selectedSkillName: {
    color: Colors.black,
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    marginBottom: 10,
    marginTop: 10,
    textAlign: 'left'

  },
  textStyle: {
    fontFamily: fonts.medium,
    fontSize: fontSize.small,
    color: '#fff'
  },
});
