import { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import React from 'react';

type props = {
  children: ReactNode;
  customStyles?: ViewStyle;
};

export default function ({ children }: props) {
  return <View style={styles.notchPadding}>{children}</View>;
}

const styles = StyleSheet.create({
  notchPadding: {
    paddingTop: DeviceInfo?.hasNotch() ? 45 : 0,
  },
});
