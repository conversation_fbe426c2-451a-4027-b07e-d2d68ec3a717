import React from 'react';
import { I18nManager, Image, Platform, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '.';
import { AssetsImages, Colors, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { flipIconStyles } from '@utils/constants';
import { useTranslation } from 'react-i18next';

type OnBoardingStepperProps = {
    length?: number;
    step: number | string;
    showNextBtn: boolean;
    onPressNext: () => void;
    showStepper?: boolean;
    nextBtn?: string;
}

const OnBoardingStepper = ({
    length = 3,
    step,
    showNextBtn,
    onPressNext,
    showStepper = true,
    nextBtn
}: OnBoardingStepperProps) => {
    const { theme, isDarkMode } = useTheme();
    const { t } = useTranslation()
    return (
        <View style={styles.container}>
            {showStepper &&
                <View style={styles.stepperView}>
                    {Array.from({ length }).map((res, index) => {
                        return (
                            <View style={step == (index + 1) ? [styles.countCounter, isDarkMode && { backgroundColor: Colors.activeStateDark }] : styles.unSelectedCounter}>
                                <TextView style={step == (index + 1) ? [[styles.selectedStepCount, I18nManager.isRTL && { marginTop: -3 }], isDarkMode && { color: Colors.black }] :
                                    [[styles.stepCount, I18nManager.isRTL && { marginTop: -3 }], { color: theme.textColor }]}
                                    text={t(index + 1)} />
                            </View>
                        )
                    })}
                </View>}
            {showNextBtn && <Pressable onPress={onPressNext}
                style={styles.nextView}>
                <Pressable onPress={onPressNext}>
                    <TextView style={[styles.next, isDarkMode && { color: theme.textColor }]} text={nextBtn ? nextBtn : 'Next'} />
                </Pressable>
                <View style={[styles.nextContainer, isDarkMode && { backgroundColor: Colors.activeStateDark }]}>
                    <Image source={AssetsImages.nextArrow}
                        style={[styles.nextArrow, flipIconStyles, isDarkMode && { tintColor: 'black' }]} />
                </View>
            </Pressable>}

        </View>

    )
}

export default OnBoardingStepper;

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        bottom: Platform.OS == 'android' ? 20 : 36,
        height: 50,
        width: '100%',
        paddingHorizontal: 20,
    },
    stepCount: {
        color: 'black',
        fontFamily: fonts.medium,
        fontSize: fontSize.small,
        marginTop: Platform.OS == 'android' ? -2 : 0
    },
    selectedStepCount: {
        color: 'white',
        fontFamily: fonts.medium,
        fontSize: fontSize.small,
        marginTop: Platform.OS == 'android' ? -2 : 0
    },
    stepperView: {
        flexDirection: 'row',
        alignItems: 'center',

    },
    next: {
        color: 'black',
        fontFamily: fonts.semiBold,
        fontSize: fontSize.h4, //h4
        marginHorizontal: 15
    },
    countCounter: {
        backgroundColor: 'black',
        width: 20,
        height: 20,
        borderRadius: 15,
        marginRight: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    unSelectedCounter: {
        width: 20,
        height: 20,
        borderRadius: 15,
        marginRight: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    nextContainer: {
        width: Platform.OS == 'android' ? 50 : 56,
        height: Platform.OS == 'android' ? 50 : 56,
        borderRadius: 45,
        backgroundColor: 'black',
        alignItems: 'center',
        justifyContent: 'center'
    },
    nextView: {
        flexDirection: 'row',
        alignItems: 'center',
        position: 'absolute',
        right: 20
    },
    nextArrow: {
        width: 30,
        height: 30,
        resizeMode: 'contain'
    }
})