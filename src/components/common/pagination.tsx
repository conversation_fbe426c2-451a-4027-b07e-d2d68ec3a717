import React from 'react';
import { StyleSheet, View } from 'react-native';
import { widgetPadding } from '@utils/constants';

const Pagination = ({ data, selectedIndex }) => {
  return (
    <View style={[styles.container]}>
      {data.map((item, index) => (
        <View
          key={item.id}
          style={[styles.indicator, index == selectedIndex && styles.selectedIndicator]}
        />
      ))}
    </View>
  );
};

export default Pagination;

const styles = StyleSheet.create({
  indicator: {
    width: 12,
    height: 12,
    borderRadius: 10,
    backgroundColor: 'rgba(187, 187, 187, 1)',
    marginHorizontal: 3,
  },
  selectedIndicator: {
    width: 28,
    height: 12,
    borderRadius: 10,
    backgroundColor: 'rgba(120, 120, 120, 1)',
    marginHorizontal: 3,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: widgetPadding,
  },
});
