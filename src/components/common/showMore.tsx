import React from 'react';
import { Image, StyleProp, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { AssetsImages } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { isPad } from '@utils/fontSize';
import Colors from '../../theme/Colors';
import { LabelConfig } from '../../theme/labelConfig';
import { fonts, fontSize } from '../../utils';
import TextView from './TextView';

const { showMore, showLess } = LabelConfig.talentProfile.about;

type ShowMoreProps = {
  onExpend: () => void;
  isExpend: boolean;
  style?: StyleProp<ViewStyle>;
};

const ShowMore = ({ onExpend, isExpend, style }: ShowMoreProps) => {
  const { theme } = useTheme();
  const color = { color: theme.textColor };
  const tintColor = { tintColor: theme.tintColor };
  return (
    <TouchableOpacity onPress={onExpend} style={[styles.viewMoreContainer, style]}>
      <TextView style={[styles.ViewMore, color]} text={!isExpend ? showMore : showLess} />
      <Image
        style={[
          styles.dropdown,
          tintColor,
          {
            transform: [{ scaleY: isExpend ? -1 : 1 }],
          },
        ]}
        source={AssetsImages.dropdown}
      />
    </TouchableOpacity>
  );
};

export default ShowMore;

const styles = StyleSheet.create({
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginHorizontal: 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  dropdown: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
    marginStart: 0,
  },
});
