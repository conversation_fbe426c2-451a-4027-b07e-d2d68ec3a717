import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Dimen } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { TotaraTheme } from '@totara/theme/Theme';
import { widgetPadding } from '@utils/constants';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const Skeleton = ({ skeletonStyle, count }) => {
  const { theme, isDarkMode } = useTheme();
  const bgColor = isDarkMode ? theme.bgLightDark : TotaraTheme.colorNeutral3;
  const highlightColor = isDarkMode ? theme.bgDark : 'silver';

  if (count) {
    return (
      <View style={styles.container}>
        {Array.from({ length: count }).map((_, index) => (
          <SkeletonPlaceholder
            key={index}
            highlightColor={highlightColor}
            backgroundColor={bgColor}
          >
            <View style={[styles.ItemContainer, skeletonStyle]} />
          </SkeletonPlaceholder>
        ))}
      </View>
    );
  } else {
    return (
      <SkeletonPlaceholder highlightColor={highlightColor} backgroundColor={bgColor}>
        <View style={[styles.ItemContainer, skeletonStyle]} />
      </SkeletonPlaceholder>
    );
  }
};

export default Skeleton;

const styles = StyleSheet.create({
  ItemContainer: {
    width: Dimen.width - widgetPadding * 2,
    borderRadius: 16,
    overflow: 'hidden',
  },
  container: {
    flexDirection: 'row',
  },
});
