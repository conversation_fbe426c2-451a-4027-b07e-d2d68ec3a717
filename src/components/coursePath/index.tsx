import { AssetsImages, Colors, Dimen, Icons, Images } from '../../theme';
import React from 'react';
import { Image, ImageBackground, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { Text } from 'react-native-paper';
import { Rating } from 'react-native-ratings';
import { lineHeight } from '@utils/constants';

type props = {
  index: number;
  item: Record<string, string>;
};

const CoursesPath = ({ index, item }: props) => {
  return (
    <View
      key={index}
      style={[
        styles.itemView,
        {
          backgroundColor: item.backgroundColor,
        },
      ]}
    >
      <View style={styles.imageView}>
        <View style={styles.actionView}>
          <View style={styles.coursesCountContainer}>
            <Text style={styles.courseCount}>{item.courses}</Text>
            {/* <TextView style={styles.courseCount} text="12" /> */}
            <TextView style={styles.courseText} text="Courses" />
          </View>
          <Pressable>
            {item?.courseid ? (
              <FavItem style={styles.like} color={Colors.white} courseid={item?.courseid} />
            ) : (
              <Image style={styles.like} source={Images.like} />
            )}
          </Pressable>
        </View>
      </View>
      <View style={styles.detailView}>
        <TextView style={styles.courseHeading} text={item.Text} type="h1" />
        <View style={styles.rowJustify}>
          <View style={styles.row}>
            <Image source={AssetsImages.clock} style={styles.clock} />
            <TextView style={styles.hours} type="h5" text={item.hours + ' hours'} />
          </View>
        </View>
      </View>
    </View>
  );
};

export default CoursesPath;

const styles = StyleSheet.create({
  itemView: {
    width: Dimen.width * 0.6,
    padding: 16,
    borderRadius: 10,
    marginRight: 15,
    overflow: 'hidden',
    backgroundColor: 'rgba(125, 161, 196, 0.4)',
  },
  imageView: {
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    marginBottom: 24,
  },
  detailView: {
    flex: 0.7,
  },

  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  coursesCountContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  courseCount: {
    fontFamily: fonts.regular,
    fontSize: 56,
    height: 45,
    marginEnd: 8,
    lineHeight: lineHeight(56)
  },
  courseText: {
    fontFamily: fonts.regular,
    color: 'rgba(30, 30, 30, .7)',
    fontSize: fontSize.medium,
    fontWeight: '500',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  like: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
    tintColor: 'gray',
  },
  viewMore: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  courseHeading: {
    color: Colors.black,
    fontFamily: fonts.medium,
    marginBottom: 16,
    fontSize: fontSize.xxxlarge,
    lineHeight: lineHeight(fontSize.xxxlarge)
  },
  clock: {
    width: 20,
    height: 20,
    tintColor: 'rgba(30, 30, 30, 1)',
    marginRight: 8,
  },
  hours: {
    fontFamily: fonts.regular,
    color: 'rgba(30, 30, 30, .7)',
    fontSize: fontSize.medium,
  },
});
