import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Image, ImageBackground, Pressable, StyleSheet, View } from 'react-native';
import { FavItem, TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import { IconsType } from '../../theme/Icons';
import { AssetsImages, Colors, Icons } from '../../theme';
import { setBottomSheetVisible } from '../../totara/reducers/addToListBottomSheetReducer';
import { useDispatch } from 'react-redux';
import { setReferCourse } from '../../totara/reducers/referCourseReducer';
import { convertNumbersToArabicNumerals, lineHeight, parseAndFormatDuration } from '../../utils/constants';
import { useTheme } from '@theme/ThemeProvider';
import LinearGradient from 'react-native-linear-gradient';

type props = {
  index: number;
  item: Record<string, number | string>;
  tag?: boolean;
};

const CoursesItem = ({ item, index, tag }: props) => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const navigate = () => navigation.navigate('CourseDetail', { ...item, from: 'required' });
  const dispatch = useDispatch();
  const onPressMore = () => {
    setReferCourse(dispatch, {
      courseid: item?.courseid,
      name: item?.title,
      image: item?.image,
    });
    setBottomSheetVisible(dispatch, true);
  };


  return (
    <Pressable onPress={navigate} key={index} style={styles.itemView}>
      <ImageBackground
        source={{ uri: item?.image }}
        style={styles.imageView}
        imageStyle={styles.image}
      >
        <LinearGradient
          start={{ x: 10, y: 0.9 }}
          end={{ x: 4, y: 0 }}
          colors={['transparent', 'rgba(0, 0, 0, 0.2)']}
          style={styles.gradientOverlay}
        />

        <View style={styles.actionView}>
          {tag && (
            <View style={styles.courseTag}>
              <TextView style={[styles.tagText, { color: theme.textColor }]} text={item?.coursetype || ''} />
            </View>
          )}
          <View style={styles.heartAndThreeDotStyle}>
            <FavItem style={styles.like} color={Colors.white} courseid={item?.courseid} />
            <Pressable onPress={onPressMore}>
              <Icons
                color={Colors.white}
                size={18}
                type={IconsType.Entypo}
                style={styles.viewMore}
                name={'dots-three-vertical'}
              />
            </Pressable>
          </View>
        </View>
      </ImageBackground>


      <View style={styles.detailView}>
        <TextView numberOfLines={2} style={[styles.courseHeading, { color: theme.textColor }]} text={item?.title} />
        <View>
          <View style={styles.rowJustify}>
            {item?.duration && item?.duration != '-' && item.duration != ' ' ? (
              <View style={styles.clockTime}>
                <Image source={AssetsImages.clock} style={[styles.clock, { tintColor: theme.textColor }]} />
                <TextView
                  numberOfLines={1}
                  style={[styles.ratingText, { color: theme.textColor }]}
                  type="h5"
                  text={
                    Number(item?.duration) ? parseAndFormatDuration(item?.duration) : convertNumbersToArabicNumerals(item?.duration)
                  }
                />
              </View>
            ) : null}
            <View style={[styles.row]}>
              <Icons
                color={Colors.rating}
                size={19}
                type={IconsType.Entypo}
                style={{ marginRight: 5 }}
                name={'star'}
              />
              <TextView numberOfLines={1} type="h5" style={[styles.ratingText, { color: theme.textColor }]}
                text={convertNumbersToArabicNumerals(item?.rating)} />
            </View>
          </View>
          <View style={styles.rowJustify}>
            <View style={[styles.clockTime, { maxWidth: 100 }]}>
              <Image
                source={{ uri: item?.providerlogo }}
                style={[styles.courseSourceIcon, { borderRadius: 99 }]}
              />
              <TextView numberOfLines={1} style={[styles.ratingText, { color: theme.textColor, marginStart: 5 }]} type="h5"
                text={item?.providername} />
            </View>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default CoursesItem;

const styles = StyleSheet.create({
  itemView: {
    width: 220,
    marginRight: 15,
    overflow: 'hidden',
    paddingBottom: 20,
    flex: 1,
  },
  linearGradient: {
    backgroundColor: 'black',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    opacity: 0.12,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  imageView: {
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    height: 170,
  },
  image: {
    borderRadius: 20,
  },
  clockTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 10,
  },
  detailView: {
    justifyContent: 'space-between',
    flex: 1
  },
  courseTag: {
    borderColor: Colors.borderGray,
    borderWidth: 1,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 10,
  },
  tagText: {
    fontFamily: fonts.semiBold,
    fontSize: isPad ? 12 : fontSize.mini,
    textTransform: 'uppercase',
    paddingHorizontal: 16,
  },
  heartAndThreeDotStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
    width: 40,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 40,
  },
  like: {
    resizeMode: 'contain',
    marginRight: 10,
    height: 20,
    width: 20,
  },
  viewMore: {
    resizeMode: 'contain',
  },
  courseHeading: {
    paddingVertical: 15,
    fontSize: fontSize.small,
    fontFamily: fonts.medium,
    textAlign: 'left'
  },
  star: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  clock: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  rowJustify: {
    flexDirection: 'row',
    paddingBottom: 8,
  },
  ratingText: {
    fontFamily: fonts.medium,
    fontSize: fontSize.xmini,
  },
  courseSourceIcon: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 20,
  },
});
