import { useNavigation } from '@react-navigation/native';
import React from 'react';
import {
  I18nManager,
  Image,
  ImageBackground,
  Platform,
  Pressable,
  StyleSheet,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { FavItem, TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import { IconsType } from '../../theme/Icons';
import { AssetsImages, Colors, Icons } from '../../theme';
import { setBottomSheetVisible } from '../../totara/reducers/addToListBottomSheetReducer';
import { useDispatch } from 'react-redux';
import { setReferCourse } from '../../totara/reducers/referCourseReducer';
import {
  convertNumbersToArabicNumerals,
  lineHeight,
  lineHeightForAndroidLTR,
  parseAndFormatDuration,
} from '../../utils/constants';
import { useTheme } from '@theme/ThemeProvider';

type props = {
  index: number;
  item: Record<string, number | string>;
  tag?: boolean;
};

const CoursesItemSmallCard = ({ item, index, tag }: props) => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const navigate = () => {
    navigation.navigate(
      item?.course_days == 'Multi Day' && item?.coursetype != 'Self-Paced'
        ? 'MultipleSession'
        : 'CourseDetail',
      {
        ...item,
        courseid: item?.courseid ? item?.courseid : item?.id,
      },
    );
  }
  const dispatch = useDispatch();
  const onPressMore = () => {
    setReferCourse(dispatch, {
      courseid: item?.courseid,
      name: item?.title,
      image: item?.image,
    });
    setBottomSheetVisible(dispatch, true);
  };
  const logo = item?.providerlogo ? { uri: item?.providerlogo } : AssetsImages.tomouhProvider;

  return (
    <Pressable onPress={navigate} key={index} style={styles.itemView}>
      <View style={styles.imageContainer}>
        <ImageBackground
          source={{ uri: item?.image }}
          style={[styles.imageView, { backgroundColor: theme.bgLightDark }]}
          imageStyle={styles.image}
        >
          <LinearGradient
            start={{ x: 0, y: 0.2 }}
            end={{ x: 0, y: 1 }}
            colors={isDarkMode ? ['rgba(0,0,0,0.5)', 'rgba(0,0,0,0.2)'] : ['transparent', 'rgba(0, 0, 0, 0.1)']}
            style={styles.gradientOverlay}
          />
          <View style={styles.actionView}>
            {tag && (
              <View style={styles.courseTag}>
                <TextView
                  style={[styles.tagText, { color: theme.textColor }]}
                  text={item?.coursetype || ''}
                />
              </View>
            )}
            <View style={[styles.heartAndThreeDotStyle]}>
              {item?.courseid ? (
                <FavItem style={styles.favItem} color={'white'} courseid={item?.courseid} />
              ) : null}

              <Pressable onPress={onPressMore}>
                <Icons
                  color={Colors.white}
                  size={18}
                  type={IconsType.Entypo}
                  style={styles.viewMore}
                  name={'dots-three-vertical'}
                />
              </Pressable>
            </View>
          </View>
        </ImageBackground>
      </View>
      <View style={styles.detailView}>
        <TextView
          numberOfLines={2}
          style={[
            styles.courseHeading,
            { color: theme.textColor },
            Platform.OS == 'android' && !I18nManager.isRTL && { lineHeight: 20 },
          ]}
          text={item?.title}
        />
        <View>
          <View style={styles.rowJustify}>
            {item?.duration && item?.duration !== '-' && item.duration !== ' ' ? (
              <View style={[styles.clockTime, { maxWidth: 100 }]}>
                <Image
                  source={AssetsImages.clock}
                  style={[styles.clock, { tintColor: theme.textColor, resizeMode: 'contain' }]}
                />
                <TextView
                  style={[
                    styles.ratingText,
                    {
                      color: theme.textColor,
                      paddingBottom: 2,
                    },
                  ]}
                  type="h5"
                  text={
                    Number(item?.duration)
                      ? parseAndFormatDuration(item?.duration)
                      : convertNumbersToArabicNumerals(item?.duration)
                  }
                />
              </View>
            ) : null}
            {!!item?.rating && (
              <View style={[styles.row]}>
                <Image style={styles.star} source={AssetsImages.ratingStar} />
                <TextView
                  numberOfLines={1}
                  type="h5"
                  style={[styles.ratingText, { color: theme.textColor }]}
                  text={convertNumbersToArabicNumerals(item?.rating)}
                />
              </View>
            )}
          </View>
          <View style={styles.rowJustify}>
            {item?.providername || item?.provider ? (
              <View style={[styles.clockTime, { maxWidth: 150 }]}>
                <Image source={logo} style={styles.courseSourceIcon} />
                <TextView
                  numberOfLines={1}
                  style={[
                    styles.ratingText,
                    { color: theme.textColor, marginStart: 5, marginEnd: 8 },
                  ]}
                  type="h5"
                  text={item?.providername || item?.provider}
                />
              </View>
            ) : null}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default CoursesItemSmallCard;

const styles = StyleSheet.create({
  itemView: {
    width: 150,
    marginRight: 6,
    overflow: 'hidden',
    flex: 1,
  },
  imageContainer: {
    borderRadius: 10,
    height: 111,
    overflow: 'hidden',
  },
  imageView: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.borderGray,
    borderRadius: 10,
    overflow: 'hidden',
  },
  image: {
    borderRadius: 10,
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 10,
  },
  clockTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 10,
  },
  detailView: {
    flex: 1,
    justifyContent: 'space-between',
  },
  courseTag: {
    borderColor: Colors.borderGray,
    borderWidth: 1,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 10,
    paddingTop: 10,
  },
  tagText: {
    fontFamily: fonts.semiBold,
    fontSize: isPad ? 12 : fontSize.mini,
    textTransform: 'uppercase',
    paddingHorizontal: 16,
  },
  heartAndThreeDotStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
    width: 40,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 40,
    marginEnd: 16,
  },
  like: {
    resizeMode: 'contain',
    height: 20,
    width: 20,
  },
  viewMore: {
    resizeMode: 'contain',
  },
  courseHeading: {
    paddingTop: 15,
    paddingBottom: 5,
    fontSize: fontSize.small,
    fontFamily: fonts.medium,
    textAlign: 'left',
  },
  star: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginTop: -1,
  },
  clock: {
    width: 18,
    height: 18,
    marginRight: 5,
  },
  courseSourceIcon: {
    width: 15,
    height: 15,
    borderRadius: 5,
  },
  rowJustify: {
    flexDirection: 'row',
    paddingBottom: 8,
  },
  ratingText: {
    color: Colors.black,
    fontSize: fontSize.xmini,
    fontFamily: fonts.regular,
    paddingHorizontal: 5,
  },
  favItem: {
    marginTop: 2,
    marginRight: 5,
    tintColor: 'white',
  },
});
