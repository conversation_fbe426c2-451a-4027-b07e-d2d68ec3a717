import { RenderListItemPropsType, ScreenNavigationProp } from 'interfaces/courseListingInterface';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { TextView } from '../../components/common';
import { Colors, Dimen, Icons } from '../../theme';
import { routes } from '../../utils';

const CoursesListingItem =
  (navigation: ScreenNavigationProp) =>
  ({ item, index }: RenderListItemPropsType) => {
    const param = { heading: item.Text };
    const navigate = () => navigation.navigate(routes.CategoryListing, param);

    return (
      <TouchableOpacity onPress={navigate} key={index} style={styles.listItem}>
        <View style={styles.iconAndHeadingContainer}>
          <View style={[styles.iconContainer]}>
            <Icons style={styles.listIcon} name={item.Icon} />
          </View>
          <TextView type="h2" text={item.Text} />
        </View>
        <Icons style={styles.listIconRight} name="chevronRight" />
      </TouchableOpacity>
    );
  };

export default CoursesListingItem;

const styles = StyleSheet.create({
  header: {
    marginBottom: 40,
    paddingHorizontal: 10,
  },
  headerContainer: {
    width: Dimen.width,
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  categoryCount: {},
  listItem: {
    padding: 18,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 0.5,
    borderColor: Colors.borderGray,
    justifyContent: 'space-between',
  },
  iconAndHeadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listContainer: {
    flexDirection: 'row',
  },
  iconContainer: {
    marginEnd: 14,
    borderRadius: 50,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.creamColor,
  },
  listIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  listIconRight: {
    resizeMode: 'contain',
    width: 24,
    height: 24,
  },
});
