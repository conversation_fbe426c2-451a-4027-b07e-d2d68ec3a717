import React from 'react';
import { FlatList, View } from 'react-native';
import { CoursesItemSmallCard, NoRecordsTextView } from '../index';


const LearningUnder30Mints = ({ learningUnder30Mints }) => {

  return (
    <View>
      <FlatList
        showsHorizontalScrollIndicator={false}
        horizontal
        style={{ alignSelf: 'flex-start' }}
        data={learningUnder30Mints}
        ListEmptyComponent={<NoRecordsTextView containerStyles={{ paddingHorizontal: 24 }} />}
        renderItem={({ item, index }) => {
          const customStyles = {
            marginStart: index === 0 ? 24 : 0,
            marginEnd: index === learningUnder30Mints.length - 1 ? 24 : 8,
          };
          return (
            <View key={index} style={customStyles}>
              <CoursesItemSmallCard tag={false} item={item} index={index} />
            </View>
          );
        }}
        keyExtractor={(item) => item?.title}
      />
    </View>
  );
};

export default LearningUnder30Mints;
