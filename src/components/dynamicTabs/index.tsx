import React, { useEffect, useState } from 'react';
import { Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import { useTheme } from '@theme/ThemeProvider';
import Colors from '../../theme/Colors';
import { fonts, fontSize } from '../../utils';
import { FlatList } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { I18nManager } from 'react-native';
import { lineHeight, widgetPadding } from '@utils/constants';
import { useTranslation } from 'react-i18next';

const DynamicTabs = ({
  tabs,
  getSelectedTab,
  containerStyle,
  horizontal,
  initiallySelected,
  getSelectedTabValue,
  scrollEnabled
}) => {
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();
  const [selectedTab, setTab] = useState();
  const handleOnSelect = (item) => () => {
    setTab(item.id);
    getSelectedTab(item.id);
    getSelectedTabValue && getSelectedTabValue(item)
  };

  useEffect(() => {
    if (initiallySelected && tabs?.length) {
      setTab(tabs[0]?.id);
      getSelectedTab(tabs[0]?.id);
    }
    else {
      setTab();
    }
  }, [initiallySelected, tabs?.length]);

  const renderItem = ({ item }) => {
    const style = selectedTab == item?.id
      ? isDarkMode ? styles.selectedTabDarkStyle : styles.selectedTabStyle
      : [styles.tabStyle, isDarkMode && { borderColor: theme.bgLightDark }];

    return (
      <Pressable
        onPress={handleOnSelect(item)}
        style={[style, { flexDirection: 'row' }]} key={item?.id}>

        <TextView
          style={[
            styles.tabText,
            { color: theme.textColor },
            selectedTab == item?.id && { color: !isDarkMode ? Colors.white : Colors.black },
          ]}
          text={`${t(item?.name)?.charAt(0).toUpperCase() + (t(item?.name)?.toLowerCase())?.slice(1)}`}
        />
        {/* {item?.name == 'Newly Added' &&
          <LinearGradient
            colors={['rgba(183, 244, 83, 1)', 'rgba(112, 181, 0, 1)']} style={styles.newlyAddedCount}>
            <TextView style={styles.newlyAddedTxt} text='2' />
          </LinearGradient>} */}
      </Pressable>
    );
  };

  return (
    <FlatList
      scrollEnabled={scrollEnabled}
      nestedScrollEnabled
      contentContainerStyle={styles.container}
      data={tabs}
      style={[styles.scrollViewStyle, containerStyle]}
      renderItem={renderItem}
      keyExtractor={(item) => item?.id?.toString()}
      horizontal
      showsHorizontalScrollIndicator={false}
    />
  )



};

export default DynamicTabs;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingEnd: 40,
    justifyContent: 'flex-start',
  },
  tabStyle: {
    borderWidth: 1,
    borderColor: Colors.borderGray,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: widgetPadding / 2
  },
  selectedTabStyle: {
    borderWidth: 1,
    borderColor: Colors.black,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.black,
    marginRight: widgetPadding / 2
  },
  selectedTabDarkStyle: {
    borderWidth: 1,
    borderColor: Colors.activeStateDark,
    backgroundColor: Colors.activeStateDark,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: widgetPadding / 2
  },
  tabText: {
    paddingHorizontal: widgetPadding,
    fontFamily: fonts.regular,
    fontSize: fontSize.h7,
    paddingVertical: widgetPadding / 2
  },
  scrollViewStyle: {
    marginVertical: widgetPadding
  },
  newlyAddedCount: {
    marginEnd: 15,
    backgroundColor: 'rgba(183, 244, 83, 1)',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginStart: -5
  },
  newlyAddedTxt: {
    fontSize: fontSize.xmini,
    fontFamily: fonts.medium,
    color: 'black'
  }
});
