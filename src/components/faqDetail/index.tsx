import React, { useEffect, useState } from 'react';
import { BottomSheetContainer } from '../index';
import { Platform, StyleSheet, View, BackHandler, Text } from 'react-native';
import AssetsImages from '../../theme/AssetsImages';
import Colors from '../../theme/Colors';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../totara/reducers';
import fontSize, { isPad } from '../../utils/fontSize';
import BottomSheet from '../bottomSheet';
import { lineHeight } from '@utils/constants';
import { I18nManager } from 'react-native';
import { useTheme } from "@theme/ThemeProvider";

const FaqDetail = ({ data, onCloses }) => {
  const { theme } = useTheme();

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={{ paddingBottom: 100 }}
      visiblity={true}
      setVisibility={onCloses}
      children={
        <View style={styles.body}>
          <Text style={[styles.text, { color: theme.textColor }]}>{I18nManager.isRTL ? data?.question?.ar_SA : data?.question?.en_US}</Text>
          <Text style={[styles.texth2, { color: theme.textColor }]}>{I18nManager.isRTL ? data?.answer?.ar_SA : data?.answer?.en_US}</Text>
        </View>
      }
    />
  );
};

export default FaqDetail;

const styles = StyleSheet.create({
  line: { borderBottomWidth: 0.5, borderBottomColor: Colors.grayline },
  bottomSheet: { paddingHorizontal: 0 },
  body: { paddingTop: 20, paddingHorizontal: 20 },
  text: {
    fontSize: fontSize.large,
    fontWeight: '500',
    color: Colors.black,
    textAlign: 'left'
  },
  texth2: {
    marginTop: 10,
    fontSize: fontSize.small,
    fontWeight: '400',
    color: '#4B494A',
    lineHeight: lineHeight(fontSize.small),
    textAlign: 'left'
  },
});
