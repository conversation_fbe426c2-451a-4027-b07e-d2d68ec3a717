import React, { useMemo } from 'react';
import { I18nManager, ImageBackground, Pressable, StyleSheet, Text, View } from 'react-native';
import AnalyticsConstants from '@analytics/AnalyticsConstants';
import { analyticsService } from '@analytics/AnalyticsService';
import { useNavigation } from '@react-navigation/native';
import { AssetsImages, Colors, Dimen, fonts } from '@theme';
import { commonStyle } from '@utils';
import { widgetPadding } from '@utils/constants';
import { useTranslation } from 'react-i18next';
import FastImage from 'react-native-fast-image';

const DeviceWidths = {
  S24_ULTRA: 480,
  IPHONE_16_PRO_MAX: 440,
  IPHONE_16: 393,
  IPHONE_13_MINI: 375,
  S24: 360,
  IPHONE_SE_1ST: 320,
};

function getFontSizes(width) {
  switch (true) {
    case width <= DeviceWidths.IPHONE_SE_1ST:
      return { fontSizeTitle: 14, fontSizeSubtitle: 10 };
    case width >= DeviceWidths.S24_ULTRA:
      return { fontSizeTitle: 24, fontSizeSubtitle: 16 };
    case width >= DeviceWidths.IPHONE_16_PRO_MAX:
      return { fontSizeTitle: 22, fontSizeSubtitle: 15 };
    case width >= DeviceWidths.IPHONE_13_MINI:
      return { fontSizeTitle: 17, fontSizeSubtitle: 12 };
    default:
      return { fontSizeTitle: 16, fontSizeSubtitle: 11 };
  }
}

const getGapSizes = (width) => {
  switch (true) {
    case width >= DeviceWidths.S24_ULTRA:
    case width >= DeviceWidths.IPHONE_16_PRO_MAX:
      return 0;
    case width >= DeviceWidths.IPHONE_16:
      return 2;
    case width >= DeviceWidths.IPHONE_13_MINI:
      return 4;
    case width >= DeviceWidths.S24:
      return 5;
    case width >= DeviceWidths.IPHONE_SE_1ST:
      return 6;
    default:
      return 7;
  }
};

const getImage = (width) => {
  switch (true) {
    case width >= DeviceWidths.S24_ULTRA:
    case width >= DeviceWidths.IPHONE_16_PRO_MAX:
      return AssetsImages.digitalLearningChallengeBgLarge;
    case width >= DeviceWidths.IPHONE_16:
    case width >= DeviceWidths.IPHONE_13_MINI:
      return AssetsImages.digitalLearningChallengeBgMedium;
    case width >= DeviceWidths.IPHONE_SE_1ST:
      return AssetsImages.digitalLearningChallengeBgSmall;
    default:
      return AssetsImages.digitalLearningChallengeBgSmall;
  }
};

const DigitalLearningChallengeBanner = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const padding = useMemo(() => (Dimen.width > DeviceWidths.S24 ? 24 : 20) * 0.9, [Dimen.width]);
  const fontSizes = useMemo(() => getFontSizes(Dimen.width), [Dimen.width]);
  const gap = useMemo(() => getGapSizes(Dimen.width), [Dimen.width]);
  const styles = useStyles(padding, fontSizes, gap);
  const image = useMemo(() => getImage(Dimen.width), [Dimen.width]);

  const navigateTo = ({ navigate, routeId, props }) => {
    navigate(routeId, props);
  };

  return (
    <ImageBackground
      resizeMode={FastImage.resizeMode.contain}
      source={image}
      style={[styles.bannerStyle]}
      imageStyle={styles.imageStyle}
    >
      <View style={styles.container}>
        <View style={styles.top}>
          <View>
            <Text style={styles.title}>{t('Join the 90-Day Digital')}</Text>
            <Text style={styles.title}>{t('Learning Challenge!')}</Text>
          </View>

          <View>
            <Text style={styles.subtitle}>{t('Ready to learn anywhere? Take on')}</Text>
            <Text style={styles.subtitle}>{t('the challenge with Tomouh’s')}</Text>
            <Text style={styles.subtitle}>{t('extensive range of online courses.')}</Text>
          </View>
        </View>

        <Pressable
          onPress={() => {
            analyticsService.logEvent(AnalyticsConstants.DIGITAL_LEARNING_CHALLENGE_BANNER_CLICKED);
            navigateTo({
              navigate: navigation.navigate,
              routeId: 'Learning',
              props: {},
            });
          }}
        >
          <Text style={styles.cta}>{t(`Start learning now`)}</Text>
        </Pressable>
      </View>
    </ImageBackground>
  );
};

const useStyles = (bannerPaddingSize, fontSizes, gap) =>
  StyleSheet.create({
    top: {
      gap: gap,
      paddingTop: bannerPaddingSize,
    },
    container: {
      height: '100%',
      flexDirection: 'column',
      alignContent: 'space-between',
      justifyContent: 'space-between',
    },
    bannerStyle: {
      width: Dimen.width / 1.08,
      borderRadius: 10,
      marginVertical: widgetPadding,
      alignSelf: 'center',
      marginHorizontal: widgetPadding,
      paddingHorizontal: bannerPaddingSize,
      aspectRatio: 343 / 155,
    },
    imageStyle: {
      transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
      borderRadius: 16,
    },
    title: {
      overflow: 'hidden',
      color: Colors.white,
      fontFamily: fonts.bold,
      fontSize: fontSizes?.fontSizeTitle * 0.95,
      lineHeight: I18nManager.isRTL
        ? fontSizes?.fontSizeTitle * 1.3
        : fontSizes?.fontSizeTitle * 1.2,
      ...commonStyle.textDirection,
    },
    subtitle: {
      color: Colors.white,
      fontFamily: fonts.regular,
      fontSize: fontSizes?.fontSizeSubtitle * 0.95,
      lineHeight: fontSizes?.fontSizeSubtitle * 1.4,
      letterSpacing: 0.5,
      ...commonStyle.textDirection,
    },
    cta: {
      color: Colors.white,
      fontFamily: fonts.regular,
      fontSize: fontSizes?.fontSizeSubtitle * 0.95,
      lineHeight: fontSizes?.fontSizeSubtitle * 1.4,
      letterSpacing: 0.5,
      paddingBottom: bannerPaddingSize,
      textDecorationLine: 'underline',
      ...commonStyle.textDirection,
    },
  });

export default DigitalLearningChallengeBanner;
