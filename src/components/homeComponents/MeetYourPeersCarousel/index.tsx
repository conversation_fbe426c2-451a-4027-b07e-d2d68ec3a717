import React, { useState } from 'react';
import { FlatList, Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { AssetsImages, fonts, fontSize } from '@theme';
import { commonStyle } from '@utils';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { generateRandomString } from '@utils/AppUtils';
import { TotaraTheme } from '@totara/theme/Theme';
import { NAVIGATION } from '@totara/lib/navigation';

const peersList = [{
  id: 123456,
  name: "<PERSON><PERSON><PERSON>",
  role: "Director General"
}, {
  id: 2346789,
  name: "<PERSON>",
  role: "Senior Project Manager"
}, {
  id: 5679876,
  name: "<PERSON>",
  role: "Trainer ADSG"
}]

const MeetYourPeersCarousel = () => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const { styles, theme, isDarkMode } = useStyles();
  const [isLoading, setLoading] = useState<boolean>(false);

  const bgColor = isDarkMode ? theme.bgLightDark : TotaraTheme.colorNeutral3;
  const highlightColor = isDarkMode ? theme.bgDark : 'silver'

  return (
    <View style={styles.container}>
      <View style={styles.headingContainer}>
        <Text style={styles.heading}>
          {t('Meet Your Peers')}
        </Text>

        <Pressable
          onPress={() => navigation.navigate(NAVIGATION.MEET_YOUR_PEERS_LIST)}
          style={styles.viewAllContainer}
        >
          <Text style={styles.viewAllText}>
            {`${t('View all')}`}
          </Text>
        </Pressable>
      </View>

      <View style={styles.flatListWrapper}>
        {!isLoading ? (
          <FlatList
            data={peersList}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.flatListContainer}
            horizontal
            keyExtractor={({ id, name, role }) => `${id}${name}${role}`}
            renderItem={({ item, index }) => (
              <View style={[styles.cardContainer, index === 0 && {marginLeft: 16}]}>
                <Image
                  defaultSource={AssetsImages.soulvite}
                  source={AssetsImages.soulvite}
                  style={styles.imageStyle}
                />
                <View style={styles.peerInfo}>
                  <Text style={styles.peerName}>
                    {item.name}
                  </Text>
                  <Text style={styles.peerRole}>
                    {item.role}
                  </Text>
                </View>
              </View>
            )}
          />
        ) : (
          <FlatList
            data={Array.from([1, 2, 3])}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.flatListContainer}
            horizontal
            keyExtractor={(item) => `${item}${generateRandomString()}`}
            renderItem={({ item, index }) => (
              <View style={[styles.cardContainer, index === 0 && {marginLeft: 16}]}>
                <SkeletonPlaceholder
                  highlightColor={highlightColor}
                  backgroundColor={bgColor}
                >
                  <Image
                    defaultSource={AssetsImages.soulvite}
                    source={AssetsImages.soulvite}
                    style={styles.imageStyle}
                  />
                </SkeletonPlaceholder>

                <SkeletonPlaceholder
                  highlightColor={highlightColor}
                  backgroundColor={bgColor}
                >
                  <View style={styles.peerInfo}>
                    <View style={styles.placeholderName} />
                    <View style={styles.placeholderRole} />
                  </View>
                </SkeletonPlaceholder>
                
              </View>
            )}
          />
        )}
      </View>
    </View>
  );
};

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingVertical: 24,
      paddingHorizontal: 0,
      paddingRight: 0,
      backgroundColor: !isDarkMode ? theme.backgroundColor : theme.bgDark,
      borderRadius: 12,
      margin: 16,
      flexDirection: 'column',
      gap: 16,
    },
    headingContainer: {
      flex: 1,
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
    },
    heading: {
      color: theme.thunder100,
      fontWeight: '700',
      fontSize: 20,
    },
    viewAllText: {
      fontSize: fontSize.h6,
      fontFamily: fonts.regular,
      textDecorationLine: 'underline',
      color: theme.thunder80,
    },
    viewAllContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    cardContainer: {
      padding: 16,
      flexDirection: 'row',
      flexWrap: 'nowrap',
      alignItems: 'center',
      justifyContent: 'flex-start',
      gap: 16,
      borderWidth: 1,
      borderColor: theme.thunder10,
      borderRadius: 16,
      width: 300,
    },
    imageStyle: {
      width: 64,
      height: 64,
      borderRadius: 64 / 2,
      backgroundColor: theme.thunder0,
    },
    flatListWrapper: { flex: 1, width: '100%' },
    flatListContainer: {
      minWidth: '100%',
      gap: 16,
      paddingRight: 16
    },
    peerInfo: {
      flexDirection: 'column',
      gap: 16,
      flexShrink: 1,
    },
    peerName: {
      fontWeight: '400',
      fontSize: 16,
      color: theme.thunder100,
      flexWrap: 'wrap',
      ...commonStyle.textDirection,
    },
    peerRole: {
      fontWeight: '400',
      fontSize: 14,
      color: theme.thunder80,
      ...commonStyle.textDirection,
    },
    placeholderName: { height: 20,  width: 150 },
    placeholderRole: { height: 15,  width: 100 }
  });

  return { styles, theme, isDarkMode };
}

export default MeetYourPeersCarousel;
