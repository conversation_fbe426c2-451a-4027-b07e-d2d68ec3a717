import { lineHeight, marginTop, progressTabs, widgetPadding } from '@utils/constants';
import React, { useEffect, useRef, useState } from 'react';
import { I18nManager, Platform, StyleSheet, View, ViewStyle } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import { BreadCrumb, DynamicTabs, NoRecordsTextView, Pagination, Skeleton, Spacer } from '../..';
import { Colors, Dimen, LabelConfig } from '../../../theme';
import { fontSize, fonts } from '../../../utils';
import RecommendedItem from './recommendedItem';
import { changeLearningPathwayTab } from '@totara/reducers/learningPathwayReducer';
import { useDispatch } from 'react-redux';
import { navigate } from '@navigation/navigationService';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { TotaraTheme } from '@totara/theme/Theme';
import { FlatList } from 'react-native';
import Swipeable from '@screens/Discover/Swipeable';
import { analyticsService } from '@analytics/AnalyticsService';
import AnalyticsConstants from '@analytics/AnalyticsConstants';

type Prop = {
    header?: string;
    data?: any;
    showTabs?: boolean;
    carouselStyle?: any;
    carouselHeight?: number;
    skeletonStyle?: ViewStyle;
    isLoading?: boolean;
    carouselWidth?: number;
    onViewAllPress?: () => void;
    showComponentIfEmpty?: boolean
}
const { myLearningPathways, RequiredTab, RecommendedTab, PersonalTab } = LabelConfig.learningPathways;

const bannerHeight = Dimen.height * 0.3

const HorizontalCarousel = ({ header, data, showTabs, carouselStyle, skeletonStyle, isLoading, carouselHeight, carouselWidth, showComponentIfEmpty, onViewAllPress, showFav }: Prop) => {
    const [selectedIndex, setIndex] = useState(0);
    const [tab, setTab] = useState('');
    const [filteredData, setData] = useState(null);
    const dispatch = useDispatch();
    const carouselRef = useRef(null);

    useEffect(() => {
        if (showTabs) {
            const result = data?.filter(item => (item.progress > 0 && item.progress < 100))?.slice(0, 3)
            setData(result)
        }
        else if (data) {
            setData(data)
        }
    }, [data])

    const getSelectedTab = (tabName) => {
        setTab(tabName);
        if (tabName == 'inprogress') {
            const result = data?.filter(item => item?.progress > 0 && item.progress < 100)?.slice(0, 3)
            setIndex(0)
            setData(result)

        }
        if (tabName == 'notstarted') {
            const result = data?.filter(item => item?.progress <= 0)?.slice(0, 3)
            setData(result)
        }
    }

    const onPress = () => {
        if (header == 'Recommended') {
            analyticsService.logEvent(AnalyticsConstants.CATEGORY_CLICKED, { tab: 'Recommended' })
            changeLearningPathwayTab(dispatch, RecommendedTab)
            setTimeout(() => {
                navigate('MyLearningPathways', data);
            }, 0);
        }
    }

    if (isLoading && !filteredData?.length) {
        return (
            <View style={{ paddingHorizontal: widgetPadding }}>
                <Skeleton skeletonStyle={[styles.skeletonStyle, { height: carouselHeight || 260 }, skeletonStyle, { marginTop: widgetPadding / 2 }]} />

            </View>
        )
    }


    if (showComponentIfEmpty || filteredData?.length) {
        return (
            <View style={styles.container}>
                {header && <BreadCrumb length
                    vie
                    onPress={onViewAllPress || onPress}
                    showViewAll={filteredData?.length}
                    text={header} />}
                <View style={{ alignItems: 'flex-start', flex: 1, paddingHorizontal: widgetPadding }}>
                    {showTabs && <DynamicTabs
                        initiallySelected
                        containerStyle={{ marginTop: -widgetPadding / 4 }}
                        getSelectedTab={getSelectedTab}
                        tabs={progressTabs.slice(1)}
                    />}
                </View>

                {(!isLoading && !filteredData?.length) &&
                    <View style={styles.noRecordContainer}>
                        <NoRecordsTextView
                            textStyle={{ textAlign: 'center' }}
                        />
                    </View>}


                {filteredData?.length ?
                    <Swipeable
                        onViewAllPress={onViewAllPress}
                        header={false}
                        isLoading={false}
                        containerStyle={{ height: Dimen.width * 0.70, marginBottom: 0, paddingStart: -16 }}
                        carouselHeight={(Dimen.width - (widgetPadding * 1)) * 0.75}
                        skeletonStyle={{ width: Dimen.width / 1.2, paddingHorizontal: 0, marginTop: widgetPadding, height: (Dimen.width - (widgetPadding * 2)) * 0.75 }}
                        listStyle={{ width: Dimen.width / 1.1 }}
                        renderItem={({ item, index }) => (
                            <View style={{ marginHorizontal: widgetPadding }}>
                                <RecommendedItem
                                    inProgress={tab == 'inprogress'}
                                    carouselStyle={{ width: Dimen.width / 1.21 }}
                                    showFav={showFav}
                                    bannerHeight={(Dimen.width - (widgetPadding * 4.2)) * 0.75}
                                    item={{
                                        ...item,
                                        type: item?.type || item?.component_type || 'course',
                                        courseid: item.id ? item.id : item.courseid
                                    }}
                                    index={index} />
                            </View>
                        )} data={filteredData} />
                    // <Carousel
                    //     loop={filteredData?.length > 1}
                    //     style={{ alignSelf: 'center' }}
                    //     scrollAnimationDuration={200}
                    //     width={carouselWidth || Dimen.width / 1.18}
                    //     height={carouselHeight || bannerHeight}//280
                    //     data={filteredData}
                    //     onSnapToItem={(index) => setIndex(index)}
                    //     renderItem={({ item, index }) => <RecommendedItem
                    //         inProgress={tab == 'inprogress' || tab == 'notstarted'}
                    //         bannerHeight={carouselHeight || bannerHeight}
                    //         carouselStyle={carouselStyle}
                    //         item={{
                    //             ...item,
                    //             type: item?.type || item?.component_type || 'course',
                    //             courseid: item.id ? item.id : item.courseid
                    //         }}
                    //         index={index} />}
                    // />
                    : null}
                {/* {filteredData?.length > 1 && <Pagination
                    selectedIndex={selectedIndex}
                    data={filteredData} />} */}
            </View>
        );
    }
};

export default HorizontalCarousel;

const styles = StyleSheet.create({
    bannerStyle: {
        width: Dimen.width / 1.1,
        borderRadius: 10,
        overflow: 'hidden',
        flexDirection: 'row',
        padding: 15,
        alignSelf: 'center',
        height: 250,
        marginHorizontal: 10,
        position: 'relative',
        backgroundColor: Colors.darkblue
    },
    container: {
        flex: 1,
        marginVertical: widgetPadding,
    },
    flatListStyle: {
        alignSelf: 'center',
        width: Dimen.width,
        marginVertical: 10,
        marginHorizontal: 20,
        height: 170,
    },
    detailView: {
        flex: 0.9,
    },
    badgeBtn: {
        backgroundColor: Colors.badgeBtnColor,
        alignItems: 'center',
        justifyContent: 'center',
        height: 30,
        borderRadius: 20,
        position: 'absolute',
        left: 15,
        bottom: 19,
    },
    courseName: {
        color: Colors.white,
        fontFamily: fonts.semiBold,
        fontSize: fontSize.large,
        textAlign: 'left',

    },
    badge: {
        width: 20,
        height: 15,
        resizeMode: 'contain',
    },
    courseDetail: {
        color: Colors.white,
        fontFamily: fonts.thin,
        fontSize: fontSize.xmini,
        marginTop: 5,
        marginBottom: 'auto',
        textAlign: 'left',
    },
    badgesText: {
        fontFamily: fonts.semiBold,
        fontSize: I18nManager.isRTL ? fontSize.mini : fontSize.xxmini,
        lineHeight: lineHeight(I18nManager.isRTL ? fontSize.mini : fontSize.xxmini),
        paddingHorizontal: 25,
    },
    paginationContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignSelf: 'center',
    },
    tabStyle: {
        marginBottom: widgetPadding + 6,
        marginStart: 20,
    },

    skeletonStyle: {
        width: Dimen.width - (widgetPadding * 2),
        height: 260,
        borderRadius: 16,
        alignSelf: 'center',
        marginBottom: 20,
    },
    noRecordContainer: {
        height: 280,
        width: Dimen.width / 1.2,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
    }
});
