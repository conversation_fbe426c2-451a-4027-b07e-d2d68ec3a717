import { FavItem, SubItem, TextView } from '@components';
import { useNavigation } from '@hooks';
import { navigate } from '@navigation/navigationService';
import { AssetsImages, Colors, Dimen, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { LineHeight, commonStyle } from '@utils';
import { RenderContentType, defaultCreator, navigateToCourseDetail, widgetPadding } from '@utils/constants';
import React from 'react';
import { I18nManager, ImageBackground, Platform, Pressable, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useTranslation } from 'react-i18next';

const RecommendedItem = ({ item, index, carouselStyle, bannerHeight, inProgress, showFav }) => {
  const { isDarkMode } = useTheme();
  const navigation = useNavigation();
  const { t } = useTranslation();

  const onPress = () => {
    navigateToCourseDetail({
      ...item,
      type: item.type || item.component_type || 'course'
    }, navigation, inProgress);
  };

  return (
    <Pressable onPress={onPress}>
      <FastImage
        resizeMode={FastImage.resizeMode.cover}
        key={index}
        source={{ uri: item?.image }}
        style={[styles.bannerStyle, carouselStyle, { height: bannerHeight }]}
      >
        <View style={commonStyle.overlay} />
        <View style={styles.topViewView}>
          <TextView style={styles.courseType}
            text={item?.type || item?.component_type || 'Course'} />
          {(!showFav && item?.courseid &&
            ((item?.type == 'course' || item.type == 'Course') ||
              (item?.component_type == 'course' || item?.component_type == 'Course'))) && !inProgress
            && <FavItem style={styles.like} color={Colors.white} courseid={item?.courseid} />}
        </View>

        <View style={styles.bottomView}>
          <TextView style={styles.couresCreator} text={item?.creator || defaultCreator} />
          <TextView numberOfLines={2} style={styles.courseName} text={item?.title} />
          {inProgress ?
            <Pressable style={commonStyle.mt20} onPress={onPress}>
              <TextView style={styles.continueCourse}
                text={`${t('Continue')} ${t(item?.type || item?.component_type || 'course')}`}
              />
            </Pressable> :

            <View style={[commonStyle.rowAlign, commonStyle.mt20]}>
              {item.duration && (
                <SubItem
                  iconStyle={styles.iconStyle}
                  headingStyle={styles.headingStyle}
                  heading={`${item.duration}`}
                  icon={AssetsImages.clock}
                />
              )}

              <RenderContentType
                item={item}
                type={(item.type || item.component_type) || 'course'}
                styles={styles}
                t={t}
              />


              {/* {(item.type == 'course' || item?.component_type == 'course') && (
                <SubItem
                  iconStyle={styles.iconStyle}
                  headingStyle={styles.headingStyle}
                  heading={
                    item?.lessons <= 0 || item?.total_lesson <= 0
                      ? `1 ${t('Activity')}`
                      : `${item?.lessons || item?.total_lesson || item?.activity_count || ''} ${t('Activities')}`
                  }
                  icon={AssetsImages.lessonIcon}
                />
              )} */}
              {/* {item.type != 'course' && (
                <SubItem
                  iconStyle={styles.iconStyle}
                  headingStyle={styles.headingStyle}
                  heading={
                    item?.courses <= 0 || item?.total_course <= 0
                      ? `1 ${t('Course')}`
                      : `${item?.courses || item?.total_course || ''} ${t('Courses')}`
                  }
                  icon={AssetsImages.lessonIcon}
                />
              )} */}
            </View>}
        </View>
      </FastImage>
    </Pressable>
  );
};
export default RecommendedItem;
const styles = StyleSheet.create({
  bannerStyle: {
    width: Dimen.width / 1.08,
    borderRadius: 10,
    padding: widgetPadding,
    alignSelf: 'center',
    backgroundColor: Colors.grayline,
    overflow: 'hidden',

  },
  topViewView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'flex-start',
  },
  bottomView: {
    width: '100%',
    flex: 1,
    justifyContent: 'flex-end',
  },
  courseType: {
    color: Colors.courseWhiteColor,
    fontFamily: fonts.regular,
    fontSize: fontSize.h7,
    textTransform: 'uppercase',
    textAlign: 'left',
  },
  couresCreator: {
    color: Colors.courseWhiteColor,
    fontFamily: fonts.regular,
    fontSize: fontSize.h6,
    textAlign: 'left',
    marginBottom: widgetPadding / 2
  },
  like: {
    marginTop: -3,
  },
  courseName: {
    color: Colors.courseWhiteColor,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.h2,
    textAlign: 'left',
    lineHeight: Platform.OS == 'android' ? 36 : 32,
  },
  headingStyle: {
    color: 'white',
    fontFamily: fonts.regular,
  },
  iconStyle: {
    tintColor: 'white',
  },
  continueCourse: {
    fontFamily: fonts.thin,
    color: 'white',
    textDecorationLine: 'underline',
    textAlign: 'left',
    fontSize: fontSize.h6,
    marginTop: -5,
  },
});
