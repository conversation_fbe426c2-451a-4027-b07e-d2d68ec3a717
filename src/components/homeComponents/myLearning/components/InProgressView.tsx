import { CoursesItemNewCard, NoRecordsTextView, Skeleton, TextView } from '@components';
import { Dimen } from '@theme';
import { widgetPadding } from '@utils/constants';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, FlatList, StyleSheet } from 'react-native';
import { View } from 'react-native';

const InProgressView = ({ data, isLoading, inProgress, tabName }) => {
    const isFavouritable = tabName === "favourites";
    const { t } = useTranslation()

    if (isLoading)
        return <Skeleton
            count={2}
            skeletonStyle={styles.skeletonStyle} />

    if (!isLoading && !data?.length)
        return <View style={styles.noRecordContainer}>
            <NoRecordsTextView
                textStyle={{ textAlign: 'center' }}
            />
        </View>

    return (
        <FlatList
            showsHorizontalScrollIndicator={false}
            horizontal
            data={data}
            renderItem={({ item, index }) => renderItem(item, index, inProgress, isFavouritable)}
            keyExtractor={(item, index) => `${item?.courseid}${index.toString()}`}
            onEndReachedThreshold={0.5}
        />
    )
}
export default InProgressView;


const renderItem = (item, index, inProgress, isFavouritable) => {
    return (
        <View key={index}>
            <CoursesItemNewCard
                isFavouritable={isFavouritable}
                inProgress={inProgress}
                tag={false}
                item={{
                    ...item,
                }}
                index={index}
            />
        </View>
    );
}


const styles = StyleSheet.create({
    list: {
        alignSelf: 'flex-start',
        marginStart: 10
    },
    skeletonStyle: {
        width: Dimen.width * 0.53,
        height: 175,
        borderRadius: 10,
        marginRight: widgetPadding
    },
    noRecordContainer: {
        flex: 1,
        width: '100%',
        height: 170,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
    }
})
