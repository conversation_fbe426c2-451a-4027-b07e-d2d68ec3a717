import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { TOTARA_BASE_URL } from '@api/api_client';
import constants from '@api/constants';
import { CardContainer, DynamicTabs } from '@components';
import { useNavigation } from '@hooks';
import { LabelConfig } from '@theme';
import { useSession } from '@totara/core';
import { changeLearningPathwayTab } from '@totara/reducers/learningPathwayReducer';
import { myLearningTabs, widgetPadding } from '@utils/constants';
import crashReportLogger from '@utils/crashlyticsLogger';
import { trackHttpRequestMetrics } from '@utils/trackHttpRequestMetrics';
import { useDispatch } from 'react-redux';
import InProgressView from './components/InProgressView';

const { RequiredTab } = LabelConfig.learningPathways;

const tabKeys = {
  inprogress: '',
  required: 'required',
  favourites: 'personal',
};

const MyLearning = () => {
  const [tab, setTab] = useState('inprogress');
  const [inProgressData, setInProgressData] = useState([]);
  const [learningData, setLearningData] = useState([]);
  const [requiredData, setRequiredData] = useState([]);
  const [personalData, setPersonalData] = useState([]);
  const [loading, setLoading] = useState(true);
  const { apiToken } = useSession();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const getSelectedTab = async (tabName) => {
    setTab(tabName);

    const response = await fetchMyLearningData(tabKeys[tabName]);
    switch (tabName) {
      case 'inprogress':
        setLearningData(response.inprogresscourses.slice(0, 10));
        break;
      case 'required':
        setLearningData(response?.pathways?.required?.contents.slice(0, 10));
        break;
      case 'favourites':
        setLearningData(response?.pathways?.personal?.contents.slice(0, 10));
        break;
    }
  };

  const fetchMyLearningData = async (tab = '') => {
    const apiUrl = TOTARA_BASE_URL;
    let requestData: Record<string, string | number> = {
      wstoken: apiToken,
      wsfunction: constants.LEARNING_DATA,
      moodlewsrestformat: 'json',
    };
    if (tab) {
      requestData = {
        ...requestData,
        wsfunction: constants.GET_LEARNING_PATHWAYS,
        language: 'en',
        requiredlearningfilter: 'all',
        recommendedlearningfilter: 'all',
        personallearningfilter: 'all',
        tab: tab,
        page: 1,
        perpage: 20,
      };
    }

    let response: Response | undefined;
    try {
      setLoading(true);
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: Object.keys(requestData)
          .map((key) => encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]))
          .join('&'),
      });
      return await response.json();
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'MyLearning fetchMyLearningData',
        url: apiUrl,
        additionalInfo: 'Failed to fetch learning data',
      });
    } finally {
      setLoading(false);
      if (response)
        trackHttpRequestMetrics(apiUrl, 'POST', response, 'totaraApis getCoursesProgress');
    }
  };

  useEffect(() => {
    if (tab == 'required') changeLearningPathwayTab(dispatch, RequiredTab);
    if (tab == 'inprogress') changeLearningPathwayTab(dispatch, 'Enrolled');
    if (tab == 'favourites') changeLearningPathwayTab(dispatch, 'Favourites');
    if (tab == 'lucky-course') changeLearningPathwayTab(dispatch, 'lucky-course');
  }, [tab]);

  const myCourseList = () => {
    if (tab == 'required') changeLearningPathwayTab(dispatch, RequiredTab);
    if (tab == 'inprogress') changeLearningPathwayTab(dispatch, 'Enrolled');
    if (tab == 'favourites') changeLearningPathwayTab(dispatch, 'Favourites');
    if (tab == 'lucky-course') changeLearningPathwayTab(dispatch, 'lucky-course');
    navigation.navigate('MyLearningPathways', {
      data: learningData,
    });
  };

  return (
    <CardContainer
      containerStyle={styles.containerStyle}
      onView
      viewAllStyle={{ paddingEnd: widgetPadding }}
      onViewAllPress={myCourseList}
      title="My Learning"
    >
      <>
        <DynamicTabs
          scrollEnabled={false}
          initiallySelected
          getSelectedTab={getSelectedTab}
          tabs={myLearningTabs}
        />
        {tab == 'inprogress' && (
          <InProgressView tabName={tab} inProgress={true} isLoading={loading} data={learningData} />
        )}
        {tab == 'required' && (
          <InProgressView
            tabName={tab}
            inProgress={false}
            isLoading={loading}
            data={learningData}
          />
        )}
        {tab == 'favourites' && (
          <InProgressView
            tabName={tab}
            inProgress={false}
            isLoading={loading}
            data={learningData}
          />
        )}
      </>
    </CardContainer>
  );
};

export default MyLearning;

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: 'transparent',
    width: '100%',
    padding: 0,
    overflow: 'hidden',
  },
  containerStyle: {
    marginHorizontal: widgetPadding,
    paddingBottom: widgetPadding,
    marginVertical: widgetPadding,
    alignItems: 'flex-start',
    minHeight: 300,
    paddingStart: widgetPadding,
    overflow: 'hidden',
  },
});
