import { TextView } from "@components";
import { AssetsImages, Dimen, fontSize, fonts } from "@theme";
import { LineHeight, commonStyle, useCommonThemeStyles } from "@utils";
import { flipIconStyles, widgetPadding } from "@utils/constants";
import React from "react";
import { I18nManager, Image, Platform, StyleSheet, View } from "react-native";

const Quote = () => {
    const { themeStyle } = useCommonThemeStyles();
    const quote = I18nManager.isRTL ? 'التعلم المستمر هو وقود هذا المحرك الذي يقود وطننا إلى الأمام' : "Lifelong learning is the fuel that propels our nation forward."
    const author = I18nManager.isRTL ? 'صاحب السمو الشيخ محمد بن زايد آل نهيان' : '—<PERSON><PERSON><PERSON><PERSON> <PERSON> bin <PERSON>'
    return (
        <View style={styles.container}>
            <View style={styles.row}>
                <Image source={AssetsImages.Quote} style={[styles.quoteIcon, flipIconStyles]} />
                <View style={styles.quoteView}>
                    {/* <TextView style={[styles.quoteoftheday, themeStyle.themedTextColor]} text="#quoteoftheday" /> */}
                    <TextView style={[styles.quote, themeStyle.themedTextColor]} text={quote} />
                    <TextView style={[styles.author, themeStyle.themedTextColor]} text={author} />
                </View>
            </View>

        </View>

    )
}

export default Quote;

const styles = StyleSheet.create({
    quoteIcon: {
        width: 35,
        height: 35,
        resizeMode: 'contain'
    },
    quoteoftheday: {
        textAlignVertical: 'top',
        textTransform: 'uppercase',
        fontFamily: fonts.thin,
        fontSize: fontSize.h8,
        textAlign: 'left'


    },
    author: {
        fontFamily: fonts.thin,
        fontSize: fontSize.h6,
        marginTop: 10,
        textAlign: 'left'
    },
    row: {
        alignItems: 'flex-start',
        flexDirection: 'row',
    },
    container: {
        marginHorizontal: widgetPadding,
        marginVertical: widgetPadding,
        paddingVertical: 40
    },
    quoteView: {
        marginHorizontal: 15,
        marginTop: Platform.OS == 'ios' ? -2 : -5,
        width: '80%',

    },
    quote: {
        marginTop: 10,
        fontFamily: fonts.thin,
        fontSize: fontSize.h1,
        lineHeight: (Platform.OS == 'android' && !I18nManager.isRTL) ? 34 : LineHeight.h1_LH,
        textAlign: 'left'

    }
})