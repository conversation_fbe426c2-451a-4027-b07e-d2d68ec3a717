import React from 'react';
import Svg, { Rect, Line, Defs, Mask, Path } from 'react-native-svg';

interface DayIconProps {
  width?: number;
  height?: number;
  color?: string;
  strokeWidth?: number;
}

const DayIcon: React.FC<DayIconProps> = ({
  width = 20,
  height = 20,
  color = '#1E1E1E',
  strokeWidth = 1.5,
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <Defs>
        <Mask id="path-1-inside-1_25095_283867" fill="white">
          <Rect x="3.33337" y="7.77734" width="13.3333" height="7.77778" rx="1" />
        </Mask>
      </Defs>
      <Rect
        x="3.33337"
        y="7.77734"
        width="13.3333"
        height="7.77778"
        rx="1"
        stroke={color}
        strokeWidth="3"
        mask="url(#path-1-inside-1_25095_283867)"
        fill="none"
      />
      <Rect
        x="0.75"
        y="0.75"
        width="18.5"
        height="18.5"
        rx="2.25"
        stroke={color}
        strokeWidth={strokeWidth}
        fill="none"
      />
      <Path
        d="M4.44446 4.44531H15.5556"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        fill="none"
      />
    </Svg>
  );
};

export default DayIcon;
