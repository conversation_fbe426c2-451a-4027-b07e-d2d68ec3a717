import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface InlineCalendarIconProps {
  size?: number;
  color?: string;
}

const InlineCalendarIcon: React.FC<InlineCalendarIconProps> = ({ 
  size = 16, 
  color = "#4B4B4B" 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 21 21" fill="none">
      <Path 
        d="M6.19747 6.94462H14.5593M5.62685 2.2041V3.62643M15.0201 2.2041V3.62626M17.7855 6.2289L17.7855 16.1933C17.7855 17.6307 16.6202 18.796 15.1828 18.796H5.63976C4.20236 18.796 3.03711 17.6307 3.03711 16.1933V6.2289C3.03711 4.7915 4.20235 3.62626 5.63976 3.62626H15.1828C16.6202 3.62626 17.7855 4.7915 17.7855 6.2289Z" 
        stroke={color} 
        strokeWidth="1.47483" 
        strokeLinecap="round" 
        strokeLinejoin="round" 
      />
    </Svg>
  );
};

export default InlineCalendarIcon; 