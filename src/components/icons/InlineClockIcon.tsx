import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface InlineClockIconProps {
  size?: number;
  color?: string;
}

const InlineClockIcon: React.FC<InlineClockIconProps> = ({ 
  size = 16, 
  color = "#4B4B4B" 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 21 21" fill="none">
      <Path 
        d="M13.773 12.4523L10.8451 11.4763V7.39526M18.653 10.5003C18.653 14.8125 15.1573 18.3083 10.8451 18.3083C6.53285 18.3083 3.03711 14.8125 3.03711 10.5003C3.03711 6.18812 6.53285 2.69238 10.8451 2.69238C15.1573 2.69238 18.653 6.18812 18.653 10.5003Z" 
        stroke={color} 
        strokeWidth="1.56159" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default InlineClockIcon; 