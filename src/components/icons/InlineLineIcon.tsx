import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface InlineLineIconProps {
  width?: number;
  height?: number;
  color?: string;
}

const InlineLineIcon: React.FC<InlineLineIconProps> = ({ 
  width = 36, 
  height = 4, 
  color = "#E1E1E1" 
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 36 4" fill="none">
      <Path d="M2 2H34" stroke={color} strokeWidth="4" strokeLinecap="round" />
    </Svg>
  );
};

export default InlineLineIcon; 