import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface LocationPinIconProps {
  width?: number;
  height?: number;
  color?: string;
}

const LocationPinIcon: React.FC<LocationPinIconProps> = ({
  width = 20,
  height = 20,
  color = '#1E1E1E',
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
      <Path
        d="M10 18C10 18 16.2609 12.4348 16.2609 8.26087C16.2609 4.80309 13.4578 2 10 2C6.54222 2 3.73914 4.80309 3.73914 8.26087C3.73914 12.4348 10 18 10 18Z"
        stroke={color}
        strokeWidth="1.2"
      />
      <Path
        d="M12.0003 8.00013C12.0003 9.1047 11.1048 10.0001 10.0003 10.0001C8.89569 10.0001 8.00026 9.1047 8.00026 8.00013C8.00026 6.89556 8.89569 6.00013 10.0003 6.00013C11.1048 6.00013 12.0003 6.89556 12.0003 8.00013Z"
        stroke={color}
        strokeWidth="1.2"
      />
    </Svg>
  );
};

export default LocationPinIcon; 