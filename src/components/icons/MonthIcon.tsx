import React from 'react';
import Svg, { Rect, Line } from 'react-native-svg';

interface MonthIconProps {
  width?: number;
  height?: number;
  color?: string;
  strokeWidth?: number;
}

const MonthIcon: React.FC<MonthIconProps> = ({ 
  width = 24, 
  height = 24, 
  color = '#1E1E1E',
  strokeWidth = 1.75 
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      <Rect 
        x="0.875" 
        y="0.875" 
        width="22.25" 
        height="22.25" 
        rx="2.125" 
        stroke={color} 
        strokeWidth={strokeWidth}
      />
      <Line 
        x1="5.6748" 
        y1="6.32501" 
        x2="18.3248" 
        y2="6.32501" 
        stroke={color} 
        strokeWidth={strokeWidth} 
        strokeLinecap="round"
      />
      <Line 
        x1="5.6748" 
        y1="11.125" 
        x2="18.3248" 
        y2="11.125" 
        stroke={color} 
        strokeWidth={strokeWidth} 
        strokeLinecap="round"
      />
      <Line 
        x1="5.6748" 
        y1="15.925" 
        x2="12.3248" 
        y2="15.925" 
        stroke={color} 
        strokeWidth={strokeWidth} 
        strokeLinecap="round"
      />
    </Svg>
  );
};

export default MonthIcon; 