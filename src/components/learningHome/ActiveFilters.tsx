import { fontFamily } from '../../utils/AppUtils';
import { Colors, Icons } from '../../theme';
import React, { useState } from 'react';
import { Platform, Pressable, ScrollView, Text } from 'react-native';
import { StyleSheet } from 'react-native';
import { View } from 'react-native';
import fonts from '../../utils/fonts';
import { IconButton } from 'react-native-paper';
import { IconsType } from '../../theme/Icons';
import { useDispatch } from 'react-redux';

const ActiveFilters = ({ filterData }) => {
  const dispatch = useDispatch();
  if (filterData?.length == 0) return null;
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{ alignItems: 'center', paddingBottom: 15 }}
        horizontal
        showsHorizontalScrollIndicator={false}
      >
        <Text style={styles.filterheading}>Filter</Text>
        <View style={styles.itemContainer}>
          {filterData?.map((it) => {
            const onRemove = () => {
              const filteredItems = filterData.filter((item) => item != it);
              dispatch({
                type: 'filtersRequiredCourses',
                payload: filteredItems,
              });
            };
            return <FilterItem text={it} onRemove={onRemove} />;
          })}
        </View>
      </ScrollView>
    </View>
  );
};

export default ActiveFilters;

const FilterItem = ({ text, onRemove }) => {
  return (
    <Pressable style={[styles.category]} onPress={onRemove}>
      <Text numberOfLines={1} style={[styles.textcategory]}>
        {text}
      </Text>
      <IconButton
        style={{ backgroundColor: Colors.black, marginLeft: 10 }}
        icon={() => {
          return <Icons type={IconsType.Entypo} name={'cross'} size={10} color={Colors.white} />;
        }}
        size={Platform.OS == 'ios' ? 8 : 10}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingLeft: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.grayline,
  },

  category: {
    marginRight: 10,
    borderRadius: 18,
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: Colors.lbgcolor1,
    backgroundColor: Colors.lbgcolor1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textcategory: {
    color: Colors.black,
    fontWeight: '500',
    fontSize: 13,
    marginLeft: 7,
    textAlign: 'center',
    fontFamily: fontFamily('ST-MEDIUM'),
  },
  filterheading: {
    color: Colors.black,
    fontFamily: fonts.regular,
    fontSize: 16,
    marginLeft: 6,
  },
  itemContainer: { flexDirection: 'row', marginLeft: 10 },
});
