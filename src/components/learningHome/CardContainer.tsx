import { IconsType } from '../../theme/Icons';
import { Colors, Icons, fonts } from '../../theme';
import React from 'react';
import {
  Alert,
  I18nManager,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import { IconButton } from 'react-native-paper';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import fontSize, { isPad } from '../../utils/fontSize';
import { Toast } from 'native-base';
import { BreadCrumb, TextView } from '../common';
import { useTheme } from '../../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { convertNumbersToArabicNumerals, lineHeight, widgetPadding } from '@utils/constants';
import { CurrentLearningProgress } from '.';

type CardContainer = {
  children: JSX.Element;
  header?: boolean;
  containerStyle?: ViewStyle | ViewStyle[];
  title?: string;
  count?: string;
  plus?: boolean;
  onView?: () => void;
  onPlusPress?: () => void;
  opacity?: boolean;
  onViewAllPress?: () => void;
  viewAllStyle?: any;
  breadCrumbStyle?: any

};

const CardContainer = ({
  children,
  header = true,
  containerStyle,
  title,
  count,
  plus = false,
  onView,
  opacity = false,
  onPlusPress,
  onViewAllPress,
  loading,
  showCurrentLearning,
  viewAllStyle,
  breadCrumbStyle
}: CardContainer) => {
  const { theme, isDarkMode } = useTheme();
  const { t, i18n } = useTranslation();

  return (
    <View style={[styles.contianer, isDarkMode && { backgroundColor: theme.bgDark }, containerStyle]}>
      {showCurrentLearning && <CurrentLearningProgress style={{ paddingHorizontal: widgetPadding }} />}
      {header && (
        <View style={[styles.mentorHeader, breadCrumbStyle]}>
          <View style={styles.subCon}>
            {title ? <TextView
              numberOfLines={1}
              style={[styles.text, { color: theme.textColor }]}
              text={title}
            /> : null}

            {plus && (
              <IconButton
                onPress={onPlusPress}
                style={{
                  backgroundColor: Colors.black,
                  opacity: coming_soon_opacity,
                }}
                icon={() => {
                  return (
                    <Icons
                      type={IconsType.AntDesign}
                      name={'plus'}
                      size={14}
                      color={Colors.white}
                    />
                  );
                }}
                size={Platform.OS == 'ios' ? 14 : 15}
              />
            )}
          </View>

          {onView && !loading ? (
            <Pressable
              onPress={
                onViewAllPress
                  ? onViewAllPress
                  : opacity
                    ? () => Toast.show({ text: 'Coming soon', textStyle: { textAlign: 'left' } })
                    : onView
              }
              style={[styles.viewAllContainer, opacity && { opacity: coming_soon_opacity }, viewAllStyle]}
            >
              <Text style={[styles.viewAllText, isDarkMode && { color: theme.textColor }]}>
                {`${t('View all')}`}
              </Text>
            </Pressable>
          ) : null}
        </View>
      )}

      {children}
    </View>
  );
};

export default CardContainer;

const styles = StyleSheet.create({
  subCon: {
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
  },
  mentorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  contianer: {
    paddingTop: widgetPadding * 1.5,
    alignItems: 'center',
    borderRadius: 10,
    backgroundColor: 'white',
  },

  text: {
    fontSize: fontSize.h3,
    fontFamily: fonts.semiBold,
    textTransform: 'capitalize',
  },

  viewAllText: {
    fontSize: fontSize.h6,
    fontFamily: fonts.regular,
    textDecorationLine: 'underline',
    color: 'rgba(120, 120, 120, 1)',
  },

  viewAllContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginEnd: widgetPadding,
  },
  rtlIcon: {
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
  },
});
