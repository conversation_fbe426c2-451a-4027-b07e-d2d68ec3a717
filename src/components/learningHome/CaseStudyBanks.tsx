import React from 'react';
import { StyleSheet, FlatList, View, Dimensions } from 'react-native';
import Card<PERSON>ontainer from './CardContainer';
import { useNavigation } from '../../hooks';
import CaseStudyBanksItem from './CaseStudyBanksItem';

type CaseStudyBanksTypes = {
  id: string;
  name: string;
  size: string;
};

const ITEM_WIDTH = Dimensions.get('window').width / 1.5;

const CaseStudyBanksWidget = () => {
  const navigation = useNavigation();
  const data: CaseStudyBanksTypes[] = [
    {
      id: 'test1',
      name: 'Design Thinking: A framework for innovation.pdf',
      size: '782 KB',
    },
    {
      id: 'test2',
      name: 'Innovative Solutions for Modern Problems.pdf',
      size: '900 KB',
    },
    {
      id: 'test3',
      name: 'The Future of Design in Tech.pdf',
      size: '1.2 MB',
    },
    {
      id: 'test4',
      name: 'Human-Centered Design Strategies.pdf',
      size: '850 KB',
    },
    {
      id: 'test5',
      name: 'User Experience and Beyond.pdf',
      size: '920 KB',
    },
    {
      id: 'test6',
      name: 'Effective Design Thinking.pdf',
      size: '750 KB',
    },
  ];

  const onViewAll = () => {
    navigation?.navigate('CaseStudyBanksList', {
      list: data,
    });
  };

  const renderItem = ({ item }: { item: CaseStudyBanksTypes }) => (
    <View style={{ width: ITEM_WIDTH }}>
      <CaseStudyBanksItem key={item.id} item={item} />
    </View>
  );

  return (
    <CardContainer
      title={'Recommended Courses'}
      count={data.length.toString()}
      containerStyle={styles.container}
      onView={onViewAll}
    >
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        getItemLayout={(data, index) => (
          { length: ITEM_WIDTH, offset: ITEM_WIDTH * index, index }
        )}
      />
    </CardContainer>
  );
};

export default CaseStudyBanksWidget;

const styles = StyleSheet.create({
  container: { paddingBottom: 15 },
});
