import React from 'react';
import { View } from 'native-base';
import { Image, Pressable } from 'react-native';
import { AssetsImages } from '@theme/index.tsx';
import { useTheme } from '@theme/ThemeProvider.tsx';
import { StyleSheet } from 'react-native';
import { fonts } from '@theme/index.tsx';
import { fontSizes } from '@totara/theme/constants.ts';
import TextView from '@components/common/TextView';
import Colors from '../../theme/Colors';
import { useNavigation } from '@react-navigation/native';



interface IItemProp {
  name: string;
  size: string;
}

type TCaseStudyBanksItemProps = {
  item: IItemProp;
};

const CaseStudyBanksItem = ({ item }: TCaseStudyBanksItemProps) => {
  const {theme,isDarkMode} = useTheme()
  const navigation = useNavigation()
  return (
    <Pressable onPress={()=>navigation.navigate("MultipleSession")}>
        <View style={styles.view}>
      <View style={[styles.iconView,{backgroundColor:isDarkMode? theme.bgLightDark: Colors.bgBlue}]}>
        <Image style={[styles.pdfIconImage]} source={AssetsImages.pdf} />
        <View style={styles.contentView}>
            <TextView text={item.name} style={[styles.nameText,{color:theme.textColor}]} numberOfLines={2} />
            <View style={styles.sizeView}>
            <Image style={[styles.downloadIconImage,{tintColor:theme.textColor}]} source={AssetsImages.downloadIcon} />
            <TextView text={item.size} style={[styles.sizeText,{color:theme.textColor}]} />
            </View>
        </View>
      </View>
    </View>
    </Pressable>
  
  );
};

export default CaseStudyBanksItem;



const styles = StyleSheet.create({
  view: {
    width: '100%',
    flexDirection: 'column',
    padding: 8,
  },

  iconView: {
    justifyContent: 'center',
    borderRadius: 16,
    padding: 16,
    gap:8,
    marginTop:8
  },
  pdfIconImage: {
    width: 32,
    height: 40,
  },
  contentView: {
    width: '75%',
    justifyContent: 'space-around',
  },
  downloadIconImage: {
    height: 12,
    aspectRatio: 1,
    marginRight: 4,
  },
  nameText: {
    fontSize: fontSizes.fontSizeMX,
    fontFamily: fonts.medium,
    marginBottom: 8,
  },
  sizeText: {
    fontSize: fontSizes.fontSizeXS2,
    fontFamily: fonts.regular,
    color: Colors.ldark,
  },
  sizeView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
