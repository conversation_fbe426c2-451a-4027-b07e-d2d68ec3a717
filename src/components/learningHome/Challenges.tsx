import { AssetsImages, Colors, Dimen, fonts } from '../../theme';
import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import CardContainer from './CardContainer';
import { Toast } from 'native-base';
import { TextView } from '../index';

const challengesData = [
  {
    title: 'Lorem ispum',
    // title: "Blockchain Quiz",
    rank: 7,
    image: AssetsImages.c2,
  },
  {
    title: 'Lorem ispum',
    // title: "Idea Hackathon",
    rank: 4,
    image: AssetsImages.c3,
  },
  {
    title: 'Lorem ispum',
    // title: "PMP Certificate",
    rank: 4,
    image: AssetsImages.c1,
  },
];

const Challenges = () => {
  return (
    <CardContainer
      title={'My Challenges'}
      count={'0'}
      opacity={true}
      // containerStyle={{ opacity: coming_soon_opacity }}
    >
      <Pressable
        style={styles.challengeContainer}
        onPress={() => Toast.show({ text: 'Coming soon', textStyle: {textAlign: 'left'} })}
      >
        <TextView
          style={{ marginVertical: 10, fontFamily: fonts.medium, fontSize: 18 }}
          text="Available Soon"
        />
        {/* {challengesData.map((item) => {
          return <ChallengeItem item={item} />;
        })} */}
      </Pressable>
    </CardContainer>
  );
};

export default Challenges;

const ChallengeItem = ({ item }) => {
  return (
    <View style={[styles.ChallengeItem]}>
      <View style={styles.containerImage}>
        <Image
          source={AssetsImages.default}
          // source={item?.image}
          style={styles.image}
        />
      </View>

      <Text numberOfLines={2} style={styles.titleText}>
        {item?.title}
      </Text>

      <View style={styles.textContainer}>
        <View style={styles.imageContainer}>
          <Image source={AssetsImages.ellipse} style={styles.imageElpis} />
          <Image source={AssetsImages.trophy} style={styles.trophy} />
        </View>

        <Text style={styles.rankText}>Rank {item?.rank}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ChallengeItem: {
    flex: 1,
    borderColor: Colors.lightGray,
  },

  challengeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
    // paddingHorizontal: 5,
  },

  rankText: {
    fontSize: 10,
    fontFamily: fontFamily('ST-MEDIUM'),
    marginLeft: 5,
    color: Colors.primaryYello,
    fontWeight: '500',
  },

  titleText: {
    fontSize: 14,
    fontFamily: fontFamily('ST-MEDIUM'),
    fontWeight: '500',
    marginTop: 10,
    textAlign: 'left',
    marginBottom: 5,
    // height: 40,
    marginLeft: 10,
    marginRight: 10,
    // backgroundColor: "#000",
    color: Colors.black,
  },

  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 10,
    alignSelf: 'center',
  },

  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 10,
    marginTop: 3,
  },

  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  imageElpis: { width: 23, height: 23, resizeMode: 'contain' },

  trophy: {
    position: 'absolute',
    height: 11,
    width: 13,
    resizeMode: 'contain',
  },

  containerImage: {
    borderRadius: 16,
    overflow: 'hidden',
    alignSelf: 'center',
    width: Dimen.width / 3.7,
    height: Dimen.width / 4.2,
  },
});
