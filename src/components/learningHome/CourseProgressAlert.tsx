import React, { useEffect } from 'react';
import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import ProgressBar from './ProgressBar';
import { Colors, Icons } from '../../theme';
import { IconsType } from '../../theme/Icons';
import { fonts, fontSize } from '../../utils/index';
import { useLastOpenedCourseProgress, useNavigation } from '../../hooks';
import { isPad } from '@utils/fontSize';
import { TextView } from '@components';
import { I18nManager } from 'react-native';

const CourseProgressAlert = () => {
  const navigation = useNavigation();
  const { coursesList: courseProgressData, lastOpenedCourseItem: courseItem } =
    useLastOpenedCourseProgress();


  const onPressViewAll = () => {
    if (courseProgressData?.length > 0)
      navigation.navigate('InProgressCourses', { data: courseProgressData });
  };

  if (!courseItem || Object.keys(courseItem).length == 0) {
    return null;
  }

  return (
    <View style={[styles.container]}>
      <View style={styles.subCon}>
        <Pressable
          style={styles.row}
          onPress={() => navigation.navigate('CourseDetail', courseItem)}
        >
          <View style={styles.iconBg}>
            <Icons
              name="play"
              type={IconsType.Feather}
              color="white"
              size={18}
              style={styles.icon}
            />
          </View>
          <View style={[styles.textCon, { flex: 1 }]}>
            <TextView style={styles.textProgress} text="Last Viewed" />
            <Text
              numberOfLines={2}
              style={[styles.textHeading, Platform.OS == 'android' && { marginTop: -3 }]}
            >
              {courseItem?.title}
            </Text>
          </View>
        </Pressable>

        <ProgressBar
          total={100}
          active={courseItem?.progress || 0}
          style={styles.progress}
          label={false}
          inAtiveColor={Colors.inActiveProgress}
          ativeColor={Colors.statusTextInProgress}
        />
      </View>

      <Pressable onPress={onPressViewAll}>
        <TextView style={styles.viewAll} text="View all">
          <Text style={styles.viewAll}>
            {` `}
            ({courseProgressData && courseProgressData?.length})
          </Text>
        </TextView>


      </Pressable>
    </View>
  );
};

export default CourseProgressAlert;

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    marginHorizontal: 3,
    backgroundColor: '#000',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    position: 'absolute',
    bottom: Platform.OS == 'ios' ? (isPad ? 75 : 60) : 65,
    left: 6,
    right: 6,
  },

  subCon: {
    flex: 1,
    borderRightWidth: 0.5,
    borderRightColor: 'rgba(255, 255, 255, .7)',
    marginVertical: isPad ? 16 : 10,
    paddingRight: 20,
    marginRight: 15,
  },

  row: { flexDirection: 'row', alignItems: 'center' },
  iconBg: {
    backgroundColor: 'rgba(255, 255, 255, 0.19)',
    height: isPad ? 50 : 38,
    width: isPad ? 50 : 38,
    borderRadius: (isPad ? 50 : 38) / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {},
  textCon: { marginLeft: 10 },
  textProgress: {
    color: 'rgba(255, 255, 255, .8)',
    fontFamily: fonts.medium,
    fontSize: Platform.OS == 'ios' ? (isPad ? fontSize.large15 : fontSize.small) : 12,
    textTransform: 'capitalize',
    textAlign: 'left'
  },
  textHeading: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: Platform.OS == 'ios' ? (isPad ? fontSize.large : fontSize.medium) : 14,
    marginTop: 5,
    textAlign: 'left'

  },
  viewAll: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: Platform.OS == 'ios' ? (isPad ? fontSize.large : fontSize.medium) : 14,
  },
  progress: { marginTop: 10, marginLeft: 0 },
});
