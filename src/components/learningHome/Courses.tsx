import React from 'react';
import { StyleSheet } from 'react-native';
import CoursesItem from './CoursesItem';
import CardContainer from './CardContainer';
import { useNavigation } from '../../hooks';
import CoursesType from 'models/CourseType';
import fontSize, { isPad } from '@utils/fontSize';

type RequiredLearningType = {
  data: CoursesType[];
};

const Courses = ({ data }: RequiredLearningType) => {
  const navigation = useNavigation();
  if (!data) {
    return null;
  }

  const onViewAll = () => {
    navigation?.navigate('CategoryListing', {
      list: data,
      title: 'Recommended Courses',
    });
  };

  const navigate = (item) => () => navigation.navigate('CourseDetail', item);

  return (
    <CardContainer
      title={'Recommended courses'}
      count={JSON.stringify(data?.length)}
      containerStyle={styles.container}
      onView={onViewAll}
    >
      <>
        {data?.slice(0, 3)?.map((item) => {
          return (
            <CoursesItem
              key={item.courseid}
              titleStyle={{ fontSize: isPad ? fontSize.large : fontSize.medium }}
              uniqueId={item.courseid}
              image={item?.image}
              name={item?.title}
              courseid={item?.courseid?.toString()}
              hours={item?.duration}
              rating={item?.rating?.toString()}
              onPress={navigate(item)}
              category={item?.coursetype}
            />
          );
        })}
      </>
    </CardContainer>
  );
};

export default Courses;

const styles = StyleSheet.create({
  container: { paddingBottom: 15 },
});
