import { AssetsImages, Colors, Dimen, Icons, fonts } from '../../theme';
import React from 'react';
import {
  Image,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
import { IconsType } from '../../theme/Icons';
import ProgressBar from './ProgressBar';
import FavItem from './FavItem';
import fontSize, { isPad } from '../../utils/fontSize';
import { useDispatch } from 'react-redux';
import { setBottomSheetVisible } from '../../totara/reducers/addToListBottomSheetReducer';
import { IconButton } from 'react-native-paper';
import FastImage from 'react-native-fast-image';
import { setReferCourse } from '../../totara/reducers/referCourseReducer';
import { useTheme } from '@theme/ThemeProvider';
import { lineHeight, lineHeightForAndroidLTR, parseAndFormatDuration } from '@utils/constants';
import { convertNumbersToArabicNumerals } from '@utils/constants';
import { commonStyle } from '@utils';
import { useTranslation } from 'react-i18next';

type CoursesItemType = {
  courseid: string;
  style?: ViewStyle;
  viewType?:
  | 'filter'
  | 'courses'
  | 'progress-courses'
  | 'announcement'
  | 'learning-record-course'
  | 'learning-record-course-certificate';
  onPress: () => void;
  category?: string;
  name: string;
  hours: string;
  rating: string;
  progress?: number;
  image: string;
  date?: string;
  hideFav?: boolean;
  percentage?: number;
  uniqueId: number;
  contentContainerStyles?: ViewStyle;
  issuingAuthority?: string;
  issuingAuthorityImage?: string;
  expiry?: string;
  completionDate?: string;
  idAndLocation?: string;
  isCertificateAvailable?: boolean;
  titleStyle?: TextStyle;
  dateStyle?: TextStyle;
  adjustHeartIcon?: boolean;
};

const CoursesItem = ({
  courseid,
  style,
  viewType = 'courses',
  onPress,
  category,
  name,
  hours,
  image,
  rating,
  progress,
  date,
  hideFav = false,
  percentage,
  uniqueId,
  contentContainerStyles,
  issuingAuthority,
  issuingAuthorityImage,
  expiry,
  completionDate,
  idAndLocation,
  isCertificateAvailable,
  titleStyle,
  dateStyle,
  adjustHeartIcon = false,
  announcementImageStyle,
}: CoursesItemType) => {
  const { theme, isDarkMode } = useTheme();
  const dispatch = useDispatch();
  const onPressMore = () => {
    setReferCourse(dispatch, { courseid, name, image });
    setBottomSheetVisible(dispatch, true);
  };

  const { t } = useTranslation();

  const RenderFileIcon = ({ isCertificate, url }) => {
    if (!isCertificate) {
      return (
        <View> 
          <FastImage style={styles.noImageCertificate} source={AssetsImages.noImage}/>
        </View>
        
      )
    }  else {
      const match = url?.match(/\.([a-zA-Z0-9]+)(?:[\?#]|$)/);
      const fileExtesion = match ? match[1] : null;
      const supportedExtensions = ['png', 'jpg', 'jpeg'];
      const isImage = supportedExtensions.includes(fileExtesion);

      if (isImage) {
        return (
          <Icons
            color={'#B3BAC1'}
            type={IconsType.FontAwesome5}
            name={'file-image'}
            size={65}
            style={{ alignSelf: 'center' }}
          />
        );
      } else {
        return (
          <Icons
            color={'#B3BAC1'}
            type={IconsType.FontAwesome5}
            name={'file-pdf'}
            size={65}
            style={{ alignSelf: 'center' }}
          />
        );
      }
    }
  };

  return (
    <Pressable
      key={uniqueId}
      style={[
        styles.CoursesContainer,
        { backgroundColor: isDarkMode ? 'transparent' : conditionalView.background[viewType] },
        style,
      ]}
      onPress={onPress}
    >
      <View style={styles.CoursesItem}>
        <View>
          {viewType != 'announcement' && (
            <View
              style={[
                styles.imageCon,
                {
                  width:
                    viewType === 'learning-record-course' ||
                      viewType === 'learning-record-course-certificate'
                      ? 126
                      : 90,
                  height:
                    viewType === 'learning-record-course' ||
                      viewType === 'learning-record-course-certificate'
                      ? 126
                      : Platform.OS == 'ios'
                        ? 86
                        : 90,
                },
              ]}
            >
              {viewType === 'learning-record-course-certificate' ? (
                <RenderFileIcon isCertificate={isCertificateAvailable} url={image} />
              ) : (
                <FastImage
                  source={
                    image
                      ? {
                        uri: image,
                        priority: FastImage.priority.high,
                      }
                      : AssetsImages.placeHolderCourseImage
                  }
                  style={[styles.image, { width: '100%', height: '100%' }]}
                />
              )}

              <View style={styles.linearGradient} />

              {conditionalView.progress[viewType] && progress != undefined && (
                <ProgressBar
                  total={100}
                  active={progress}
                  style={styles.progress}
                  label={false}
                  inAtiveColor={'rgba(255, 255, 255, 0.3)'}
                  ativeColor={Colors.statusTextInProgress}
                  height={10}
                />
              )}
            </View>
          )}

          {/* {conditionalView.imageFavIcon[viewType] && !hideFav && (
            <FavItem
              style={styles.favItem}
              color={theme.textColor}
              courseid={courseid}
            />
          )} */}
        </View>

        <View style={[styles.subcon, contentContainerStyles]}>
          {viewType == 'announcement' ? (
            <View style={[commonStyle.rowAlign, { marginBottom: 5 }]}>
              <Icons type={IconsType.Feather} name={'calendar'} size={12} color={theme.textColor} />
              <Text
                style={[
                  styles.text,
                  { maxWidth: '100%', color: theme.textColor, fontFamily: fonts.regular, fontSize: fontSize.xmini },
                  , isDarkMode && { color: '#C7C7C7' }]}
              >
                {date}
              </Text>
            </View>
          ) : null}
          <View>
            {conditionalView.category[viewType] && category && (
              <View style={{ flexDirection: 'row', marginBottom: 8 }}>
                <View
                  style={[
                    styles.category,
                    {
                      backgroundColor:
                        category == 'Self-paced'
                          ? Colors.selfPaced
                          : category == 'Instructor-led'
                            ? isDarkMode ? Colors.activeStateDark : Colors.instructorLed
                            : 'transparent',
                      borderWidth: category != 'Self-paced' && category != 'Instructor-led' ? 1 : 0,
                      borderColor: Colors.lightGray,
                    },
                    isDarkMode && { backgroundColor: 'transparent', borderColor: theme.borderBackground, borderWidth: 1 }
                  ]}
                >
                  <Text
                    style={[styles.textcategory, { color: isDarkMode ? theme.textColor : theme.textColor }]}
                  >
                    {category ?? ''}
                  </Text>
                </View>
              </View>
            )}
            <View>
              <Text
                numberOfLines={conditionalView.announcement[viewType] ? 3 : 2}
                style={[
                  styles.titleText,
                  {
                    fontSize:
                      viewType !== 'learning-record-course' ? fontSize.medium : fontSize.semiLarge,
                    marginBottom:
                      viewType !== 'learning-record-course' &&
                        viewType !== 'learning-record-course-certificate'
                        ? 8
                        : 0,
                    marginEnd: 5,
                    color: theme.textColor,
                  },
                  titleStyle,
                  lineHeightForAndroidLTR,
                ]}
              >
                {name ?? ''}
              </Text>

              {issuingAuthority ? (
                <View style={styles.issuingAuthorityContainer}>
                  {issuingAuthorityImage ? (
                    <Image
                      style={styles.issuingAuthorityImage}
                      source={{ uri: issuingAuthorityImage }}
                    />
                  ) : null}
                  <Text
                    style={[styles.issuingAuthority, { color: theme.textColor }]}
                    numberOfLines={2}
                  >
                    {issuingAuthority}
                  </Text>
                </View>
              ) : null}
            </View>
          </View>

          <View style={styles.row}>
            {/* {hours &&
              hours !== '-' &&
              hours != '0 hours' &&
              hours !== ' ' &&
              category != 'Self-paced' &&
              viewType !== 'learning-record-course' &&
              viewType !== 'learning-record-course-certificate' ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: 10,
                }}
              >
                <Icons
                  type={IconsType.AntDesign}
                  name={'clockcircleo'}
                  size={13}
                  color={theme.textColor}
                />
                <Text numberOfLines={1} style={[styles.timeAndDateText, { color: theme.textColor }]}>
                  {convertNumbersToArabicNumerals(hours ?? '')}
                </Text>
              </View>
            ) : null} */}

            {conditionalView.calendarIcon[viewType] && date ? (
              <View
                style={{
                  marginEnd: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <Icons
                  type={IconsType.Feather}
                  name={'calendar'}
                  size={13}
                  color={theme.textColor}
                />
                <Text style={[styles.timeAndDateText, { color: theme.textColor }]}>{date}</Text>
              </View>
            ) : null}
            {viewType !== 'announcement' &&
              viewType !== 'learning-record-course' &&
              viewType !== 'learning-record-course-certificate' ? (
              <>
                <Icons
                  type={IconsType.Entypo}
                  name={'star'}
                  size={isPad ? 20 : 13}
                  color={Colors.rating}
                />
                <Text style={[styles.timeAndDateText, { color: theme.textColor }]}>
                  {convertNumbersToArabicNumerals(rating ?? '0')}
                </Text>
              </>
            ) : null}

            {conditionalView.announcement[viewType] ? (
              <>
                <Icons
                  type={IconsType.Feather}
                  name={'calendar'}
                  size={15}
                  color={theme.textColor}
                />
                <Text
                  style={[styles.text, { maxWidth: '100%', color: theme.textColor }, dateStyle]}
                >
                  {date}
                </Text>
              </>
            ) : null}
          </View>

          <View>
            {(viewType === 'learning-record-course' ||
              viewType === 'learning-record-course-certificate') &&
              hours ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: 10,
                  marginTop: Dimen.height * 0.01,
                  marginBottom: issuingAuthority ? 0 : 8,
                }}
              >
                {category != 'Self-paced' ? (
                  <Icons
                    type={IconsType.AntDesign}
                    name={'clockcircleo'}
                    size={16}
                    color={theme.textColor}
                  />
                ) : null}
                <Text
                  numberOfLines={1}
                  style={[styles.timeAndDateText, { color: theme.textColor }]}
                >
                  {Number(hours)
                    ? parseAndFormatDuration(hours)
                    : convertNumbersToArabicNumerals(hours)}
                </Text>
              </View>
            ) : null}

            {(viewType === 'learning-record-course' ||
              viewType === 'learning-record-course-certificate') &&
              !issuingAuthority &&
              completionDate ? (
              <View
                style={{
                  marginEnd: 10,
                  flexDirection: 'row',
                  marginTop: Platform.OS === 'ios' ? (issuingAuthority ? 0 : 5) : 0,
                  alignItems: 'center',
                }}
              >
                <Icons
                  type={IconsType.Feather}
                  name={'calendar'}
                  size={17}
                  color={theme.textColor}
                />
                <Text
                  style={[styles.timeAndDateText, { color: theme.textColor, textAlign: 'left' }]}
                >{`${t('Completion date:')} ${completionDate}`}</Text>
              </View>
            ) : null}

            {(viewType === 'learning-record-course' ||
              viewType === 'learning-record-course-certificate') &&
              issuingAuthority &&
              expiry ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: 10,
                  marginTop: Platform.OS === 'ios' ? 12 : 0,
                }}
              >
                <Text numberOfLines={1} style={[styles.expiry, { color: theme.textColor }]}>
                  {expiry}
                </Text>
              </View>
            ) : null}

            {(viewType === 'learning-record-course' ||
              viewType === 'learning-record-course-certificate') &&
              idAndLocation ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginRight: 10,
                  marginTop: Platform.OS === 'ios' ? 5 : 0,
                }}
              >
                <Text numberOfLines={1} style={[styles.expiry, { color: theme.textColor }]}>
                  {idAndLocation}
                </Text>
              </View>
            ) : null}
          </View>

          {viewType === 'learning-record-course' && percentage ? (
            <View style={styles.progressBar}>
              <View style={styles.progressUnfilleddPercentage}>
                <View
                  style={[
                    styles.progressPercentage,
                    { width: '100%', backgroundColor: isDarkMode ? Colors.progressBarDarkColor : 'rgba(133, 207, 59, 1)' },
                  ]}
                />
              </View>
              <Text style={[styles.progressText, { color: theme.textColor }]}>
                {convertNumbersToArabicNumerals('100')}%
              </Text>
            </View>
          ) : null}
        </View>
        {viewType == 'announcement' && (
          <View
            style={[
              styles.imageCon,
              {
                width: 90,
                height: 90,
                marginRight: 15,
              }, announcementImageStyle
            ]}
          >
            <FastImage
              defaultSource={AssetsImages.announcementPlaceholder}
              source={
                image
                  ? {
                    uri: image,
                    priority: FastImage.priority.high,
                  }
                  : AssetsImages.announcementPlaceholder
              }
              style={[styles.image, { width: '100%', height: '100%' }]}
            />
            <View style={styles.linearGradient} />

            {conditionalView.progress[viewType] && progress != undefined && (
              <ProgressBar
                total={100}
                active={progress}
                style={styles.progress}
                label={false}
                inAtiveColor={'rgba(255, 255, 255, 0.3)'}
                ativeColor={Colors.statusTextInProgress}
                height={10}
              />
            )}
          </View>
        )}

        {(conditionalView.imageFavIcon_secondary[viewType] || adjustHeartIcon) &&
          viewType != 'announcement' && (
            <FavItem style={styles.favItemPosition} courseid={courseid} color={theme.textColor} />
          )}

        {conditionalView.moreIcon[viewType] && (
          <IconButton
            icon={() => {
              return (
                <Icons
                  type={IconsType.Entypo}
                  style={{}}
                  name={'dots-three-vertical'}
                  size={18}
                  color={theme.textColor}
                />
              );
            }}
            size={16}
            style={{ marginTop: Platform.OS == 'ios' ? -4 : -3 }}
            onPress={onPressMore}
          />
        )}
      </View>
    </Pressable>
  );
};

export default CoursesItem;

function calculateRemainingPercentage(total, achieved) {
  if (total <= 0) {
    return 0;
  }

  if (achieved < 0) {
    return 0;
  }

  if (achieved > total) {
    return 0;
  }

  let remaining = achieved / total;
  let remainingPercentage = remaining * 100;
  let converted = remainingPercentage;
  return Number(converted.toFixed(2));
}

const conditionalView = {
  background: {
    filter: Colors.white,
    'progress-courses': Colors.white,
    courses: Colors.backgroundLight,
    announcement: '#fff',
  },
  category: {
    filter: true,
    'progress-courses': true,
    'learning-record-course': true,
    'learning-record-course-certificate': true,
  },
  imageFavIcon: {
    filter: true,
  },
  imageFavIcon_secondary: {
    courses: true,
  },
  calendarIcon: {
    filter: true,
  },
  moreIcon: {
    courses: true,
    filter: true,
  },
  progress: {
    'progress-courses': true,
  },
  announcement: {
    announcement: false,
  },
};

const styles = StyleSheet.create({
  linearGradient: {
    backgroundColor: 'rgba(125, 161, 196, 1)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    opacity: 0.15,
    borderRadius: 12,
  },

  row: { flexDirection: 'row', alignItems: 'center' },
  CoursesItem: {
    borderColor: Colors.lightGray,
    flexDirection: 'row',
  },

  text: {
    marginLeft: 5,
    maxWidth: 80,
    fontSize: isPad ? fontSize.semiMedium : fontSize.semiMini,
    fontFamily: fonts.medium,
  },

  subcon: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'column',
    paddingLeft: 12,
  },

  CoursesContainer: {
    marginTop: 10,
    borderRadius: 12,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 5,
    marginLeft: 5,
  },

  titleText: {
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    textAlign: 'left',
  },

  issuingAuthorityContainer: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    marginTop: 8,
  },

  issuingAuthorityImage: {
    width: Dimen.width * 0.07,
    height: Dimen.width * 0.07,
    resizeMode: 'contain',
    marginEnd: 8,
  },

  issuingAuthority: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    fontFamily: fonts.regular,
    textAlign: 'left',
    letterSpacing: -1,
  },
  expiry: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    fontFamily: fonts.regular,
    textAlign: 'left',
    letterSpacing: -1,
    lineHeight: lineHeight(isPad ? fontSize.semiMedium : fontSize.small),
  },

  image: {
    width: 80,
    height: Platform.OS == 'ios' ? 86 : 90,
    borderRadius: 12,
    resizeMode: 'cover',
    alignSelf: 'center',
  },

  favItem: { position: 'absolute', right: 0, top: 0 },

  favItemPosition: { marginTop: -3, marginRight: 8 },

  category: {
    paddingVertical: Platform.OS == 'ios' ? 3 : 0,
    flexDirection: 'row',
    paddingHorizontal: 8,
    borderRadius: 20,
  },
  textcategory: {
    fontSize: isPad ? fontSize.medium : fontSize.mini,
    textAlign: 'center',
    fontFamily: fonts.medium,
    textTransform: 'uppercase',
  },

  progress: {
    position: 'absolute',
    bottom: -1,
    left: -6,
    right: -6,
  },
  imageCon: {
    justifyContent: 'center',
    overflow: 'hidden',
    borderRadius: 10,
    marginLeft: 10,
  },

  progressBar: {
    marginTop: Platform.OS == 'ios' ? 10 : 5,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressUnfilleddPercentage: {
    backgroundColor: 'rgba(30, 30, 30, 0.1)',
    height: 5,
    justifyContent: 'center',
    borderRadius: 5,
    marginBottom: 2.4,
    flex: 1,
  },
  progressPercentage: {
    backgroundColor: 'rgba(30, 30, 30, 0.1)',
    height: 5,
    justifyContent: 'center',
    flex: 1,
    borderRadius: 5,
  },
  progressText: {
    fontSize: isPad ? 12 : fontSize.xxmini,
    textAlign: 'center',
    fontFamily: fonts.regular,
    marginStart: 12,
    marginEnd: 8,
    opacity: 0.7,
    lineHeight: lineHeight(isPad ? 12 : fontSize.xxmini),
    top: -3,
  },
  timeAndDateText: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    textAlign: 'center',
    fontFamily: fonts.regular,
    opacity: 0.7,
    marginStart: 5,
  },
    noImageCertificate: {
    width: 126,
    height: 126,
    resizeMode: 'contain',
    alignSelf: 'center',
    justifyContent: 'center'
  },
});