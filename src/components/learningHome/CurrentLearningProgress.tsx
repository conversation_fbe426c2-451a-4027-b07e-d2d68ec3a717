import { TextView } from '@components';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import { fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { commonStyle, useCommonThemeStyles } from '@utils';
import { widgetPadding } from '@utils/constants';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { I18nManager, Platform, StyleSheet, View } from 'react-native';

const CurrentLearningProgress = ({ style, showStatusOnTop, separatorStyle }) => {
    const { themeStyle } = useCommonThemeStyles()
    const textColorForDark = themeStyle.themedTextColor
    const { theme, isDarkMode } = useTheme();

    const { loading, requiredProgramsSummary, } = useGetLearningPrograms('required');
    const unCompletedWidthProgress = (99 - (requiredProgramsSummary?.overallProgress)) + '%'
    const completedWidth = (requiredProgramsSummary?.overallProgress || '0') + '%'
    const { t } = useTranslation();
    return (
        <View style={[styles.container, style]}>
            <TextView style={[styles.learningProgress, textColorForDark]} text='Your current learning progress' />
            <View style={styles.row}>
                <View style={commonStyle.rowAlign}>
                    <TextView text={requiredProgramsSummary?.overallProgress + '%'} style={[styles.percentage, textColorForDark]} />
                    {/* <TextView text={'%'} style={[styles.percentageSymbol, textColorForDark]} /> */}
                </View>

                {/* {showStatusOnTop ? <TextView style={[styles.requiredCourses, Platform.OS == 'android' && { position: 'absolute', bottom: I18nManager.isRTL ? 10 : 13, right: 0 }, I18nManager.isRTL && { fontSize: fontSize.h8 }]}
                    text={`${requiredProgramsSummary.completedcount} ${t('of')} ${requiredProgramsSummary.totalCourses} ${t('required courses completed')}`} />
                    : null} */}
            </View>
            {(completedWidth == '0%' || completedWidth == '100%') ?
                <View style={styles.progressView}>
                    <View style={[styles.unCompletedProgress, { width: '100%', borderTopStartRadius: 5, borderBottomStartRadius: 5 }, isDarkMode && { backgroundColor: '#787878' }]} />
                </View> :

                <View style={styles.progressView}>
                    <View style={[styles.completedProgress, { width: completedWidth }]} />
                    <View style={[styles.separtor, isDarkMode && { backgroundColor: 'transparent' }, separatorStyle]} />
                    <View style={[styles.unCompletedProgress, { width: unCompletedWidthProgress }, isDarkMode && { backgroundColor: '#787878' }]} />
                </View>}
            <TextView style={styles.requiredCourses}
                text={`${requiredProgramsSummary.completedcount} ${t('of')} ${requiredProgramsSummary.totalCourses} ${t('required courses completed')}`} />

        </View>
    )
}

export default CurrentLearningProgress;

const styles = StyleSheet.create({
    container: {
        marginBottom: widgetPadding * 3,
    },
    learningProgress: {
        fontFamily: fonts.semiBold,
        fontSize: fontSize.h3,
        textAlign: 'left'
    },
    percentage: {
        fontFamily: fonts.semiBold,
        fontSize: fontSize.tg_h3,
        // marginTop: Platform.OS == 'android' ? 10 : widgetPadding * 2,
        // lineHeight: Platform.OS == 'android' ? 31 : 34
        marginTop: (Platform.OS == 'ios' && !I18nManager.isRTL) ? widgetPadding * 2 : widgetPadding,
        marginBottom: I18nManager.isRTL ? -widgetPadding / 3 : -5,
        letterSpacing: -1
    },
    percentageSymbol: {
        fontFamily: fonts.bold,
        fontSize: fontSize.tg_h3,
        marginTop: (Platform.OS == 'ios' && !I18nManager.isRTL) ? widgetPadding * 2 : 0,
        marginBottom: I18nManager.isRTL ? -widgetPadding : -5,
        // marginTop: (Platform.OS == 'ios' && !I18nManager.isRTL) ? widgetPadding * 2.5 : widgetPadding * 0.5,
        // marginBottom: I18nManager.isRTL ? -widgetPadding / 0.9 : -3,
        marginHorizontal: 2
    },
    progressView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: Platform.OS == 'android' ? widgetPadding / 3 : widgetPadding,
    },
    unCompletedProgress: {
        backgroundColor: 'rgba(210, 210, 210, 1)',
        height: 6,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10,
    },
    completedProgress: {
        backgroundColor: 'rgba(102, 185, 95, 1)',
        height: 6,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10,
    },
    separtor: {
        backgroundColor: 'white',
        height: 6,
        width: '1%'
    },
    requiredCourses: {
        marginTop: widgetPadding / 2,
        color: 'rgba(120, 120, 120, 1)',
        fontFamily: fonts.regular,
        fontSize: fontSize.h7,
        textAlign: 'left',
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        // marginTop: Platform.OS == 'android' ? 20 : 0
    }
})