import { Colors } from '../../theme';
import React from 'react';
import { ActivityIndicator, Platform, ViewStyle } from 'react-native';
import { IconButton } from 'react-native-paper';
import HeartIconFilledSvg from '@resources/icons/svgs/HeartIconFilledSvg';
import HeartIconOutlinedSvg from '@resources/icons/svgs/HeartIconOutlinedSvg';
import useFavCourses from '@hooks/useFavCourses';
import { IconSource } from 'react-native-paper/lib/typescript/components/Icon';

type FavItemType = {
  courseid: string;
  color?: string;
  style?: ViewStyle;
  size?: number;
  hitSlop?: any;
};

const FavItem = ({ courseid, style, color = Colors.black, size, hitSlop }: FavItemType) => {
  const { isLoading, loadingCourseId, addToFav, removeFromFav, isAddedToFav } = useFavCourses();
  const isCourseLoading = isLoading || (loadingCourseId === Number(courseid))

  const onPress = async () => {
    const id = Number(courseid);
    if (isAddedToFav(id)) {
      removeFromFav(id)
    } else {
      addToFav(id);
    }
  };

  const renderIcon = (): IconSource => {
    if (isCourseLoading) {
      return <ActivityIndicator />;
    }
    return (
      isAddedToFav(Number(courseid)) ? <HeartIconFilledSvg /> : <HeartIconOutlinedSvg color={color} />
    );
  }

  return (
    <IconButton
      hitSlop={hitSlop}
      style={[style, { backgroundColor: 'transparent' }]}
      onPress={onPress}
      disabled={(loadingCourseId ?? -1)?.toString() === courseid}
      icon={() => renderIcon()}
      size={size ? size : Platform.OS == 'ios' ? 15 : 17}
    />
  );
};

export default FavItem;