import { fontFamily } from '../../utils/AppUtils';
import { Colors } from '../../theme';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Platform, Pressable, Text } from 'react-native';
import { StyleSheet } from 'react-native';
import { Dropdown } from '../index';
import Button from '../common/ButtonPrimary';
// import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import { View } from 'react-native';
import BottomSheet from 'reanimated-bottom-sheet';
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../totara/reducers';

const options = [
  {
    label: 'Default',
    value: 'default',
  },
  {
    label: 'Newest',
    value: 'newest',
  },
  {
    label: 'Ratings',
    value: 'ratings',
  },
  {
    label: 'Due soon',
    value: 'due soon',
  },
];

const duration = [
  {
    title: '< 10 mins',
  },
  {
    title: '10-30 mins',
  },
  {
    title: '30-60 mins',
  },
  {
    title: '1-2 hours',
  },
  {
    title: '2-3 hours',
  },
  {
    title: '3+ hours',
  },
];

const types = [
  {
    title: 'Assessment',
  },
  {
    title: 'E-learning',
  },
  {
    title: 'Virtual Class',
  },
  {
    title: 'On Premise',
  },
];

const Filters = ({ onCloses }) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const dispatch = useDispatch();

  const { filtersRequiredCourses: data } = useSelector(
    (state: RootState) => state.filtersRequiredCourses,
  );

  const [selectedFilters, setSelectedFilters] = useState(data ?? []);
  const [sortBy, setSortBy] = useState('');
  const [sortedByTags, setSortedTags] = useState('');

  useEffect(() => {
    bottomSheetRef?.current?.snapTo(1);
  }, []);

  const snapPoints = useMemo(() => ['65%', '90%'], []);

  const onFilterApply = () => {
    dispatch({ type: 'filtersRequiredCourses', payload: selectedFilters });
    onCloses();
  };

  const onOptionSelected = (e) => setSortedTags(e);

  const onPressItem = (isActive, item) => {
    let types = [...selectedFilters];
    if (!isActive) {
      const filterItems = types?.filter((it) => it != item?.title);
      setSelectedFilters(filterItems);
    } else {
      types.push(item?.title);
      setSelectedFilters(types);
    }
  };

  const onResetPress = () => {
    dispatch({ type: 'filtersRequiredCourses', payload: [] });
    onCloses();
  };

  return (
    <>
      <Pressable onPress={onCloses} style={styles.darkBg} />
      <BottomSheet
        snapPoints={snapPoints}
        enabledContentGestureInteraction={true}
        ref={bottomSheetRef}
        enabledInnerScrolling={true}
        onCloseEnd={onCloses}
        renderContent={() => (
          <View style={styles.mainCon}>
            <View style={styles.line} />
            <View style={styles.filterTextCon}>
              <Text style={styles.text}>Filters</Text>
              <Text style={styles.resetText} onPress={onResetPress}>
                Reset All
              </Text>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              <Text style={styles.textH}>Type</Text>
              <View style={styles.itemCon}>
                {types?.map((item) => {
                  return (
                    <FilterItem
                      item={item}
                      onPressItem={(isActive) => onPressItem(isActive, item)}
                    />
                  );
                })}
              </View>

              <Text style={styles.textH}>Duration</Text>
              <View style={[styles.itemCon, { marginBottom: 20 }]}>
                {duration?.map((item) => {
                  return (
                    <FilterItem
                      item={item}
                      onPressItem={(isActive) => onPressItem(isActive, item)}
                    />
                  );
                })}
              </View>

              <Dropdown
                placeholder={'Sort by'}
                data={options}
                dropdownPosition="top"
                getSelectedValue={onOptionSelected}
                style={{ marginHorizontal: 0, marginTop: 0 }}
              />

              <Button
                disabled={!sortedByTags && !selectedFilters.length}
                style={{ marginTop: 25, marginHorizontal: 0 }}
                onPress={onFilterApply}
                label={'Apply Filters'}
              />
            </ScrollView>
          </View>
        )}
        enabledGestureInteraction={true}
        enabledBottomInitialAnimation={true}
      />
    </>
  );
};

export default Filters;

const FilterItem = ({ item, onPressItem }) => {
  const [isOptionSelected, setAcive] = useState(false);

  const { filtersRequiredCourses: data } = useSelector(
    (state: RootState) => state.filtersRequiredCourses,
  );

  useEffect(() => {
    if (data?.length > 0) {
      const isItemActive = data?.some((it) => it === item?.title);
      setAcive(isItemActive);
    }
  }, []);

  const onPress = () => {
    const newActiveStatus = !isOptionSelected;
    setAcive(newActiveStatus);
    onPressItem(newActiveStatus);
  };

  return (
    <Pressable
      style={[
        styles.category,
        isOptionSelected && {
          backgroundColor: Colors.lbgcolor1,
          borderColor: Colors.lbgcolor1,
        },
      ]}
      onPress={onPress}
    >
      <Text
        numberOfLines={1}
        style={[styles.textcategory, isOptionSelected && { color: Colors.black }]}
      >
        {item?.title}
      </Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  darkBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  mainCon: {
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    paddingHorizontal: 22,
    // flex: 1,
    backgroundColor: Colors.white,
    height: '100%',
  },

  text: {
    fontFamily: fontFamily('ST-MEDIUM'),
    color: Colors.black,
    fontSize: 20,
    fontWeight: '500',
  },

  textH: {
    fontFamily: fontFamily('ST-MEDIUM'),
    color: Colors.black,
    fontSize: 16,
    fontWeight: '500',
    marginTop: 20,
  },

  filterTextCon: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },

  resetText: {
    fontFamily: fontFamily('ST-MEDIUM'),
    color: Colors.black,
    fontSize: 14,
  },

  category: {
    marginRight: 10,
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: Colors.grayline,
    marginTop: 10,
  },
  textcategory: {
    color: Colors.filerText,
    // fontWeight: "700",
    fontSize: 12,
    textAlign: 'center',
    fontFamily: fontFamily('ST-MEDIUM'),
  },

  itemCon: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: Platform.OS == 'ios' ? 5 : 0,
  },

  line: {
    alignSelf: 'center',
    marginTop: 15,
    borderRadius: 12,
    height: 3,
    width: 70,
    backgroundColor: Colors.darkblue,
  },
});
