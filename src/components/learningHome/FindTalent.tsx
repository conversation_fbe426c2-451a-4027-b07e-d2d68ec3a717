import { AssetsImages, Colors, Dimen, fontSize, fonts } from '../../theme';
import React from 'react';
import {
  Dimensions,
  Alert,
  Image,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import CardContainer from './CardContainer';
import { isPad } from '../../utils/fontSize';
import { usePermissionRoles } from '../../hooks';
import { I18nManager } from 'react-native';
import { useTheme } from '../../theme/ThemeProvider';
import { TextView } from '@components';
import { lineHeight, widgetPadding } from '@utils/constants';
import { LineHeight } from '@utils';

const FindTalent = () => {
  const { onPress, isRecommendedEnabled } = usePermissionRoles();
  const { theme, isDarkMode } = useTheme();

  return (
    <CardContainer
      header={false}
      containerStyle={[
        styles.con,
        isDarkMode && {
          backgroundColor: theme.bgDark,
        },
      ]}
    >
      <>
        <View style={styles.subCon}>
          <TextView
            style={[styles.text, { color: theme.textColor }, !I18nManager.isRTL && { lineHeight: LineHeight.h2_LH }]}
            text="Help us find talent, spot leaders, and uncover potential."
          />

          <View style={{ flexDirection: 'row', paddingBottom: isPad ? 30 : 20 }}>
            <Pressable
              style={[
                styles.btn,
                { borderColor: theme.textColor },
                isDarkMode && { backgroundColor: '#313131' }
              ]}
              onPress={onPress}
            >
              <TextView
                style={[styles.text2, { color: theme.textColor }]}
                text="Recommend a Talent"
              />
            </Pressable>
          </View>
        </View>

        <View style={styles.imageContainer}>
          <Image
            source={isDarkMode ? AssetsImages.talent_imgDark : AssetsImages.talent_img}
            style={styles.imageBg}
          />
        </View>
      </>
    </CardContainer>
  );
};

export default FindTalent;

const styles = StyleSheet.create({
  imageBg: {
    width: !isPad ? '100%' : Dimensions.get('window').width / 2,
    height: isPad ? 185 : 190,
    resizeMode: 'contain',
    marginBottom: isPad ? 0 : -9,
    transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }],
  },
  con: {
    paddingTop: widgetPadding * 2,
    paddingLeft: 0,
    paddingRight: 0,
    alignItems: undefined,
    overflow: 'hidden',
    opacity: coming_soon_opacity,
    flexDirection: !isPad ? 'column' : 'row',
    justifyContent: 'space-between',
    marginHorizontal: widgetPadding,
    marginBottom: widgetPadding,
    marginTop: widgetPadding,
    paddingBottom: 0
  },
  text: {
    fontSize: fontSize.h2,
    fontFamily: fonts.regular,
    color: Colors.black,
    textAlign: 'left',
    // marginTop: Platform.OS == 'ios' ? -10 : 0,
  },
  imageContainer: {
    flex: 1.2,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    bottom: -5
  },
  btn: {
    borderRadius: 10,
    borderWidth: 1,
    marginTop: widgetPadding,
    flexDirection: 'row',
    minWidth: 124,
    justifyContent: 'center',
    marginBottom: 25
  },
  text2: {
    textAlign: 'center',
    fontFamily: fonts.regular,
    color: Colors.black,
    fontSize: fontSize.h7,
    paddingVertical: widgetPadding / 2,
    paddingHorizontal: widgetPadding
  },

  imageItem: {
    flex: 1,
    marginTop: 20,
    width: Dimen.width / 4,
    height: 180,

    borderTopRightRadius: 180 / 2,
    borderTopLeftRadius: 180 / 2,
    overflow: 'hidden',
    marginRight: 0.5,
  },

  subCon: {
    paddingHorizontal: widgetPadding * 1.5,
    flex: 1,
  },

  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
    marginTop: 3,
  },
});
