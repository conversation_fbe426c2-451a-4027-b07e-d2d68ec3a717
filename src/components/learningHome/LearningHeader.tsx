import React from 'react';
import {
  I18nManager,
  Image,
  Platform,
  Pressable,
  StatusBar,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import AIIcon from '@components/AI/AIIcon.tsx';
import SearchIcon from '@components/Search/searchIcon';
import { useTheme } from '@theme/ThemeProvider';
import { LineHeight, useCommonThemeStyles } from '@utils';
import { lineHeightForAndroidLTRLarge, widgetPadding } from '@utils/constants';
import { useFeatureFlags } from '@utils/FeatureFlagsProvider';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigation, useNotificationList } from '../../hooks';
import { AssetsImages, Colors } from '../../theme';
import { RootState } from '../../totara/reducers';
import { coming_soon_opacity } from '../../utils/AppUtils';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { Avatar, TextView } from '../common';

type LearningHeaderProps = {
  customStyles?: StyleProp<ViewStyle>;
  showName?: boolean;
  showDetail?: boolean;
};

const LearningHeader = ({
  customStyles,
  showName = true,
  showDetail = false,
}: LearningHeaderProps) => {
  const { theme, isDarkMode } = useTheme();
  const user = useSelector((state: RootState) => state?.getWFMProfile?.response);
  const navigation = useNavigation();
  const { unreadCount } = useNotificationList();
  const onPress = () => navigation.navigate('TelentProfile');
  const onBellIconPress = () => navigation.navigate('Notification');
  const onCalendarIconPress = () => navigation.navigate('Calendar');
  const { t } = useTranslation();
  const { themeStyle } = useCommonThemeStyles();
  const { featureFlags } = useFeatureFlags();

  const backgroundColor = isDarkMode ? theme.bgDark : '#ffffff';

  return (
    <View
      style={{
        backgroundColor,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20,
        marginBottom: widgetPadding,
      }}
    >
      <View
        style={[
          styles.container,
          { backgroundColor, shadowColor: theme.textColor },
          customStyles,
          showName && { shadowOpacity: 0.2, elevation: 2 },
        ]}
      >
        <StatusBar
          backgroundColor={isDarkMode ? theme.bgDark : 'white'}
          barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        />
        <Pressable style={styles.pressableStyle} onPress={onPress}>
          <Avatar editable={false} containerStyles={[styles.avatarStyle]} />
          {showName && (
            <View style={styles.nameTextStyle}>
              <TextView
                numberOfLines={2}
                style={[styles.text, { color: theme.textColor }, lineHeightForAndroidLTRLarge]}
                type={'h1'}
                // text={
                //   user?.firstnameEN || user?.firstnameAR
                //     ? `${t('Hi')} ` + (I18nManager.isRTL ? (user?.firstnameAR || '') : (user?.firstnameEN || ''))
                //     : `${t('Hi')}`
                // }
              />
            </View>
          )}
        </Pressable>
        <View style={styles.iconContainer}>
          <SearchIcon />
          <Pressable onPress={onCalendarIconPress}>
            <Image
              source={AssetsImages.calendar}
              style={[styles.icon, { tintColor: isDarkMode ? Colors.activeStateDark : '#000' }]}
            />
          </Pressable>
          <Pressable onPress={onBellIconPress}>
            {unreadCount ? (
              <View
                style={[
                  styles.countView,
                  {
                    backgroundColor: '#DD3333',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.count,
                    isDarkMode && {
                      color: '#0d0d0d',
                    },
                  ]}
                >
                  {unreadCount > 10 ? '9+' : unreadCount}
                </Text>
              </View>
            ) : null}
            <Image
              source={AssetsImages.bell}
              style={[styles.icon, { tintColor: isDarkMode ? Colors.activeStateDark : '#000' }]}
            />
          </Pressable>
          {featureFlags.aiFeature && <AIIcon />}
        </View>
      </View>
      {showDetail && user && (
        <View style={styles.detailSection}>
          <TextView style={[styles.welcomeText, themeStyle.themedTextColor]}>
            {t('Hi')}{' '}
            <TextView
              style={[styles.text, { color: theme.textColor }]}
              text={
                I18nManager.isRTL ? user?.firstnameAR + '،' || '' : (user?.firstnameEN || '') + ','
              }
            />
          </TextView>
          <TextView
            style={[styles.yourSpace, themeStyle.themedTextColor]}
            text={'This is the space where you can learn, grow your skills and thrive'}
          />
        </View>
      )}
    </View>
  );
};

export default LearningHeader;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowRadius: 1.41,
    zIndex: 1000000,
    paddingEnd: 28,
    paddingVertical: 10,
  },
  avatarStyle: { height: 50, width: 50 },
  nameTextStyle: { flexDirection: 'row', flex: 1 },
  pressableStyle: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  subCon: { flexDirection: 'row', alignItems: 'center' },
  text: {
    fontSize: fontSize.h3,
    fontFamily: fonts.semiBold,
    marginStart: 10,
    textTransform: 'capitalize',
    textAlign: 'left',
  },

  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    opacity: coming_soon_opacity,
  },
  imageUser: { height: 45, width: 45, resizeMode: 'contain' },

  iconContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  count: {
    color: 'white',
    fontSize: 9,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  countView: {
    width: 16,
    height: 16,
    backgroundColor: 'red',
    borderRadius: 21 / 2,
    position: 'absolute',
    right: -4,
    zIndex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -4,
  },
  detailSection: {
    marginHorizontal: 20,
    marginVertical: 6,
    marginBottom: 18,
  },
  welcomeText: {
    fontFamily: fonts.regular,
    fontSize: fontSize.h3,
    textAlign: 'left',
  },
  yourSpace: {
    marginTop: Platform.OS == 'ios' && !I18nManager.isRTL ? 6 : 0,
    fontFamily: fonts.regular,
    fontSize: fontSize.h5,
    textAlign: 'left',
    lineHeight: LineHeight.h5_LH,
  },
});
