import React, { useEffect, useState } from 'react';
import { I18nManager, Image, Platform, Pressable, StyleSheet, View } from 'react-native';
import { useMentors, useNavigation } from '@hooks';
import useScreenPerformanceMonitor from '@hooks/useScreenRenderTracker';
import { IconsType } from '@theme/Icons';
import { TotaraTheme } from '@totara/theme/Theme';
import { convertNumbersToArabicNumerals, widgetPadding } from '@utils/constants';
import FastImage from 'react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { AssetsImages, Colors, Dimen, fonts, fontSize, Icons } from '../../theme';
import { useTheme } from '../../theme/ThemeProvider';
import { TextView } from '../index';
import CardContainer from './CardContainer';

const MentorShip = () => {
  const navigation = useNavigation();
  const [tab, setTab] = useState('1');
  const { list, loading } = useMentors({ tab: null });
  const { theme, isDarkMode } = useTheme();
  const { initialize } = useScreenPerformanceMonitor();

  useEffect(() => {
    initialize();
  }, []);

  const bgColor = isDarkMode ? theme.bgLightDark : TotaraTheme.colorNeutral3;
  const highlightColor = isDarkMode ? theme.bgDark : 'silver';

  return (
    <CardContainer
      title={'Coaching'}
      count={loading ? '' : (list?.length?.toString() ?? '0')}
      onView={list?.length ? () => navigation.navigate('MentorShip', { list, tab }) : false}
      containerStyle={[
        styles.mentorContainerStyle,
        isDarkMode && {
          backgroundColor: theme.bgDark,
        },
      ]}
    >
      <>
        <View style={styles.mentorContainer}>
          {loading ? (
            <>
              {Array.from({ length: 3 }).map((_, index) => (
                <View key={index} style={[[styles.mentorItem, { marginBottom: 15 }]]}>
                  <SkeletonPlaceholder highlightColor={highlightColor} backgroundColor={bgColor}>
                    <FastImage style={styles.imageStyle} source={AssetsImages.courseImage} />
                  </SkeletonPlaceholder>
                  <View style={{ marginTop: 10 }} />
                  <SkeletonPlaceholder highlightColor={highlightColor} backgroundColor={bgColor}>
                    <View
                      style={{
                        width: 80,
                        height: 12,
                        borderRadius: 5,
                        alignSelf: 'center',
                      }}
                    />
                  </SkeletonPlaceholder>
                  <SkeletonPlaceholder highlightColor={highlightColor} backgroundColor={bgColor}>
                    <View
                      style={{
                        marginTop: 2,
                        width: 40,
                        height: 12,
                        borderRadius: 5,
                        alignSelf: 'center',
                      }}
                    />
                  </SkeletonPlaceholder>
                </View>
              ))}
            </>
          ) : (
            Array.isArray(list) &&
            list?.slice(0, 3)?.map((item, index) => {
              return (
                <MentorItem
                  index={index}
                  key={index}
                  item={item}
                  border={index == 2 ? false : true}
                  onPress={() => navigation.navigate('MentorShipDetails', { item, tab })}
                />
              );
            })
          )}
        </View>
      </>
    </CardContainer>
  );
};

export default MentorShip;

const MentorItem = ({ item, border, onPress }: { item; border: boolean; onPress: () => void }) => {
  const { theme, isDarkMode } = useTheme();
  return (
    <Pressable
      style={[
        styles.mentorItem,
        {
          borderRightWidth: border ? 1 : 0,
          borderRightColor: !isDarkMode ? 'rgb(240,240,240)' : theme.borderBackground,
        },
      ]}
      onPress={onPress}
    >
      <Image
        defaultSource={AssetsImages.soulvite}
        source={item?.picture ? { uri: item?.picture } : AssetsImages.soulvite}
        style={styles.imageStyle}
      />

      <TextView
        text={`${item?.firstname} ${item?.lastname}`}
        numberOfLines={1}
        style={[styles.username, { color: theme.textColor }]}
      />

      <TextView
        numberOfLines={1}
        text={item?.designation ? item.designation : ' '}
        style={[
          styles.designation,
          {
            textAlign: 'center',
            width: '80%',
            marginTop: I18nManager.isRTL ? -5 : Platform.OS == 'android' ? 2 : 5,
          },
          isDarkMode && { color: '#C7C7C7', opacity: 0.8 },
        ]}
      />
      <View style={styles.ratingCon}>
        <Icons type={IconsType.Entypo} name={'star'} size={15} color={Colors.rating} />
        <TextView
          text={convertNumbersToArabicNumerals((+item?.rating)?.toFixed(1)) + ' '}
          style={[styles.ratingText, isDarkMode && { color: theme.textColor, textAlign: 'center' }]}
        />
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  mentorContainerStyle: {
    alignItems: 'flex-start',
    marginHorizontal: widgetPadding,
    marginVertical: widgetPadding,
    paddingHorizontal: widgetPadding,
  },
  ratingCon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  mentorItem: {
    alignItems: 'center',
    flex: 1,
    maxWidth: Dimen.width * 0.32,
    paddingHorizontal: widgetPadding / 2,
  },

  mentorContainer: {
    flexDirection: 'row',
    marginVertical: widgetPadding,
    width: '100%',
  },

  ratingText: {
    fontSize: 13,
    fontFamily: fonts.medium,
    color: 'rgba(86, 86, 86, 1)',
    marginLeft: 3,
  },

  username: {
    fontSize: fontSize.h6,
    fontFamily: fonts.regular,
    marginTop: widgetPadding,
    textAlign: 'center',
    color: Colors.black,
  },

  imageStyle: {
    width: 70,
    height: 70,
    borderRadius: 70 / 2,
    backgroundColor: '#f5f5f5',
  },
  designation: {
    fontSize: fontSize.h8,
    fontFamily: fonts.thin,
    color: 'rgba(0, 0, 0, 1)',
  },
});
