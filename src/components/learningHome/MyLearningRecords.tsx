import { AssetsImages, Colors, fonts } from '../../theme';
import React from 'react';
import { I18nManager, Image, ImageSourcePropType, Pressable, StyleSheet, View } from 'react-native';
import CardContainer from './CardContainer';
import { useBadges, useNavigation } from '../../hooks';
import fontSize, { isPad } from '../../utils/fontSize';
import CourseProgress from 'models/CourseProgress';
import { useSelector } from 'react-redux';
import { BreadCrumb, TextView } from '@components';
import LinearGradient from 'react-native-linear-gradient';
import { useTheme } from '@theme/ThemeProvider';
import { myLearningRecordData, widgetPadding } from '@utils/constants';
import useCommonThemeStyle from '@utils/commonThemeStyles';
import { LineHeight } from '@utils';
import { Platform } from 'react-native';

const MyLearningRecords = ({ data }) => {
  const navigation = useNavigation();
  const { badges } = useBadges();
  const { isDarkMode, theme } = useTheme();

  const externalCertificates = useSelector(
    (state) => state?.getWFMProfile?.talentProfile?.training,
  );

  return (
    <View style={{ marginVertical: widgetPadding }}>
      <BreadCrumb text="My Learning Records" />
      <View style={styles.requiredContianer}>
        {myLearningRecordData.map((it, index) => {
          return (
            <ListItem
              item={it}
              index={index}
              count={
                index === 0
                  ? data?.completedCourses?.length
                    ? data?.completedCourses?.length
                    : '0'
                  : index == 2
                    ? `${badges?.userbadges && badges?.userbadges?.length > 0 ? badges?.userbadges.length : '0'}`
                    : index == 1
                      ? data?.certificates?.length + (externalCertificates?.length || 0)
                      : '0'
              }
              onPress={() => {
                navigation.navigate(it.route, {
                  data: index === 0 ? data?.completedCourses : data?.certificates,
                  title: it.key,
                });
              }}
            />
          );
        })}
      </View>
    </View>
  );
};

export default MyLearningRecords;

type itemTitle = {
  title: string;
  icon: ImageSourcePropType;
  backgroundColor: string;
  lastCompletedHeading: string;
  lastCompletedDes: string;
};

const ListItem = ({
  index,
  item,
  count,
  onPress,
}: {
  index: number;
  count: number;
  item: itemTitle;
  onPress;
}) => {
  const { isDarkMode, theme } = useTheme();
  const { themeStyle } = useCommonThemeStyle();
  const lightModeText = isDarkMode && themeStyle.themedTextColor;

  return (
    <View style={[styles.itemContainer, isDarkMode && { backgroundColor: theme.bgDark }]}>
      <Pressable
        style={{ paddingVertical: widgetPadding, paddingHorizontal: widgetPadding }}
        key={index}
        onPress={onPress}
      >
        <Image source={item.icon} style={styles.iconImage} />
        <TextView
          text={count?.toString() || '0'}
          numberOfLines={2}
          style={[styles.coursesNumbers, { color: theme.textColor }]}
        />
        <TextView
          text={item?.title}
          numberOfLines={2}
          style={[styles.courses, isDarkMode && { color: theme.textColor }]}
        />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flex: 1,
    marginHorizontal: 5,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: Colors.white,
  },
  requiredContianer: {
    flexDirection: 'row',
    marginHorizontal: 12,
  },
  courses: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.h6,
    color: 'rgba(30, 30, 30, 1)',
    textAlign: 'left',
    fontFamily: fonts.regular,
    minHeight: LineHeight.h6_LH,
    marginTop: Platform.OS == 'ios' && !I18nManager.isRTL ? -2 : -8,
  },
  coursesNumbers: {
    fontSize: fontSize.h1,
    textAlign: 'left',
    fontFamily: fonts.semiBold,
    color: Colors.black,
    marginTop: widgetPadding * 1.5,
  },
  lastCompletedHeading: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    textAlign: 'left',
    fontFamily: fonts.regular,
    color: Colors.black,
    marginBottom: 2,
  },
  lastCompletedDes: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    textAlign: 'left',
    fontFamily: fonts.thin,
    color: Colors.black,
  },
  icon: {
    backgroundColor: '#fff',
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40 / 2,
  },
  iconImage: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
});
