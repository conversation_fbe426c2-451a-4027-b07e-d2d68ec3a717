import { fontFamily } from '../../utils/AppUtils';
import { Colors, fontSize } from '../../theme';
import React from 'react';
import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';
import fonts from '../../utils/fonts';

type ProgressBar = {
  active: number;
  total: number;
  style?: StyleProp<ViewStyle>;
  label?: boolean;
  inAtiveColor?: string;
  ativeColor?: string;
  height?: number;
};

const ProgressBar = ({
  active,
  total,
  label = true,
  style,
  inAtiveColor,
  ativeColor,
  height,
}: ProgressBar) => {
  const activeProgress = (active / total) * 100;

  return (
    <>
      {label && (
        <View style={[styles.textCon]}>
          <Text
            style={[
              styles.text,
              {
                fontFamily: fonts.bold,
                fontWeight: '700',
              },
            ]}
          >
            Achieved: {active}
          </Text>
          <Text style={styles.text}>Total: {total}</Text>
        </View>
      )}
      <View style={[styles.subCon, style]}>
        <View
          style={[
            styles.totalProgress,
            inAtiveColor && { backgroundColor: inAtiveColor },
            height && { height },
          ]}
        />

        <View
          style={[
            styles.activeProgress,
            {
              width: `${activeProgress}%`,
            },
            ativeColor && { backgroundColor: ativeColor },
            height && { height },
          ]}
        />
      </View>
    </>
  );
};

export default ProgressBar;

const styles = StyleSheet.create({
  textCon: {
    marginTop: 10,
    flexDirection: 'row',
    marginBottom: 7,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 10,
  },
  text: {
    fontSize: fontSize.small,
    fontFamily: fontFamily('ST-MEDIUM'),
    color: Colors.black,
  },
  subCon: { marginHorizontal: 0 },

  activeProgress: {
    height: 3,
    backgroundColor: Colors.pgbar,
    borderRadius: 10,
    position: 'absolute',
    left: 0,
    right: 0,
  },

  totalProgress: {
    height: 3,
    borderRadius: 10,
    backgroundColor: Colors.lightGray,
    width: '100%',
  },
});
