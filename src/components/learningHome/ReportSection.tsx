import { TextView } from '@components';
import { AssetsImages, Colors, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { LineHeight, commonStyle } from '@utils';
import { reportSection, widgetPadding } from '@utils/constants';
import React from 'react';
import { I18nManager, Image, Platform } from 'react-native';
import { StyleSheet, View } from 'react-native';

const ReportSection = () => {
    const { isDarkMode, theme } = useTheme();

    return (
        <View style={[commonStyle.rowAlign, { marginTop: widgetPadding }]}>
            {reportSection.map((res, index) => {
                return (
                    <View style={[styles.container, { backgroundColor: res.background }, { marginRight: index == reportSection.length - 1 ? 0 : widgetPadding }]}>
                        <Image style={styles.image} source={res.image} />
                        <TextView style={styles.name} text={res.name} />
                        <TextView style={styles.detail} text={res.detail} />
                    </View>
                )
            })}
        </View>
    )
}

export default ReportSection;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        borderRadius: 5,
        paddingTop: widgetPadding,
        minHeight: 175,
        paddingStart: widgetPadding,
        paddingEnd: widgetPadding,
    },
    image: {
        width: 30,
        height: 30,
        resizeMode: 'contain',
        marginBottom: widgetPadding / 2
    },
    detail: {
        marginTop: 5,
        fontFamily: fonts.regular,
        fontSize: fontSize.h7,
        lineHeight: LineHeight.h7_LH,
        color: Colors.lightBlackText,
        textAlign: 'left'
    },
    name: {
        marginTop: widgetPadding,
        fontFamily: fonts.semiBold,
        fontSize: fontSize.h5,
        lineHeight: LineHeight.h5_LH,
        color: Colors.lightBlackText,
        textAlign: 'left'
    },

})