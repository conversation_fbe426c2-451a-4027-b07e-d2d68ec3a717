import { AssetsImages, Colors, fonts } from '../../theme';
import React, { useEffect, useState } from 'react';
import {
  Image,
  ImageSourcePropType,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { fontFamily } from '../../utils/AppUtils';
import CardContainer from './CardContainer';
import { ProgressBar } from '.';
import { useNavigation } from '../../hooks';
import { getCoursesProgress } from '../../api/totaraApis';
import { useSession } from '@totara/core';
import fontSize, { isPad, normalize } from '../../utils/fontSize';
import CourseProgress from 'models/CourseProgress';
import { useIsFocused } from '@react-navigation/native';

const requiredLearningData = [
  {
    title: 'Self-Paced',
    key: 'Self-paced',
    // title: "Virtual Class",
    icon: AssetsImages.g_play,
    data: [] as CourseProgress['requiredcourses'],
    backgroundColor: Colors.lbgcolor1,
  },
  {
    title: 'Instructor Led',
    // title: "On Premise",
    icon: AssetsImages.Vector,
    data: [],
    backgroundColor: Colors.lbgcolor2,
    key: 'Instructor-led',
  },
  {
    title: 'Assessment',
    icon: AssetsImages.Union,
    data: [],
    backgroundColor: Colors.lbgcolor3,
  },
];

const RequiredLearning = () => {
  const navigation = useNavigation();
  const { apiToken } = useSession();
  const isFocused = useIsFocused();
  const [requiredCoursesList, setRequiredCoursesList] = useState<CourseProgress['requiredcourses']>(
    [],
  );
  const onView = () => {
    // navigation.navigate("RequiredLearning", {
    //   data: requiredCoursesList,
    // });
    navigation.navigate('RequiredLearning');
  };

  useEffect(() => {
    getCoursesProgress({
      apiToken: apiToken,
      userId: global.userData?.id,
    })
      .then((response) => {
        const classes = [] as CourseProgress['requiredcourses'];
        const instructorLed = [] as CourseProgress['requiredcourses'];
        response?.requiredcourses?.map((it) => {
          if (it?.coursetype == 'Self-paced') {
            classes.push(it);
          }
          if (it?.coursetype == 'Instructor-led') {
            instructorLed.push(it);
          }
        });
        requiredLearningData[0]['data'] = classes;
        requiredLearningData[1]['data'] = instructorLed;
        setRequiredCoursesList(response?.requiredcourses ?? []);
      })
      .catch((e) => console.warn(e));
  }, [global.userData?.id, isFocused]);

  return (
    <CardContainer
      title={'Required Learning'}
      count={requiredCoursesList?.length?.toString() ?? 0}
      containerStyle={{ paddingBottom: 20 }}
      onView={onView}
    >
      <View style={{ width: '100%' }}>
        <View style={styles.requiredContianer}>
          {requiredLearningData.map((it, index) => {
            return (
              <ListItem
                item={it}
                index={index}
                count={it?.data?.length}
                onPress={() => {
                  if (it?.data?.length > 0)
                    navigation.navigate('RequiredLearning', {
                      data: it?.data,
                      title: it.key,
                    });
                }}
              />
            );
          })}
        </View>

        {/* <ProgressBar total={100} active={0} style={{ marginHorizontal: 10 }} /> */}
      </View>
    </CardContainer>
  );
};

export default RequiredLearning;

type itemTitle = {
  title: string;
  icon: ImageSourcePropType;
  backgroundColor: string;
};

const ListItem = ({
  index,
  item,
  count,
  onPress,
}: {
  index: number;
  count: number;
  item: itemTitle;
  onPress;
}) => {
  return (
    <Pressable
      style={[styles.itemContainer, { backgroundColor: item?.backgroundColor }]}
      onPress={onPress}
    >
      <Image source={item?.icon} style={styles.iconImage} />

      <View>
        <Text numberOfLines={1} style={styles.courses}>
          {item?.title}
        </Text>
        <Text style={styles.coursesNumbers}>{count}</Text>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flex: 1,
    borderColor: Colors.lightGray,
    backgroundColor: Colors.bgcard,
    paddingTop: 15,
    paddingBottom: 5,
    // paddingBottom: 10,
    paddingHorizontal: 10,
    marginHorizontal: 5,
    borderRadius: 16,
    // justifyContent: "space-between",
  },

  requiredContianer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
  },

  courses: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    marginTop: 15,
    height: 20,
    color: Colors.lightesetGray,
    textAlign: 'left',
    fontWeight: '500',
    fontFamily: fonts.medium,
  },
  coursesNumbers: {
    fontSize: isPad ? 30 : fontSize.xxxxlarge,
    textAlign: 'left',
    fontFamily: fonts.medium,
    color: Colors.black,
    marginTop: isPad ? 10 : 0,
  },

  icon: {
    backgroundColor: '#fff',
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40 / 2,
  },

  iconImage: { width: isPad ? 55 : 42, height: isPad ? 55 : 42, resizeMode: 'contain' },
});
