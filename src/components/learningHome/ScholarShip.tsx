import { AssetsImages, Colors } from '../../theme';
import React from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import CardContainer from './CardContainer';
import fonts from '../../utils/fonts';
import { Toast } from 'native-base';
import { TextView } from '../index';

export const scholarShipData = [
  {
    title: 'Lorem ispum',
    // title: "Master of Applied Data Science",
    image: AssetsImages.ss1,
    status: 'In Review',
  },
  {
    title: 'Lorem ispum',
    // title: "Career Advancement Scholarship",
    image: AssetsImages.ss2,
    status: 'In Progress',
  },
  {
    title: 'Lorem ispum',
    // title: "Design Thinking Degree Scholarship",
    image: AssetsImages.ss3,
    status: 'Shortlisted',
  },
];

const ScholarShip = () => {
  return (
    <CardContainer
      title={'Scholarships'}
      count={'0'}
      containerStyle={{
        opacity: coming_soon_opacity,
      }}
      opacity={true}
    >
      <Pressable style={styles.mentorContainer} onPress={() => Toast.show({ text: 'Coming soon', textStyle: {textAlign: 'left'} })}>
        <TextView
          style={{ marginVertical: 10, fontFamily: fonts.medium, fontSize: 18 }}
          text="Available Soon"
        />
        {/* {scholarShipData.map((item, index) => {
          return <ScholarItem item={item} />;
        })} */}
      </Pressable>
    </CardContainer>
  );
};

export default ScholarShip;

const statusBGColor = {
  'In Review': {
    bgColor: Colors.s1,
    txtColor: Colors.statusTextInReview,
  },
  'In Progress': {
    bgColor: Colors.s2,
    txtColor: Colors.statusTextInProgress,
  },
  Shortlisted: {
    bgColor: Colors.s3,
    txtColor: Colors.statusTextShortlisted,
  },
};

const ScholarItem = ({ item }) => {
  return (
    <View style={[styles.ScholarItem]}>
      <Image
        source={AssetsImages.default}
        //  source={item?.image}
        style={styles.imageStyle}
      />

      <Text numberOfLines={3} style={styles.username}>
        {item?.title}
      </Text>

      <View style={{ flexDirection: 'row' }}>
        <View style={[styles.statusCon, { backgroundColor: statusBGColor[item?.status].bgColor }]}>
          <Text style={[styles.reviewText, { color: statusBGColor[item?.status].txtColor }]}>
            {item?.status}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  statusCon: {
    borderRadius: 20,
    backgroundColor: 'rgba(125, 161, 196, .3)',
    paddingVertical: Platform.OS == 'ios' ? 4 : 2,
    paddingHorizontal: 6,

    justifyContent: 'center',
    marginTop: 10,
  },
  ScholarItem: {
    flex: 1,
    borderColor: Colors.lightGray,
    backgroundColor: Colors.bgColor,
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginHorizontal: 5,
    borderRadius: 12,
  },

  mentorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 15,
  },

  ratingText: {
    fontSize: 13,
    fontFamily: fontFamily('ST-MEDIUM'),
  },

  username: {
    fontSize: 12,
    fontFamily: fontFamily('ST-MEDIUM'),
    fontWeight: '500',
    marginTop: 15,
    textAlign: 'left',
    maxWidth: 80,
    marginBottom: 5,
    color: Colors.black,
  },

  reviewText: {
    fontSize: 10,
    textAlign: 'center',
    fontFamily: fonts.medium,
    color: 'rgba(125, 161, 196, 1)',
    fontWeight: '500',
  },
  imageStyle: { width: 40, height: 40, borderRadius: 40 / 2 },
});
