import { Colors, Dimen, fonts } from '../../theme';
import React, { useEffect, useState } from 'react';
import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import CardContainer from './CardContainer';
import { useNavigation } from '../../hooks';
import fontSize, { isPad } from '../../utils/fontSize';
import { Toast } from 'native-base';
import { TextView } from '@components';
import useFavCourses from '@hooks/useFavCourses';

const selfCreatedListData = [
  {
    title: 'My Favorites',
    count: 0,
  },
  // {
  //   title: "All Things Python",
  //   count: 0,
  // },
  // {
  //   title: "Leadership Related",
  //   count: 0,
  // },
];

const SelfCreated = () => {
  const navigation = useNavigation();
  const { data } = useFavCourses();

  const onPlusPress = () => Toast.show({ text: 'Coming soon', textStyle: {textAlign: 'left'} });
  const onViewAllPress = () => navigation.navigate('MyFavorites');
  return (
    <CardContainer
      onViewAllPress={onViewAllPress}
      onPlusPress={onPlusPress}
      plus={false}
      title={'My List'}
      count={(data?.length ?? 0)?.toString() ?? ''}
    >
      <View style={styles.mentorContainer}>
        {selfCreatedListData.map((item, index) => {
          return <ListItem key={index} {...{ item, navigation, index }} />;
        })}
      </View>
    </CardContainer>
  );
};

export default SelfCreated;

const ListItem = ({ index, item, navigation }) => {
  const { data } = useFavCourses();

  const onPress = () => {
    if (item?.title == 'My Favorites') {
      navigation.navigate('MyFavorites');
    }
  };
  return (
    <Pressable
      style={[styles.ScholarItem, index > 0 && { opacity: coming_soon_opacity }]}
      onPress={index > 0 ? () => Toast.show({ text: 'Coming soon', textStyle: {textAlign: 'left'} }) : onPress}
    >

      <TextView text={item?.title} numberOfLines={2} style={styles.username} />

      <TextView text={index == 0 ? data?.length : item?.count} style={styles.coursesNumbers} />

      <TextView text={"Courses"} style={styles.courses} />

    </Pressable>
  );
};

const styles = StyleSheet.create({
  statusCon: {
    borderRadius: 20,
    backgroundColor: '#fff',
    width: 60,
    padding: 4,
    justifyContent: 'center',
    marginTop: 10,
  },
  ScholarItem: {
    flex: 1,
    maxWidth: Dimen.width * 0.32,
    borderColor: Colors.lightGray,
    backgroundColor: Colors.bgcard,
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginHorizontal: 5,
    borderRadius: 16,
    justifyContent: 'space-between',
  },

  mentorContainer: {
    flexDirection: 'row',
    // justifyContent: "space-between",
    paddingVertical: 15,
    // backgroundColor: 'red',
    width: '100%',
  },

  ratingText: {
    fontSize: 13,
    fontFamily: fonts.medium,
  },

  username: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    fontFamily: fonts.medium,
    fontWeight: '500',
    textAlign: 'left',
    height: 40,
    color: Colors.black,
  },

  courses: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    color: 'rgba(30, 30, 30, 1)',
    textAlign: 'left',
    fontFamily: fonts.regular,
  },
  coursesNumbers: {
    // marginTop: 20,
    fontSize: 30,
    color: Colors.black,
    fontWeight: '500',
    textAlign: 'left',
    fontFamily: fonts.medium,
  },
});
