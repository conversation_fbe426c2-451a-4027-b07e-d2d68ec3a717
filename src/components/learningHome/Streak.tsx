import { Colors, Dimen, fonts, fontSize } from '../../theme';
import { coming_soon_opacity } from '../../utils/AppUtils';
import React from 'react';
import { I18nManager, Platform, Pressable, StyleSheet, View, ViewStyle } from 'react-native';
import * as Progress from 'react-native-progress';
import { TextView } from '../index';
import streakMduleSizes, { isPad } from '../../utils/fontSize';
import { getCurrentDayName, widgetPadding } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';
import { useSelector } from "react-redux";
import CircleProgressBar from '@screens/Streaks/circleProgressBar';

type props = {
  navigation: any;
  noText?: boolean;
  containerStyles?: ViewStyle | ViewStyle[];
};
const dayOrder = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

const Streak = ({ navigation, noText, containerStyles }: props) => {
  const { theme, isDarkMode } = useTheme();
  const streakData = useSelector((state) => state?.getStreakData);

  const navigate = () => {
    navigation.navigate('Streaks');
  };


  if (!streakData?.weekRecord?.current_weekstreak_record?.length) return null;

  const sortedData = streakData?.weekRecord?.current_weekstreak_record.sort((a, b) => dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day));

  return (
    <>
      <Pressable style={[styles.container, containerStyles]} onPress={navigate}>
        {!isPad && !noText ? <TextView style={[styles.text, { color: theme.textColor }]} text="MY STREAK" /> : null}
        <View style={styles.subCon}>
          {(isPad && !noText && !I18nManager.isRTL) ? (
            <TextView
              style={[styles.text, { fontSize: fontSize.h3, color: theme.textColor }]}
              text="MY STREAK"
            />
          ) : null}

          <View style={styles.daysConList}>
            {(I18nManager.isRTL ? sortedData.reverse() : sortedData)?.map((item, index) => {
              const isCurrentDay = getCurrentDayName('short') === item?.day;
              const progress = ((+item?.progress || 0) / 100) > 1 ? 1 : (+item?.progress || 0) / 100

              return (
                <View key={index} style={[styles.daysCon, { alignItems: 'center', justifyContent: 'center' }]}>
                  <TextView
                    text={item?.day[0]}
                    style={[styles.daysText,
                    {
                      color: isDarkMode ? item?.is_streak ? '#F5F5F5' : Colors.thunder40 : item?.is_streak ? Colors.black : Colors.thunder40,//thnunder60
                      position: 'absolute', fontSize: fontSize.xmini
                    }, Platform.OS == 'android' && { top: 8 }]}
                  />
                  <CircleProgressBar
                    circleThickness={3.5}
                    circleSize={35}
                    progress={progress > 1 ? 1 : (progress < 0.01 && progress > 0 ? 0 : progress)} />
                  {isCurrentDay ? <View style={[styles.dot, { backgroundColor: theme.textColor }]} /> : null}
                </View>
              );
            })}
          </View>
          {(isPad && !noText && I18nManager.isRTL) ? (
            <TextView
              style={[styles.text, { fontSize: streakMduleSizes.semiMedium, color: theme.textColor }]}
              text="MY STREAK"
            />
          ) : null}

        </View>
      </Pressable>
    </>
  );
};

export default Streak;

const styles = StyleSheet.create({
  text: {
    marginTop: Platform.OS == 'ios' ? -12 : -25,
    textAlign: 'left',
    marginVertical: widgetPadding,
    fontSize: isPad ? fontSize.xlarge : fontSize.h3,
    fontFamily: fonts.semiBold,
    textTransform: 'capitalize',
  },
  container: {
    opacity: coming_soon_opacity,
    marginHorizontal: 22,
    marginVertical: widgetPadding,
    width: '100%',
  },
  subCon: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: isPad ? 16 : 0,
  },
  daysConList: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  daysCon: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  daysText: {
    fontFamily: fonts.medium,
    fontSize: isPad ? streakMduleSizes.semiMedium : streakMduleSizes.small,
    marginBottom: Platform.OS === "android" ? 2 : 0,
  },
  dot: {
    width: isPad ? 7 : 4,
    height: isPad ? 7 : 4,
    borderRadius: isPad ? 7 : 4,
    position: 'absolute',
    bottom: isPad ? -12 : -8,

  },
});
