import { IconsType } from '../../theme/Icons';
import { AssetsImages, Colors, Icons } from '../../theme';
import React from 'react';
import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { IconButton } from 'react-native-paper';
import { coming_soon_opacity, fontFamily } from '../../utils/AppUtils';
import fonts from '../../utils/fonts';
import { Toast } from 'native-base';
import { lineHeight } from '@utils/constants';

const VirtualClassAlert = () => {
  return (
    <Pressable
      style={[styles.contianer, { opacity: coming_soon_opacity }]}
      onPress={() => Toast.show({ text: 'Coming soon', textStyle: {textAlign: 'left'} })}
    >
      <Image source={AssetsImages.calender_round} style={styles.calenderIcon} />

      <Text style={styles.text}>
        Upcoming virtual classroom training on <Text style={styles.bold}>Business Ethics</Text> on
        11 Dec 2023
      </Text>

      <IconButton
        icon={() => {
          return <Icons type={IconsType.Entypo} name={'cross'} color={Colors.black} size={20} />;
        }}
        size={12}
      />
    </Pressable>
  );
};

export default VirtualClassAlert;

const styles = StyleSheet.create({
  contianer: {
    marginTop: 10,
    height: 65,
    paddingLeft: 10,
    paddingRight: 5,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    marginHorizontal: 10,
    borderRadius: 16,
  },
  calenderIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },

  text: {
    fontSize: 13,
    fontFamily: fonts.medium,
    flex: 1,
    marginStart: 10,
    color: Colors.black,
    lineHeight: lineHeight(13)
  },

  bold: { fontWeight: '700', fontFamily: fonts.bold },
});
