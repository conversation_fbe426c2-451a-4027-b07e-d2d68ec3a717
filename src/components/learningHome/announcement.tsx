import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { getAnnouncements } from '@api/totaraApis';
import useScreenPerformanceMonitor from '@hooks/useScreenRenderTracker';
import { useTheme } from '@theme/ThemeProvider';
import { useSession } from '@totara/core';
import { widgetPadding } from '@utils/constants';
import moment from 'moment';
import momentWithLocales from 'moment/min/moment-with-locales'; // Import with alias for locale-specific functionality

import { useNavigation } from '../../hooks';
import { AnnouncementModel } from '../../models';
import fonts from '../../utils/fonts';
import fontSize, { isPad } from '../../utils/fontSize';
import CardContainer from './CardContainer';
import CoursesItem from './CoursesItem';

const Announcement = () => {
  const [data, setAnnouncements] = useState<AnnouncementModel[]>([]);
  const [loading, setLoading] = useState(false);
  const { apiToken, core } = useSession();
  const { initialize } = useScreenPerformanceMonitor();

  useEffect(() => {
    initialize();
  }, []);

  useEffect(() => {
    getHomeData();
  }, []);

  const getHomeData = async () => {
    const config = {
      apiToken,
    };

    const [announcementsData] = await Promise.all([getAnnouncements(config)]);

    if (announcementsData) setAnnouncements(announcementsData?.records);
    setLoading(false);
  };

  const navigation = useNavigation();
  if (!data) {
    return null;
  }
  const { theme, isDarkMode } = useTheme();
  const sortedData = data.sort(
    (a, b) => moment(b.created, 'DD/MM/YYYY').valueOf() - moment(a.created, 'DD/MM/YYYY').valueOf(),
  );

  const onViewAll = () => {
    navigation?.navigate('CategoryListing', {
      list: sortedData,
      title: 'Announcements',
      viewType: 'announcement',
    });
  };

  const navigate = (item) => () => navigation.navigate('AnnoucementDetail', item);

  return (
    <CardContainer
      title={'Announcements'}
      count={JSON.stringify(data?.length)}
      containerStyle={[
        styles.container,
        isDarkMode && {
          backgroundColor: theme.bgDark,
        },
      ]}
      onView={onViewAll}
    >
      <>
        {sortedData?.slice(0, 1)?.map((item, index) => {
          const locale = 'en';
          momentWithLocales?.locale(locale);
          const date = momentWithLocales(item?.created, 'DD/MM/YYYY').format('DD MMM YYYY');

          return (
            <CoursesItem
              style={[
                { marginBottom: -widgetPadding, marginStart: 0 },
                isDarkMode && { backgroundColor: theme.bgDark },
              ]}
              titleStyle={[styles.itemTitle, { color: theme.textColor }]}
              dateStyle={styles.itemDate}
              key={index}
              image={item?.image}
              name={item?.name}
              contentContainerStyles={{ paddingLeft: 0 }}
              courseid={item?.courseid?.toString()}
              date={date}
              onPress={navigate(item)}
              viewType="announcement"
              announcementImageStyle={{
                marginRight: -5,
                width: 130,
                height: 120,
              }}
            />
          );
        })}
      </>
    </CardContainer>
  );
};

export default Announcement;

const styles = StyleSheet.create({
  container: {
    paddingBottom: widgetPadding,
    marginVertical: widgetPadding,
    marginHorizontal: widgetPadding,
    paddingHorizontal: widgetPadding,
  },
  itemTitle: {
    fontSize: fontSize.h6,
    fontFamily: fonts.regular,
    textAlign: 'left',
    // lineHeight: LineHeight.h6_LH,
    marginTop: 5,
    color: 'rgba(30, 30, 30, 1)',
    width: '90%',
    marginLeft: 0,
  },
  itemDate: {
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    fontFamily: fonts.medium,
    textAlign: 'left',
    // backgroundColor: Colors.red
  },
});
