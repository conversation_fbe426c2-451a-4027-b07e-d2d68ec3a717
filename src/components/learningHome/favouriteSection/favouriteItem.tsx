import { FavItem, SubItem, TextView } from '@components';
import { AssetsImages, Colors, Dimen, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { LineHeight, commonStyle } from '@utils';
import useCommonThemeStyle from '@utils/commonThemeStyles';
import { navigateToCourseDetail, widgetPadding } from '@utils/constants';
import React from 'react';
import { Platform, Pressable, StyleSheet } from 'react-native';
import { Image, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@hooks';


const FavouriteItem = ({ res, inProgress }) => {
    const { theme, isDarkMode } = useTheme();
    const { themeStyle } = useCommonThemeStyle();
    const lightModeText = isDarkMode && themeStyle.themedTextColor
    const { t } = useTranslation();
    const navigation = useNavigation();

    const onPress = () => {
        navigateToCourseDetail(res, navigation, inProgress);
    };

    return (
        <Pressable
            onPress={onPress}
            key={res?.id} style={[styles.itemContainer, isDarkMode && { backgroundColor: theme.bgDark }]}>
            <FastImage source={{ uri: res?.image }} style={styles.imageView} >
                <View style={commonStyle.overlay} />
            </FastImage>

            <View style={styles.detailView}>
                <View style={styles.topViewView}>
                    <TextView

                        style={[styles.courseType, lightModeText]} text={t(res?.type)} />
                    {res?.id && <FavItem
                        color="red"
                        style={styles.like}
                        courseid={res?.id} />}
                </View>
                <View style={styles.bottomView}>
                    <TextView
                        numberOfLines={4}
                        style={[styles.courseName, lightModeText, Platform.OS == 'android' && { lineHeight: LineHeight.h5_LH }]} text={res?.title} />
                    {inProgress ?
                        <Pressable style={commonStyle.mt10} onPress={onPress}>
                            <TextView
                                style={[styles.continueCourse, isDarkMode && { color: Colors.activeStateDark }]}
                                text={`${t('Continue')} ${t(res?.type || res?.component_type || 'course')}`}
                            />
                        </Pressable> :
                        <View style={[commonStyle.rowAlign, commonStyle.mt10]}>
                            <SubItem
                                headingStyle={styles.headingStyle}
                                heading={res?.duration}
                                icon={AssetsImages.clock} />
                            <SubItem
                                headingStyle={styles.headingStyle}
                                heading={(res?.total_lesson == "1") ? `1 ${t('Activity')}` : `${res?.lessons || res?.total_lesson || ''} ${t('Activities')}`}
                                icon={AssetsImages.lessonIcon} />
                        </View>}
                </View>
            </View>
        </Pressable>
    )
}

export default FavouriteItem;

const styles = StyleSheet.create({
    itemContainer: {
        backgroundColor: 'white',
        marginHorizontal: widgetPadding,
        padding: widgetPadding,
        borderRadius: 10,
        flexDirection: 'row',
        marginBottom: widgetPadding
    },
    topViewView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    bottomView: {
        width: '100%',
        flex: 1,
        justifyContent: 'flex-end'
    },
    courseType: {
        color: Colors.lightBlackText,
        fontFamily: fonts.regular,
        fontSize: fontSize.h8,
        textTransform: 'uppercase',

    },
    like: {
        marginTop: -3,
        tintColor: 'red',
    },
    detailView: {
        flex: 1
    },
    imageView: {
        backgroundColor: Colors.grayline,
        width: Dimen.width * 0.3,
        height: Dimen.width * 0.3,
        borderRadius: 10,
        marginRight: widgetPadding
    },
    courseName: {
        color: Colors.lightBlackText,
        fontFamily: fonts.semiBold,
        fontSize: fontSize.h5,
        textAlign: 'left',
    },
    headingStyle: {
        color: Colors.lightBlackText,
        fontFamily: fonts.thin,
    },
    continueCourse: {
        fontFamily: fonts.thin,
        color: Colors.lightBlackText,
        textDecorationLine: 'underline',
        textAlign: 'left',
        fontSize: fontSize.h6,
        marginTop: -5,
    },
})