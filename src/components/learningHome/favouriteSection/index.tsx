import { BreadCrumb, DynamicTabs, NoRecordsTextView, Skeleton } from '@components';
import { progressTabs, widgetPadding } from '@utils/constants';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';
import FavouriteItem from './favouriteItem';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import { changeLearningPathwayTab } from '@totara/reducers/learningPathwayReducer';
import { useDispatch } from 'react-redux';
import { navigate } from '@navigation/navigationService';
import { Dimen } from '@theme';
import { analyticsService } from '@analytics/AnalyticsService';
import AnalyticsConstants from '@analytics/AnalyticsConstants';

const FavouriteSection = ({ refreshing }) => {
    const { t } = useTranslation();
    const [tab, setTab] = useState('inprogress');
    const { personalCourses, loading, fetchNextPersonalPage, refreshPersonalCourses } = useGetLearningPrograms('personal');
    const [filteredData, setData] = useState(null)

    useEffect(() => {
        if (refreshing) {
            refreshPersonalCourses()
        }
    }, [refreshing])

    useEffect(() => {
        if (personalCourses) {
            filterData(tab)
        }
    }, [personalCourses])

    const getSelectedTab = (tabName) => {
        setTab(tabName);
        filterData(tabName)
    }

    const filterData = (tab) => {
        if (tab == 'inprogress') {
            const result = personalCourses?.list?.filter(item => item?.status == "In Progress")?.slice(0, 3)
            setData(result)

        }
        if (tab == 'notstarted') {
            const result = personalCourses?.list?.filter(item => item?.status == "Not Yet Started")?.slice(0, 3)
            setData(result)
        }
    }

    const dispatch = useDispatch()
    const onPress = () => {
        analyticsService.logEvent(AnalyticsConstants.CATEGORY_CLICKED, { tab: 'Favourites' })
        changeLearningPathwayTab(dispatch, 'Favourites')
        setTimeout(() => {
            navigate('MyLearningPathways')
        }, 0);
    }

    if (loading && !filteredData?.length) {
        return (
            <View>
                <BreadCrumb
                    showViewAll
                    length
                    onPress={onPress}
                    text={t('Favourite Courses')}
                    customContainerStyles={{ marginVertical: widgetPadding }}
                />
                <View style={{ alignItems: 'flex-start', marginStart: 12 }}>
                    <DynamicTabs
                        initiallySelected
                        containerStyle={styles.tabStyle}
                        getSelectedTab={getSelectedTab}
                        tabs={progressTabs.slice(1)}
                    />
                </View>

                <Skeleton skeletonStyle={styles.skeletonStyle} />
            </View>

        )
    }
    return (
        <View>
            <BreadCrumb
                showViewAll
                length
                onPress={onPress}
                text={t('Favourite Courses')}
                // showViewAll={trendingCourses?.length > 3}
                // onPress={navigateTrendingCoursesListing}
                customContainerStyles={{ marginVertical: widgetPadding }}
            />
            <View style={{ alignItems: 'flex-start', marginStart: 12 }}>
                <DynamicTabs
                    initiallySelected
                    containerStyle={styles.tabStyle}
                    getSelectedTab={getSelectedTab}
                    tabs={progressTabs.slice(1)}
                />
            </View>

            {(!loading && !filteredData?.length) &&
                <View style={styles.noRecordContainer}>
                    <NoRecordsTextView
                        textStyle={{ textAlign: 'center' }}
                    />
                </View>}


            {filteredData?.map((res) => {
                return (
                    <FavouriteItem
                        inProgress={tab == 'inprogress'}
                        res={{ ...res, type: res?.type ? res?.type : 'course' }} />
                )
            })}
        </View>
    )
}

export default FavouriteSection;

const styles = StyleSheet.create({
    tabStyle: {
        marginTop: -widgetPadding / 4,
        marginStart: 4
    },
    skeletonStyle: {
        width: Dimen.width / 1.09,
        height: 140,
        marginHorizontal: widgetPadding,
        borderRadius: 5
    },
    noRecordContainer: {
        height: 140,
        width: Dimen.width / 1.2,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
    }

})