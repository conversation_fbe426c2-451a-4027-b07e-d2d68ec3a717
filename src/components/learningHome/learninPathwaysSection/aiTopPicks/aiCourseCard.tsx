import { useNavigation } from '@react-navigation/native';
import React from 'react';
import {
  I18nManager,
  Pressable,
  StyleSheet,
  View,
} from 'react-native';
import { FavItem, SubItem, TextView } from '../../../index.tsx';
import fonts from '../../../../utils/fonts';
import fontSize, { isPad } from '../../../../utils/fontSize';
import { AssetsImages, Colors, Dimen, Icons } from '../../../../theme';
import { setBottomSheetVisible } from '../../../../totara/reducers/addToListBottomSheetReducer';
import { useDispatch } from 'react-redux';
import { setReferCourse } from '../../../../totara/reducers/referCourseReducer';
import {
  RenderContentType,
  defaultCreator,
  navigateToCourseDetail,
  widgetPadding,
} from '../../../../utils/constants';
import { LineHeight, commonStyle } from '@utils';
import { useTranslation } from 'react-i18next';
import FastImage from 'react-native-fast-image';
import {getLatestCourseDetails} from "@api/totaraApis.tsx";

type props = {
  index: number;
  apiToken: string;
  userId: string;
  item: Record<string, number | string>;
  tag?: boolean;
  inProgress?: boolean;
  isFavouritable?: boolean
};

const AICourseCard = ({ item, index, tag, inProgress,apiToken,userId, isFavouritable = true }: props) => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const navigate = async () => {
   const course  = await getLatestCourseDetails({
      courseId: item?.courseid,
      apiToken,
      userId: userId,
    });
    if (course) {
      navigateToCourseDetail({...course,courseid: course?.courseId,type: "course"}, navigation, inProgress);
    }

  };


  return (
    <Pressable onPress={navigate} key={index} style={styles.itemView}>
      <View style={styles.imageContainer}>
        <FastImage source={{ uri: item?.image }} style={[styles.imageView]}>
          {item?.image && <View style={styles.overlay} />}
          <View style={{ padding: widgetPadding, flex: 1 }}>
            <View style={styles.heartAndCategoryContainer}>
              <TextView style={styles.categoryTitle} text={item?.component_type || item.type} />
              {(item?.courseid || item.id) &&

                (item?.type == 'course' || item?.component_type == 'course' || item.component_type || 'Course') && isFavouritable
                ? (
                  <FavItem
                    style={styles.favItem}
                    color={'white'}
                    courseid={item?.courseid || item.id}
                  />
                ) : null}
            </View>
            <TextView style={styles.university}
              text={(item?.creator || item?.curator_name || item?.course_curatorname) || defaultCreator} />
            <TextView numberOfLines={2} style={styles.title} text={item?.title} />
              <View style={styles.row}>
                {item.duration && (
                  <SubItem
                    iconStyle={styles.iconStyle}
                    headingStyle={styles.headingStyle}
                    heading={`${item.duration}`}
                    icon={AssetsImages.clock}
                  />
                )}

                <RenderContentType
                  item={item}
                  type={(item.type || item.component_type) || 'course'}
                  styles={styles}
                  t={t}
                />
              </View>

          </View>
        </FastImage>
      </View>
    </Pressable>
  );
};

export default AICourseCard;

const styles = StyleSheet.create({
  itemView: {
    width: Dimen.width * 0.53,
    marginRight: widgetPadding,
    overflow: 'hidden',
    flex: 1,
  },
  imageContainer: {
    borderRadius: 10,
    height: (Dimen.width * 0.53) * 0.82, //175
    overflow: 'hidden',
  },
  imageView: {
    width: '100%',
    height: '100%',
    backgroundColor: Colors.grayline,
    borderRadius: 10,
    overflow: 'hidden',
  },
  image: {
    borderRadius: 10,
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 10,
  },
  clockTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 10,
  },
  detailView: {
    flex: 1,
    justifyContent: 'space-between',
  },
  courseTag: {
    borderColor: Colors.borderGray,
    borderWidth: 1,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 10,
    paddingTop: 10,
  },
  tagText: {
    fontFamily: fonts.semiBold,
    fontSize: isPad ? 12 : fontSize.mini,
    textTransform: 'uppercase',
    paddingHorizontal: 16,
  },
  heartAndThreeDotStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
    width: 40,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  like: {
    resizeMode: 'contain',
    height: 20,
    width: 20,
  },
  viewMore: {
    resizeMode: 'contain',
  },
  courseHeading: {
    paddingTop: 15,
    paddingBottom: 5,
    fontSize: fontSize.small,
    fontFamily: fonts.medium,
    textAlign: 'left',
  },
  star: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginTop: -1,
  },
  clock: {
    width: 18,
    height: 18,
    marginRight: 5,
  },
  courseSourceIcon: {
    width: 15,
    height: 15,
    borderRadius: 5,
  },
  rowJustify: {
    flexDirection: 'row',
    paddingBottom: 8,
  },
  duration: {
    color: Colors.white,
    fontSize: fontSize.xmini,
    fontFamily: fonts.regular,
    paddingHorizontal: 5,
  },
  favItem: {
    marginTop: -4,
    marginRight: -5,
    tintColor: 'white',
  },
  heartAndCategoryContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 'auto',
  },
  categoryTitle: {
    textAlign: 'left',
    textTransform: 'uppercase',
    color: Colors.courseWhiteColor,
    fontFamily: fonts.thin,
    fontSize: fontSize.h8,
  },
  university: {
    fontSize: fontSize.h7,
    fontFamily: fonts.regular,
    textAlign: 'left',
    color: Colors.courseWhiteColor,
  },
  title: {
    fontSize: fontSize.h6,
    fontFamily: fonts.semiBold,
    textAlign: 'left',
    color: 'rgba(255, 255, 255, 1)',
    marginVertical: I18nManager.isRTL ? 8 : 8,
    lineHeight: LineHeight.h6_LH,
    marginTop: I18nManager.isRTL ? 8 : 6,
  },
  headingStyle: {
    color: 'white',
    fontFamily: fonts.regular,
  },
  iconStyle: {
    tintColor: 'white',
  },
  continueCourse: {
    fontFamily: fonts.thin,
    color: 'white',
    textDecorationLine: 'underline',
    textAlign: 'left',
    fontSize: fontSize.h6,
    marginTop: -5,
  },
});
