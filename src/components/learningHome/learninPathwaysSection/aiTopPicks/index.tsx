import React, {useEffect, useState} from 'react';
import { useTranslation } from 'react-i18next';
import {widgetPadding} from "@utils/constants.tsx";
import {CardLoadingSkeleton} from "@screens/Discover";
import {getAITopPicsCourses} from "@api/AI";
import {useSelector} from "react-redux";
import {FlatList, View} from "react-native";
import {useSession} from "@totara/core";
import {useQuery} from "@apollo/client";
import {userOwnProfile} from "@totara/features/profile/api";
import AICourseCard from "@components/learningHome/learninPathwaysSection/aiTopPicks/aiCourseCard.tsx";
import {RootState} from "@totara/reducers";
import {getRandom10} from "@utils/AppUtils.tsx";

const AITopPicks = () => {
  const {t} = useTranslation();
  const [randomCourse, setRandomCourse] = React.useState(null);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
const [courses,setCourses] = useState([])
const [isLoading,setIsLoading] = useState(false)
  const { apiToken } = useSession();
  const supportCenterData = useSelector((state: RootState) => state.supportCenterData);

  useEffect(() => {
    setIsLoading(true)
    const AITopPicsCourses = async () => {
      const response = await getAITopPicsCourses(talentProfile,supportCenterData);
      if (response.data?.search_results['gov-academy-courses']?.length > 0) {
        const tmpCourses = getRandom10(response.data.search_results['gov-academy-courses'])

        setCourses(tmpCourses);
      }
      setIsLoading(false)

    }
    AITopPicsCourses()

  }, [])
  useEffect(() => {
    console.log(apiToken,talentProfile?.id)
  }, [courses]);
  return (
    (!isLoading && courses.length > 0) ?
      <FlatList
        showsHorizontalScrollIndicator={false}
        horizontal
        style={{ alignSelf: 'flex-start', marginBottom: widgetPadding }}
        data={courses?.filter((_, index) => index < 10)}
        renderItem={({ item, index }) => {
          const customStyles = {
            marginStart: index === 0 ? widgetPadding : 0,
          };
          return (
            <View key={index} style={customStyles}>
              <AICourseCard
                tag={false}
                apiToken={apiToken}
                userId={talentProfile?.id}
                item={{
                  ...item,
                  type: 'course',
                  image: item.ImageURL,
                  id: item.CourseURL.split('id=')[1],
                  courseid: item.CourseURL.split('id=')[1],
                  duration: item.Duration,
                  title: item.Name
                }}
                index={index}
              />
            </View>
          );
        }}
        keyExtractor={(item) => item?.title}
      />
      :  <CardLoadingSkeleton
      itemCount={4}
      cardHeight={205}
      cardWidth={230}
    />

  );
}
export default AITopPicks;
