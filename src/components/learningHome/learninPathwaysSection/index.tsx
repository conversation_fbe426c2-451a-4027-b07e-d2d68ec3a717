import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { I18nManager, Platform, StyleSheet } from 'react-native';
import { LabelConfig } from '../../../theme/labelConfig';
import { learningPathwaysHomeTabs } from '../../../utils/constants';
import { DynamicTabs } from '../../index';
import CardContainer from '../CardContainer';
import PersonalLearning from './personalLearning';
import RecommendedLearning from './recommendedLearning';
import RequiredLearning from './requiredLearning';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import { View } from 'react-native-animatable';
import { navigate } from '@navigation/navigationService';
import { changeLearningPathwayTab } from '@totara/reducers/learningPathwayReducer';
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from '@theme/ThemeProvider';
import LuckyLearning from "@components/learningHome/learninPathwaysSection/luckyLearning";

const { myLearningPathways, RequiredTab, RecommendedTab, PersonalTab,LuckyTab } = LabelConfig.learningPathways;

const LearningPathwaySection = () => {
  const [tab, setTab] = useState('');
  const dispatch = useDispatch();
  const { theme, isDarkMode } = useTheme()

  const onView = () => {
    navigate('MyLearningPathways');
  }

  useEffect(() => {
    if (tab == 'required') changeLearningPathwayTab(dispatch, RequiredTab)
    if (tab == 'recommended') changeLearningPathwayTab(dispatch, RecommendedTab)
    if (tab == 'personal') changeLearningPathwayTab(dispatch, PersonalTab)
  }, [tab])

  const { requiredPathways, recommendedPathways, personalCourses, loading } = useGetLearningPrograms(tab);
  const getSelectedTab = (tabName) => {
    setTab(tabName);
    if (tabName == 'required') setViewAllCount(requiredPathways?.count)
    if (tabName == 'recommended') setViewAllCount(recommendedPathways?.count)
    if (tabName == 'personal') setViewAllCount(personalCourses?.count)

  }
  const requiredPathwaysList = requiredPathways?.list;
  const recommendedPathwaysList = recommendedPathways?.list;
  const FavList = personalCourses?.list;
  const [viewAllCount, setViewAllCount] = useState(0)

  useEffect(() => {
    if (requiredPathways?.count > 0 && tab == 'required') {
      setViewAllCount(requiredPathways?.count)
    }
    if (recommendedPathways?.count > 0 && tab == 'recommended') {
      setViewAllCount(recommendedPathways?.count)
    }
    if (personalCourses?.count >= 0 && tab == 'personal') {
      setViewAllCount(personalCourses?.count)
    }

  }, [requiredPathways?.count, personalCourses?.count, recommendedPathways?.count, tab])

  return (
    <CardContainer
      count={viewAllCount || 0}
      title={myLearningPathways}
      containerStyle={[styles.containerStyle, isDarkMode && {
        backgroundColor: theme.bgDark,

      }]}
      onView={onView}
      loading={tab == 'personal' ? loading : false}
    >
      <>
        <DynamicTabs
          initiallySelected
          containerStyle={{ marginStart: (Platform.OS == 'android' && I18nManager.isRTL) ? -10 : (I18nManager.isRTL && Platform.OS == 'ios') ? 8 : (!I18nManager.isRTL && Platform.OS == 'ios') ? -12 : -10, marginTop: 15, marginBottom: 8 }}
          getSelectedTab={getSelectedTab}
          tabs={learningPathwaysHomeTabs}
        />
        {tab == 'required' && <View style={styles.cardContainer}>
          <RequiredLearning loading={loading} requiredPathways={requiredPathways} />
        </View>}
        {tab == 'recommended' && <View style={styles.cardContainer}>
          <RecommendedLearning loading={loading} recommendedPathways={recommendedPathways} />
        </View>}
        {tab == 'personal' && <View style={styles.cardContainer}>
          <PersonalLearning loading={loading} personalCourses={personalCourses} />
        </View>}

      </>

    </CardContainer>
  );
};
export default LearningPathwaySection;

const styles = StyleSheet.create({
  containerStyle: {
    paddingBottom: 20,
    alignItems: 'flex-start',
  },
  cardContainer: {
    backgroundColor: 'transparent',
    width: '100%',
    padding: 0,
    overflow: 'hidden'
  },
  ItemStyle: {
    paddingHorizontal: 15,
    backgroundColor: "transparent",
  }
});
