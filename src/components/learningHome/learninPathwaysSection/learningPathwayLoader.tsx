import { AssetsImages, Colors, Dimen, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { TotaraTheme } from '@totara/theme/Theme';
import { lineHeight } from '@utils/constants';
import React from 'react';
import { StyleSheet } from 'react-native';
import { View } from 'react-native-animatable';
import FastImage from 'react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const LearningPathWayLoading = ({ style, count = 1, isDetail }) => {
    const { theme, isDarkMode } = useTheme();

    const bgColor = isDarkMode ? theme.bgLightDark : TotaraTheme.colorNeutral3;
    const highlightColor = isDarkMode ? theme.bgDark : 'silver'

    return (
        <>
            {Array.from({ length: count }).map((_, index) => (
                <View
                    style={[styles.container, style]}>
                    <View style={[styles.rowStart, {
                        justifyContent: isDetail ? 'flex-start' : 'space-between'
                    }]}>
                        {isDetail && <SkeletonPlaceholder
                            highlightColor={highlightColor}
                            backgroundColor={bgColor} >
                            <View style={[styles.imageView, {
                                marginRight: 10
                            }]}>
                                <FastImage style={styles.imageStyle} source={AssetsImages.courseImage} />
                            </View>
                        </SkeletonPlaceholder>}

                        <View>
                            <SkeletonPlaceholder
                                highlightColor={highlightColor}
                                backgroundColor={bgColor}
                            >
                                <View style={{ width: 200, height: 15, borderRadius: 5 }} />
                            </SkeletonPlaceholder>

                            <View style={{ marginTop: 10 }} />
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                                <SkeletonPlaceholder
                                    highlightColor={highlightColor}
                                    backgroundColor={bgColor}
                                >
                                    <View style={{ width: 120, height: 15, borderRadius: 5 }} />
                                </SkeletonPlaceholder>
                            </View>

                            <View style={{ marginTop: 10 }} />
                            <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                                <SkeletonPlaceholder
                                    highlightColor={highlightColor}
                                    backgroundColor={bgColor}
                                >
                                    <View style={{ width: 80, height: 15, borderRadius: 5 }} />
                                </SkeletonPlaceholder>
                            </View>

                        </View>

                        {!isDetail && <SkeletonPlaceholder
                            highlightColor={highlightColor}
                            backgroundColor={bgColor} >
                            <View style={styles.imageView}>
                                <FastImage style={styles.imageStyle} source={AssetsImages.courseImage} />
                            </View>
                        </SkeletonPlaceholder>}

                    </View>

                </View >
            ))}
        </>
    );
};

export default LearningPathWayLoading;

const styles = StyleSheet.create({
    container: {
        width: Dimen.width * 0.8,
        padding: 15,
        borderRadius: 20,
        marginRight: 10,
        paddingBottom: 20,
        paddingStart: 10,
        marginStart: 10,
        height: 160,
    },
    detailView: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    rowStart: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flexShrink: 0,
    },
    imageView: {
        height: 70,
    },
    imageStyle: {
        width: 70,
        height: 70,
        resizeMode: 'contain',
        borderRadius: 10,
        backgroundColor: '#f5f5f5'
    },
    descrptionView: {
        flex: 1,
        // marginRight: 10
    },
    courseDescription: {
        marginTop: 15,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '85%',
    },
    icon: {
        width: 15,
        height: 15,
        resizeMode: 'contain',
        marginRight: 5,
    },
    coursesIcon: {
        width: 15,
        height: 15,
        resizeMode: 'contain',
        marginRight: 5,
    },
    star: {
        width: 12,
        height: 12,
        resizeMode: 'contain',
        marginRight: 5,
        marginTop: -1,
    },
    desc: {
        fontFamily: fonts.regular,
        fontSize: fontSize.xmini,
        color: Colors.greyText,
        marginRight: 10,
    },
    itemTitle: {
        fontFamily: fonts.medium,
        fontSize: fontSize.medium,
        color: Colors.black,
        flex: 1,
        marginEnd: 20,
        lineHeight: lineHeight(fontSize.medium)
    },
    courseProviderView: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    overdueView: {
        backgroundColor: Colors.overDue,
        borderRadius: 10,
    },
    inProgress: {
        backgroundColor: Colors.inProgressCourse,
        borderRadius: 20,
        marginTop: 15,
        width: 100,
        alignItems: 'center',
        justifyContent: 'center',
    },
    providerView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginStart: 40,
    },
    companyView: {},
    companyLogo: {
        width: 15,
        height: 15,
        borderRadius: 5
    },
    overdueText: {
        color: Colors.white,
        textTransform: 'uppercase',
        fontSize: fontSize.mini,
        fontFamily: fonts.bold,
        padding: 5,
        paddingHorizontal: 10,
        letterSpacing: 1
    },
    companyName: {
        color: Colors.black,
        fontSize: fontSize.xmini,
        fontFamily: fonts.regular,
        paddingStart: 5,
    },
    progressBar: {
        borderRadius: 5,
        flex: 1,
    },
    progressUnfilleddPercentage: {
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        height: 8,
        justifyContent: 'center',
        borderRadius: 5,
        marginTop: 5,
    },
    progressPercentage: {
        backgroundColor: 'rgba(148, 228, 17, 1)',
        height: 5,
        justifyContent: 'center',
        flex: 1,
        borderRadius: 5,
    },
    progressText: {
        fontSize: fontSize.xmini,
        fontFamily: fonts.medium,
        color: Colors.black,
    },
    rowJustified: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        width: '100%',
    },
    dots: {
        tintColor: Colors.black,
        width: 15,
        height: 15,
        resizeMode: 'contain',
        marginTop: -1
    },
    courseImage: {
        height: 70,
        marginRight: 10,
    },
    dotsView: {
        // flex: 0.1,
        alignItems: 'flex-end',
    },
    currentView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 15,
        backgroundColor: 'rgba(237, 241, 246, 1)',
        borderRadius: 5,
    },
    hrPartner: {
        fontFamily: fonts.regular,
        fontSize: fontSize.mini,
        paddingVertical: 8,
        color: Colors.black,
    },
    current: {
        fontFamily: fonts.medium,
        fontSize: fontSize.mini,
        paddingStart: 10,
        color: Colors.black,
    },
    nextSection: {
        color: 'rgba(125, 161, 196, 1)',
        marginHorizontal: 10,
        marginTop: 15,
        fontSize: fontSize.mini,
        fontFamily: fonts.regular,
    },
    avatar: {
        width: 22,
        height: 22,
        resizeMode: 'contain',
        borderRadius: 11,
        backgroundColor: 'black',
        borderWidth: 2,
        borderColor: 'white',
    },
    avatarView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 15,
        marginStart: 13,
    },
    peopleJoined: {
        marginStart: 10,
        fontSize: fontSize.xmini,
        color: Colors.greyText,
        fontFamily: fonts.medium,
    },
    favItem: {
        marginTop: 2,
        marginRight: 10
    },
});

