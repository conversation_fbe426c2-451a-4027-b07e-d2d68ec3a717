import React, {useEffect} from 'react';
import { useTranslation } from 'react-i18next';
import useRecommended from "@hooks/useRecommended";
import InProgressView from "@components/homeComponents/myLearning/components/InProgressView";
import {Dimen} from "@theme";
import {widgetPadding} from "@utils/constants.tsx";
import RecommendedItem from "@components/homeComponents/horizontalCarousel/recommendedItem.tsx";
import {CardLoadingSkeleton} from "@screens/Discover";

const luckyLearning = () => {
  const {t} = useTranslation();
  const [randomCourse, setRandomCourse] = React.useState(null);
  const {recommendedCourses, isLoading} = useRecommended()

  useEffect(() => {
    if (recommendedCourses?.length) {
      const randomIndex = Math.floor(Math.random() * recommendedCourses?.length);
      const course = recommendedCourses[randomIndex];
      setRandomCourse(course)

    }
  }, [recommendedCourses])

  return (
    (!isLoading && randomCourse) ?<RecommendedItem
      inProgress={false}
      bannerHeight={(Dimen.width - (widgetPadding * 2)) * 0.75}
      item={{
        ...randomCourse,
        type: randomCourse?.type || randomCourse?.component_type || 'course',
        courseid: randomCourse.id ? randomCourse.id : randomCourse.courseid
      }}
      index={randomCourse.id} />:  <CardLoadingSkeleton
      itemCount={4}
      cardHeight={205}
      cardWidth={230}
    />

  );
}
export default luckyLearning;
