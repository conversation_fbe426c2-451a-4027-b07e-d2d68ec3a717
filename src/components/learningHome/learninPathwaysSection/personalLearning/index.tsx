import { useNavigation } from '@hooks';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import Colors from '@theme/Colors';
import { useTheme } from '@theme/ThemeProvider';
import {
  convertNumbersToArabicNumerals,
  lineHeight,
  lineHeightForAndroidLTRMed,
} from '@utils/constants';
import React from 'react';
import { FlatList, Image, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { LearningPathWayItem, TextView } from '../../..';
import { AssetsImages, Dimen, LabelConfig } from '../../../../theme';
import { fontSize, fonts, routes } from '../../../../utils';
import LearningPathWayLoading from '../learningPathwayLoader';
import { learningPathwayItemHeight } from '@totara/utils/MyConstants';

const { createPersonalized, startJouney, noCoursesAdded } = LabelConfig.learningPathways;
import { useTranslation } from 'react-i18next';

const PersonalLearning = () => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const { t } = useTranslation();
  const { personalCourses, loading } = useGetLearningPrograms('personal');

  const CategoryListing = () => navigation.navigate('Learning');

  if (loading)
    return <LearningPathWayLoading style={{ backgroundColor: isDarkMode ? theme.bgLightDark : Colors.lightBlue, width: '94.5%', height: learningPathwayItemHeight }} />;

  if (personalCourses?.list?.length) {
    return (
      <View style={styles.scrollStyle}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          horizontal
          data={personalCourses?.list}
          renderItem={({ item }) => {
            const lessons = item?.total_lesson > 1 ? 'Activities' : 'Activity';
            const courses = item?.total_course > 1 ? 'Courses' : 'Course';
            const dgeTitleLogo = item.curator_name || item?.curator_logo ? false : true;

            return (
              <LearningPathWayItem
                itemStyle={{ height: learningPathwayItemHeight }}
                backgroundColor={Colors.requiredLearningColor}
                name={item?.title}
                courses={item?.total_course}
                overDue={item?.due_date ? item?.due_date : item?.course_due_date}
                provider={
                  dgeTitleLogo ? 'Gov Academy' : item.curator_name ? item.curator_name : undefined
                }
                providerLogo={
                  dgeTitleLogo
                    ? AssetsImages.dgeLogo1
                    : item?.curator_logo
                      ? { uri: item?.curator_logo }
                      : undefined
                }
                progress={item?.progress || '0'}
                image={item?.image}
                showRating={false}
                rating={item?.rating}
                isPersonal={false}
                duration={item?.duration || '1'}
                totalContent={
                  item?.type || item?.course_type
                    ? convertNumbersToArabicNumerals(item?.total_lesson) + ' ' + t(lessons)
                    : convertNumbersToArabicNumerals(item?.total_course) + t(courses)
                }
                item={{ ...item, course_type: item.course_type, type: 'course', courseid: item.id }}
              />
            );
          }}
        />
      </View>
    );
  } else if (!loading && !personalCourses?.list.length) {
    return (
      <View style={{ margin: 0, width: '95%', overflow: 'hidden', alignSelf: 'center' }}>
        <FastImage style={styles.background} source={AssetsImages.personalGradient}>
          <View style={styles.textSection}>
            <TextView
              style={[styles.heading, lineHeightForAndroidLTRMed]}
              text={createPersonalized}
            />
            <Pressable onPress={CategoryListing} style={styles.journeyBtn}>
              <Image source={AssetsImages.addPersonalized} style={styles.addPersonalized} />
              <TextView style={styles.startJouney} text={startJouney} />
            </Pressable>
            <TextView style={styles.noCoursesAdded} text={noCoursesAdded} />
          </View>
          <View style={styles.mountainView}>
            <Image source={AssetsImages.mountain} style={styles.mountain} />
          </View>
        </FastImage>
      </View>
    );
  }
};

export default PersonalLearning;

const styles = StyleSheet.create({
  scrollStyle: {
    marginStart: 10,
  },
  personalizedJourney: {
    height: 140,
    width: '95%',
    borderColor: Colors.borderGray,
    borderWidth: 1,
    alignSelf: 'center',
    borderRadius: 15,
  },
  background: {
    width: '100%',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
    alignSelf: 'center',
    borderRadius: 15,
    flexDirection: 'row',
    height: 160,
  },
  heading: {
    color: Colors.black,
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    marginTop: 5,
    textAlign: 'left',
  },
  discoverLearning: {
    color: Colors.black,
    fontSize: fontSize.xxmini,
    fontFamily: fonts.thin,
    marginHorizontal: 20,
    marginTop: 15,
    width: '60%',
    lineHeight: lineHeight(fontSize.xxmini),
  },
  mountain: {
    alignSelf: 'center',
    resizeMode: 'contain',
    width: 160,
    height: 160,
    position: 'absolute',
    bottom: -32,
    left: -20,
  },
  mountainView: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  textSection: {
    flex: 1.3,
    padding: 10,
  },
  journeyBtn: {
    backgroundColor: Colors.black,
    width: 140,
    marginTop: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingHorizontal: 15,
  },
  startJouney: {
    color: 'white',
    fontSize: fontSize.xmini,
    paddingVertical: 12,
  },
  addPersonalized: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 8,
  },
  noCoursesAdded: {
    fontFamily: fonts.thin,
    fontSize: fontSize.xmini,
    color: 'black',
    marginTop: 20,
    textAlign: 'left',
  },
});
