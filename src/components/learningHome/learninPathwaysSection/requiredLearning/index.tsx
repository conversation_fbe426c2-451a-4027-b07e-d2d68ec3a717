import React from 'react';
import { LearningPathWayItem, TextView } from '../../..';
import { I18nManager, Image, ScrollView, StyleSheet, View } from 'react-native';
import Colors from '@theme/Colors';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import LearningPathWayLoading from '../learningPathwayLoader';
import { useTranslation } from 'react-i18next';
import { learningPathwayItemHeight } from '@totara/utils/MyConstants';
import { AssetsImages, fontSize, fonts } from '@theme';
import { FlatList } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { convertNumbersToArabicNumerals } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

const RequiredLearning = ({ requiredPathways, loading }) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const CategoryListing = () => navigation.navigate(routes.Categories);
  const { theme, isDarkMode } = useTheme()

  if (loading && !requiredPathways?.list?.length)
    return (
      <LearningPathWayLoading
        style={{ backgroundColor: isDarkMode ? theme.bgLightDark : Colors.requiredLearningColor, width: '94.5%', height: learningPathwayItemHeight }}
      />
    );
  if (requiredPathways?.list?.length) {
    return (
      <View style={styles.scrollStyle}>
        <FlatList
          showsHorizontalScrollIndicator={false}
          horizontal
          keyExtractor={(item, index) => `${item.id}-${item.title}-${index}`}
          data={requiredPathways?.list}
          renderItem={({ item }) => {
            const lessons = item?.total_lesson > 1 ? 'Activities' : 'Activity';
            const dgeTitleLogo = item.curator_name || item?.curator_logo ? false : true;

            return (
              <LearningPathWayItem
                itemStyle={{ height: learningPathwayItemHeight }}
                backgroundColor={Colors.requiredLearningColor}
                name={item.title}
                courses={item.total_course}
                overDue={item.due_date}
                provider={
                  dgeTitleLogo ? 'Gov Academy' : item.curator_name ? item.curator_name : undefined
                }
                providerLogo={
                  dgeTitleLogo
                    ? AssetsImages.dgeLogo1
                    : item?.curator_logo
                      ? { uri: item?.curator_logo }
                      : undefined
                }
                progress={item.progress}
                image={item.image}
                totalContent={
                  item.type == 'course'
                    ? convertNumbersToArabicNumerals(item.total_lesson) + ' ' + t(lessons)
                    : convertNumbersToArabicNumerals(item.total_course) + ' ' + t('courses')
                }
                duration={item.duration}
                showInProgress={(item.status === 'inprogress' || item.status === 'In Progress') && item.progress > 0}
                item={{
                  ...item,
                  courseid: item.id,
                }}
              />
            );
          }}
        />
      </View>
    );
  } else if (!loading && !requiredPathways?.list.length) {
    return (
      <View style={{ margin: 0, width: '95%', alignSelf: 'center' }}>
        <FastImage style={styles.background} source={AssetsImages.personalGradient}>
          <View style={styles.textSection}>
            <TextView
              style={styles.heading}
              text={'You’re all up to date on your required learning!'}
            />
            <TextView
              style={styles.noCoursesAdded}
              text={'Remember to check back for any new learning'}
            />
          </View>
          <View style={styles.mountainView}>
            <Image source={AssetsImages.mountain} style={styles.mountain} />
          </View>
        </FastImage>
      </View>
    );
  }
};
export default RequiredLearning;

const styles = StyleSheet.create({
  scrollStyle: {
    marginStart: 10,
  },
  noCourses: {
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    textAlign: 'left',
  },
  background: {
    width: '100%',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
    alignSelf: 'center',
    borderRadius: 15,
    flexDirection: 'row',
    height: 160,
  },
  heading: {
    color: Colors.black,
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    marginTop: 5,
    textAlign: 'left',
    lineHeight: !I18nManager.isRTL ? 20 : 26,
  },
  discoverLearning: {
    color: Colors.black,
    fontSize: fontSize.xxmini,
    fontFamily: fonts.thin,
    marginHorizontal: 20,
    marginTop: 15,
    width: '60%',
  },
  mountain: {
    alignSelf: 'center',
    resizeMode: 'contain',
    width: 160,
    height: 160,
    position: 'absolute',
    bottom: -32,
    left: -20,
  },
  mountainView: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  textSection: {
    flex: 1.3,
    padding: 10,
  },
  journeyBtn: {
    backgroundColor: Colors.black,
    width: 140,
    marginTop: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingHorizontal: 15,
  },
  startJouney: {
    color: 'white',
    fontSize: fontSize.xmini,
    paddingVertical: 12,
  },
  addPersonalized: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 8,
  },
  noCoursesAdded: {
    fontFamily: fonts.thin,
    fontSize: fontSize.xmini,
    color: 'black',
    marginTop: 30,
    textAlign: 'left',
    lineHeight: !I18nManager.isRTL ? 18 : 24,
  },
});
