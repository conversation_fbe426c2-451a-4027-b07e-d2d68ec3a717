import { BreadCrumb, SubItem, TextView } from '@components';
import { AssetsImages, Colors, Dimen, Icons, fontSize, fonts } from '@theme';
import { IconsType } from '@theme/Icons';
import { useTheme } from '@theme/ThemeProvider';
import { LineHeight, commonStyle } from '@utils';
import useCommonThemeStyle from '@utils/commonThemeStyles';
import { widgetPadding } from '@utils/constants';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { I18nManager, Pressable } from 'react-native';
import { Image, ScrollView } from 'react-native';
import { Platform, StyleSheet, View } from 'react-native';


type props = {
    title?:string;
    isDiscover?: boolean;
}

const MyScholarShips = ({ title, isDiscover }:props) => {
    const { t } = useTranslation();
    const { theme, isDarkMode } = useTheme();
    const { themeStyle } = useCommonThemeStyle();
    const lightModeText = isDarkMode && themeStyle.themedTextColor

    return (
        <View style={{ marginVertical: widgetPadding }}>
            <BreadCrumb
                showViewAll
                length
                text={t(title || 'My Scholarships')}
                subTitle
            />
            <TextView style={[styles.subDetail, lightModeText]} text='Use the opportunity offered to you, to pursue first class education offered by internationally recognised institutions. Read more and apply now.' />
            <ScrollView horizontal style={styles.scrollStyle}>
                {[1, 2].map((res) => {
                    return (
                        <View style={[styles.itemContainer, isDarkMode && { backgroundColor: theme.bgDark }]}>
                            <Image source={AssetsImages.scholarship} style={styles.scholarship} />
                            <TextView style={[styles.source, lightModeText]} text='New York University' />
                            <TextView style={[styles.name, lightModeText]} text='Master of Applied Data Science' />
                            <View style={[commonStyle.mt10]}>
                                <SubItem

                                    heading='3 months'
                                    icon={AssetsImages.clock} />
                                <SubItem
                                    containerStyle={commonStyle.mt5}
                                    heading='International'
                                    icon={AssetsImages.world} />
                            </View>
                            <View style={[styles.seperator, isDarkMode && { backgroundColor: theme.borderBackground }]} />

                            {isDiscover ? <View style={styles.deadLineContainer}>
                                <View style={[styles.deadLineIconContainer, {backgroundColor: isDarkMode ? "#787878" : "#D2D2D2"}]}>
                                     <Icons
                                                  color={isDarkMode ? 'white' : Colors.black}
                                                  size={12}
                                                  type={IconsType.AntDesign}
                                                  name={'warning'}
                                                />
                                </View>
                                <TextView text='Deadline: 14 Oct 2024' style={styles.deadLineText} />

                            </View> : <Pressable style={styles.reviewBtn}>
                                <TextView text='UNDER REVIEW' style={styles.underReview} />
                            </Pressable>}
                            
                        </View>
                    )
                })}
            </ScrollView>
        </View>

    )
}

export default MyScholarShips;

const styles = StyleSheet.create({
    subDetail: {
        marginHorizontal: widgetPadding,
        fontFamily: fonts.thin,
        color: Colors.lightBlackText,
        fontSize: fontSize.h6,
        textAlign: 'left',
        lineHeight: LineHeight.h6_LH
    },
    itemContainer: {
        backgroundColor: 'white',
        marginRight: 10,
        padding: widgetPadding,
        borderRadius: 10,
        marginBottom: widgetPadding,
        maxWidth: Dimen.width * 0.7
    },
    scrollStyle: {
        marginTop: widgetPadding,
        paddingStart: widgetPadding
    },
    source: {
        fontFamily: fonts.regular,
        fontSize: fontSize.h8,
        color: Colors.lightBlackText,
        textAlign: 'left'
    },
    name: {
        fontFamily: fonts.semiBold,
        fontSize: fontSize.h5,
        color: Colors.lightBlackText,
        maxWidth: '80%',
        lineHeight: Platform.OS == 'android' ? 22 : 20,
        marginTop: widgetPadding / 2,
        textAlign: 'left'
    },
    headingStyle: {
        fontSize: fontSize.custom(Platform.OS == 'android' ? 11 : 10)
    },
    scholarship: {
        width: 40,
        height: 40,
        marginBottom: widgetPadding,
        resizeMode: 'contain'
    },
    seperator: {
        height: 1,
        width: '97%',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
        alignSelf: 'center',
        marginVertical: widgetPadding
    },
    reviewBtn: {
        backgroundColor: Colors.inProgressCourse,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 15,
        maxWidth: I18nManager.isRTL ? 105 : 120
    },
    underReview: {
        fontFamily: fonts.semiBold,
        color: Colors.white,
        letterSpacing: 1,
        fontSize: fontSize.h8,
        paddingVertical: Platform.OS == 'android' ? 3 : 5
    },
    deadLineContainer:{
        flexDirection: 'row',
        alignItems: 'center',
    },
    deadLineIconContainer:{
        width:22,
        height:22,
        borderRadius: 18,
        marginEnd:8,
        justifyContent:'center',
        alignItems:'center',
    },
    deadLineText:{
        fontFamily: fonts.regular,
        fontSize: fontSize.h6,
        color: "#000",
    }

})