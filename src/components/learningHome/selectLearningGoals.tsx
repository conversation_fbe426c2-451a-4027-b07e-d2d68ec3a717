import fontSize, { isPad } from '@utils/fontSize';
import React, { useEffect, useState } from 'react';
import { Platform, Pressable } from 'react-native';
import { RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { AssetsImages, Colors, Dimen, Icons, fonts } from '../../theme';
import { TextView, Dropdown } from '../index';
import LinearGradient from 'react-native-linear-gradient';
import { Image } from 'react-native-animatable';
import CardContainer from './CardContainer';
import { widgetPadding } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

type props = {
  navigation: any;
};

const SelectLearningGoals = ({ navigation }: props) => {
  const { isDarkMode, theme } = useTheme();

  const navigateToUpdateGoals = () => navigation.navigate('UpdateLearningGoals');
  return (
    <CardContainer header={false} containerStyle={[styles.container, isDarkMode && {
      backgroundColor: theme.bgDark
    }]}>
      <>
        <TextView style={[styles.text, isDarkMode && { color: 'white' }]} text="Set your learning goal and track your streaks" />
        <Image style={styles.bgDot} source={isDarkMode ? AssetsImages.calendarGradientDark : AssetsImages.lightMode_new} />

        <Pressable onPress={navigateToUpdateGoals} style={[styles.button, isDarkMode && { backgroundColor: Colors.activeStateDark }]}>
          <Image style={styles.buttonIcon} source={AssetsImages.yellowStreak} />
          <TextView style={[styles.buttonText, isDarkMode && { color: 'black' }]} text="Set your goal" />
        </Pressable>
      </>
    </CardContainer>
  );
};

export default SelectLearningGoals;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    position: 'relative',
    paddingHorizontal: 20,
    overflow: 'hidden',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: isPad ? 30 : 20,
    paddingBottom: isPad ? 30 : 20,
    marginHorizontal: widgetPadding
  },
  bgDot: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: isPad ? 350 : 500,
    height: 200,
    zIndex: -1,
  },
  text: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xxlarge : fontSize.h5,
    color: Colors.black,
    width: isPad ? '60%' : '50%',
    textAlign: 'left'
  },
  button: {
    paddingHorizontal: isPad ? 25 : 12,
    backgroundColor: '#000',
    paddingVertical: isPad ? 20 : 9,
    borderRadius: isPad ? 20 : 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonText: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.small,
    color: Colors.white,
    marginStart: 10,
  },
  buttonIcon: {
    width: isPad ? 32 : 22,
    height: isPad ? 32 : 22,
  },
});
