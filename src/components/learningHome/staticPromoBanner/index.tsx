import React, { useEffect, useState } from 'react';
import { Pressable, StyleSheet, Text, View, Image, ImageBackground, Platform } from 'react-native';
import FastImage from 'react-native-fast-image';
import Carousel from 'react-native-reanimated-carousel';
import { TextView } from '../../../components';
import { AssetsImages, Colors, Dimen } from '../../../theme';
import { LineHeight, fontSize, fonts } from '../../../utils';
import { useNavigation } from '@react-navigation/native';
import Animated, {
  Extrapolate,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { I18nManager } from 'react-native';
import { lineHeight, lineHeightForAndroidLTR, lineHeightForAndroidLTRLarge, lineHeightForAndroidLTRMed, lineHeightForAndroidLTRSmall, lineHeightForAndroidLTRXLarge, widgetPadding } from '@utils/constants';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@theme/ThemeProvider';
import { getStaticPromoBanner } from '@api/totaraApis';
import { useSession } from '@totara/core';
import { useSelector } from 'react-redux';

const bannerHeight = Platform.OS == 'android' ? 160 : 170;

const StaticPromoBanner = () => {
  const { apiToken, core } = useSession();
  const [data, setBannerData] = useState(null);
  const talentProfile = useSelector((state) => state?.getWFMProfile);

  useEffect(() => {
    getHomeData();
  }, [apiToken]);

  const getHomeData = async () => {
    global.liferayUser = talentProfile;

    const config = {
      apiToken,
    };

    const [statisPromoBanner] =
      await Promise.all([
        getStaticPromoBanner(config),
      ]);

    if (statisPromoBanner?.length) setBannerData(statisPromoBanner);
  };

  const progressValue = useSharedValue<number>(0);

  if (!data) return null;

  return (
    <View style={styles.container}>
      <Carousel
        enabled={false}
        scrollAnimationDuration={1000}
        width={Dimen.width}
        // height={I18nManager.isRTL ? 190 : 174}
        style={{
          minHeight: I18nManager.isRTL ? 190 : bannerHeight,
          flexGrow: 1
        }}
        data={data}
        onProgressChange={(_, absoluteProgress) => (progressValue.value = absoluteProgress)}
        renderItem={renderBannerItem}
      />

      {/* {data?.length > 1 && (
        <View style={styles.paginationContainer}>
          {data?.map((backgroundColor, index) => {
            return (
              <PaginationItem
                animValue={progressValue}
                index={index}
                key={index}
                isRotate={false}
                length={data.length}
              />
            );
          })}
        </View>
      )} */}
    </View>
  );
};

const renderBannerItem = ({ item, index }) => {
  const navigation = useNavigation();
  const { t } = useTranslation();
  const [isBadgesEnabled] = useState(true);
  const { isDarkMode } = useTheme();

  const navigateTo = ({ navigate, routeId, props }) => {
    navigate(routeId, props);
  };

  return (
    <ImageBackground
      resizeMode={FastImage.resizeMode.cover}
      key={index}
      source={{ uri: item?.image }}
      style={[styles.bannerStyle]}
      imageStyle={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }], borderRadius: 30 }}
    >
      <View style={{ width: Dimen.width / 2 }}>
        <TextView numberOfLines={2} style={styles.courseName} text={item?.title} />
        <TextView numberOfLines={2} style={[styles.courseDetail, isDarkMode && { color: '#c7c7c7' }]} text={item?.subtitle} />
        <Pressable
          onPress={() => {
            if (isBadgesEnabled && item?.buttontext !== 'activity') {
              navigation.navigate('MyBadges');
              return;
            } else if (item?.buttontext == 'activity') {
              navigateTo({
                navigate: navigation.navigate,
                routeId: 'CourseWebviewActivity',
                props: {
                  activity: { viewurl: item?.url, customWebiew: true },
                  backAction: () => { },
                  title: item?.title,
                },
              });
            }
          }}
        >
          <TextView
            style={styles.badgesText}
            text={isBadgesEnabled ? t(`Start your badge now`) : t(`Coming soon`)}
          />
        </Pressable>

      </View>

    </ImageBackground>
  );
};

export default StaticPromoBanner;

const PaginationItem: React.FC<{
  index: number;
  length: number;
  animValue: Animated.SharedValue<number>;
  isRotate?: boolean;
}> = (props) => {
  const { animValue, index, length, isRotate } = props;
  const width = 8;

  const animStyle = useAnimatedStyle(() => {
    let inputRange = [index - 1, index, index + 1];
    let outputRange = [-width, 0, width];

    if (index === 0 && animValue?.value > length - 1) {
      inputRange = [length - 1, length, length + 1];
      outputRange = [-width, 0, width];
    }

    return {
      transform: [
        {
          translateX: interpolate(animValue?.value, inputRange, outputRange, Extrapolate.CLAMP),
        },
      ],
    };
  }, [animValue, index, length]);
  return (
    <View
      style={{
        backgroundColor: Colors.lightGray,
        width,
        height: width,
        borderRadius: 50,

        marginHorizontal: 2,
        overflow: 'hidden',
        transform: [
          {
            rotateZ: isRotate ? '90deg' : '0deg',
          },
        ],
      }}
    >
      <Animated.View
        style={[
          {
            borderRadius: 50,
            backgroundColor: Colors.skyBlue,
            flex: 1,
          },
          animStyle,
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  bannerStyle: {
    width: Dimen.width / 1.08,
    borderRadius: 10,
    padding: widgetPadding,
    alignSelf: 'center',
    minHeight: I18nManager.isRTL ? 185 : bannerHeight,
    marginHorizontal: widgetPadding,
    // backgroundColor: 'rgba(17, 47, 80, 1)',
    paddingHorizontal: widgetPadding * 1.5
  },
  container: {
    flex: 1,
    alignSelf: 'center',
    marginVertical: widgetPadding,

  },
  flatListStyle: {
    alignSelf: 'center',
    width: Dimen.width,
    marginVertical: 10,
    marginHorizontal: 20,
    height: 170,
  },
  detailView: {
    flex: 0.9,
  },
  badgeBtn: {
  },
  courseName: {
    color: Colors.white,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.h3,
    textAlign: 'left',
    lineHeight: LineHeight.h4_LH,
  },
  badge: {
    width: 20,
    height: 15,
    resizeMode: 'contain',
  },
  courseDetail: {
    color: Colors.white,
    fontFamily: fonts.regular,
    fontSize: fontSize.h6,
    marginBottom: 'auto',
    textAlign: 'left',
    marginVertical: widgetPadding / 2,
    lineHeight: LineHeight.h6_LH
  },
  badgesText: {
    fontFamily: fonts.regular,
    fontSize: fontSize.h6,
    color: 'white',
    textDecorationLine: 'underline',
    marginTop: widgetPadding,
    textAlign: 'left'
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignSelf: 'center',
    marginTop: 10,
  },
});
