import React, { useState } from 'react';
import { I18nManager, Image, TouchableOpacity } from 'react-native';
import { IconsType } from '@theme/Icons';
import { useTheme } from '@theme/ThemeProvider';
import { setBottomSheetVisible } from '@totara/reducers/addToListBottomSheetReducer';
import { setReferCourse } from '@totara/reducers/referCourseReducer';
import { commonStyle } from '@utils';
import { convertNumbersToArabicNumerals, isOnlyNumbers, RenderContentType } from '@utils/constants';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native-animatable';
import FastImage from 'react-native-fast-image';
import { IconButton } from 'react-native-paper';
import { useDispatch } from 'react-redux';
import { useNavigation } from '../../hooks';
import { LearningPathwayType } from '../../interfaces/learningPathWayType';
import { AssetsImages, Colors, Icons } from '../../theme';
import { FavItem, TextView } from '../index';
import styles from './styles';

const LearningPathWayItem = ({
  name,
  months,
  courses,
  overDue,
  provider,
  backgroundColor,
  progress,
  isDetail,
  itemStyle,
  showInProgress = false,
  showPeopleJoined = false,
  showMore = false,
  showRating = false,
  showCurrentModule = false,
  isSelectMode = false,
  onSelect,
  onUnselect,
  onMorePress,
  image,
  providerLogo,
  rating,
  totalContent,
  duration,
  isPersonal,
  type,
  item,
  showFav,
  peopleJoined,
  creator,
}: LearningPathwayType) => {
  const { navigate } = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const [isChecked, setChecked] = useState<boolean>(false);

  const handleNavigateToDetails = () => {
    if ((item.type == 'program' || item.type == 'Program') && item.onboarding_program) {
      navigate('NewJoinerPrograms', { id: item.id });
    } else if (
      ((item.type == 'course' || item.type == 'Course') &&
        (item.course_type == 'Self-paced' ||
          item.coursetype == 'Self-paced' ||
          item.course_type == 'Blended' ||
          item.course_type === undefined)) ||
      item.course_days == 'One Day' ||
      item.course_day == 'One Day'
    ) {
      navigate('CourseDetail', { ...item, courseid: item.courseid ? item?.courseid : item.id });
    } else if (
      ((item.type == 'course' || item.type == 'Course') && item.course_day == 'Multi Day') ||
      item.course_type == 'Blended' ||
      item.course_days == 'Multi Day'
    ) {
      navigate('MultipleSession', { courseid: item.courseid ? item?.courseid : item.id });
    } else if (item.type != 'course' && item.type != 'Course')
      navigate('ProgramDetails', {
        id: item.id,
        name,
        months,
        courses,
        overDue,
        provider,
        backgroundColor,
        progress,
        isDetail,
        itemStyle,
        showInProgress,
        showPeopleJoined,
        showMore,
        showRating,
        showCurrentModule,
      } as LearningPathwayType);
  };

  const dispatch = useDispatch();

  const onPressMore = () => {
    if (onMorePress) {
      onMorePress();
    } else {
      setReferCourse(dispatch, { courseid: item?.courseid, name, image });
      setBottomSheetVisible(dispatch, true);
    }
  };

  const { t } = useTranslation();
  const isNumeric = /^\d+$/.test(duration);
  const durationExtension = isNumeric ? `${Number(duration) > 1 ? t('hours') : t('hour')}` : '';

  const handleSelectedStatusChange = (): void => {
    if (isChecked) {
      onUnselect?.();
    } else {
      onSelect?.();
    }
    setChecked(!isChecked);
  };

  return (
    <TouchableOpacity
      onPress={isSelectMode ? handleSelectedStatusChange : handleNavigateToDetails}
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode
            ? isDetail
              ? theme.backgroundColor
              : 'rgba(109, 163, 215, 0.1)'
            : backgroundColor,
        },
        itemStyle,
      ]}
    >
      <View style={styles.rowStart}>
        {isDetail && (
          <View style={styles.courseImage}>
            <FastImage
              style={styles.imageStyle}
              source={image ? { uri: image } : AssetsImages.placeHolderCourseImage}
            />
          </View>
        )}
        <View style={{ flex: 1 }}>
          <View style={[styles.detailView]}>
            <View style={styles.descrptionView}>
              <View style={[styles.rowJustified, { alignItems: 'flex-start' }]}>
                <TextView
                  numberOfLines={!isDetail ? 2 : 10}
                  style={[styles.itemTitle, { color: theme.textColor }]}
                  text={name}
                />
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    justifyContent: 'flex-start',
                    marginRight: -12,
                  }}
                >
                  {showFav &&
                    item?.courseid &&
                    item.type !== 'program' &&
                    item?.type !== 'Program' && (
                      <FavItem
                        style={[styles.favItem, { marginRight: 0, marginTop: -2 }]}
                        color={theme.textColor}
                        courseid={item?.courseid}
                      />
                    )}
                  {showMore && (
                    <IconButton
                      icon={() => {
                        return (
                          <Icons
                            type={IconsType.Entypo}
                            style={{}}
                            name={'dots-three-vertical'}
                            size={18}
                            color={theme.textColor}
                          />
                        );
                      }}
                      size={15}
                      style={{ marginTop: -1 }}
                      onPress={onPressMore}
                    />
                  )}
                  {isSelectMode && (
                    <Image
                      style={{
                        width: 20,
                        height: 20,
                        resizeMode: 'contain',
                        marginRight: 10,
                        borderRadius: isDarkMode ? 5 : 0,
                      }}
                      source={isChecked ? AssetsImages.selectedCheckBox : AssetsImages.uncheck}
                    />
                  )}
                </View>
              </View>

              <View
                style={[
                  styles.courseDescription,
                  {
                    justifyContent: 'flex-start',
                  },
                ]}
              >
                {duration != undefined && duration != '' && (
                  <View style={styles.row}>
                    <Image
                      style={[styles.icon, { tintColor: theme.textColor }]}
                      source={AssetsImages.clock}
                    />
                    <TextView
                      style={[styles.desc, { color: theme.textColor }]}
                      text={
                        !isOnlyNumbers(duration)
                          ? duration
                          : `${convertNumbersToArabicNumerals(duration)} ${durationExtension}`
                      }
                    />
                  </View>
                )}
                <RenderContentType
                  item={item}
                  type={item.type || item.component_type || item.courseType || 'course'}
                  styles={styles}
                  iconStyle={[styles.coursesIcon, { tintColor: theme.textColor }]}
                  textStyle={[styles.desc, { color: theme.textColor }]}
                  t={t}
                />
                {/* {totalContent && !totalContent?.startsWith('0') && (
                  <View style={styles.row}>
                    <Image
                      style={[styles.coursesIcon, { tintColor: theme.textColor }]}
                      source={AssetsImages.courses}
                    />
                    <TextView
                      style={[styles.desc, { color: theme.textColor }]}
                      text={totalContent}
                    />
                  </View>
                )} */}
                {rating != '0' && rating != 0.0 && showRating && (
                  <View style={styles.row}>
                    <Image style={styles.star} source={AssetsImages.ratingStar} />
                    <TextView
                      style={[styles.desc, { color: theme.textColor }]}
                      text={convertNumbersToArabicNumerals(rating)}
                    />
                  </View>
                )}
              </View>

              <View
                style={[
                  styles.providerView,
                  {
                    marginTop: 10,
                    marginStart: 0,
                    width: isDetail ? '100%' : '80%',
                    alignItems: 'flex-start',
                  },
                ]}
              >
                {creator && (
                  <TextView
                    style={[styles.companyName, { color: theme.textColor }]}
                    text={creator}
                  />
                )}
              </View>

              {/* {showPeopleJoined && (
                <View style={styles.avatarView}>
                  <Image
                    style={[styles.avatar, { tintColor: theme.textColor }]}
                    source={AssetsImages.peopleJoined}
                  />
                  <TextView
                    style={[styles.peopleJoined, { color: theme.textColor }]}
                    text={`${peopleJoined} ${t('people completed this course')}`}
                  />
                </View>
              )} */}
            </View>

            {!isDetail && (
              <View style={styles.imageView}>
                <FastImage
                  style={styles.imageStyle}
                  source={image ? { uri: image } : AssetsImages.placeHolderCourseImage}
                />
              </View>
            )}
          </View>
          <View style={[commonStyle.rowAlign, { marginTop: 12 }]}>
            {isDetail && showInProgress && progress && progress != 100 && (
              <View
                style={[
                  styles.inProgress,
                  isDarkMode && { backgroundColor: 'rgba(247,200,107,1)' },
                ]}
              >
                <TextView
                  style={[styles.overdueText, isDarkMode && { color: 'black' }]}
                  text={'In Progress'}
                />
              </View>
            )}
            {overDue?.includes('overdue') && progress != 100 && (
              <View
                style={[
                  styles.overdueView,
                  isDarkMode && { backgroundColor: 'rgba(229,102,102,1)' },
                ]}
              >
                <TextView
                  style={[styles.overdueText, isDarkMode && { color: 'black' }]}
                  text={overDue}
                />
              </View>
            )}
            {overDue?.includes('Due') && (
              <View
                style={[
                  [styles.overdueView, { backgroundColor: 'rgba(84, 152, 217, 1)' }],
                  isDarkMode && { backgroundColor: 'rgba(229,102,102,1)' },
                ]}
              >
                <TextView
                  style={[styles.overdueText, isDarkMode && { color: 'black' }]}
                  text={overDue}
                />
              </View>
            )}
            {/* {item?.course_type && isDetail && (
              <View style={[styles.courseType, { borderColor: theme.borderBackground }]}>
                <TextView
                  style={[styles.courseTypeText, { color: theme.textColor }]}
                  text={item?.course_type}
                />
              </View>
            )} */}
          </View>

          {showCurrentModule && (
            <View>
              <View style={styles.currentView}>
                <TextView
                  style={[styles.current, { color: theme.textColor }]}
                  text={`${t('Current')}: `}
                />
                <TextView
                  style={[styles.hrPartner, { color: theme.textColor }]}
                  text="HR Business Partnering"
                />
              </View>

              <TextView
                style={[styles.nextSection, { color: theme.textColor }]}
                text="UP NEXT: Artificial Intelligence Session"
              />
            </View>
          )}

          <>
            {isDetail && showInProgress && (
              <View style={[styles.progressBar, { marginTop: 20 }]}>
                <View style={styles.rowJustified}>
                  <TextView
                    style={[styles.progressText, { color: theme.textColor }]}
                    text={`${t('Progress')}: ${convertNumbersToArabicNumerals(progress)}%`}
                  />
                  {isDetail ? (
                    <TextView
                      style={[styles.progressText, { color: theme.textColor }]}
                      text={`${convertNumbersToArabicNumerals(progress)}/${convertNumbersToArabicNumerals('100')}`}
                    />
                  ) : null}
                </View>

                <View
                  style={[
                    styles.progressUnfilleddPercentage,
                    isDarkMode && { backgroundColor: theme.bgLightDark },
                  ]}
                >
                  <View
                    style={[
                      styles.progressPercentage,
                      { width: `${progress}%` },
                      isDarkMode && { backgroundColor: Colors.progressBarDarkColor },
                    ]}
                  />
                </View>
              </View>
            )}
          </>
        </View>
      </View>
      <View
        style={[
          styles.courseProviderView,
          { position: 'absolute', bottom: 10, marginTop: 10, marginStart: 20 },
        ]}
      >
        {!isDetail && showInProgress && (
          <View style={[styles.progressBar, { marginBottom: 5 }]}>
            <View style={styles.rowJustified}>
              <View style={commonStyle.rowAlign}>
                <TextView
                  style={[styles.progressText, { color: theme.textColor }]}
                  text={`${t('Progress')}: `}
                />
                {I18nManager.isRTL ? (
                  <TextView style={[styles.progressText, { color: theme.textColor }]} text={`%`} />
                ) : null}
                <TextView
                  style={[styles.progressText, { color: theme.textColor }]}
                  text={`${convertNumbersToArabicNumerals(progress)}`}
                />
                {!I18nManager.isRTL ? (
                  <TextView style={[styles.progressText, { color: theme.textColor }]} text={`%`} />
                ) : null}
              </View>

              {isDetail ? (
                <TextView
                  style={[styles.progressText, { color: theme.textColor }]}
                  text={`${progress}/100`}
                />
              ) : null}
            </View>

            <View
              style={[
                styles.progressUnfilleddPercentage,
                isDarkMode && { backgroundColor: theme.backgroundColor, opacity: 0.9 },
              ]}
            >
              <View
                style={[
                  styles.progressPercentage,
                  { width: `${progress}%` },
                  isDarkMode && { backgroundColor: Colors.progressBarDarkColor },
                ]}
              />
            </View>
          </View>
        )}

        {/* {(!isDetail && provider) && (
              <View style={[styles.providerView, { alignSelf: 'flex-end', marginStart: isPersonal ? 0 : 40 }]}>
                <Image style={styles.companyLogo} source={logo} />
                <TextView style={[styles.companyName, { color: theme.textColor }]} text={provider} />
              </View>
            )} */}
      </View>
    </TouchableOpacity>
  );
};

export default LearningPathWayItem;
