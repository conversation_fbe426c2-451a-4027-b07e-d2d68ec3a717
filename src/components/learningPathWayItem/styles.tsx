import { Colors, Dimen, fontSize, fonts } from '../../theme';
import { Platform, StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    flex: 1,
    width: Dimen.width * 0.75,
    padding: 15,
    borderRadius: 20,
    backgroundColor: 'rgba(233, 241, 250, 1)',
    marginEnd: 10,
    paddingBottom: 20,
  },
  detailView: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowStart: {
    flexDirection: 'row',
  },
  imageView: {
    height: 70,
  },
  imageStyle: {
    width: 70,
    height: 70,
    resizeMode: 'contain',
    borderRadius: 10,
    backgroundColor: '#f5f5f5',
  },
  descrptionView: {
    flex: 1,
  },
  courseDescription: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '85%',
  },
  icon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginRight: 5,
  },
  coursesIcon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginRight: 5,
  },
  star: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginRight: 5,
    marginTop: -1,
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: fontSize.xmini,
    color: Colors.greyText,
    marginRight: 10,
  },
  itemTitle: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    flex: 1,
    marginRight: 20,
    textAlign: 'left',
    marginBottom: 10,
    lineHeight: Platform.OS == 'android' ? 22 : 22
  },
  courseProviderView: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  overdueView: {
    backgroundColor: Colors.overDue,
    borderRadius: 15,
    marginRight: 15,
  },
  courseType: {
    borderRadius: 15,
    borderColor: Colors.borderColorSt,
    borderWidth: 1,
  },
  inProgress: {
    backgroundColor: Colors.inProgressCourse,
    borderRadius: 20,
    width: 100,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  providerView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginStart: 40,
  },
  companyView: {},
  companyLogo: {
    width: 15,
    height: 15,
    borderRadius: 3,
  },
  overdueText: {
    color: Colors.white,
    textTransform: 'uppercase',
    fontSize: fontSize.custom(Platform.OS == 'ios' ? 7 : 9),
    fontFamily: fonts.bold,
    padding: Platform.OS == 'android' ? 2 : 5,
    paddingHorizontal: 6,
    letterSpacing: 1,
    marginTop: Platform.OS == 'ios' ? 1.2 : 0.3

  },
  courseTypeText: {
    textTransform: 'uppercase',
    fontSize: fontSize.custom(Platform.OS == 'ios' ? 7 : 10),
    fontFamily: fonts.bold,
    padding: Platform.OS == 'android' ? 3 : 5,
    paddingHorizontal: 10,
    letterSpacing: 1,
    marginTop: Platform.OS == 'ios' ? 1.2 : 0.2
  },
  companyName: {
    color: Colors.black,
    fontSize: fontSize.xmini,
    fontFamily: fonts.regular,
    paddingHorizontal: 5,
  },
  progressBar: {
    borderRadius: 5,
    flex: 1,
    // maxWidth: '60%'
  },
  progressUnfilleddPercentage: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    height: 8,
    justifyContent: 'center',
    borderRadius: 5,
    marginTop: 5,
  },
  progressPercentage: {
    backgroundColor: 'rgba(148, 228, 17, 1)',
    height: 5,
    justifyContent: 'center',
    flex: 1,
    borderRadius: 5,
  },
  progressText: {
    fontSize: fontSize.xmini,
    fontFamily: fonts.medium,
    color: Colors.black,
  },
  rowJustified: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    width: '100%',
  },
  dots: {
    tintColor: Colors.black,
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginTop: -3,
  },
  courseImage: {
    height: 70,
    marginRight: 10,
  },
  dotsView: {
    alignItems: 'flex-end',
  },
  currentView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    backgroundColor: 'rgba(237, 241, 246, 1)',
    borderRadius: 5,
  },
  hrPartner: {
    fontFamily: fonts.regular,
    fontSize: fontSize.mini,
    paddingVertical: 8,
    color: Colors.black,
  },
  current: {
    fontFamily: fonts.medium,
    fontSize: fontSize.mini,
    paddingStart: 10,
    color: Colors.black,
  },
  nextSection: {
    color: 'rgba(125, 161, 196, 1)',
    marginHorizontal: 10,
    marginTop: 15,
    fontSize: fontSize.mini,
    fontFamily: fonts.regular,
  },
  avatar: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  avatarView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  peopleJoined: {
    marginStart: 5,
    fontSize: fontSize.xmini,
    color: Colors.greyText,
    fontFamily: fonts.thin,
  },
  favItem: {
    marginRight: 10,
  },
});
