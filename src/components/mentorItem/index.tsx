import { AssetsImages, Colors, Dimen, Icons, Images } from '../../theme';
import React from 'react';
import { Image, ImageBackground, Platform, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { IconsType } from '../../theme/Icons';
import { lineHeight } from '@utils/constants';

type props = {
  index: number;
  item: Record<string, string>;
};

const ScholarshipItem = ({ item, index }: props) => {
  const array = [Images.face1, Images.face2, Images.face1];

  return (
    <View style={styles.itemView}>
      <View style={styles.imageViewContainer}>
        <Image
          // source={item.image}
          source={AssetsImages.default}
          style={styles.imageView}
        />
      </View>
      <View style={styles.detailView}>
        <View>
          <TextView numberOfLines={1} style={styles.courseHeading} text={item.title} />
          <TextView numberOfLines={1} style={styles.releaseName} text="Basics for Data Analysis" />
        </View>

        <View style={styles.rowJustify}>
          <View style={styles.row}>
            <View style={styles.imageStack}>
              {array?.map((res, index) => {
                return (
                  <Image
                    style={[
                      styles.mentorImage,
                      {
                        marginRight: -12,
                      },
                    ]}
                    source={AssetsImages.default}
                  // source={res}
                  />
                );
              })}
            </View>
            <TextView style={styles.names} text="1.4k Mentees" />
          </View>

          <View style={[styles.row]}>
            <Icons
              color={Colors.rating}
              size={18}
              type={IconsType.Entypo}
              style={{ marginRight: 5 }}
              name={'star'}
            />
            <TextView type="h5" style={styles.ratingText} text={item.rating} />
          </View>
        </View>
      </View>
    </View>
  );
};

export default ScholarshipItem;

const styles = StyleSheet.create({
  itemView: {
    width: Dimen.width * 0.85,
    borderRadius: 10,
    marginRight: 15,
    overflow: 'hidden',
    flexDirection: 'row',
    borderColor: Colors.borderGray,
    borderWidth: 1,
    // justifyContent: "space-between",
    // padding: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  imageViewContainer: {
    alignItems: 'center',
  },
  imageView: {
    borderRadius: Platform.OS == 'ios' ? 70 / 2 : 80 / 2,
    height: Platform.OS == 'ios' ? 70 : 80,
    width: Platform.OS == 'ios' ? 70 : 80,
  },
  detailView: {
    backgroundColor: Colors.white,
    justifyContent: 'space-between',
    marginLeft: 12,
    flex: 1,
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Platform.OS == 'ios' ? 0 : 10,
  },
  imageStack: {
    flexDirection: 'row',
    marginEnd: 5,
  },
  mentorImage: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    flexWrap: 'wrap',
    width: '60%',
    marginStart: 10,
    color: Colors.black,
    fontSize: fontSize.small,
  },
  like: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
    tintColor: 'black',
  },
  viewMore: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  courseHeading: {
    color: Colors.black,
    fontSize: fontSize.large,
    fontFamily: fonts.medium,
    lineHeight: lineHeight(fontSize.large)
  },
  star: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  rowJustify: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ratingText: {
    fontFamily: fonts.medium,
    color: Colors.black,
  },
  releaseName: {
    fontSize: fontSize.small,
    color: '#62605F',
  },
});
