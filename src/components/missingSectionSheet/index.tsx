import React from 'react';
import { Image, Pressable, View } from 'react-native';
import TextView from '../common/TextView';
import AssetsImages from '../../theme/AssetsImages';
import { TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StyleSheet } from 'react-native';
import { commonStyle, fonts, fontSize } from '../../utils';
import { Colors } from '../../theme';
import { useTheme } from '@theme/ThemeProvider';
import { I18nManager } from 'react-native';

const MissingSectionsSheet = ({ list, setIsVisible }) => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const color = { color: theme.textColor }

  const missingSections = list?.map((section) => {
    if (section.title === 'Work Experience') {
      return { ...section, icon: AssetsImages.workExpIcon };
    }
    if (section.title === 'education') {
      return { ...section, icon: AssetsImages.educationIcon };
    }
    if (section.title === 'Achievements' || section.title === 'achievements') {
      return { ...section, icon: AssetsImages.achievementIcon };
    }
    if (section.title === 'Mentoring Experience') {
      return { ...section, icon: AssetsImages.mentoringIcon };
    }
    if (section.title === 'Trainings & Certificates') {
      return { ...section, icon: AssetsImages.trainingIcon };
    }
    if (section.title === 'Skills') {
      return { ...section, icon: AssetsImages.skillIcon };
    }
    if (section.title === 'About' || section.title === 'Bio') {
      return { ...section, icon: AssetsImages.aboutIcon };
    }
    if (section.title === 'Performance') {
      return { ...section, icon: AssetsImages.performanceIcon };
    }
    if (section.title === 'Assessments') {
      return { ...section, icon: AssetsImages.achievementIcon };
    }

    return section;
  });

  const navigationToRespectiveRoute = (item) => () => {
    setIsVisible(false);
    if (item?.route) {
      navigation.navigate(item.route, {
        title: item?.title,
        type: item.type,
      });
    }
  };

  return (
    <View style={styles.sheetContainer}>
      {missingSections?.map((item, index) => {
        return (
          <TouchableOpacity
            onPress={navigationToRespectiveRoute(item)}
            key={item?.title}
            style={[styles.missingContainer, {
              borderBottomColor: theme.borderBackground
            }]}>
            <View style={commonStyle.rowAlign}>
              <Image source={item.icon} style={[styles.icon, isDarkMode && { tintColor: Colors.activeStateDark }]} />
              <TextView style={[styles.missingSectionText, color]} text={item?.title} />
            </View>

            <Pressable>
              <Image source={AssetsImages.next} style={[styles.nextImage,
              { transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] },
              isDarkMode && {
                tintColor: Colors.activeStateDark,
              }]} />
            </Pressable>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default MissingSectionsSheet;

const styles = StyleSheet.create({
  missingContainer: {
    borderBottomColor: Colors.lightGray,
    paddingTop: 10,
    borderBottomWidth: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 5
  },
  missingSectionText: {
    textTransform: 'capitalize',
    fontFamily: fonts.medium,
    fontSize: fontSize.semiMedium,
    marginVertical: 10,
  },
  nextImage: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
  },
  sheetContainer: {
    paddingHorizontal: 20,
    paddingBottom: 60,
  },
});
