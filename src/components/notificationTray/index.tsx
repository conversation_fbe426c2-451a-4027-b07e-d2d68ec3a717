import React from 'react';
import { Image, Pressable, View, StyleSheet, I18nManager } from 'react-native';
import { navigationRef } from '../../navigation/navigationService';
import AssetsImages from '../../theme/AssetsImages';
import { fonts, fontSize } from '../../utils';
import TextView from '../../components/common/TextView';
import { useGetCourseType } from '@hooks/useGetCourseType';

const NotificationTray = ({ message }) => {
  const { onGetCourseType } = useGetCourseType();

  const onPress = async () => {
    if (message?.description.type == 'announcement') {
      navigationRef.current.navigate('AnnoucementDetail', {
        name: message?.message,
        subject: message?.description?.description,
        image: message?.description?.image,
      });
    } else {
      const item = await onGetCourseType(message?.description?.courseId);
      const isMultiDay =
        (item?.course_day == 'Multi Day' && item?.course_type != 'Self-paced') ||
        (item?.course_day == 'Multi Day' && item?.course_type != 'Self-Paced');
      const route =
        item?.course_type == 'Self-paced' || item?.course_type == 'Self-Paced'
          ? 'CourseDetail'
          : isMultiDay
            ? 'MultipleSession'
            : 'CourseDetail';

      navigationRef.current.navigate(route, {
        courseid: message?.description?.courseId,
      });
    }
  };
  return (
    <Pressable onPress={onPress} style={styles.notificationContainer}>
      <View style={[styles.row, { flexDirection: 'row' }]}>
        <View style={styles.logoWrapper}>
          <View style={styles.logoShadow} />
          <Image source={AssetsImages.newNotificationLogo} style={styles.logo} />
        </View>
        <View style={{ flex: 1 }}>
          <TextView
            numberOfLines={1}
            style={styles.heading}
            text={message?.message || '📢 Upcoming Course Deadlines Alert! '}
          />
          <TextView
            numberOfLines={2}
            style={styles.subHeading}
            text={
              message?.description?.description ||
              'Stay on track and mark your calendars. Dont miss out!'
            }
          />
        </View>
      </View>
    </Pressable>
  );
};

export default NotificationTray;

const styles = StyleSheet.create({
  notificationContainer: {
    marginTop: 80,
    marginHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    borderRadius: 20,
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    paddingBottom: 20,
  },
  heading: {
    flexWrap: 'wrap',
    fontFamily: fonts.medium,
    fontSize: fontSize.small,
    textAlign: 'auto',
    writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
  },

  subHeading: {
    marginTop: 5,
    flexWrap: 'wrap',
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    textAlign: 'auto',
    writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
  },

  logoWrapper: {
    width: 40,
    height: 40,
    marginHorizontal: 10,
    marginTop: 8,
    position: 'relative',
  },
  logoShadow: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 5,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logo: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 5,
    backgroundColor: 'transparent',
    resizeMode: 'cover',
  },

  row: {
    alignItems: 'flex-start',
    width: '100%',
  },
});
