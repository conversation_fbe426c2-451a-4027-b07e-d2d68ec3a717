import { onBoarding } from '../../utils/constants';
import { Colors, Dimen, fonts } from '../../theme';
import { Animated, StyleSheet, View } from 'react-native';
import { fontFamily } from '../../utils/AppUtils';
import { memo } from 'react';
import React from 'react';

type Prop = {
  index: any;
  selectedIndex: number;
  testID?: string;
};

const OnBoardingIndicator = ({ index, selectedIndex, testID }: Prop) => {
  return (
    <View testID={testID} style={styles.indicatorStyle}>
      {onBoarding.map((_, i) => {
        const translateX = Animated.multiply(Animated.subtract(index, i), 20);
        const transform = {
          transform: [{ translateX }],
        };
        const isSelected = i === selectedIndex;
        const circleStyle: any = {
          width: isSelected ? 20 : 5,
        };
        return (
          <View style={[styles.circle, circleStyle]} key={i}>
            <Animated.View style={[styles.mover, transform]} />
          </View>
        );
      })}
    </View>
  );
};

export default memo(OnBoardingIndicator);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sheetContainer: {
    backgroundColor: 'white',
    borderTopRightRadius: 40,
    borderTopLeftRadius: 40,
    height: Dimen.height * 0.42,
    position: 'absolute',
    bottom: 0,
    width: '100%',
    paddingVertical: 30,
  },
  launchImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  title: {
    marginVertical: 15,
    lineHeight: 30,
    marginHorizontal: 20,
    fontFamily: fonts.bold,
  },
  subTitle: {
    lineHeight: 20,
    color: Colors.light.black,
    marginHorizontal: 20,
  },
  circle: {
    width: 8,
    overflow: 'hidden',
    height: 5,
    backgroundColor: 'grey',
    borderRadius: 20,
    marginRight: 4,
  },
  mover: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 20,
    height: 20,
    borderRadius: 2,
    backgroundColor: 'white',
  },
  item: {
    width: Dimen.width,
    height: Dimen.height * 0.85,
  },
  indicatorStyle: {
    flexDirection: 'row',
    position: 'absolute',
    top: -20,
    alignSelf: 'center',
  },
  getStarted: {
    marginHorizontal: 20,
    fontFamily: fontFamily('REGULAR'),
  },
  flatlistStyle: { height: Dimen.height * 0.85 },
});
