import React from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { Dimen } from '../../theme';

type OnBoardingItemProps = {
  item: {
    image: any;
    heading: string;
    subHeading: string;
  };
  imageTestId: string;
  testID: string;
};
const OnBoardingItem: React.FC<OnBoardingItemProps> = ({ item, imageTestId, testID }) => {
  return (
    <View testID={testID} style={styles.item}>
      <Image testID={imageTestId} style={styles.launchImage} source={item.image} />
    </View>
  );
};

export default OnBoardingItem;

const styles = StyleSheet.create({
  launchImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  item: {
    width: Dimen.width,
    height: Dimen.height * 0.75,
  },
});
