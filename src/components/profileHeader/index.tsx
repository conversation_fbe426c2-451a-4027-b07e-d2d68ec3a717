import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ImageBackground,
  Pressable,
  TouchableOpacity,
  View,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { Avatar, DynamicTabs, Header, TextView } from '../index';
import BottomSheet from '../bottomSheet';
import MissingSectionsSheet from '../missingSectionSheet';
import { AssetsImages, Colors } from '../../../src/theme';
import { updateProfile, updateWFMField } from '../../totara/actions/getWFMProfileAction';
import { setBottomSheetVisible } from '../../totara/reducers/referralHistoryBottomSheetReducer';
import commonStyle from '../../utils/commonStyle';
import {
  cameraList,
  convertNumbersToArabicNumerals,
  pickImage,
  pickImageFromCamera,
  profileTabs,
} from '../../utils/constants';
import useStyles from './styles';
import { useTranslation } from 'react-i18next';
import { I18nManager } from 'react-native';
import UserAvatar from '@components/UserAvatar';

const ProfileHeader = ({
  onBackPress,
  onSettingIconPress,
  data,
  onEditIconPress,
  currentWorkExperience,
  percentage,
  missingSections,
  getSelectedTab,
}) => {
  const dispatch = useDispatch();
  const { styles, theme, isDarkMode } = useStyles();
  const [userId, setUserId] = useState('');
  const profile = useSelector((state) => state?.getWFMProfile);
  const [s, setFileName] = useState('');
  const navigation = useNavigation();
  const [isVisible, setIsVisible] = useState(false);
  const [imageOptions, setImageOptions] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [uri, setUri] = useState('');
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const talentProfile = useSelector((state) => state?.getWFMProfile.talentProfile);
  const userProfile = useSelector((state) => state?.getWFMProfile);
  const uaePassResponse = useSelector((state) => state?.getWFMProfile?.response);
  const { t } = useTranslation();


  const onChange = (fields) => {
    if (profile?.talentProfile?.id !== undefined) {
      dispatch(
        updateProfile(
          profile?.talentProfile?.id,
          fields,
          profile?.response?.liferayaccesstoken,
          navigation,
        ),
      );
    }
  };

  const onMorePress = () => {
    setBottomSheetVisible(dispatch, true);
  };

  const handleMissingSection = () => setIsVisible(!isVisible);

  const toggleVisibility = async () => setImageOptions(!imageOptions);

  const navigateToFun = (item) => () => {
    toggleVisibility();
    setTimeout(() => {
      if (item.id == '1') navigation.navigate('ImageViewer', { uri });
      if (item.id == '2') openCameraCropper();
      if (item.id == '3') openGallery();
    }, 500);
  };

  const openGallery = async () => {
    setLoading(true);
    const response = await pickImage(token, getFile, 'profilepicture', data?.response?.idn);
    getFile(response);
  };

  const openCameraCropper = async () => {
    setLoading(true);
    const response = await pickImageFromCamera(
      token,
      getFile,
      'profilepicture',
      data?.response?.idn,
    );
    getFile(response);
  };

  const getFile = (data) => {
    if (data?.fileName !== talentProfile?.picture) {
      onChange && onChange({ picture: data?.fileName, noSuccessMessage: true });
    }
    setFileName(data?.fileName);
    dispatch(updateWFMField({ key: 'imageUrl', value: data?.base64 }));
    setLoading(false);
  };

  useEffect(() => {
    if (userProfile?.response?.imageUrl) {
      setUri(userProfile?.response?.imageUrl);
    }
  }, [userProfile?.response?.imageUrl]);


  const tintColor = { tintColor: theme.tintColor }


  const formatExperience = (years) => {
    const integerPart = Math.floor(years);
    const decimalPart = years - integerPart;
    if (decimalPart > 0) {
      return `${integerPart}+`;
    } else {
      return `${integerPart}`;
    }
  };

  return (
    <View>
      <FastImage
        resizeMode={FastImage.resizeMode.stretch}
        style={styles.header}
        source={isDarkMode ? AssetsImages.talentProfileDarkHeader : AssetsImages.talentProfileLightHeader}
      >
        <Header
          customRightIconStyles={{ paddingEnd: 10 }}
          IconColor="#fff"
          customStyles={{ backgroundColor: 'transparent' }}
          onSettingIconPress={onSettingIconPress}
          onBackPress={onBackPress}
          onMorePress={onMorePress}
        />
      </FastImage>
      <LinearGradient
        start={{ x: 0, y: 20 }}
        end={{ x: 4, y: 1 }}
        colors={isDarkMode ? [theme.bgDark, theme.bgDark, theme.bgDark] : ['rgb(212, 225, 241)', 'rgba(255, 255, 255, 0)', 'rgb(240,244,249)']}
        style={styles.profileView}
      ></LinearGradient>
      <UserAvatar />

      <View style={[styles.nameSection]}>
        <TextView style={[styles.name, { color: theme.textColor }]} text={I18nManager.isRTL ? uaePassResponse?.fullnameAR?.replace(/,/g, ' ') : uaePassResponse?.fullnameEN || ''} />
        {(currentWorkExperience && talentProfile?.workExperience?.length) ? (
          <TextView
            style={[styles.role, { color: theme.textColor }]}
            text={currentWorkExperience}
          />
        ) : null}

        {talentProfile?.workExperience?.length ?
          <View style={[commonStyle.rowAlign]}>
            {(talentProfile?.totalYearsOfExperience && talentProfile?.totalYearsOfExperience > 0) &&
              <View style={[styles.experienceSection, commonStyle.mt15]}>
                <Image style={[styles.noOfExp, tintColor]} source={AssetsImages.noOfExp} />
                <TextView
                  style={[styles.numOfExp, { color: theme.textColor }]}
                  text={`${formatExperience(talentProfile?.totalYearsOfExperience)} ${t('Years experience')}`}
                />
              </View>}
            {((talentProfile?.totalYearsOfExperience && talentProfile?.totalYearsOfExperience) > 0 && talentProfile?.department) && <View style={[commonStyle.dot, commonStyle.mt15]} />}
            {talentProfile?.department &&
              <View style={[styles.experienceSection, commonStyle.mt15]}>
                <Image style={[styles.noOfExp, tintColor]} source={AssetsImages.noOfYears} />
                <TextView style={[styles.numOfExp, { color: theme.textColor }]} text={`${talentProfile?.department}`} />
              </View>}
          </View> : null}
      </View>
      <View style={styles.profileCompletion}>
        <View style={styles.rowJustified}>
          <TextView style={[styles.completionText, { color: theme.textColor }]} text={"Profile completion"} />
          <TextView style={[styles.percentage, { color: theme.textColor }]} text={(percentage !== "0%") ? `${convertNumbersToArabicNumerals(percentage)}%` : `${convertNumbersToArabicNumerals(0)}%`} />
        </View>
        <View style={styles.progressBar}>
          <View style={[styles.progressPercentage, isDarkMode && { backgroundColor: theme.backgroundColor, opacity: 0.9 }]}>
            <Image
              style={[{ width: `${Number(percentage)}%`, borderRadius: 10 }, isDarkMode && { tintColor: Colors.progressBarDarkColor }]}
              source={AssetsImages.ProgressBar}
            />
          </View>
        </View>
      </View>

      {missingSections?.length ? (
        <Pressable onPress={handleMissingSection} style={styles.missingSections}>
          <Image source={AssetsImages.info} style={styles.info} />
          <TextView style={[styles.completeYourProfile]} text="Complete your profile" />
        </Pressable>
      ) : null}
      <View style={{ backgroundColor: isDarkMode ? theme.backgroundColor : 'transparent', alignItems: 'flex-start' }}>
        <DynamicTabs
          initiallySelected={true}
          getSelectedTab={getSelectedTab}
          containerStyle={styles.tabStyle}
          tabs={profileTabs}
        />
      </View>


      <BottomSheet
        visiblity={isVisible}
        setVisibility={setIsVisible}
        children={<MissingSectionsSheet setIsVisible={setIsVisible} list={missingSections} />}
      />
    </View>
  );
};

export default ProfileHeader;
