import { I18nManager, Platform, StyleSheet } from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import { Colors, Dimen } from '../../../src/theme';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import { lineHeight } from '@utils/constants';

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    header: {
      width: '100%',
      height: Dimen.height * 0.22,
      resizeMode: 'stretch',
      zIndex: 0,
      backgroundColor: theme.backgroundColor,
    },
    ipadAvatarStyle: {
      width: 130,
      height: 130,
      borderRadius: 130,
    },
    userImage: {
      width: 120,
      height: 120,
      borderColor: 'white',
      borderWidth: 5,
      borderRadius: 60,
      overflow: 'hidden',
    },
    cameraIcon: {
      width: 30,
      height: 30,
      resizeMode: 'contain',
      position: 'absolute',
      right: 2,
      bottom: 2,
    },
    editIcon: {
      width: 35,
      height: 35,
      marginTop: -50,
      resizeMode: 'contain',
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileView: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      borderTopRightRadius: 30,
      borderTopLeftRadius: 30,
      height: 100,
      zIndex: 0,
      marginTop: -40,
      backgroundColor: isDarkMode ? theme.bgDark : 'transparent',
    },
    avatarContainer: {
      top: Platform.OS == 'ios' ? -Dimen.height * 0.19 : -Dimen.height * 0.2,
      bottom: 0,
      overflow: 'visible',
      left: 20,
      width: 120,
    },
    nameSection: {
      backgroundColor: isDarkMode ? theme.bgDark : 'transparent',
      paddingStart: 20,
      marginTop: Dimen.height * -0.18,
    },
    name: {
      fontFamily: fonts.medium,
      fontSize: fontSize.xxlarge,
      textTransform: 'capitalize',
      color: theme.textColor,
      textAlign: 'left'
    },
    role: {
      fontFamily: fonts.thin,
      fontSize: isPad ? fontSize.xlarge : fontSize.medium,
      marginTop: Platform.OS == 'ios' ? 5 : 5,
      width: '93%',
      color: theme.textColor,
      textAlign: 'left'
    },
    experience: {
      marginTop: Platform.OS == 'ios' ? 8 : 0,
      fontFamily: fonts.medium,
      fontSize: isPad ? fontSize.semiMedium : fontSize.medium,
      opacity: 0.87,
      color: theme.textColor,
    },
    department: {
      fontFamily: fonts.medium,
      fontSize: isPad ? fontSize.medium : fontSize.small,
      opacity: 0.7,
      marginTop: 5,
      color: theme.textColor,
    },
    departnmentImage: {
      width: 19,
      height: 19,
      resizeMode: 'contain',
      marginRight: 8,
    },
    row: {
      marginTop: Platform.OS == 'android' ? -5 : 5,
      flexDirection: 'row',
      alignItems: 'center',
      width: '85%',
    },
    rowJustified: {
      marginTop: 10,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    activeBtnView: {
      marginStart: 20,
      flexDirection: 'row',
      alignItems: 'center',
      borderColor: Colors.black,
      borderWidth: 1,
      borderRadius: 30,
      paddingHorizontal: Platform.OS == 'android' ? 8 : 8,
      paddingVertical: Platform.OS == 'android' ? 2 : 8,
    },
    activeText: {
      color: Colors.black,
      fontFamily: fonts.medium,
      fontSize: fontSize.medium,
      marginStart: 1,
      paddingHorizontal: 5,
    },
    completionText: {
      fontSize: fontSize.medium,
      color: theme.textColor,
      fontFamily: fonts.regular,
    },
    percentage: {
      opacity: 0.7,
      fontSize: fontSize.medium,
      color: theme.textColor,
      fontFamily: fonts.medium,
    },
    profileCompletion: {
      backgroundColor: isDarkMode ? theme.bgDark : 'transparent',
      paddingHorizontal: 20,
      paddingTop: 10,
    },
    progressBar: {
      backgroundColor: Colors.progress,
      width: '100%',
      height: 10,
      marginTop: Platform.OS == 'ios' ? 10 : 5,
      borderRadius: 5,
      paddingBottom: 25
    },
    progressPercentage: {
      backgroundColor: isDarkMode ? "#eee" : 'rgba(30, 30, 30, 0.1)',
      width: '100%',
      height: 10,
      justifyContent: 'center',
      borderRadius: 5,
    },
    missingSections: {
      backgroundColor: isDarkMode ? theme.bgDark : 'transparent',
      paddingHorizontal: 20,
      paddingBottom: 15,
      flexDirection: 'row',
      alignItems: 'center',
    },
    info: {
      width: 15,
      height: 15,
      resizeMode: 'contain',
      marginRight: 8,
    },
    completeYourProfile: {
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium : fontSize.small,
      color: 'rgba(14, 121, 219, 1)',
    },
    nextImage: {
      width: 15,
      height: 15,
      resizeMode: 'contain',
    },
    experienceSection: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    noOfExp: {
      width: 16,
      height: 16,
      resizeMode: 'contain',
      marginRight: 8,
    },
    numOfExp: {
      color: 'rgba(86, 86, 86, 1)',
      fontFamily: fonts.regular,
      fontSize: fontSize.xxmini,
      lineHeight: lineHeight(fontSize.xxmini)
    },
    tabStyle: {
      backgroundColor: isDarkMode ? theme.backgroundColor : 'transparent',
      paddingHorizontal: 10,
      marginStart: (I18nManager.isRTL && Platform.OS == 'android') ? -10 : (I18nManager.isRTL && Platform.OS != 'android') ? 20 : 0,
    },
    avatarBorder: {},
    linearGradient: {
      borderRadius: 70,
      width: 115,
      height: 115,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 0,
    },
    innerContainer: {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      borderWidth: 10,
    },
    buttonText: {
      fontSize: 18,
      fontFamily: 'Gill Sans',
      fontWeight: 'bold',
      textAlign: 'center',
      margin: 10,
      color: '#cc2b5e',
      backgroundColor: 'transparent',
    },
    editContainer: {
      position: 'absolute',
      borderColor: Colors.borderGray,
      right: 0,
      bottom: 11,
      zIndex: 1000000000,
    },
    missingSectionText: {
      textTransform: 'capitalize',
      fontFamily: fonts.medium,
      fontSize: fontSize.semiMedium,
      marginVertical: 10,
    },
    icon: {
      width: 20,
      height: 20,
      resizeMode: 'contain',
      marginRight: 10,
      marginTop: 2,
    },
    sheetContainer: {
      paddingHorizontal: 20,
      paddingBottom: 60,
    },
    missingContainer: {
      borderBottomColor: '#f5f5f5',
      paddingTop: 15,
      borderBottomWidth: 1,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    loadingContainer: {
      borderColor: theme.textColor,
      borderWidth: 1,
      borderRadius: 20,
    },
    loading: {
      padding: 2,
    },
  });

  return { styles, theme, isDarkMode };
};

export default useStyles;
