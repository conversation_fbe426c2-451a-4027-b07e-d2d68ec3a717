import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Image, ImageBackground, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { AssetsImages, Colors, Images } from '../../theme';
import { AvatarOverlap } from '../../components';
import { lineHeight } from '@utils/constants';
type props = {
  index: number;
  item: Record<string, number | string>;
  tag?: boolean;
};

const ProgramCard = ({ item, index, tag }: props) => {
  return (
    <Pressable onPress={() => { }} key={index} style={styles.itemView}>
      <ImageBackground
        source={{ uri: item?.image }}
        style={styles.imageView}
        imageStyle={styles.image}
      >
        <View
          style={{
            paddingVertical: 20,
            paddingHorizontal: 20,
            flexDirection: 'column',
          }}
        >
          <TextView numberOfLines={3} style={styles.courseHeading} text={item?.title} />
          <TextView
            numberOfLines={3}
            style={styles.durationTextStyle}
            text={'Duration: 4 months'}
          />
          <View style={styles.avatarContainer}>
            <AvatarOverlap images={[Images.face1, Images.face2]} />

            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                paddingStart: 8,
              }}
            >
              <TextView numberOfLines={3} style={styles.peopleViewTextStyle} text={'2.4k people'} />
            </View>
          </View>
          <View style={styles.academyContainer}>
            <Image
              key={index}
              source={AssetsImages?.companyLogo}
              style={styles.academyImageStyle}
            />
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                paddingStart: 8,
              }}
            >
              <TextView numberOfLines={3} style={styles.peopleViewTextStyle} text={'Gov Academy'} />
            </View>
          </View>
        </View>
      </ImageBackground>
    </Pressable>
  );
};

export default ProgramCard;

const styles = StyleSheet.create({
  itemView: {
    width: 300,
    marginRight: 15,
    overflow: 'hidden',
  },
  imageView: {
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    height: 220,
  },
  image: {
    borderRadius: 20,
  },
  courseHeading: {
    color: Colors.white,
    fontSize: fontSize.xxxlarge,
    fontFamily: fonts.medium,
    height: 80,
    lineHeight: lineHeight(fontSize.xxxlarge)

  },
  durationTextStyle: {
    paddingVertical: 5,
    color: Colors.white,
    fontSize: fontSize.semiSmall,
    fontFamily: fonts.medium,
    lineHeight: lineHeight(fontSize.semiSmall)

  },
  peopleViewTextStyle: {
    color: Colors.white,
    fontSize: fontSize.semiSmall,
    fontFamily: fonts.medium,
    lineHeight: lineHeight(fontSize.semiSmall)

  },
  avatarContainer: {
    flexDirection: 'row',
  },

  academyContainer: {
    flexDirection: 'row',
    paddingTop: 20,
  },

  avatar: {
    marginLeft: 0,
    height: 60,
    width: 60,
    borderRadius: 50,
    backgroundColor: '#f5f5f5',
  },
  academyImageStyle: {
    width: 28,
    height: 28,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: Colors.grayline,
  },
});
