import React, { useEffect, useRef, useState } from 'react';
import { Alert, Animated, Image, Platform, Pressable, StyleSheet, View } from 'react-native';
import crashReportLogger from '@utils/crashlyticsLogger';
import { useTranslation } from 'react-i18next';
import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  AVModeIOSOption,
} from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import { useAppState } from '../../hooks';
import usePermissions, { REQUIRED_PERMISSIONS } from '../../hooks/usePermissions';
import { AssetsImages } from '../../theme';
import { useTheme } from '../../theme/ThemeProvider';
import { formatTime, getName } from '../../utils/AppUtils';
import { Spacer, TextView } from '../index';
import PlayRecording from './PlayRecording';

const audioSet = {
  AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
  AudioSourceAndroid: AudioSourceAndroidType.MIC,
  AVModeIOS: AVModeIOSOption.measurement,
  AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
  AVNumberOfChannelsKeyIOS: 2,
  AVFormatIDKeyIOS: AVEncodingOption.aac,
};
const meteringEnabled = false;

let interval: NodeJS.Timeout;

const AudioRecord = ({ uri, setUri, onResetCallback }) => {
  const { t } = useTranslation();
  const appStateVisible = useAppState();
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const [isRecorded, setRecording] = useState(false);
  const [timer, setTimer] = useState(0);
  const permissionRef = useRef(false);
  const [recordingStart, setRecordingStart] = useState(false);
  const { isPermissionEnabled, requestMultiplePermission } = usePermissions();
  const { theme } = useTheme();

  const onPress = async () => {
    if (recordingStart) {
      onStopRecording();
      return;
    }

    if (isPermissionEnabled) {
      permissionRef.current = true;
      onStartRecording();
    } else {
      const status = await requestMultiplePermission(
        Platform.OS == 'ios' ? REQUIRED_PERMISSIONS.audio.ios : REQUIRED_PERMISSIONS.audio.android,
      );
      if (!status) {
        permissionRef.current = false;
        return;
      }

      permissionRef.current = true;
      onStartRecording();
    }
  };

  const onStartRecording = async () => {
    if (!permissionRef.current) {
      return;
    }

    try {
      const audioPath = Platform.select({
        ios: getName(6) + '.m4a',
        android: `${RNFS.DocumentDirectoryPath}/${getName(6)}.m4a`,
      });

      await audioRecorderPlayer.startRecorder(audioPath, audioSet, meteringEnabled);

      setRecordingStart(true);

      if (interval != null) clearInterval(interval);

      interval = setInterval(() => {
        setTimer((s) => s + 1);
      }, 1000);
    } catch (e) {
      crashReportLogger(e as Error, {
        component: 'AudioRecord.tsx onStartRecording',
        additionalInfo: 'Failed to start recording',
      });
      if (interval != null) clearInterval(interval);
    }
  };

  const onStopRecording = async () => {
    if (!permissionRef.current) {
      return;
    }

    if (interval != null) {
      clearInterval(interval);
      const result = await audioRecorderPlayer.stopRecorder();

      if (Platform.OS == 'ios') {
        await audioRecorderPlayer.startPlayer();
      }

      setUri(result);
      setRecordingStart(false);
      setRecording(true);
      setTimer(0);
    }
  };

  const onReset = () => {
    Alert.alert(t('Alert'), t('Are you sure you want to remove audio?'), [
      {
        text: t('Cancel'),
        onPress: () => {},
      },
      {
        text: t('Ok'),
        onPress: () => {
          setRecording(false);
          setUri('');
          onResetCallback();
        },
      },
    ]);
  };

  useEffect(() => {
    if (uri) {
      setRecordingStart(false);
      setRecording(true);
    }
  }, [uri]);

  useEffect(() => {
    if (
      (recordingStart && timer >= 30) ||
      appStateVisible == 'inactive' ||
      appStateVisible == 'background'
    ) {
      if (recordingStart && timer >= 30) {
        Alert.alert(t('Your recording has exceeded the 30-second limit.'));
        onStopRecording();
      }
    }
  }, [timer, appStateVisible]);

  const animatedStyle = { transform: [{ scale: scaleValue }] };

  return (
    <View style={styles.container}>
      {isRecorded ? (
        <PlayRecording {...{ uri, onReset }} />
      ) : (
        <>
          <Pressable onPress={onPress}>
            <Animated.View style={[styles.imageContainer, animatedStyle]}>
              <Image
                source={recordingStart ? AssetsImages.stop_audio : AssetsImages.mic}
                style={styles.mic}
              />
            </Animated.View>
          </Pressable>
          <Spacer height={12} />
          <TextView
            text={timer == 0 ? 'Press to start recording' : formatTime(timer)}
            style={[styles.text, { color: theme.textColor }]}
          />
        </>
      )}
    </View>
  );
};

export default AudioRecord;

const styles = StyleSheet.create({
  container: {
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },

  mic: { height: 25, width: 25, resizeMode: 'contain' },

  text: {},

  imageContainer: {
    overflow: 'hidden',
    backgroundColor: '#fff',
    borderRadius: 50,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
  },
});
