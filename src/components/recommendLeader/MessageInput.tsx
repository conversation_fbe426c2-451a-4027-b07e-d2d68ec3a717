import React from 'react';
import { Keyboard, StyleSheet, TextInput, View } from 'react-native';
import { TextView } from '../common';
import { Colors, fonts } from '../../theme';
import { Spacer } from '../index';
import { fontFamily } from '../../utils/AppUtils';
import { useTheme } from '../../theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { I18nManager } from 'react-native';

type MessageInputPros = {
  value: string;
  onChange: (value: string) => void;
};

const MessageInput = ({ value, onChange }: MessageInputPros) => {
  const { theme, isDarkMode } = useTheme();
  const { t } = useTranslation();
  return (
    <View style={styles.container}>
      <Spacer height={15} />
      <TextView text={'Message'} style={[styles.text, isDarkMode && { color: theme.textColor }]} />
      <Spacer height={8} />
      <TextInput
        placeholder={t('Explain the brief about the person')}
        placeholderTextColor={theme.placeholderText}
        multiline
        value={value}
        blurOnSubmit={true}
        style={[
          styles.input,
          isDarkMode && styles.darkModeInput,
          { writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr', color: theme.textColor },

        ]}
        textAlignVertical="top"
        onChangeText={onChange}
        returnKeyType="done"
        returnKeyLabel="done"
        maxLength={500}
        onSubmitEditing={() => {
          Keyboard.dismiss();
        }}
      />
    </View>
  );
};

export default MessageInput;

const styles = StyleSheet.create({
  text: {
    fontWeight: '500',
    fontFamily: fonts.bold,
    color: '#212529',
    opacity: 0.7,
    fontSize: 14,
    textAlign: 'left',
  },
  container: {
    marginHorizontal: 20,
  },
  input: {
    height: 100,
    backgroundColor: Colors.white,
    color: Colors.black,
    padding: 10,
    borderRadius: 8,
    fontFamily: fontFamily('BLACKI-TALIC'),
    fontWeight: '400',
  },
  darkModeInput: {
    backgroundColor: 'transparent',
    borderWidth: 0.3,
    borderColor: Colors.borderGray,
  },
});
