/* eslint-disable react/display-name */
import React, { memo, useEffect, useState } from 'react';
import { Image, Pressable, StyleSheet, View } from 'react-native';
import Slider from '@react-native-community/slider';
import crashReportLogger from '@utils/crashlyticsLogger';
import Sound from 'react-native-sound';
import { AssetsImages, Colors, Icons } from '../../theme';
import { IconsType } from '../../theme/Icons';
import { fontFamily } from '../../utils/AppUtils';
import { Spacer, TextView } from '../index';

Sound.setCategory('Playback');

const getMMSSFromMillis = (millis: number) => {
  const totalSeconds = millis;
  const seconds = Math.floor(totalSeconds % 60);
  const minutes = Math.floor(totalSeconds / 60);

  const padWithZero = (number: number) => {
    const string = number.toString();
    if (number < 10) {
      return '0' + string;
    }
    return string;
  };
  return padWithZero(minutes) + ':' + padWith<PERSON>ero(seconds);
};

type PlayingRecordingProps = { uri: string; onReset: () => void };

let interval: NodeJS.Timeout;

const PlayRecording = memo(({ uri, onReset }: PlayingRecordingProps) => {
  const [sliderValue, setSliderValue] = useState(0);
  const [isPlaying, setPlaying] = useState(false);
  const [soundPlayer, setSound] = useState<Sound | undefined>(undefined);
  let sliderEditing = false;

  useEffect(() => {
    setupPlayer();

    return () => {
      soundPlayer?.release();
      if (interval) clearInterval(interval);
    };
  }, []);

  const setupPlayer = () => {
    const mySound = new Sound(uri, '', (error) => {
      if (error) {
        crashReportLogger(error as Error, {
          component: 'PlayRecording setupPlayer',
          additionalInfo: 'Failed to load the sound',
        });
        return;
      }
      setSound(mySound);
    });
  };

  const onFinished = (success: boolean) => {
    if (success) {
      setPlaying(false);
      if (interval) clearInterval(interval);
      setSliderValue(0);
    }
  };

  const onPlayRecording = () => {
    if (isPlaying) {
      if (interval) clearInterval(interval);
      soundPlayer?.pause();
      setPlaying(false);
    } else {
      setPlaying(true);
      soundPlayer?.play(onFinished);
      interval = setInterval(updateSlider, 100);
    }
  };

  const updateSlider = () => {
    if (soundPlayer && !sliderEditing) {
      soundPlayer.getCurrentTime((currentTime) => {
        setSliderValue(currentTime);
      });
    }
  };

  const onSliderEditStart = () => {
    sliderEditing = true;
  };

  const onSliderEditEnd = () => {
    sliderEditing = false;
  };

  const onSliderEditing = (value: number) => {
    if (soundPlayer) {
      soundPlayer.setCurrentTime(value);
    }
  };

  const onResetPress = () => {
    soundPlayer?.release();
    onReset();
  };

  return (
    <>
      <View style={styles.playContainer}>
        <Pressable onPress={onPlayRecording}>
          <Image
            source={isPlaying ? AssetsImages.pause : AssetsImages.play}
            style={styles.pauseImage}
          />
        </Pressable>

        <Pressable
          // onTouchStart={onSliderEditStart}
          // onTouchEnd={onSliderEditEnd}
          style={{ width: '73%' }}
        >
          <Slider
            style={{ height: 80, marginHorizontal: 5 }}
            minimumValue={0}
            step={0.1}
            tapToSeek={true}
            maximumValue={soundPlayer?.getDuration() || 1}
            value={sliderValue}
            minimumTrackTintColor={Colors.black}
            maximumTrackTintColor={Colors.black}
            thumbTintColor={Colors.black}
            onValueChange={onSliderEditing}
            onSlidingStart={onSliderEditStart}
            onSlidingComplete={onSliderEditEnd}
          />
        </Pressable>

        <View style={styles.dotRed} />
        <TextView text={getMMSSFromMillis(sliderValue)} style={styles.text} />
      </View>
      <Spacer height={10} />

      <Pressable style={styles.resetConainter} onPress={onResetPress}>
        <Icons type={IconsType.Ionicons} name={'reload'} size={16} />
        <TextView text={'Reset'} style={styles.reset} />
      </Pressable>
    </>
  );
});

export default PlayRecording;

const styles = StyleSheet.create({
  playContainer: {
    borderRadius: 16,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    justifyContent: 'space-between',
    marginHorizontal: 20,
    height: 70,
  },

  pauseImage: { height: 38, width: 38, marginTop: -3 },

  text: { width: 50, textAlign: 'center' },

  reset: {
    marginLeft: 5,
    fontSize: 14,
    fontFamily: fontFamily('BLACKI-TALIC'),
    fontWeight: '400',
  },

  resetConainter: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginRight: 20,
  },
  dotRed: {
    width: 7,
    height: 7,
    borderRadius: 7 / 2,
    backgroundColor: '#FA3B33',
  },
});
