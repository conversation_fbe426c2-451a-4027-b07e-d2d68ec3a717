import { IconsType } from '../../theme/Icons';
import { Colors, fonts, fontSize, Icons } from '../../theme';
import React, { useEffect, useState } from 'react';
import { Image, ImageSourcePropType, Platform, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '../common';
import { fontFamily } from '../../utils/AppUtils';
import { useDispatch } from 'react-redux';
import { useTheme } from '../../theme/ThemeProvider';
import { I18nManager } from 'react-native';
import { lineHeight } from '@utils/constants';

type RecommendItemType = {
  icon: ImageSourcePropType;
  maxHeight: number;
  heading: string;
  subHeading: string;
  children: JSX.Element;
  expended?: boolean;
  onCallback?: () => void;
  shouldActive?: boolean;
};

const RecommendItem = ({
  icon,
  maxHeight,
  heading,
  subHeading,
  children,
  expended = true,
  onCallback,
  shouldActive = false,
  isActive,
  setActive,
}: RecommendItemType) => {
  const { theme, isDarkMode } = useTheme();

  useEffect(() => {
    if (shouldActive) {
      setActive(true);
    }
  }, [shouldActive]);

  const onPress = () => {
    if (expended) {
      setActive(!isActive);
    } else {
      onCallback && onCallback();
    }
  };
  return (
    <Pressable
      style={[
        styles.container,
        {
          backgroundColor: isActive ? 'rgba(209, 224, 241, 1)' : '#E9F1FA',
        },
        isDarkMode && { backgroundColor: theme.bgDark },
      ]}
      onPress={onPress}
    >
      <View style={styles.row}>
        <Image source={icon} resizeMode="contain" style={styles.iconStyle} />

        <View style={styles.subCon}>
          <TextView
            text={heading}
            style={[
              styles.text,
              { color: theme.textColor },
              I18nManager.isRTL && {
                marginTop: -4,
              },
            ]}
          />
          <TextView
            text={subHeading}
            style={[
              styles.subText,
              { color: theme.textColor },
              I18nManager.isRTL && {
                fontSize: Platform.OS == 'ios' ? fontSize.medium : fontSize.small,
              },
            ]}
          />
        </View>
      </View>
      {isActive && children}
    </Pressable>
  );
};

export default RecommendItem;

const styles = StyleSheet.create({
  icon: { marginStart: 10 },
  text: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: fonts.medium,
    textAlign: 'left',
    textTransform: 'capitalize',
  },
  subText: {
    fontSize: 12,
    color: Colors.lightgray,
    fontFamily: fonts.medium,
    opacity: 0.7,
    textAlign: 'left',
    textTransform: 'capitalize',
    marginTop: Platform.OS == 'android' ? 1 : 3,
  },
  container: {
    // backgroundColor: 'rgba(233, 226, 221, 0.50)',
    backgroundColor: '#E9F1FA',
    marginHorizontal: 18,
    borderRadius: 12,
    paddingVertical: 13,
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconStyle: { width: 43, height: 43, marginLeft: 20 },

  subCon: { flex: 1, marginLeft: 15 },
});
