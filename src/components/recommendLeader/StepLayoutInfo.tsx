import React, { useEffect, useState } from 'react';
import { I18nManager, StyleSheet, Switch, View } from 'react-native';
import { EditText, Spacer, TextView } from '../index';
import { AssetsImages, Colors, fonts, fontSize } from '../../theme';
import Button from '../common/Button';
import { LabelConfig } from '../../theme/labelConfig';
import { dispatchRLBio } from '../../totara/reducers/recommendedLeaderReducer';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../totara/reducers';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import commonStyle from '../../utils/commonStyle';
import { isPad } from '@utils/fontSize';
import { useTheme } from '../../theme/ThemeProvider';
import { t } from 'i18next';

type StepLayoutInfoType = {
  onGoNext: () => void;
  isLoadingApi: boolean;
};

const validationSchema = Yup.object().shape({
  occupation: Yup.string(),
  talentName: Yup.string().required(t('Name')),
  phone: Yup.number().typeError(t('Invalid phone number')).required(t('Phone number')),
  linkedin: Yup.string()
    .url(t('Invalid URL'))
    .matches(/^(https?:\/\/)?(www\.)?linkedin\.com\/.*$/i, t('Invalid LinkedIn URL')),
});

const StepLayoutInfo = ({ isLoadingApi, onGoNext }: StepLayoutInfoType) => {
  const { theme, isDarkMode } = useTheme();
  const { rlBio }: any = useSelector((state: RootState) => state.recommendedLeaderReducer);
  const dispatch = useDispatch();
  const [isAnonyomous, setAnonyomous] = useState(false);
  const [countryCode, setCountryCode] = useState('971');

  const { values, errors, touched, setFieldValue, handleSubmit } = useFormik({
    initialValues: {
      occupation: '',
      talentName: '',
      phone: '',
      linkedin: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      onGoNext();
    },
  });

  const onChange = () => setAnonyomous(!isAnonyomous);

  const onChangeText = (key, value) => {
    setFieldValue(key, value);
    dispatchRLBio(dispatch, { ...values, [key]: value });
  };

  useEffect(() => {
    if (rlBio && Object.keys(rlBio).length > 0) {
      Object.keys(rlBio).forEach((key) => {
        setFieldValue(key, rlBio[key]);
      });
    }
  }, []);

  const {
    pleaseProvideTalentInformation,
    occupation,
    phoneNumber,
    KeepmeAnonymous,
    SubmitRecommendation,
    linkedIn,
    linkedInURL,
  } = LabelConfig.recommandLeader;

  return (
    <KeyboardAwareScrollView
      keyboardShouldPersistTaps={'always'}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <View style={{ flex: 1 }}>
        <TextView
          text={pleaseProvideTalentInformation}
          style={[styles.heading, { color: theme.textColor }]}
        />

        <Spacer height={20} />

        <EditText
          required
          label={'Name'}
          imageTestId="img-email"
          placeholder={'Name'}
          value={values?.talentName}
          placeholderTextColor={Colors.placeholderColor}
          labelStyle={styles.label}
          image={AssetsImages.userprofile}
          iconStyle={styles.image}
          onChange={(text) => onChangeText('talentName', text)}
          helperText={touched?.talentName && errors?.talentName ? errors?.talentName : ''}
          helperTextColor={isDarkMode ? Colors.notificationRedForDark : 'red'}
        />

        <Spacer height={20} />

        <EditText
          imageTestId="img-occupation"
          label={occupation}
          placeholder={occupation}
          value={values?.occupation}
          image={AssetsImages.whitePencil}
          labelStyle={styles.label}
          placeholderTextColor={Colors.placeholderColor}
          iconStyle={styles.image}
          onChange={(text) => onChangeText('occupation', text)}
        />
        <Spacer height={20} />

        <EditText
          required
          imageTestId="img-phonenumber"
          label={phoneNumber}
          editable
          maxLength={15}
          // codeCountry={countryCode}
          value={values?.phone}
          // getCountryCode={getCountryCode}
          placeholder={phoneNumber}
          labelStyle={styles.label}
          placeholderTextColor={Colors.placeholderColor}
          image={AssetsImages.mobile}
          keyboardType="numeric"
          iconStyle={styles.image}
          onChange={(text) => onChangeText('phone', text)}
          helperText={touched?.phone && errors?.phone ? errors?.phone : ''}
          helperTextColor={isDarkMode ? Colors.notificationRedForDark : 'red'}
        />
        <Spacer height={20} />

        <EditText
          imageTestId="img-linkedin"
          label={linkedIn}
          value={values?.linkedin}
          placeholderTextColor={Colors.placeholderColor}
          labelStyle={styles.label}
          placeholder={
            I18nManager.isRTL ? 'السابق: https://www.linkedin.com' : 'ex: https://www.linkedin.com/'
          }
          iconStyle={styles.image}
          onChange={(text) => onChangeText('linkedin', text)}
          helperText={touched?.linkedin && errors?.linkedin ? errors?.linkedin : ''}
        />

        <Button
          type="fill"
          text={SubmitRecommendation}
          isLoading={isLoadingApi}
          style={[{
            backgroundColor:
              isDarkMode ?
                Colors.activeStateDark : 'black'
          }, styles.btn]}
          textStyle={[{
            color: isDarkMode ? 'black' : 'white',
            textTransform: 'capitalize'
          }]}
          onPress={handleSubmit}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default StepLayoutInfo;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  heading: {
    marginStart: 18,
    fontSize: isPad ? fontSize.xxxlarge : fontSize.xlarge,
    color: Colors.lightgray,
    textAlign: 'left',
    fontFamily: fonts.medium,
  },
  label: {
    fontSize: 12,
    fontFamily: fonts.regular,
    textAlign: 'left',
  },
  row: {
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
  },
  text: {
    fontSize: 14,
    color: Colors.black,
    fontFamily: fonts.medium,
    flex: 1,
    textAlign: 'left',
  },
  btn: {
    marginBottom: 20,
    marginTop: 25,
  },

  image: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
});
