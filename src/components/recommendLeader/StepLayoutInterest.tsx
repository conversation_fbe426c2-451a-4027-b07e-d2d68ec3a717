import React, { useEffect, useState } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import { NoRecordsTextView, Spacer, TextView } from '../index';
// import {fontFamily} from '../../utils/AppUtils';
import { Colors, fonts } from '../../theme';
import { ScrollView } from 'react-native-gesture-handler';
import { Button, ThemeBottomButton } from '../common';
import { LabelConfig } from '../../theme/labelConfig';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../totara/reducers';
import { dispatchRLGoodFit } from '../../totara/reducers/recommendedLeaderReducer';
import { getPickList } from '../../api/picklistApi';
import { lineHeight, lineHeightForAndroidLTRLarge, lineHeightForAndroidLTRMed, pickListType } from '../../utils/constants';
import { BEARER_TOKEN } from '../../api/urls';
import { ActivityIndicator } from 'react-native-paper';
import { isPad } from '@utils/fontSize';
import { useTheme } from '../../theme/ThemeProvider';
import { I18nManager } from 'react-native';
import { useTranslation } from 'react-i18next';

type StepLayoutInterestProps = {
  onGoNext: () => void;
  isLoadingApi: boolean;
  hideStepLayoutInfo: boolean;
};

const StepLayoutInterest = ({
  isLoadingApi,
  onGoNext,
  hideStepLayoutInfo,
}: StepLayoutInterestProps) => {
  const { Wheredoyouseethispersonagoodfit, NoteSelectManyResponses, next } =
    LabelConfig.recommandLeader;
  const { theme, isDarkMode } = useTheme();
  const [isLoading, setLoading] = useState(true);
  const [list, setList] = useState([]);
  const [noData, setNodata] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const { rlGoodFit }: any = useSelector((state: RootState) => state.recommendedLeaderReducer);

  useEffect(() => {
    getLists();
  }, []);

  const getLists = async () => {
    setLoading(true);
    const result = await getPickList(token, pickListType.recommendInterest);

    if (result?.length > 0) {
      const nList = result?.map((it) => {
        return it?.name;
      });
      setList(nList);
    } else {
      setNodata(true);
    }
    setLoading(false);
  };

  const { SubmitRecommendation } = LabelConfig.recommandLeader;
  const { t } = useTranslation();

  return (
    <View style={{ flex: 1 }}>
      {isLoading ? (
        <ActivityIndicator style={styles.loader} color={Colors.black} />
      ) : (
        <ScrollView style={{ paddingHorizontal: 5, marginBottom: 100 }}>
          <TextView
            text={Wheredoyouseethispersonagoodfit}
            style={[styles.heading, isDarkMode && { color: theme.textColor }, lineHeightForAndroidLTRLarge]}
          />
          <Spacer height={15} />

          <TextView
            text={NoteSelectManyResponses}
            style={[styles.note, isDarkMode && { color: theme.textColor }]}
          />
          <Spacer height={20} />

          <View style={styles.row}>
            {noData ? (
              <NoRecordsTextView
                textStyle={{ textAlign: 'center' }}
                containerStyles={{ paddingHorizontal: 24 }}
              />
            ) : (
              list?.map((item, i: number) => {
                return <InterestItem {...{ item, i }} />;
              })
            )}
          </View>
        </ScrollView>
      )}
      <ThemeBottomButton
        isLoading={isLoadingApi}
        isDisabled={rlGoodFit.length == 0 ? true : false}
        heading={hideStepLayoutInfo ? SubmitRecommendation : next}
        onPress={onGoNext}
      />

      {/* <Button
        type="fill"
        isDisabled={rlGoodFit.length == 0 ? true : false}
        text={hideStepLayoutInfo ? SubmitRecommendation : next}
        isLoading={isLoadingApi}
        // style={styles.btn}
        style={{
          backgroundColor: rlGoodFit.length == 0 ? (isDarkMode ? theme.bgLightDark : Colors.disabledBtn) :
            isDarkMode ?
              Colors.activeStateDark : 'black'
        }}
        onPress={onGoNext}
      /> */}
    </View>
  );
};

export default StepLayoutInterest;

type InterestItemType = {
  i: number;
  item: string;
};

const InterestItem = ({ i, item }: InterestItemType) => {
  const dispatch = useDispatch();
  const { theme, isDarkMode } = useTheme();
  const [selected, setSelected] = useState<boolean>(false);
  const { rlGoodFit }: any = useSelector((state: RootState) => state.recommendedLeaderReducer);

  useEffect(() => {
    if (rlGoodFit?.length > 0) {
      setSelected(rlGoodFit?.includes(item));
    }
  }, []);

  const onPress = () => {
    let goodFits: any = rlGoodFit?.slice() ?? [];
    const isSelected = selected;
    if (isSelected) {
      goodFits = rlGoodFit?.filter((it) => it != item);
    } else {
      goodFits.push(item);
    }
    setSelected(!isSelected);
    dispatchRLGoodFit(dispatch, goodFits);
  };

  return (
    <Pressable
      key={i}
      style={[
        styles.interesItem,
        {
          backgroundColor: isDarkMode ? (!selected ? theme.chips : Colors.activeStateDark) : selected ? theme.chips : Colors.white,
        },
      ]}
      onPress={onPress}
    >
      <TextView
        text={item}
        isTranslated={true}
        style={[
          styles.text,
          { color: isDarkMode ? (!selected ? Colors.white : Colors.black) : selected ? Colors.white : Colors.black },
        ]}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  heading: {
    marginStart: 18,
    fontSize: 22,
    color: Colors.lightgray,
    textAlign: 'left',
    fontFamily: fonts.medium,
  },
  note: {
    fontSize: 14,
    color: Colors.lightHeading,
    marginStart: 18,
    textAlign: 'left',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: 18,
    marginBottom: 15,
  },
  interesItem: {
    borderRadius: 50,
    borderWidth: 1,
    borderColor: Colors.interestItem,
    paddingHorizontal: 12,
    paddingVertical: 7,
    marginRight: 5,
    marginTop: 15,
    justifyContent: 'center',
  },
  btn: {
    marginBottom: isPad ? 24 : 10,
  },
  text: {
    fontSize: 14,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },

  loader: { justifyContent: 'center', flex: 1 },
});
