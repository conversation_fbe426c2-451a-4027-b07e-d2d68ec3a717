import React, { memo, useEffect, useRef, useState } from 'react';
import { Alert, Image, Platform, Pressable, StyleSheet, Switch, View } from 'react-native';
import {
  AudioRecord,
  MessageInput,
  RecommendItem,
  EditText,
  Spacer,
  SuggestionsList,
  TextView,
  ThemeBottomButton,
} from '../index';
import { AssetsImages, Colors, Icons, fontSize, fonts } from '../../theme';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { ImagePickerResponse, launchCamera } from 'react-native-image-picker';
import Video, { LoadError } from 'react-native-video';
import { IconsType } from '../../theme/Icons';
import Button from '../common/Button';
import { LabelConfig } from '../../theme/labelConfig';
import { useReferCourse } from '../../hooks';
import { useDispatch, useSelector } from 'react-redux';
import { dispatchRLName } from '../../totara/reducers/recommendedLeaderReducer';
import { RootState } from '../../totara/reducers';
import usePermissions, { REQUIRED_PERMISSIONS } from '../../hooks/usePermissions';
import { isPad } from '@utils/fontSize';
import { useTheme } from '../../theme/ThemeProvider';
import { I18nManager } from 'react-native';
import commonStyle from '@utils/commonStyle';
import { useTranslation } from 'react-i18next';

type StepLayoutInfoProps = {
  onGoNext: () => void;
  showStepLayoutInfo: React.Dispatch<React.SetStateAction<boolean>>;
};

const StepLayoutName = ({ onGoNext, showStepLayoutInfo }: StepLayoutInfoProps) => {
  const { t } = useTranslation();
  const { theme, isDarkMode } = useTheme();
  const { isPermissionEnabled, requestMultiplePermission } = usePermissions();
  const dispatch = useDispatch();
  const [inbox, setInbox] = useState('');
  const [uri, setUri] = useState('');
  const [isResetLoading, setResetLoading] = useState(true);
  const [isActive, setActive] = useState(false);
  const [activeTab, setActiveTab] = useState(-1);
  const [videoData, setVideo] = useState<ImagePickerResponse | undefined>(undefined);
  const scrollRef = useRef(null);
  const [isAnonyomous, setAnonyomous] = useState(false);

  const onScroll = () => {
    setTimeout(() => {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }, 100);
  };
  const onPressVideo = async () => {
    if (isPermissionEnabled) {
      onCameraLunch();
    } else {
      const status = await requestMultiplePermission(
        Platform.OS == 'ios' ? REQUIRED_PERMISSIONS.video.ios : REQUIRED_PERMISSIONS.video.android,
      );
      if (!status) {
        return;
      }

      onCameraLunch();
    }
  };

  const onCameraLunch = async () => {
    if (videoData) {
      setActiveTab(3);
      return;
    }
    const result = await launchCamera({
      mediaType: 'video',
      videoQuality: 'medium',
      durationLimit: 30,
    });
    if (!result?.didCancel) {
      setVideo(result);
      setActiveTab(3);
    }
  };

  const onChange = (text: string) => setInbox(text);

  const onPressNext = async () => {
    /**
     * If selectedEmail undefined, fetches record from API and creates payload if found.
     * If selectedEmail defined, creates payload directly. and go to next step.
     */
    if (!validate(email) && !selectedEmail) {
      Alert.alert(t('Please enter a valid email address'), '', [
        {
          text: t('Ok'),
          onPress: () => { },
        },
      ]);
      return;
    }

    if (global.liferayUser?.email?.toUpperCase() == email?.toUpperCase()) {
      Alert.alert(t('You cannot use your own email address for recommendation.'), '', [
        {
          text: t('Ok'),
          onPress: () => { },
        },
      ]);

      return;
    }

    let payload = {};
    if (!selectedEmail) {
      onSearchUserByEmail(email, false).then((list) => {
        if (list.length > 0) {
          const item = list[0];
          showStepLayoutInfo(true);
          setUsername(item?.name);
          setSelectedUsername(item?.email);
          payload = {
            email: email,
          };
          createPayLoad(payload);
        } else {
          payload = {
            email: email,
          };
          showStepLayoutInfo(false);
          createPayLoad(payload);
        }
      });
    } else {
      payload = {
        email: selectedEmail ? selectedEmail : email,
      };
      createPayLoad(payload);
    }
  };

  const createPayLoad = (payload) => {
    if (inbox) payload['inbox'] = inbox;
    if (uri) payload['audio'] = uri;
    if (videoData) payload['video'] = videoData;
    if (isAnonyomous) payload['isAnonyomous'] = true;
    dispatchRLName(dispatch, payload);
    onGoNext();
  };

  const {
    username: email,
    selectedUsername: selectedEmail,
    usernameLoading: emailLoading,
    visible,
    suggestions,
    onSearchUserByEmail,
    setVisible,
    setUsername,
    setSelectedUsername,
    onUserCrossIconPressEmail,
  } = useReferCourse();

  const { rlName }: any = useSelector((state: RootState) => state.recommendedLeaderReducer);

  useEffect(() => {
    if (rlName && Object.keys(rlName).length > 0) {
      if (rlName?.email) setUsername(rlName?.email);
      if (rlName?.video) setVideo(rlName?.video);
      if (rlName?.audio) setUri(rlName?.audio);
      if (rlName?.inbox) setInbox(rlName?.inbox);
      if (rlName?.isAnonyomous) setAnonyomous(true);
    }
  }, []);

  const validate = (email) => {
    const expression = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return expression.test(String(email).toLowerCase());
  };

  const onResetCallback = () => {
    setResetLoading(false);

    setTimeout(() => {
      setResetLoading(true);
    }, 500);
  };

  const {
    whoAreYouRecommending,
    pleaseEnterName,
    whyYouAreRecommendingthisPerson,
    writeaMessage,
    ExplaintheBriefAboutthePerson,
    RecordVoice,
    ExplaintheBriefAboutthePersonbyVoice,
    RecordVideo,
    explainthebriefaboutthepersonbyVideo,
    next,
    KeepmeAnonymous,
  } = LabelConfig.recommandLeader;

  return (
    <View style={{ flex: 1 }}>
      <KeyboardAwareScrollView ref={scrollRef} contentContainerStyle={{ marginBottom: 60 }}>
        <>
          <EditText
            imageTestId="userpofile"
            label={whoAreYouRecommending}
            image={AssetsImages.emailIcon}
            placeholder={'Please enter email address'}
            labelStyle={{ opacity: 0.7 }}
            value={email}
            placeholderTextColor={Colors.placeholderColor}
            rightIconLoading={emailLoading}
            onChange={async (txt) => {
              setUsername(txt);
              if (txt == '') setVisible(false);
            }}
          />

          {/* <CaseSensitive /> */}
          {suggestions?.length > 0 && visible && (
            <View style={styles.suggestionContainer}>
              <SuggestionsList
                suggestions={suggestions}
                onUserItemPress={(item) => {
                  showStepLayoutInfo(true);
                  setUsername(item?.name);
                  setVisible(false);
                  setTimeout(() => {
                    setSelectedUsername(item?.email);
                  }, 0);
                }}
              />
            </View>
          )}

          <Spacer height={30} />

          <TextView
            text={whyYouAreRecommendingthisPerson}
            style={[styles.heading, isDarkMode && { color: theme.textColor }]}
          />
          <Spacer height={20} />

          <RecommendItem
            icon={AssetsImages.ic_inbox}
            maxHeight={250}
            expended={false}
            heading={writeaMessage}
            subHeading={ExplaintheBriefAboutthePerson}
            {...{ isActive: activeTab == 1 ? true : false, setActive }}
            onCallback={() => {
              setActiveTab(1);
            }}
          >
            <MessageInput {...{ onChange, value: inbox }} />
          </RecommendItem>
          <Spacer height={10} />

          <RecommendItem
            icon={AssetsImages.ic_mic}
            maxHeight={190}
            expended={false}
            heading={RecordVoice}
            subHeading={ExplaintheBriefAboutthePersonbyVoice}
            {...{ isActive: activeTab == 2 ? true : false, setActive }}
            onCallback={() => {
              setActiveTab(2);
            }}
          >
            {isResetLoading ? <AudioRecord {...{ uri, setUri, onResetCallback }} /> : <View />}
          </RecommendItem>
          <Spacer height={10} />

          <RecommendItem
            icon={AssetsImages.ic_video}
            maxHeight={videoData == undefined ? 70 : 300}
            expended={activeTab == 3 && videoData !== undefined ? true : false}
            heading={RecordVideo}
            subHeading={explainthebriefaboutthepersonbyVideo}
            onCallback={onPressVideo}
            shouldActive={videoData !== undefined}
            {...{ isActive: videoData != undefined && activeTab == 3 ? true : false, setActive }}
          >
            {videoData != undefined ? (
              <>
                <Spacer height={10} />
                <PlayVideo {...{ videoData, setVideo }} />
              </>
            ) : (
              <View />
            )}
          </RecommendItem>
          <View style={styles.row}>
            <TextView
              text={KeepmeAnonymous}
              style={[
                styles.text,
                { color: theme.textColor },
                I18nManager.isRTL && {
                  fontSize: Platform.OS == 'ios' ? 14 : 17,
                  lineHeight: undefined,
                },
              ]}
            />
            <Switch
              style={commonStyle.switchStyle}
              onChange={() => setAnonyomous(!isAnonyomous)}
              value={isAnonyomous}
              trackColor={{ false: '#CBD9E7', true: isDarkMode ? theme.bgLightDark : 'black' }}
              ios_backgroundColor="#CBD9E7"
            />
          </View>

          <Spacer height={10} />
        </>
      </KeyboardAwareScrollView>

      <Button
        isDisabled={email.length == 0}
        isLoading={emailLoading}
        type="fill"
        text={next}
        style={[styles.btn, {
          backgroundColor: email.length == 0 ? (isDarkMode ? theme.bgLightDark : Colors.disabledBtn) :
            isDarkMode ?
              Colors.activeStateDark : 'black'
        }]}
        textStyle={[{
          color: email.length == 0 ? Colors.activeStateDark : isDarkMode ? 'black' : 'white'
        }]}
        onPress={onPressNext}
      />
    </View>
  );
};

export default StepLayoutName;

type PlayVideoProps = {
  videoData: ImagePickerResponse;
  setVideo: React.Dispatch<React.SetStateAction<ImagePickerResponse | undefined>>;
};

const PlayVideo = memo(({ videoData, setVideo }: PlayVideoProps) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [isPaused, setPause] = useState(false);

  useEffect(() => {
    setPause(false);
  }, []);

  const onRemoveVideo = () => {
    Alert.alert(t('Alert'), t('Are you sure you want to remove video?'), [
      {
        text: 'Cancel',
        onPress: () => console.warn('Cancel Pressed'),
      },
      {
        text: 'OK',
        onPress: () => {
          setVideo(undefined);
          setPause(false);
        },
      },
    ]);
  };

  const onLoadVideo = () => {
    setPause(true);
  };

  const onError = (error: LoadError) => { };

  const onPlay = () => {
    setPause(false);
    navigation.navigate('RecordVideo', { assets: videoData?.assets });
  };
  return (
    <View style={styles.videoContainer}>
      <Video
        source={{ uri: videoData?.assets && videoData?.assets[0]?.uri }}
        paused={isPaused}
        onLoad={onLoadVideo}
        playInBackground={false}
        playWhenInactive={false}
        onVideoLoad={onLoadVideo}
        onError={onError}
        resizeMode="cover"
        style={{ height: 200, width: '100%' }}
      />

      <Pressable style={styles.cross} onPress={onRemoveVideo}>
        <Icons type={IconsType.Entypo} name={'cross'} size={16} color={Colors.white} />
      </Pressable>

      <Pressable style={styles.btnPlay} onPress={onPlay}>
        <Image
          source={AssetsImages.play_button}
          style={{ width: 40, height: 40, resizeMode: 'contain' }}
        />
      </Pressable>
    </View>
  );
});

const styles = StyleSheet.create({
  row: {
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  heading: {
    marginLeft: 18,
    fontSize: 16,
    fontWeight: '500',
    color: Colors.lightgray,
    fontFamily: fonts.medium,
    textAlign: 'left',
    textTransform: 'capitalize',
  },
  cross: {
    position: 'absolute',
    right: 10,
    top: 10,
    backgroundColor: Colors.black,
    height: 25,
    width: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25 / 2,
  },
  videoContainer: {
    backgroundColor: Colors.videoColor,
    height: 200,
    marginTop: 10,
    borderRadius: 12,
    marginHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  btnPlay: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.black,
    borderRadius: 40 / 2,
    width: 40,
    height: 40,
    borderColor: Colors.white,
    borderWidth: 1,
  },
  btn: {
    marginBottom: isPad ? 24 : 15,
  },

  info: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginRight: 5,
  },
  completeYourProfile: {
    fontFamily: fonts.regular,
    fontSize: fontSize.mini,
    color: 'rgba(14, 121, 219, 1)',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    marginTop: 5,
    alignSelf: 'flex-end',
  },

  suggestionContainer: {
    marginHorizontal: 20,
    marginTop: 0,
    overflow: 'hidden',
    backgroundColor: Colors.white,
  },

  text: {
    fontSize: 14,
    color: Colors.black,
    fontWeight: '500',
    flex: 1,
    textAlign: 'left',
  },
});
