import React from 'react';
import Colors from '../../theme/Colors';
import { View, Text } from 'react-native';
import { StyleSheet } from 'react-native';
import { useReferralHistoryCounts } from '../../hooks';
import fontSize, { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';
import { fonts } from '@theme';

const Badge = ({ name }) => {
  const { received, sent } = useReferralHistoryCounts();
  const { theme } = useTheme()
  return (
    <View style={[styles.container, { backgroundColor: theme.bgLightDark }]}>
      <Text style={styles.text}>{name == 'Received' ? received : sent}</Text>
    </View>
  );
};

export default Badge;

const styles = StyleSheet.create({
  container: {
    minWidth: isPad ? 30 : 20,
    backgroundColor: Colors.black,
    borderRadius: isPad ? 30 : 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 5,
    paddingVertical: 3,
    paddingHorizontal: 3
  },
  text: {
    color: 'white',
    fontSize: isPad ? fontSize.large : fontSize.xxmini,
    fontFamily: fonts.regular
  },
});
