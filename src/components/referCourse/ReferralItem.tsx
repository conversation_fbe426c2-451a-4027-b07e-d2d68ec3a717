import { AssetsImages, Colors, Icons, fonts } from '../../theme';
import React, { useState } from 'react';
import { Image, Platform, Pressable, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { fontFamily } from '../../utils/AppUtils';
import { IconsType } from '../../theme/Icons';
import fontSize, { isPad } from '../../utils/fontSize';
import FastImage from 'react-native-fast-image';
import { useTranslation } from 'react-i18next';
import { lineHeightForAndroidLTRMed } from '@utils/constants';
import { useCommonThemeStyles } from '@utils';
import { useTheme } from '@theme/ThemeProvider';

type ReferralItemType = {
  style?: ViewStyle;
  onPress: () => void;
  title: string;
  hours: string;
  image: string;
  username: string;
  key?: number;
  readStatus?: number;
  type: string;
  comment: string;
};

const ReferralItem = ({
  username,
  style,
  onPress,
  title,
  hours,
  image,
  key,
  type,
  readStatus,
  comment,
}: ReferralItemType) => {
  const [isUnRead, setRead] = useState(readStatus);
  const { t } = useTranslation();
  const { themeStyle, theme } = useCommonThemeStyles()
  const { isDarkMode } = useTheme()

  return (
    <Pressable
      key={key}
      style={[styles.CoursesContainer, style,
      themeStyle.darkBackgroundColor,
      !isUnRead && { backgroundColor: isDarkMode ? theme.bgLightDark : Colors.creamColor }]}
      onPress={() => {
        setRead(true);
        onPress();
      }}
    >
      <View style={[styles.mainContainer]}>
        <View style={styles.imageCon}>
          <FastImage
            source={image != '' ? { uri: image } : AssetsImages.default}
            style={styles.image}
          />
        </View>

        <View style={styles.subcon}>
          {type == 'received' ? (
            <>
              <Text numberOfLines={1} style={[styles.titleText, themeStyle.themedTextColor]}>
                <Text
                  style={[
                    styles.titleText,
                    styles.highlightedText,
                    { textTransform: 'capitalize' },
                    lineHeightForAndroidLTRMed,
                    themeStyle.themedTextColor
                  ]}
                >
                  {username}
                </Text>{' '}
                {t('refer you to enrol to')}{' '}
                <Text style={[styles.titleText, styles.highlightedText, lineHeightForAndroidLTRMed, themeStyle.themedTextColor]}>{title}</Text>
              </Text>
            </>
          ) : (
            <>
              <Text numberOfLines={4} style={[styles.titleText, themeStyle.themedTextColor]}>
                {t("You’ve sent a course referral for")}{' '}
                <Text style={[styles.titleText, styles.highlightedText, lineHeightForAndroidLTRMed, themeStyle.themedTextColor]}>{title}</Text> {t('to')}{' '}
                <Text
                  style={[
                    styles.titleText,
                    styles.highlightedText,
                    { textTransform: 'capitalize' },
                    lineHeightForAndroidLTRMed,
                    themeStyle.themedTextColor
                  ]}
                >
                  {username}
                </Text>
              </Text>
            </>
          )}

          {comment != '' && comment != undefined && (
            <Text
              numberOfLines={3}
              style={[{
                marginTop: 5,
                fontSize: isPad ? fontSize.semiMedium : fontSize.semiMini,
                fontFamily: fonts.regular,
                textAlign: 'left',
                color: Colors.black,

              }, themeStyle.themedTextColor]}
            >
              {comment}
            </Text>
          )}

          <View style={styles.row}>
            {hours && (
              <View style={styles.hourView}>
                <Icons
                  type={IconsType.AntDesign}
                  name={'clockcircleo'}
                  size={isPad ? 20 : 13}
                  color={theme.textColor}
                />
                <Text numberOfLines={1} style={[styles.text, isDarkMode && { color: Colors.dateColorForDark }]}>
                  {hours ?? ''}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default ReferralItem;

const styles = StyleSheet.create({
  highlightedText: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
  },
  row: { flexDirection: 'row', alignItems: 'center', marginTop: 10 },
  mainContainer: {
    borderColor: Colors.lightGray,
    flexDirection: 'row',
  },
  text: {
    marginLeft: 5,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    fontFamily: fonts.medium,
  },
  subcon: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'column',
    paddingLeft: 12,
  },
  CoursesContainer: {
    // marginVertical: 5,
    // borderRadius: 12,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    // marginRight: 5,
    // marginLeft: 5,
    paddingRight: 15,
  },
  titleText: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
    fontFamily: fonts.regular,
    textAlign: 'left',
    color: Colors.black,
  },
  image: {
    width: isPad ? 110 : 90,
    height: Platform.OS == 'ios' ? (isPad ? 116 : 86) : 90,
    borderRadius: 12,
    resizeMode: 'cover',
    alignSelf: 'center',
  },
  imageCon: {
    justifyContent: 'flex-start',
    overflow: 'hidden',
    borderRadius: 10,
    marginLeft: 10,
  },
  hourView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
});
