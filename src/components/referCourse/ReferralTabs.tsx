import React from 'react';
import { Animated, View, Pressable, Text, StyleSheet, I18nManager, ScrollView } from 'react-native';
import Colors from '../../theme/Colors';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import Badge from './Badge';
import { useTheme } from '@theme/ThemeProvider';
import { useTranslation } from 'react-i18next';
import { widgetPadding } from '@utils/constants';

const ReferralTabs = ({ state, descriptors, navigation, position, showBadge = true }) => {
  const { theme, isDarkMode } = useTheme()
  const backgroundColor = theme.backgroundColor
  const { t } = useTranslation();
  return (
    <View style={{ flexDirection: 'row', backgroundColor, paddingHorizontal: widgetPadding / 2 }}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
              ? options.title
              : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        const inputRange = state.routes.map((_, i) => i);
        const opacity = position.interpolate({
          inputRange,
          outputRange: inputRange.map((i) => (i === index ? 1 : 0.5)),
        });

        return (
          <Pressable
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={[
              {
                borderBottomColor: isFocused ?
                  isDarkMode ? Colors.white : Colors.black :
                  isDarkMode ? theme.bgLightDark :
                    Colors.grayline,
              },
              styles.btn,
              (state.routes?.length < 4 || (index > 0 && index < (state?.routes.length - 1))) && { flex: 1 }
            ]}
          >
            <Animated.Text
              style={[
                {
                  opacity,
                  color: isFocused ?
                    isDarkMode ? 'white' :
                      Colors.black : isDarkMode ? 'white' : Colors.black,
                },
                styles.btnText,
                { fontSize: I18nManager.isRTL ? fontSize.h8 : fontSize.h7, textAlign: 'center' }
              ]}
            >
              {t(label)}
            </Animated.Text>
            {showBadge && <Badge name={route.name} />}
          </Pressable>
        );
      })}
    </View>
  );
};

export default ReferralTabs;

const styles = StyleSheet.create({
  btn: {
    flexDirection: 'row',
    borderBottomWidth: 2,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10
  },
  btnText: {
    fontSize: isPad ? fontSize.xlarge : fontSize.h6,
    fontFamily: fonts.regular,
  },
});
