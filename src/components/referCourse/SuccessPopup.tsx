import React from 'react';
import { BottomSheetContainer, Spacer, TextView } from '../index';
import { Image, Platform, Pressable, StyleSheet, View } from 'react-native';
import Colors from '../../theme/Colors';
import AssetsImages from '../../theme/AssetsImages';
import { Text } from 'react-native';
import fontSize, { isPad } from '../../utils/fontSize';
import fonts from '../../utils/fonts';
import FastImage from 'react-native-fast-image';
import { hitSlop } from '../../utils/constants';
import BottomSheet from '../bottomSheet';
import { useTheme } from '@theme/ThemeProvider';

const SuccessPopup = ({ onGoBack, onClose }) => {
  const { isDarkMode, theme } = useTheme();
  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      visiblity={true}
      setVisibility={onClose}
      children={
        <View>
          <View style={styles.body}>
            <FastImage
              resizeMode={FastImage.resizeMode.contain}
              // style={
              //   isDarkMode
              //     ? { width: 80, height: 80, marginTop: -30, marginBottom: 30 }
              //     : styles.tickImage
              // }
              style={styles.tickImage}
              source={isDarkMode ? AssetsImages.tickGreenDark : AssetsImages.greenTick}
            />
            <TextView
              style={[styles.text, isDarkMode && { color: theme.textColor }]}
              text="You've successfully referred a course"
            />
            <Spacer height={10} />
            <TextView
              style={[styles.textDescription, isDarkMode && { color: theme.textColor }]}
              text="Thank you for sharing the opportunity to learn and grow"
            />

            <Spacer height={30} />
            <Pressable onPress={onGoBack}>
              <TextView style={styles.textGoBack} text="Go back to course " />
            </Pressable>
          </View>

          <Pressable
            style={{
              position: 'absolute',
              right: 40,
              top: 40,
            }}
            hitSlop={hitSlop}
            onPress={onClose}
          >
            <Image
              source={AssetsImages.cross}
              style={[styles.cross, isDarkMode && { tintColor: theme.textColor }]}
            />
          </Pressable>
        </View>
      }
    />
  );
};

export default SuccessPopup;

const styles = StyleSheet.create({
  line: { borderBottomWidth: 0.5, borderBottomColor: Colors.grayline },
  bottomSheet: { paddingHorizontal: 0 },
  body: { paddingTop: 10, paddingHorizontal: 30, marginTop: 30, paddingBottom: 50 },
  image: {
    width: 68,
    height: 68,
    resizeMode: 'contain',
  },
  // tickImage: {
  //   width: 150,
  //   height: 150,
  //   resizeMode: 'contain',
  //   marginLeft: -40,
  //   marginTop: -60,
  //   borderRadius: 50,
  // },
  tickImage: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
    marginLeft: -40,
    marginTop: isPad ? -20 : -30,
    borderRadius: 50,
  },
  text: {
    fontSize: Platform.OS == 'android' ? fontSize.xxxxlarge : fontSize.xxxxxlarge,
    fontFamily: fonts.medium,
    letterSpacing: -0.5,
    color: Colors.black,
    marginTop: -10,
    textAlign: 'left',
  },

  textDescription: {
    fontSize: Platform.OS == 'android' ? fontSize.medium : fontSize.large,
    fontFamily: fonts.regular,
    letterSpacing: -0.5,
    color: Colors.dark_g,
    textAlign: 'left',
  },
  textGoBack: {
    fontSize: Platform.OS == 'android' ? fontSize.medium : fontSize.large,
    fontFamily: fonts.regular,
    letterSpacing: -0.5,
    color: Colors.primary,
    textAlign: 'left',
  },
  cross: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: 'black',
  },
});
