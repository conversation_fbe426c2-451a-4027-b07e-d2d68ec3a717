import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, Pressable } from 'react-native';
import { AssetsImages, Colors, fontSize } from '../../theme';
import { getProfileOfUser } from '../../totara/actions/getWFMProfileAction';
import { getUrl } from '../../utils/constants';
import { useSelector } from 'react-redux';
import { ScrollView } from 'react-native-gesture-handler';
import FastImage from 'react-native-fast-image';
import { useTheme } from '@theme/ThemeProvider';
import { ImageAuth } from '@components';

const SuggestionsList = ({ suggestions, onUserItemPress }) => {
  const { isDarkMode, theme } = useTheme();
  return (
    <View style={[styles.container, { borderColor: theme.borderBackground }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {suggestions?.map((item, index) => {
          return (
            <Pressable
              key={item.id + index}
              style={({ pressed }) => [
                {
                  opacity: pressed ? 0.8 : 1,
                },
                [styles.btn, { borderBottomColor: theme.borderBackground }],
              ]}
              onPress={() => onUserItemPress(item)}
            >
              <RenderAvatar
                picture={item.picture}
                id={item.id + index} />
              <Text style={[styles.name, isDarkMode && { color: theme.textColor }]}>
                {item?.name}
              </Text>
            </Pressable>
          );
        })}
      </ScrollView>
    </View>
  );
};

export default SuggestionsList;

const RenderAvatar = ({ id, picture }) => {
  const [image, setImage] = useState('');
  const token = useSelector((state) => state.getWFMProfile.response?.liferayaccesstoken);

  useEffect(() => {
    if (picture) {
      getUrl(picture, token).then((res) => {
        setImage(res);
      });
    }
    else {
      getProfileOfUser(token, `id eq '${id}'`).then((res) => {
        if (res?.profile?.picture) {
          getUrl(res?.profile?.picture, token).then((res) => {
            setImage(res);
          });
        }
      });
    }

  }, [id, picture]);

  return <ImageAuth
    loaderSize={'small'}
    uri={image}
    style={styles.avatar}
  />

};

const styles = StyleSheet.create({
  container: {
    marginTop: 5,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: Colors.borderGray,
    overflow: 'hidden',
    maxHeight: 180,
  },
  btn: {
    height: 55,
    justifyContent: 'center',
    paddingHorizontal: 7,
    borderBottomWidth: 0.5,
    borderBottomColor: Colors.borderGray,
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    textAlign: 'left',
    flex: 1,
    marginLeft: 10,
    fontSize: fontSize.medium,
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f5f5f5',
    borderColor: '#f5f5f5',
    borderWidth: 1,
  },
});
