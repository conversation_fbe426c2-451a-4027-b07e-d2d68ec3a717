import React from 'react';
import { Image, Pressable, StyleSheet, View } from 'react-native';
import { TextView, FavItem } from '../index';
import fonts from '../../utils/fonts';
import FastImage from 'react-native-fast-image';
import fontSize, { isPad } from '../../utils/fontSize';
import { Text } from 'react-native-paper';
import { Rating } from 'react-native-ratings';
import { IconsType } from '../../theme/Icons';
import { useDispatch } from 'react-redux';
import { AssetsImages, Colors, Dimen, Icons } from '../../../src/theme';
import { setBottomSheetVisible } from '../../totara/reducers/addToListBottomSheetReducer';
import { parseAndFormatDuration } from '../../utils/constants';
import { useNavigation } from '@react-navigation/native';

type props = {
  index: number;
  item: Record<string, string>;
};

const ReleaseItem = ({ index, item }: props) => {
  const navigation = useNavigation();

  const navigate = () => navigation.navigate('CourseDetail', { ...item, from: 'required' });

  const dispatch = useDispatch();
  const onPressMore = () => {
    setBottomSheetVisible(dispatch, true);
  };
  return (
    <Pressable onPress={navigate} key={index} style={styles.itemView}>
      <FastImage
        resizeMode={FastImage.resizeMode.cover}
        source={item.image ? { uri: item.image } : AssetsImages.default}
        style={styles.imageView}
      />
      <View style={styles.detailView}>
        <View>
          <View style={styles.actionView}>
            <TextView style={styles.tagText} text={item.coursetype} />
            <View style={styles.row}>
              <FavItem style={styles.like} courseid={item?.courseid} />
              <Pressable onPress={onPressMore}>
                <Icons
                  color={Colors.black}
                  size={18}
                  type={IconsType.Entypo}
                  style={styles.viewMore}
                  name={'dots-three-vertical'}
                />
              </Pressable>
            </View>
          </View>
          <TextView numberOfLines={2} style={styles.releaseName} text={item.title} />
        </View>
        <View style={styles.rowJustify}>
          {item?.duration &&
          item?.duration != '-' &&
          item?.duration != ' ' &&
          item?.duration != 'NA' ? (
            <View style={styles.clockTime}>
              <Image source={AssetsImages.clock} style={styles.clock} />
              <TextView
                numberOfLines={1}
                style={styles.ratingText}
                type="h5"
                text={item?.duration + ' h'}
                // text={parseAndFormatDuration(item?.duration)}
              />
            </View>
          ) : null}
          <View style={[styles.row]}>
            <Icons
              color={Colors.rating}
              size={19}
              type={IconsType.Entypo}
              style={{ marginRight: 5 }}
              name={'star'}
            />
            <TextView numberOfLines={1} type="h5" style={styles.ratingText} text={item?.ratings} />
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default ReleaseItem;

const styles = StyleSheet.create({
  itemView: {
    width: Dimen.width * (isPad ? 0.4 : 0.85),
    borderRadius: 15,
    marginRight: 8,
    overflow: 'hidden',
    flexDirection: 'row',
    borderColor: Colors.borderGray,
    borderWidth: 1,
    height: 120,
  },
  clockTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 10,
  },
  imageView: {
    backgroundColor: '#f5f5f5',
    flex: 0.3,
    borderTopStartRadius: 10,
    borderBottomStartRadius: 10,
  },
  detailView: {
    flex: 0.7,
    justifyContent: 'space-around',
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // paddingTop: 10,
    alignItems: 'center',
    paddingStart: 20,
    // marginBottom: 10,
  },
  tagText: {
    color: 'rgba(85, 121, 156, 1)',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xxmini : fontSize.mini,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginTop: -3,
  },
  like: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
    tintColor: 'black',
  },
  viewMore: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginEnd: 8,
  },
  star: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  clock: {
    width: 15,
    height: 15,
    tintColor: Colors.black,
    marginRight: 5,
  },
  rowJustify: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 10,
  },
  ratingText: {
    fontFamily: fonts.medium,
    color: Colors.black,
  },
  releaseName: {
    marginStart: 20,
    width: '70%',
    fontSize: isPad ? fontSize.semiMedium : fontSize.medium,
    fontFamily: fonts.regular,
  },
});
