import { TextView } from '@components';
import { Colors, fontSize, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View, useWindowDimensions, Text, TouchableOpacity } from 'react-native';
import RenderHTML from 'react-native-render-html';

const RenderCustomHtml = ({ html, limit = 20 }) => {
  const { width } = useWindowDimensions();
  const [descriptionExpanded, setDescriptionExpanded] = useState(false);
  const { theme } = useTheme();
  const { t } = useTranslation();

  const toggleDescription = () => {
    setDescriptionExpanded(!descriptionExpanded);
  };

  const truncatedHtml = descriptionExpanded ? html : `${html.slice(0, limit)}...`;

  return (
    <View style={{ marginBottom: 10 }}>
      <RenderHTML
        contentWidth={width}
        source={{
          html: truncatedHtml,
        }}
        baseStyle={{
          color: theme.textColor,
          fontSize: fontSize.medium,
          fontFamily: fonts.regular,
          textAlign: 'left',
        }}
        tagsStyles={{
          a: {
            color: theme.textColor,
          },
        }}
        systemFonts={['HelveticaNeue-Regular']}
        domVisitors={{
          onElement: (element) => {
            if (element.attribs?.style) {
              const styles = element.attribs.style.split(';');
              const filteredStyles = styles.filter((style) => !style.trim().startsWith('color:'));
              element.attribs.style = filteredStyles.join(';');
            }
          },
        }}
      />

      {truncatedHtml?.length && html?.length > limit ? (
        <TouchableOpacity onPress={toggleDescription}>
          <Text style={styles.viewMoreText}>
            {descriptionExpanded ? t('View less') : t('View more')}
          </Text>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  viewMoreText: {
    color: Colors.blue,
    fontFamily: fonts.semiBold,
    marginTop: 5,
    fontSize: fontSize.medium,
    textAlign: 'left'
  },
});

export default RenderCustomHtml;
