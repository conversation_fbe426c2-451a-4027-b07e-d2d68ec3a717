import { AssetsImages, Colors, Dimen, Icons, Images } from '../../theme';
import React from 'react';
import { Image, ImageBackground, Platform, Pressable, StyleSheet, View } from 'react-native';
import { TextView } from '../index';
import fonts from '../../utils/fonts';
import fontSize from '../../utils/fontSize';
import { Text } from 'react-native-paper';
import { Rating } from 'react-native-ratings';
import { comingSoon, lineHeight } from '../../utils/constants';

type props = {
  index: number;
  item: Record<string, string>;
};

const MentorItem = ({ item, index }: props) => {
  return (
    <Pressable onPress={comingSoon} style={styles.itemView}>
      <View style={styles.imageViewContainer}>
        <Image
          source={AssetsImages.default}
          // source={item.image}
          style={styles.imageView}
        />
      </View>
      <View style={styles.detailView}>
        <TextView style={styles.courseHeading} text={item.title} />
        <View style={styles.rowJustify}>
          <TextView style={styles.universityName} type="h5" text="New York University" />
        </View>
      </View>
    </Pressable>
  );
};

export default MentorItem;

const styles = StyleSheet.create({
  itemView: {
    width: Platform.OS == 'ios' ? Dimen.width * 0.75 : Dimen.width * 0.8,
    borderRadius: 20,
    marginRight: 15,
    overflow: 'hidden',
    flexDirection: 'row',
    borderColor: Colors.borderGray,
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: 'rgba(239, 244, 248, .8)',
  },
  imageViewContainer: {
    alignItems: 'center',
    marginEnd: 15,
  },
  imageView: {
    borderRadius: Platform.OS == 'ios' ? 70 / 2 : 80 / 2,
    height: Platform.OS == 'ios' ? 70 : 80,
    width: Platform.OS == 'ios' ? 70 : 80,
  },
  detailView: {
    flex: 1,
  },
  actionView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  like: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 10,
    tintColor: 'black',
  },
  viewMore: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  courseHeading: {
    color: Colors.black,
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    lineHeight: lineHeight(fontSize.medium)
  },

  rowJustify: {
    flexDirection: 'row',
    // justifyContent: "space-between",
    marginTop: 8,
  },
  ratingText: {
    fontFamily: fonts.medium,
    color: Colors.black,
  },
  universityName: {
    color: 'rgba(88, 131, 174, 1)',
    fontSize: fontSize.medium,
    fontFamily: fonts.regular,
  },
});
