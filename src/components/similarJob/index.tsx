import React from 'react';
import { FlatList, View, Pressable, Image, Text, StyleSheet, Platform } from 'react-native';
import { AssetsImages } from '../../theme';
import Colors from '../../theme/Colors';
import { isPad } from '../../utils/fontSize';
import { commonStyle, fonts, fontSize } from '../../utils';
import { useTheme } from '@theme/ThemeProvider';
import { TextView } from '@components';
import { I18nManager } from 'react-native';
import { lineHeight } from '@utils/constants';

const data = [
  {
    jobTitle: 'Software developer',
    industry: 'Developement',
    department: 'DGE',
    location: 'Abu Dhabi, UAE',
  },
  {
    jobTitle: 'SQA',
    industry: 'Quality Assurance',
    department: 'Department of EY',
    location: 'Abu Dhabi, UAE',
  },
  {
    jobTitle: 'Bussiness Analyst',
    industry: 'Industry',
    department: 'Department',
    location: 'Abu Dhabi, UAE',
  },
];

const SimilarJobsSection = () => {
  const { theme } = useTheme()
  const renderItem = ({ item }) => (
    <View style={[styles.container, { backgroundColor: theme.backgroundColor }]}>
      <Pressable style={styles.borderCircle}>
        <Image source={AssetsImages.book} style={styles.companyImage} />
      </Pressable>

      <View style={styles.detailContainer}>
        <View style={styles.rowEducation}>
          <View style={{ flex: 1 }}>
            <Text style={[styles.heading, { color: theme.textColor }]}>{item.jobTitle}</Text>
            <Text style={[styles.desc, { color: theme.textColor }]}>{item.industry}</Text>
            <Text style={[styles.dept, { color: theme.textColor }]}>{item.department}</Text>
          </View>
        </View>
        <View style={styles.duration}>
          <TextView style={[styles.year, { color: theme.textColor }]} text="Full-time" />
          <View style={styles.dot} />
          <Text style={[styles.year, { color: theme.textColor }]}>{item.location}</Text>
        </View>
        <View style={commonStyle.rowAlign}>
          <Image source={AssetsImages.userprofile} style={[styles.icon, { tintColor: theme.textColor }]} />
          <TextView style={[styles.description, { color: theme.textColor }]} text="Your talent competency matches this job" />
        </View>
      </View>
    </View>
  );

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item, index) => index.toString()}
    />
  );
};

export default SimilarJobsSection;

// Assuming styles are defined somewhere in your component
const styles = StyleSheet.create({
  itemContainer: {
    paddingHorizontal: 15,
    backgroundColor: 'white',
    marginHorizontal: 10,
    borderRadius: 20,
    marginBottom: 10,
    paddingVertical: 15,
  },
  aboutHeader: {
    paddingHorizontal: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: isPad ? fontSize.xlarge : fontSize.large,
    marginRight: 10,
    color: '#000',
    fontFamily: fonts.medium,
  },
  contentContainer: {
    // paddingVertical: 10,
  },
  componentContainer: {
    marginBottom: 5,
  },
  componentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  addIcon: {
    width: isPad ? 40 : 35,
    height: isPad ? 40 : 35,
    // marginHorizontal: 10,
    resizeMode: 'contain',
  },
  actionIcon: {
    width: 20,
    height: 20,
  },
  circle: {
    width: 35,
    height: 35,
    marginHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
  rowAbsolute: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 40,
  },
  actionIconContainer: {
    borderWidth: 1,
    borderRadius: 50,
    borderColor: Colors.borderGray,
  },
  contactInfo: {
    marginHorizontal: 20,
    backgroundColor: Colors.creamColorGradient,
    borderRadius: 20,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 50,
    borderColor: Colors.borderGray,
  },
  contactInfoText: {
    fontFamily: fonts.semiBold,
    fontSize: fontSize.large,
  },
  iconImage: {
    width: 20,
    height: 20,
    marginVertical: 5,
    resizeMode: 'contain',
  },
  field: {
    opacity: 0.7,
    fontSize: fontSize.medium,
    marginStart: 10,
    color: Colors.black,
  },
  value: {
    fontSize: fontSize.medium,
    marginStart: 10,
    color: Colors.black,
  },
  alternateHeading: {
    marginVertical: 10,
    marginTop: 30,
  },
  ViewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 22,
  },
  aboutContainer: {
    paddingHorizontal: 20,
  },
  warning: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  iconStyle: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
    marginRight: 10,
  },
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    borderBottomColor: Colors.borderGray,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    paddingBottom: 20,
  },
  pdfImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
  },
  actionBtn: {
    width: isPad ? 35 : 30,
    height: isPad ? 35 : 30,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  border: {
    width: '100%',
    height: 1,
    marginVertical: 15,
    backgroundColor: 'black',
    opacity: 0.1,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
  },
  detailContainer: {
    marginStart: 10,
    flex: 1,
  },
  borderCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: isPad ? 55 : 40,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
    backgroundColor: '#E9E2DD',
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    textAlign: 'left'

  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginVertical: Platform.OS == 'android' ? 0 : 10,
    textAlign: 'left'

  },
  dept: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 5,
    textAlign: 'left'

  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    opacity: 0.7,
    lineHeight: lineHeight(isPad ? fontSize.medium : fontSize.xmini)

  },
  description: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    color: Colors.black,
    marginTop: 5,
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    opacity: 0.8,
    lineHeight: lineHeight(fontSize.medium)

  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: lineHeight(fontSize.medium)

  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginHorizontal: isPad ? 70 : 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: 120,
    height: 80,
    marginVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  dot: {
    width: 2.5,
    height: 2.5,
    backgroundColor: 'rgba(147, 147, 147, 1)',
    borderRadius: 2.5,
    marginHorizontal: 5,
  },
  rowEducation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  similarJobs: {
    color: 'black',
    fontFamily: fonts.regular,
    textTransform: 'uppercase',
    marginHorizontal: 20,
    fontSize: fontSize.small,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 5,
    marginTop: 2,
  },
});
