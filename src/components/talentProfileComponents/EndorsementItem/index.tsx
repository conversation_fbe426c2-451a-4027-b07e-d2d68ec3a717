import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Platform, StyleSheet, Switch, View } from 'react-native';
import { toggleEndorsementPublication } from '../../../api/server_requests';
import { BEARER_TOKEN } from '../../../api/urls';
import { TextView } from '../../../components/common';
import { Colors, fontSize, fonts } from '../../../theme';
import { RenderAvatar } from '../skills';
import { useSelector } from 'react-redux';

const EndorsementItem = ({ res, index, getAllEndorement }) => {
  const profile = res?.endorserProfile?.profile;
  const [isEndorsed, setIsEndorsed] = useState(res?.pubEndorsed);
  const [isLoading, setLoading] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);

  useEffect(() => {
    setIsEndorsed(res?.pubEndorsed);
  }, [res?.pubEndorsed]);

  const toggleSwitch = () => {
    setLoading(true);
    toggleEndorsementPublication(
      res?.id,
      res?.endorsedUserId,
      !res?.pubEndorsed,
      res?.skillName,
      res?.talentProfileId,
      token,
    )
      .then((res) => {
        if (res) {
          getAllEndorement();
          setTimeout(() => {
            setLoading(false);
          }, 3000);
        } else {
          setLoading(false);
        }
      })
      .catch((er) => {
        setLoading(false);
      });
  };

  return (
    <View key={res.id} style={styles.itemContainer}>
      <View style={styles.row}>
        <RenderAvatar
          content={profile?.picture}
          token={token}
          index={index}
          customStyle={styles.avatar}
        />
        <View style={styles.detailView}>
          <TextView style={styles.fullName} text={profile?.fullname || 'N/A'} />
          {profile?.designation ? <TextView style={styles.designation} text={profile?.designation} /> : null}
          {profile?.department ? <TextView style={styles.department} text={profile?.department} /> : null}
          <TextView
            style={styles.location}
            text={(profile?.emirates || 'N/A') + ', ' + (profile?.country || 'N/A')}
          />
        </View>
      </View>
      {!isLoading ? (
        <View style={styles.row}>
          <TextView style={styles.status} text={!isEndorsed ? 'Off' : 'On'} />
          <Switch
            style={{ transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }], marginTop: -5 }}
            trackColor={{ false: '#CBD9E7', true: 'black' }}
            thumbColor={isEndorsed ? 'white' : 'white'}
            ios_backgroundColor="#CBD9E7"
            onValueChange={toggleSwitch}
            value={isEndorsed}
          />
        </View>
      ) : (
        <ActivityIndicator />
      )}
    </View>
  );
};

export default EndorsementItem;

const styles = StyleSheet.create({
  itemContainer: {
    borderBottomColor: Colors.borderGray,
    borderBottomWidth: 1,
    marginHorizontal: 20,
    paddingVertical: 20,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  avatar: {
    marginLeft: 0,
    height: 60,
    width: 60,
    borderRadius: 50,
    backgroundColor: '#f5f5f5',
  },
  row: {
    flexDirection: 'row',
  },
  fullName: {
    fontFamily: fonts.semiBold,
    color: Colors.black,
  },
  designation: {
    marginTop: 10,
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
  },
  department: {
    marginTop: 5,
    color: 'rgba(30, 30, 30, 0.8)',
    fontSize: fontSize.small,
  },
  detailView: {
    marginStart: 15,
  },
  location: {
    marginTop: 10,
    fontSize: fontSize.mini,
  },
  status: {
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
    marginEnd: 5,
  },
});
