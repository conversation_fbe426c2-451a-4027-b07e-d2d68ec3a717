import { isPad } from '@utils/fontSize';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TextView } from '../../../../components/common';
import { Colors, fontSize, fonts } from '../../../../theme';
import { coming_soon_opacity } from '../../../../utils/AppUtils';
import { useTheme } from '@theme/ThemeProvider';

type Props = {
  heading: string;
  list?: Object;
  route?: string;
  onPress: () => void;
};

const ListingModule = ({ heading, list, route, onPress }: Props) => {
  const { theme } = useTheme()

  return (
    <View style={styles.container}>
      <View style={styles.listContainers}>
        {list?.length && list[0] == '' ? (
          <TextView style={{ color: theme.textColor }} text={`Add ${heading}`} />
        ) : (
          <>{list?.map((item) => <ListingItem item={item} />)}</>
        )}
      </View>
    </View>
  );
};

export default ListingModule;

const ListingItem = ({ item }) => {
  const { theme, isDarkMode } = useTheme()
  const backgroundColor = { backgroundColor: isDarkMode ? theme.backgroundColor : Colors.bgBlue }
  const color = { color: theme.textColor }
  return (
    <View
      style={[
        styles.listingItemContainer,
        {
          opacity: coming_soon_opacity,
        },
        backgroundColor
      ]}
    >
      <TextView style={[styles.listItemTextStyle, color]} text={item} />
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  rowAbsolute: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 40,
  },
  circle: {
    width: 30,
    height: 30,
    marginHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionIcon: {
    width: 20,
    height: 20,
  },
  title: {
    fontSize: isPad ? fontSize.xlarge : fontSize.large,
    marginRight: 10,
    color: '#000',
    fontFamily: fonts.medium,
  },
  listingItemContainer: {
    backgroundColor: Colors.lightBlue,
    marginVertical: 5,
    marginRight: 10,
    borderRadius: 10,
    paddingHorizontal: 5,
  },
  listContainers: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  listItemTextStyle: {
    fontFamily: fonts.regular,
    color: Colors.black,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    paddingHorizontal: 10,
    paddingVertical: 8,
  },
  container: {},
});
