import { fontSize, fonts } from '../../../../utils';
import { TextView } from '../../../../components/common';
import AssetsImages from '../../../../theme/AssetsImages';
import Colors from '../../../../theme/Colors';
import React, { useState } from 'react';
import { ActivityIndicator, I18nManager, Image, Platform, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import moment from 'moment';
import { useNavigation } from '@react-navigation/native';
import { permissionCall } from '../../../../utils/downloadPdf';
import { addEllipsesInCenter, fileNameRegex, getUrl } from '../../../../utils/constants';
import { isPad } from '@utils/fontSize';
import PencilButton from '../../../PencilButton';
import { useTheme } from '@theme/ThemeProvider';
import useStyles from './styles';
import { useTranslation } from 'react-i18next';
import { getFileExtensionFromUrl } from '../../../../screens/ViewCertificates';

export const CVTab = ({ onPress, onCloseSheet, showEdit }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const cvCopy = talentProfile?.resumeCopy && talentProfile?.resumeCopy;
  const { theme, isDarkMode } = useTheme()
  const { styles } = useStyles();
  return (
    <View
      style={[
        styles.contactInfo,
        {
          flexDirection: 'row',
          backgroundColor: isDarkMode ? theme.backgroundColor : Colors.bgBlue,
          paddingVertical: 15
        },
      ]}
    >
      <AttachmentView onCloseSheet={onCloseSheet} attachment={cvCopy} />

      {showEdit &&
        <TouchableOpacity onPress={onPress} style={styles.editIcon}>
          <PencilButton onPress={onPress} />
        </TouchableOpacity>}
    </View>
  );
};

export const EmiratesTab = ({ onPress, onCloseSheet }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const emiratesId = talentProfile?.emiratesId || '-';
  const nationality = talentProfile?.nationality || '-';
  const emiratedIdCopy = talentProfile?.emiratesCopy;
  const { theme } = useTheme()
  const { styles } = useStyles();
  const { t } = useTranslation();

  return (
    <View style={styles.contactInfo}>
      <View>
        <View style={styles.row}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Emirates ID")}:`} />
          <TextView style={[styles.emiratesIdValue, { width: '60%' }]} text={emiratesId} />
        </View>

        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Nationality")}` + ': '} />
          <TextView style={styles.emiratesIdValue} text={nationality} />
        </View>
      </View>

      <AttachmentView
        onCloseSheet={onCloseSheet}
        style={{ marginTop: I18nManager.isRTL ? 5 : 20 }}
        attachment={emiratedIdCopy}
      />
    </View>
  );
};

export const PassportDetails = ({ onPress, onCloseSheet }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);

  const passportType = talentProfile?.passportType || '-';
  const passportNumber = talentProfile?.passportNumber || '-';
  const dateOfExpiry = talentProfile?.dateOfExpiry || '-';
  const passportCopy = talentProfile?.passportCopy;
  const { styles } = useStyles();
  const { t } = useTranslation();
  return (
    <View style={styles.contactInfo}>
      <View>
        <View style={styles.row}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Passport type")}:`} />
          <TextView style={styles.emiratesIdValue} text={passportType} />
        </View>
        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Passport Number")}:`} />
          <TextView style={styles.emiratesIdValue} text={addEllipsesInCenter(passportNumber, 20)} />
        </View>
        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Passport Expiry")}:`} />
          <TextView
            style={styles.emiratesIdValue}
            text={talentProfile?.dateOfExpiry ? moment(dateOfExpiry).format('DD-MM-YYYY') : '-'}
          />
        </View>
      </View>

      <AttachmentView
        onCloseSheet={onCloseSheet}
        style={{ marginTop: 20 }}
        attachment={passportCopy}
      />
    </View>
  );
};

export const FamilyBook = ({ onPress, onCloseSheet }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);

  const tribeName = talentProfile?.tribeName || '-';
  const emirates = talentProfile?.emiratesFamilyBook || '-';
  const khulasitQaidNumber = talentProfile?.khulasitQaidNumber || '-';
  const familyBookNumber = talentProfile?.familyBookNumber || '-';
  const bookRelation = talentProfile?.bookRelation || '-';
  const familyBookCopy = talentProfile?.familyBookCopy;
  const { styles } = useStyles();
  const { t } = useTranslation();

  return (
    <View style={styles.contactInfo}>
      <View>
        <View style={styles.row}>
          <TextView style={styles.emiratesIdLabel} text='Emirates' />
          <TextView style={styles.emiratesIdValue} text={": " + emirates} />
        </View>
        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Khulasit Qauid No")}:`} />
          <TextView style={styles.emiratesIdValue} text={khulasitQaidNumber} />
        </View>
        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Family No")}:`} />
          <TextView style={styles.emiratesIdValue} text={familyBookNumber} />
        </View>
        <View style={styles.rowTopMargin}>
          <TextView style={styles.emiratesIdLabel} text={`${t("Book Relation")}:`} />
          <TextView style={styles.emiratesIdValue} text={bookRelation} />
        </View>
      </View>

      <AttachmentView
        onCloseSheet={onCloseSheet}
        style={{ marginTop: 20 }}
        attachment={familyBookCopy}
      />
    </View>
  )
};


const AttachmentView = ({ attachment, style, onCloseSheet }) => {
  const imageSource = attachment?.includes('pdf')
    ? AssetsImages.pdf
    : AssetsImages.imagePlaceholder;
  const navigation = useNavigation();
  const { styles } = useStyles();


  const viewAttachment = async () => {
    const match = attachment?.match(fileNameRegex);

    if (!attachment) return;
    if (!attachment?.includes('pdf')) {
      setTimeout(() => {
        navigation.navigate('AttachmentView', attachment);
      }, 100);
    } else {
      navigation.navigate('ViewCertificates', {
        downloadLink: attachment,
        isInApp: false,
        fileName: match?.length ? match[1] : attachment ? (attachment?.includes('pdf') ? 'Downloaad.pdf' : 'Preview.png') : 'No Attachment',
      });
    }
  };


  const match = attachment?.match(fileNameRegex);
  const extension = attachment?.split('.').pop();
  const { theme } = useTheme()

  const fileName = match?.length ? (match[1] + "." + extension) : attachment ? attachment : 'No Attachment Found'

  return (

    <Pressable onPress={viewAttachment}
      style={[{ flexDirection: 'row', alignItems: 'center' }, style]}>


      <Image style={styles.pdfImage} source={imageSource} />
      <View style={{ width: '85' }}>
        <TextView style={[styles.attachment, { color: theme.textColor, }]} text={"Attachment"}>
          <TextView style={[styles.attachment, { color: theme.textColor }]} text={" : "}>
          </TextView>
        </TextView>
        <TextView
          numberOfLines={1}
          style={[
            styles.cv,
            {
              marginTop: 5,
              textDecorationLine: !attachment ? 'none' : 'underline',
              width: !attachment ? '100%' : '100%',
              textAlign: 'left',
            },
          ]}
          text={addEllipsesInCenter(fileName, 35)}
        />
      </View>
    </Pressable>
  );
};

