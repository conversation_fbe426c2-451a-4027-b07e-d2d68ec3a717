import { Colors, fonts } from '@theme';
import { useTheme } from '@theme/ThemeProvider';
import { lineHeight } from '@utils/constants';
import fontSize, { isPad } from '@utils/fontSize';
import { I18nManager, Platform, StyleSheet } from 'react-native';

const useStyles = () => {
    const { theme, isDarkMode } = useTheme();

    const styles = StyleSheet.create({
        contactInfo: {
            borderRadius: 10,
            marginBottom: 20,
            paddingHorizontal: 20
        },
        pdfImage: {
            width: isPad ? 55 : 40,
            height: isPad ? 55 : 40,
            resizeMode: 'contain',
            marginEnd: 10,
        },
        attachment: {
            fontFamily: fonts.regular,
            fontSize: isPad ? fontSize.large : fontSize.small,
            textAlign: 'left',
        },
        emiratesIdLabel: {
            fontFamily: fonts.regular,
            color: theme.textColor,
            // color: isDarkMode ? theme.textColor : 'rgba(98, 98, 98, 1)',
            fontSize: isPad ? fontSize.large : fontSize.small,
            textTransform: 'capitalize',
            lineHeight: lineHeight(isPad ? fontSize.large : fontSize.small)

        },
        emiratesIdValue: {
            fontFamily: fonts.regular,
            color: theme.placeholderText,
            // color: isDarkMode ? theme.placeholderText : 'black',
            fontSize: isPad ? fontSize.large : fontSize.small,
            marginStart: 2,
            width: 185,
            textAlign: 'left',
            lineHeight: lineHeight(isPad ? fontSize.large : fontSize.small)
        },
        cv: {
            fontFamily: fonts.medium,
            color: 'rgba(32, 87, 255, 1)',
            fontSize: isPad ? fontSize.large : fontSize.small,
            marginTop: 5,
            // width: '55%'
        },
        editIcon: {
            width: isPad ? 40 : 30,
            height: isPad ? 40 : 30,
            resizeMode: 'contain',
            justifyContent: 'center',
            alignItems: 'center',
            // borderWidth: 1,
            borderRadius: 50,
            borderColor: Colors.borderGray,
            position: 'absolute',
            right: 16,
            top: 16,
        },
        row: {
            flexDirection: 'row',
            alignItems: 'flex-start',
            // marginTop: Platform.OS == 'ios' ? 7 : 0
        },
        rowTopMargin: {
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: Platform.OS == 'ios' ? 9 : 7,
        },
        loaderView: {
            alignItems: 'center',
            justifyContent: 'center',
            width: isPad ? 55 : 40,
            height: 40,
            marginRight: 10
        }
    });

    return { styles, theme, isDarkMode };
};

export default useStyles;
