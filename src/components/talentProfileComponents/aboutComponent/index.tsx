import React, { useState } from 'react';
import {
  Image,
  Linking,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@theme/ThemeProvider';
import { setActionType } from '@totara/actions/getWFMProfileAction';
import { navigateTo, NAVIGATION } from '@totara/lib/navigation';
import crashReportLogger from '@utils/crashlyticsLogger';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import {
  Seperator,
  Spacer,
  TabView,
  TalentProfileEmptyState,
  TextView,
} from '../../../../src/components';
import { AssetsImages, Colors } from '../../../../src/theme';
import { LabelConfig } from '../../../../src/theme/labelConfig';
import PencilButton from '../../../components/PencilButton';
import { aboutComponent } from '../../../interfaces/aboutComponentType';
import {
  aboutComponentsTabsButtons,
  capitalizeFirstLetter,
  socialMedia,
} from '../../../utils/constants';
import fonts from '../../../utils/fonts';
import fontSize, { isPad } from '../../../utils/fontSize';
import { ListingModules } from '../../talentProfileComponents';
import { CVTab, EmiratesTab, FamilyBook, PassportDetails } from './aboutTabs';

const { showMoreDetails, watchIntro, linkedAccounts } = LabelConfig.talentProfile.about;

const About = ({
  onShowMore,
  data,
  isChild,
  style,
  onPress,
  onPressCv,
  onPressEmirates,
  onPressPassport,
  onPressFamilyBook,
  setVisibilityFromParent,
  closeBottomSheet,
  onCloseSheet,
}: aboutComponent) => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [attachmentModal, setAttachmentModalVisibility] = useState(false);
  const dispatch = useDispatch();
  const { theme } = useTheme();

  const isValidUrl = (url) => {
    const urlPattern = /^(http|https):\/\/[^ "]+$/;
    return urlPattern?.test(url);
  };
  const emailRegex = /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/i;

  const openURl = (url) => async () => {
    if (emailRegex?.test(url)) {
      Linking.canOpenURL(`mailto:${url}`)
        .then((res) => {
          if (res) {
            Linking.openURL(`mailto:${url}`).catch((err) =>
              crashReportLogger(err as Error, {
                component: 'About component openURL',
                url: url,
                additionalInfo: 'Failed to check URL',
              }),
            );
          }
        })
        .catch((err) => {
          crashReportLogger(err as Error, {
            component: 'About component openURL',
            url: url,
            additionalInfo: 'Failed to check URL',
          });
        });

      return;
    }
    if (isValidUrl(url)) {
      (await onCloseSheet) && onCloseSheet();
      setTimeout(() => {
        const { COURSE_WEBVIEW_ACTIVITY } = NAVIGATION;

        navigateTo({
          navigate: navigation.navigate,
          routeId: COURSE_WEBVIEW_ACTIVITY,
          props: {
            activity: {
              customWebiew: true,
              viewurl: url?.startsWith('www') ? `https://${url}` : url,
              loading: false,
            },
            backAction: () => {},
            title: 'item?.name',
          },
        });
      }, 100);

      return;
    } else {
      alert('Invalid URL');
    }
  };

  const getLanguages = () => {
    return data?.languages?.map((res) => res?.userLanguage?.name)?.join();
  };

  const openBottomSheet = () => {
    setLoading(true);
    setTimeout(() => {
      onShowMore && onShowMore();
    }, 0);
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  };

  const onEdit = (title, type) => async () => {
    dispatch(setActionType('edit'));
    await onCloseSheet();
    setTimeout(() => {
      navigation?.navigate('EditFormTelentProfile', { title, type });
    }, 100);
  };

  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);

  const preferencesList = talentProfile?.prefrences;
  const preferences = preferencesList?.split(',');
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const tintColor = { tintColor: theme.tintColor };
  const { isDarkMode } = useTheme();

  return (
    <View style={style}>
      <TextView
        viewMoreTextStyle={styles.aboutDesc}
        isViewAll={data?.bio?.length > 100}
        numberOfLines={110}
        text={data?.bio || ''}
        style={[styles.aboutDesc, { color: theme.textColor }]}
      />
      {!data?.bio && (
        <TalentProfileEmptyState
          onPress={() => {
            navigation?.navigate('EditFormTelentProfile', { title: 'About', type: 'About' });
          }}
        />
      )}

      <Spacer height={20} />
      <Seperator />
      <Spacer height={20} />
      <TextView
        style={[styles.myResume, { color: theme.textColor }]}
        text="Preferred mode of learning"
      />
      <ListingModules
        onPress={onEdit('Preferred mode of learning', 'EditPrefferedModeOfLearning')}
        heading="Preferred Mode of Learning"
        list={preferences}
      />
      <Spacer height={20} />
      <Seperator />
      <Spacer height={20} />

      {socialMedia?.length ? (
        <View>
          <TextView text={linkedAccounts} style={[styles.myResume, { color: theme.textColor }]} />
          <View style={styles.rowAlign}>
            {socialMedia?.length &&
              socialMedia?.map((res, index) => {
                if (!data[res?.url]) return null;
                return (
                  <TouchableOpacity
                    onPress={openURl(data[res?.url])}
                    key={index}
                    style={[
                      styles.circle,
                      {
                        borderColor: theme.borderBackground,
                      },
                    ]}
                  >
                    <Image source={res.icon} style={styles.addIcon} />
                  </TouchableOpacity>
                );
              })}
          </View>
        </View>
      ) : null}

      {data?.video ? (
        <Pressable
          onPress={openURl(data?.video)}
          style={[
            styles.watchIntro,
            { marginVertical: 24, width: 160 },
            backgroundColor,
            isDarkMode && { borderWidth: 0 },
          ]}
        >
          <Image source={AssetsImages.playIntro} style={[styles.platIntro, tintColor]} />
          <TextView style={[styles.watchText, { color: theme.textColor }]} text={watchIntro} />
        </Pressable>
      ) : null}

      {isChild && (
        <TabView
          disabled={true}
          selectedTabStyles={styles.selectedTabStyles}
          tabLabelStyle={styles.tabLabelStyle}
          tabLabelButtonStyles={styles.tabLabelButtonStyle}
          tabLabelContainerStyles={styles.tabLabelContainerStyle}
          components={[
            <View key="contactInfo" style={styles.contactInfo}>
              {data?.birthdate ? (
                <RenderAttributeValue
                  icon={AssetsImages.calendar}
                  field={'DOB:'}
                  value={moment(data?.birthdate).format('MM-DD-YYYY') || 'N/A'}
                />
              ) : null}
              {data?.gender ? (
                <RenderAttributeValue
                  icon={AssetsImages.gender}
                  field={'Gender:'}
                  value={data?.gender || 'N/A'}
                />
              ) : null}
              {data?.maritalStatus ? (
                <RenderAttributeValue
                  numberOfLines={2}
                  icon={AssetsImages.marriedStatus}
                  field={'Martial Status:'}
                  value={data?.maritalStatus || 'Unmarried'}
                />
              ) : null}
              {data?.languagesUser?.length ? (
                <RenderAttributeValue
                  icon={AssetsImages.language}
                  field={'Language:'}
                  value={data?.languagesUser?.map((item, index) => {
                    if (data?.languagesUser?.length === 1)
                      return capitalizeFirstLetter(item?.userLanguage);
                    return index === data?.languagesUser?.length - 1
                      ? '' + capitalizeFirstLetter(item?.userLanguage)
                      : capitalizeFirstLetter(item?.userLanguage) + ', ';
                  })}
                />
              ) : null}

              <TouchableOpacity onPress={onPress} style={styles.editIcon}>
                <PencilButton onPress={onPress} />
              </TouchableOpacity>
            </View>,
            <CVTab key="cvTab" onCloseSheet={onCloseSheet} onPress={onPressCv} />,
            <EmiratesTab
              key="emiratesTab"
              onCloseSheet={onCloseSheet}
              data={data}
              onPress={onPressEmirates}
            />,
            <PassportDetails
              key="passportDetails"
              onCloseSheet={onCloseSheet}
              onPress={onPressPassport}
            />,
            <FamilyBook
              key="familyBlock"
              onCloseSheet={onCloseSheet}
              onPress={onPressFamilyBook}
            />,
          ]}
          buttons={aboutComponentsTabsButtons}
        />
      )}
    </View>
  );
};

const RenderAttributeValue = ({ field, value, icon }) => {
  return (
    <View style={styles.attributeRow}>
      <Image style={styles.iconImage} source={icon} />
      <TextView style={styles.field} text={field} />
      <TextView numberOfLines={field == 'Language:' ? null : 1} style={styles.value} text={value} />
    </View>
  );
};

export default About;

const styles = StyleSheet.create({
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 16,
  },
  rowAlign: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 5,
  },
  showMoreBtn: {
    borderWidth: 1,
    borderColor: Colors.black,
    borderRadius: 30,
    marginRight: 10,
    paddingHorizontal: isPad ? 18 : 12,
    flexDirection: 'row',
    // width: isPad ? 210 : 165,
    minWidth: isPad ? 210 : 160,

    justifyContent: 'center',
    height: isPad ? 45 : 40,
    alignItems: 'center',
  },
  platIntro: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: 'white',
    marginTop: -2,
    marginRight: 15,
  },
  watchIntro: {
    borderWidth: 1,
    backgroundColor: Colors.black,
    borderRadius: 30,
    paddingStart: 13,
    paddingEnd: 20,
    alignItems: 'center',
    flexDirection: 'row',
    height: isPad ? 45 : 40,
  },
  showText: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    fontFamily: fonts.medium,
  },
  watchText: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.white,
    fontFamily: fonts.medium,
  },
  aboutDesc: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    lineHeight: 26,
    fontFamily: fonts.regular,
    marginTop: 10,
    color: 'rgba(86, 86, 86, 1)',
    textAlign: 'left',
  },
  linkedAccount: {
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.semiMedium,
    fontFamily: fonts.medium,
  },
  circle: {
    width: isPad ? 45 : 40,
    height: isPad ? 45 : 40,
    marginEnd: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.socialLinkBorder,
    borderRadius: 50,
  },
  addIcon: {
    width: 18,
    height: 18,
    marginHorizontal: 10,
    resizeMode: 'contain',
  },
  tabLabelContainerStyle: {
    paddingHorizontal: 0,
    marginBottom: 24,
  },
  tabLabelButtonStyle: {
    paddingVertical: 12,
    paddingHorizontal: 0,
    marginEnd: 24,
  },

  tabLabelStyle: {
    fontSize: isPad ? fontSize.large : fontSize.small,
    fontFamily: fonts.medium,
    textTransform: 'none',
    opacity: 0.5,
    color: 'rgba(30, 30, 30, 1)',
  },
  selectedTabStyles: {
    fontFamily: fonts.medium,
    color: 'black',
    opacity: 1,
  },
  contactInfo: {
    backgroundColor: Colors.creamColorGradient,
    borderRadius: 20,
    padding: 18,
  },
  editIcon: {
    width: isPad ? 40 : 30,
    height: isPad ? 40 : 30,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    borderColor: Colors.borderGray,
    position: 'absolute',
    right: 16,
    top: 16,
  },
  attributeRow: {
    // alignItems: "center",
    flexDirection: 'row',
    marginVertical: 4,
    // backgroundColor: 'red',
  },
  iconImage: {
    width: 20,
    height: 20,
    // marginVertical: 5,
    resizeMode: 'contain',
  },
  field: {
    opacity: 0.7,
    marginStart: 10,
    color: Colors.black,
    fontSize: isPad ? fontSize.large : fontSize.medium,
  },
  value: {
    fontSize: isPad ? fontSize.large : fontSize.medium,
    marginStart: 10,
    color: Colors.black,
    flex: 1,
  },
  attachmentView: { padding: 0, margin: 0 },
  myResume: {
    fontSize: fontSize.medium,
    color: 'rgba(30, 30, 30, 1)',
    fontFamily: fonts.medium,
    marginBottom: 20,
    textAlign: 'left',
  },
});
