import { TextView } from '../../../../components/common';
import { fontSize, fonts } from '../../../../utils';
import React from 'react';
import { Image, Pressable, StyleSheet, View } from 'react-native';
import Colors from '../../../../theme/Colors';
import AssetsImages from '../../../../theme/AssetsImages';
import { coming_soon_opacity } from '../../../../utils/AppUtils';

const moods = [
  {
    id: 1,
    image: AssetsImages.sadIcon,
    text: 'Sad',
  },
  {
    id: 2,
    image: AssetsImages.neutral,
    text: 'Neutral',
  },
  {
    id: 3,
    image: AssetsImages.smileyHappy,
    text: 'Relaxed',
  },
];

const MentalHealth = () => {
  return (
    <View style={styles.container}>
      <TextView style={styles.title} text={'Mental Health'} />

      <RenderMood heading={'Mood'} />
      <RenderMood heading={'Stress Level'} />
      <RenderMood heading={'Communication'} />
    </View>
  );
};

export default MentalHealth;

const RenderMood = ({ heading }) => {
  return (
    <View style={styles.moodContainer}>
      <TextView style={styles.moodHeading} text={heading} />

      <View style={styles.moodView}>
        {moods?.map((res) => {
          return (
            <Pressable style={styles.moodStyle}>
              <TextView style={styles.moodText} text={res?.text} />
              <Image source={res.image} style={styles.icon} />
            </Pressable>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: fontSize.large,
    marginRight: 10,
    color: '#000',
    fontFamily: fonts.medium,
    marginBottom: 15,
    opacity: coming_soon_opacity,
  },
  container: {
    borderColor: Colors.borderGray,
    borderTopWidth: 1,
    paddingHorizontal: 20,
    paddingBottom: 30,
    marginTop: 10,
    paddingTop: 10,
  },
  moodContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderColor: Colors.borderGray,
    borderBottomWidth: 1,
    paddingBottom: 10,
    marginVertical: 10,
  },
  icon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
    marginHorizontal: 10,
  },
  moodView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  moodHeading: {
    fontSize: fontSize.medium,
    fontFamily: fonts.medium,
    opacity: coming_soon_opacity,
  },
  moodStyle: {
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moodText: {
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.mini,
    marginBottom: 5,
  },
});
