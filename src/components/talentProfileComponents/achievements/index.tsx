import {
  Image,
  ImageBackground,
  Platform,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { ConfirmationModal, Divider, ShowMore, TextView } from '../../../../src/components';
import { AssetsImages, Colors, Dimen, Icons } from '../../../../src/theme';
import { LabelConfig } from '../../../../src/theme/labelConfig';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import { IconsType } from '../../../../src/theme/Icons';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
const { showMore, showLess } = LabelConfig.talentProfile.about;
import { useNavigation } from '@react-navigation/native';
import { setActionType, updateProfile } from '../../../totara/actions/getWFMProfileAction';
import { ScrollView } from 'react-native';
import moment from 'moment';
import { RenderDocument } from '../education';
import { useTheme } from '@theme/ThemeProvider';
import { RenderBorder } from '../training';

const Achievement = ({ viewAll, customStyle }) => {
  const data = useSelector((state) => state?.getWFMProfile);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const userProfile = useSelector((state) => state?.getWFMProfile?.response);
  const [visibility, setVisibility] = useState(false);
  const [isExpend, setIsExpend] = useState(false);
  const [achievements, setAchievements] = useState([]);
  const navigation = useNavigation();
  const [item, setItem] = useState(null);
  const dispatch = useDispatch();
  const toggleModal = () => setVisibility(!visibility);
  const stateLoading = useSelector((state) => state?.getWFMProfile.isLoading);
  const { theme } = useTheme()

  useEffect(() => {
    if (talentProfile?.achievements) {
      const array = talentProfile?.achievements.sort((a, b) => {
        return new Date(b.startDate) - new Date(a.startDate);
      });
      setIsExpend(false);
      setAchievements(viewAll ? array : array?.slice(0, 1));
    }
  }, [talentProfile, talentProfile?.achievements]);

  useEffect(() => {
    if (!viewAll) {
      if (isExpend) {
        const array = talentProfile?.achievements?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setAchievements(array);
      } else {
        const array = talentProfile?.achievements?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setAchievements(array?.slice(0, 1));
      }
    }
  }, [isExpend]);

  const onExpend = () => setIsExpend(!isExpend);
  const onEdit = (item) => () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Add Edit Achievement',
      type: 'Edit_Achievement',
      item,
    });
    dispatch(setActionType('edit'));
  };

  const scrollStyle = { marginBottom: viewAll ? 140 : 10 };

  const onDelete = () => {
    const filteredArray = talentProfile?.achievements.filter((element) => element.id != item?.id);
    const fields = {
      achievements: filteredArray,
    };
    dispatch(updateProfile(talentProfile.id, fields, userProfile?.liferayaccesstoken, navigation));
    toggleModal();
  };

  const openModal = (item) => () => {
    toggleModal();
    setItem(item);
    dispatch(setActionType('delete'));
  };

  const color = { color: theme.textColor }

  return (
    <View style={customStyle}>
      <ScrollView showsVerticalScrollIndicator={false} style={scrollStyle}>
        {!achievements?.length ? (
          <TextView style={[styles.noInfo, color]} text="No information available" />
        ) : null}
        {achievements?.map((item, index) => {
          return (
            <View key={item.id}>
              <View style={styles.container}>
                <Pressable style={styles.borderCircle}>
                  <Image source={AssetsImages.book} style={styles.companyImage} />
                </Pressable>

                <View style={styles.detailContainer}>
                  <View style={styles.rowEducation}>
                    <View style={{ flex: 1 }}>
                      <TextView style={[styles.heading, color]} text={item?.title} />
                    </View>
                    {viewAll && (
                      <View style={{ flexDirection: 'row', position: 'absolute', right: 0 }}>
                        <Pressable disabled={stateLoading} onPress={onEdit(item)}>
                          <Image source={AssetsImages.editBlack} style={styles.actionBtn} />
                        </Pressable>
                        <Pressable disabled={stateLoading} onPress={openModal(item)}>
                          <Image source={AssetsImages.deleteBlack} style={styles.actionBtn} />
                        </Pressable>
                      </View>
                    )}
                  </View>
                  <View style={styles.duration}>
                    <TextView style={[styles.year, color]} text={moment(item.date).format('MMM YYYY')} />
                  </View>
                  {item?.description ? <TextView style={[styles.description, color]} text={item?.description} /> : null}

                  {item?.attachment && !viewAll ? (
                    <RenderDocument fileName={item?.attachment} />
                  ) : null}
                </View>
              </View>
              {(isExpend || viewAll) && achievements?.length - 1 != index && (
                <RenderBorder />
              )}
            </View>
          );
        })}
      </ScrollView>

      {talentProfile?.achievements?.length > 1 && !viewAll && (
        <ShowMore
          isExpend={isExpend}
          onExpend={onExpend} />
      )}

      <ConfirmationModal
        title={item?.title}
        type="achievement"
        onDelete={onDelete}
        setVisibility={toggleModal}
        visibility={visibility}
      />
    </View>
  );
};

export default Achievement;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    borderBottomColor: 'rgba(0, 0, 0, 1)',
  },
  pdfImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    textAlign: 'left'
  },
  actionBtn: {
    width: isPad ? 35 : 30,
    height: isPad ? 35 : 30,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  border: {
    width: '100%',
    height: 1,
    marginVertical: 15,
    backgroundColor: 'black',
    opacity: 0.1,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
  },
  detailContainer: {
    marginStart: 10,
    flex: 1,
  },
  borderCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
    backgroundColor: '#E9E2DD',
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    textAlign: 'left'
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    marginVertical: Platform.OS == 'android' ? 0 : 10,
    textAlign: 'left'
  },
  dept: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 5,
    textAlign: 'left'
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'left'
  },
  description: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    marginTop: 5,
    textAlign: 'left'
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginHorizontal: isPad ? 70 : 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: isPad ? 150 : 120,
    height: isPad ? 120 : 80,
    marginVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  dot: {
    width: 2.5,
    height: 2.5,
    backgroundColor: 'rgba(147, 147, 147, 1)',
    borderRadius: 2.5,
    marginHorizontal: 5,
  },
  rowEducation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
});
