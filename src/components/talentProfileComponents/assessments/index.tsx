import { TextView } from '../../../components/common';
import { sortArray } from '../../../utils/constants';
import React, { useEffect, useState } from 'react';
import { Image, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Colors, commonStyle } from '../../../utils';
import styles from './styles';
import moment from 'moment';
import { RenderDocument } from '../education';
import AssetsImages from '../../../theme/AssetsImages';
import { useTheme } from '@theme/ThemeProvider';

const Assessments = () => {
  const [assessments, setAssessments] = useState([]);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const { theme } = useTheme()
  useEffect(() => {
    if (talentProfile?.assessments) {
      setAssessments(sortArray(talentProfile?.assessments, 'date', true));
    }
  }, [talentProfile, talentProfile?.assessments]);

  return (
    <View>
      {!assessments?.length ? (
        <TextView style={[commonStyle.noInfo, { color: theme.textColor }]} text="No information available" />
      ) : null}
      {assessments?.map((res) => (
        <AssessmentItem item={res} />
      ))}
    </View>
  );
};

export default Assessments;

const AssessmentItem = ({ item }) => {
  const { theme, isDarkMode } = useTheme()
  const handleOnPress = () => { };

  const backgroundColor = { backgroundColor: isDarkMode ? 'rgba(109, 163, 215, 0.1)' : Colors.backgroundLight, }
  const color = { color: theme.textColor }
  const tintColor = { tintColor: theme.tintColor }
  return (
    <View style={[styles.assessmentContainer, backgroundColor]}>
      <View style={{ flex: 1 }}>
        {item.assessmentFile ? (
          <RenderDocument
            customStyle={styles.docStyle}
            iconStyle={styles.iconstyle}
            item={item}
            fileName={item?.assessmentFile}
          />
        ) : (
          <Image style={[styles.errorFile, tintColor]} source={AssetsImages.errorFile} />
        )}
        {/* {item.assessmentFile ? <TextView
                    onPress={handleOnPress}
                    style={styles.report} text={'Report.pdf'} /> : null} */}
      </View>
      <View style={styles.detailsContainer}>
        <TextView style={[styles.title, color]} text={item.title} />
        {item?.description ? <TextView style={[styles.description, color]} text={item.description} /> : null}
        <TextView style={[styles.date, color]} text={item?.date ? moment(item?.date).format('DD MMMM, YYYY') : ''} />
      </View>
    </View>
  );
};
