import { fontSize, fonts } from '../../../utils';
import Colors from '../../../theme/Colors';
import { StyleSheet } from 'react-native';
import { isPad } from '@utils/fontSize';

export default StyleSheet.create({
  assessmentContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.backgroundLight,
    borderRadius: 10,
    padding: 20,
    marginTop: 15,
  },
  attachmentContainer: {
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  detailsContainer: {
    flex: 3,
    paddingTop: 8
  },
  title: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xxlarge : fontSize.large,
  },
  description: {
    color: Colors.black,
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xxmini,
    marginTop: 5,
  },
  date: {
    color: 'rgba(30, 30, 30, 1)',
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.medium : fontSize.mini,
    marginTop: 12,
  },
  report: {
    color: 'rgba(32, 87, 255, 1)',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.mini,
    marginTop: 12,
    textDecorationLine: 'underline',
  },
  docStyle: {
    width: '100%',
    marginVertical: 0,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0,
    height: 60,
    alignSelf: 'center',
  },
  iconstyle: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
    flex: 1,
  },
  errorFile: {
    width: 70,
    height: 80,
    resizeMode: 'contain',
  },
});
