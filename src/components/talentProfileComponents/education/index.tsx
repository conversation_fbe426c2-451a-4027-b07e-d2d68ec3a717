import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  I18nManager,
  Image,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { ConfirmationModal, ImageAuth, ShowMore, TalentProfileEmptyState, TextView } from '../../../../src/components';
import { AssetsImages, Colors, Icons, Images } from '../../../../src/theme';
import { IconsType } from '../../../../src/theme/Icons';
import { LabelConfig } from '../../../../src/theme/labelConfig';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { fileNameRegex, getUrl } from '../../../utils/constants';
import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { setActionType, updateProfile } from '../../../totara/actions/getWFMProfileAction';
import { permissionCall } from '../../../utils/downloadPdf';
import { useTheme } from '@theme/ThemeProvider';
import { formatDateBasedOnLocale } from '@utils/AppUtils';
const { showMore, showLess } = LabelConfig.talentProfile.about;


type Props = {
  viewAll?: boolean;
  viewEdit?: boolean;
  customStyle?: Object;
};

const Education = ({ viewAll, customStyle, viewEdit }: Props) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const userProfile = useSelector((state) => state?.getWFMProfile?.response);
  const [visibility, setVisibility] = useState(false);
  const [isExpend, setIsExpend] = useState(false);
  const [education, setEducation] = useState([]);
  const navigation = useNavigation();
  const [item, setItem] = useState(null);
  const dispatch = useDispatch();
  const { theme } = useTheme()
  const toggleModal = () => setVisibility(!visibility);
  const stateLoading = useSelector((state) => state?.getWFMProfile.isLoading);

  useEffect(() => {
    if (talentProfile?.education) {
      const array = talentProfile?.education.sort((a, b) => {
        return new Date(b.startDate) - new Date(a.startDate);
      });
      setIsExpend(false);
      setEducation(viewAll ? array : array?.slice(0, 1));
    }
  }, [talentProfile, talentProfile?.education]);

  useEffect(() => {
    if (!viewAll) {
      if (isExpend) {
        const array = talentProfile?.education?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setEducation(array);
      } else {
        const array = talentProfile?.education?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setEducation(array?.slice(0, 1));
      }
    }
  }, [isExpend]);

  const onExpend = () => setIsExpend(!isExpend);
  const onEdit = (item) => () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Edit Education',
      type: 'Edit_Education',
      item,
    });
    dispatch(setActionType('edit'));
  };

  const scrollStyle = { marginBottom: viewAll ? 140 : 10 };

  const onDelete = () => {
    const filteredArray = talentProfile?.education.filter((element) => element.id != item?.id);
    const fields = {
      education: filteredArray,
    };
    dispatch(updateProfile(talentProfile.id, fields, userProfile?.liferayaccesstoken, navigation));
    toggleModal();
  };

  const openModal = (item) => () => {
    toggleModal();
    setItem(item);
    dispatch(setActionType('delete'));
  };

  const color = { color: theme.textColor }
  const tintColor = { tintColor: theme.tintColor }

  const onPress = () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Add / Edit Education',
      type: 'Edit_Education',
    });
    dispatch(setActionType('add'));
  };

  return (
    <View style={customStyle}>
      <ScrollView showsVerticalScrollIndicator={false} style={scrollStyle}>
        {!talentProfile?.education?.length &&
          <TalentProfileEmptyState
            onPress={onPress}
          />}

        {education?.map((item, index) => {
          return (
            <View key={item.id}>
              <View style={styles.container}>
                <Pressable style={styles.borderCircle}>
                  <Image source={AssetsImages.book} style={styles.companyImage} />
                </Pressable>

                <View style={styles.detailContainer}>
                  <View style={styles.rowEducation}>
                    <View style={{ flex: 1 }}>
                      <TextView style={[styles.heading, color]} text={item.school} />
                      <TextView style={[styles.desc, color]} text={item.degreeName} />
                    </View>
                    {viewAll && (
                      <View style={{ flexDirection: 'row' }}>
                        <Pressable disabled={stateLoading} onPress={onEdit(item)}>
                          <Image source={AssetsImages.editBlack} style={styles.actionBtn} />
                        </Pressable>
                        <Pressable
                          disabled={stateLoading}
                          onPress={openModal(item)}
                        // onPress={onDelete(item)}
                        >
                          <Image source={AssetsImages.deleteBlack} style={styles.actionBtn} />
                        </Pressable>
                      </View>
                    )}
                  </View>
                  <View style={styles.duration}>
                    <TextView
                      style={[styles.year, color]}
                      text={
                        formatDateBasedOnLocale(item.startDate) +
                        ' - ' +
                        formatDateBasedOnLocale(item.endDate)
                      }
                    />
                    {(item?.location && item.location?.length < 20) ? (
                      <>
                        <View style={[styles.dot, { marginTop: 10 }]} />
                        <TextView style={[styles.year, color]} text={item.location} />
                      </>
                    ) : null}
                  </View>
                  {item.location?.length > 20 ? (
                    <TextView style={[styles.year, { marginTop: 10 }, color]} text={item.location} />
                  ) : null}
                  {!viewAll && item.description ? (
                    <TextView style={[styles.description, color]} text={item.description} />
                  ) : null}
                  {item.educationDocument && !viewAll ? (
                    <RenderDocument fileName={item.educationDocument} />
                  ) : null}
                </View>
              </View>
              {(isExpend || viewAll) && education?.length - 1 != index && (
                <View style={styles.border} />
              )}
            </View>
          );
        })}
      </ScrollView>

      {talentProfile?.education?.length > 1 && !viewAll && (
        <ShowMore
          isExpend={isExpend}
          onExpend={onExpend} />
      )}

      <ConfirmationModal
        title={item?.school}
        type="education"
        onDelete={onDelete}
        setVisibility={toggleModal}
        visibility={visibility}
      />
    </View>
  );
};

export default React.memo(Education);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: Platform.OS == 'ios' ? 5 : 20,
    borderBottomColor: 'rgba(0, 0, 0, 1)',
  },
  report: {
    color: 'rgba(32, 87, 255, 1)',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.mini,
    marginTop: 12,
    textDecorationLine: 'underline',
    alignSelf: 'center',
  },
  pdfImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
    textAlign: 'left'

  },
  actionBtn: {
    width: isPad ? 35 : 30,
    height: isPad ? 35 : 30,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  border: {
    width: '100%',
    height: 1,
    marginVertical: 15,
    backgroundColor: 'black',
    opacity: 0.1,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: 20,
    height: 20,
  },
  detailContainer: {
    marginStart: 10,
    flex: 1,
  },
  borderCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: isPad ? 55 : 40,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
    backgroundColor: '#E9E2DD',
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    textAlign: 'left'
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginVertical: (I18nManager.isRTL && Platform.OS == 'ios') ? 0 : 10,
    textAlign: 'left',
  },
  dept: {
    fontFamily: fonts.light,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 15,
    textAlign: 'left'
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'left',
    marginTop: 10,
    lineHeight: 16,
  },
  description: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    marginTop: 15,
    textAlign: 'left'
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginHorizontal: isPad ? 70 : 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xxlarge : fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: isPad ? 150 : 120,
    height: isPad ? 120 : 80,
    marginVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginVertical: 10,
  },
  dot: {
    width: 2.5,
    height: 2.5,
    backgroundColor: 'rgba(147, 147, 147, 1)',
    borderRadius: 2.5,
    marginHorizontal: 5,
  },
  rowEducation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
});

export const RenderDocument = ({ fileName, customStyle, iconStyle, item }) => {
  const [image, setImage] = useState('');
  const [isLoading, setLoading] = useState(false);
  const navigation = useNavigation();
  const [downloadLoading, setDownloadLoading] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const imageSource = fileName?.includes('pdf');
  const talentProfile = useSelector((state) => state);

  useEffect(() => {
    if (fileName && !fileName.includes('.pdf')) {
      getDocument();
    }
  }, [fileName]);

  const getDocument = async () => {
    setLoading(true);
    const doc = await getUrl(fileName, token);
    if (doc) setImage(doc);
    setLoading(false);

  };
  const viewAttachment = async () => {
    const match = fileName.match(fileNameRegex);


    if (!fileName?.includes('pdf')) {
      if (!fileName) return;
      navigation.navigate('AttachmentView', fileName);
    } else {
      // getContentUrl(fileName);
      navigation.navigate('ViewCertificates', {
        downloadLink: fileName,
        isInApp: false,
        fileName: match?.length ? match[1] : 'Download.pdf',
      });

    }
  };
  const getContentUrl = async (attachment) => {
    setDownloadLoading(true);
    const pdfContent = await getUrl(
      attachment,
      talentProfile?.getWFMProfile?.response?.liferayaccesstoken,
    );
    if (pdfContent) {
      permissionCall(pdfContent, () => {
        setDownloadLoading(false);
      }, '', token);
    }
  };
  const isRTL = I18nManager.isRTL;

  return (
    <Pressable onPress={downloadLoading ? null : viewAttachment} style={[styles.container, customStyle, { flexDirection: isRTL && item?.assessmentFile ? 'row-reverse' : 'row', }]}>
      {!isLoading ? (
        <>
          {!imageSource ? (
            <ImageAuth uri={image} style={styles.certificate} />
          ) : (
            <View style={[styles.certificate, customStyle]}>
              {downloadLoading ? (
                <ActivityIndicator />
              ) : (
                <Image source={AssetsImages.pdf} style={[styles.pdfImage, iconStyle]} />
              )}
            </View>
          )}
        </>
      ) : (
        <View style={styles.certificate}>
          <ActivityIndicator />
        </View>
      )}

      {item?.assessmentFile ? (
        <TextView
          style={[
            styles.report,
            isRTL ? { textAlign: 'right', marginLeft: 10 } : { textAlign: 'left', marginRight: 10 }
          ]}
          text={'Report.pdf'}
        />
      ) : null}
    </Pressable>
  );
};
