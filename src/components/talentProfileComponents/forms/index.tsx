/* eslint-disable react/no-children-prop */
/* eslint-disable no-unsafe-optional-chaining */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  Keyboard,
  Pressable,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { getEnhancedAIBio, getEnhancedAIResponsibilities } from '@api/AI';
import EditTextWithAI from '@components/common/EditTextWithAI';
import { useNavigation } from '@react-navigation/native';
import { RenderSelector } from '@screens/onBoardingSteps/subInterest';
import { IconsType } from '@theme/Icons';
import { useTheme } from '@theme/ThemeProvider';
import { useSession } from '@totara/core';
import { commonStyle } from '@utils';
import crashReportLogger from '@utils/crashlyticsLogger';
import { useFeatureFlags } from '@utils/FeatureFlagsProvider';
import { trackHttpRequestMetrics } from '@utils/trackHttpRequestMetrics';
import { t } from 'i18next';
import { GenerateCodeType } from 'interfaces/onBoardingInterfaces';
import moment from 'moment';
import { Toast } from 'native-base';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import OTPTextInput from 'react-native-otp-textinput';
import { useDispatch, useSelector } from 'react-redux';
import { BASE_URL } from '../../../api/api_client';
import { customFetch, customFetchGET } from '../../../api/api_client_fetch';
import { getCountriesAndEmirates, getPickList } from '../../../api/picklistApi';
import {
  addRecommendation,
  addRecommendation_notifier,
  recommendationRequestNotify,
  updateRecommendation,
} from '../../../api/server_requests';
import { fetchPreferences, getSubInterest, updateTotoraProfile } from '../../../api/totaraApis';
import { API_URL } from '../../../api/urls';
import {
  Avatar,
  BottomSheet,
  Button,
  ConfirmationModal,
  Dropdown,
  EditText,
  Spacer,
  SuggestionsList,
  TextView,
  ThemeBottomButton,
} from '../../../components';
import { useReferCourse } from '../../../hooks';
import { RenderSelectorPreFilled } from '../../../screens/onBoardingSteps/learningPreference';
import { AssetsImages, Colors, Icons, Images } from '../../../theme';
import {
  isSubmitSuccess,
  setActionType,
  setLoading,
} from '../../../totara/actions/getWFMProfileAction';
import { emailRegex } from '../../../utils/AppUtils';
import {
  capitalizeFirstLetter,
  expireSession,
  fileNameRegex,
  fileOptions,
  getCurrentWorkExperience,
  getCustomHeaders,
  pickDocument,
  pickImage,
  pickImageFromCamera,
  pickListType,
  splitCamelCase,
  validateYouTubeUrl,
} from '../../../utils/constants';
import useStyles, { languageHeader } from './styles';

const getCode = (number) => {
  return number && [...number]?.filter((_, i) => i <= 2).join('');
};

const getNumber = (number) => {
  return number && [...number]?.filter((_, i) => i >= 3).join('');
};

function checkEmptyValidation(fields) {
  const keys = Object.keys(fields);
  const emptyFields = [];

  keys.forEach((item) => {
    if (!fields[item]) {
      emptyFields.push(item);
    }
  });

  return emptyFields;
}

function checkChange(previousState, nextState) {
  const keys = Object.keys(previousState);
  const changeFields = [];

  keys.forEach((item) => {
    if (previousState[item] !== nextState[item]) {
      changeFields.push(item);
    }
  });

  return changeFields;
}

export const AboutForm = ({ onChange, data, loading }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);

  const [fields, setFields] = useState<Record<string, string>>({
    bio: data?.talentProfile?.bio,
    email: data?.talentProfile?.email,
    teamsId: data?.talentProfile?.teamsId,
    linkedInURL: data?.talentProfile?.linkedInURL,
    facebook: data?.talentProfile?.facebook,
    twitter: data?.talentProfile?.twitter,
    instagram: data?.talentProfile?.instagram,
    video: data?.talentProfile?.video,
    prefrences: data?.talentProfile?.prefrences,
  });
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [size, setSize] = useState('');
  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };
  const { featureFlags } = useFeatureFlags();
  const { styles } = useStyles();

  const [preferencesList, setPreferences] = useState([]);
  const isAnyChange = checkChange(
    {
      bio: fields?.bio,
      // email: fields?.email,
      teamsId: fields?.teamsId,
      linkedInURL: fields?.linkedInURL,
      facebook: fields?.facebook,
      twitter: fields?.twitter,
      instagram: fields?.instagram,
      video: fields?.video,
      prefrences: fields?.prefrences,
    },
    {
      bio: data?.talentProfile?.bio,
      // email: data?.talentProfile?.email,
      teamsId: data?.talentProfile?.teamsId,
      linkedInURL: data?.talentProfile?.linkedInURL,
      facebook: data?.talentProfile?.facebook,
      twitter: data?.talentProfile?.twitter,
      instagram: data?.talentProfile?.instagram,
      video: data?.talentProfile?.video,
      prefrences: data?.talentProfile?.prefrences,
    },
  );

  const isValidUrl = (url) => {
    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
    return urlRegex.test(url);
  };

  const onSubmitData = () => {
    const emptyFields = checkEmptyValidation({
      bio: fields?.bio,
      // email: fields?.email,
      teamsId: fields?.teamsId,
      linkedInURL: fields?.linkedInURL,
      facebook: fields?.facebook,
      twitter: fields?.twitter,
      instagram: fields?.instagram,
      video: fields?.video,
    });

    if (isAnyChange?.length) {
      if (fields?.teamsId?.length && !isValidUrl(fields?.teamsId)) {
        alert(`Enter Valid Teams Url`);
        return;
      } else if (fields?.linkedInURL?.length && !isValidUrl(fields?.linkedInURL)) {
        alert(`Enter Valid LinkedIn Url`);
        return;
      } else if (fields?.facebook?.length && !isValidUrl(fields?.facebook)) {
        alert(`Enter Valid Facebook Url`);
        return;
      } else if (fields?.twitter?.length && !isValidUrl(fields?.twitter)) {
        alert(`Enter Valid Twitter Url`);
        return;
      } else if (fields?.instagram?.length && !isValidUrl(fields?.instagram)) {
        alert(`Enter Valid Instagram Url`);
        return;
      } else if (fields?.video?.length && !validateYouTubeUrl(fields?.video)) {
        alert(`Enter Valid Youtube Url`);
        return;
      } else if (fields?.prefrences?.length == 0 || fields?.prefrences.split(',')?.length < 1) {
        alert('Select Atleast One Preference');
        return;
      }
      onChange(fields);
    }
  };

  const getFileName = (e) => {
    setSize(e.size);
    setFields({ ...fields, resumeCopy: e?.fileName });
  };

  const onPressDelete = () => {
    setFields({ ...fields, resumeCopy: '' });
  };

  const getLists = async () => {
    setLoading(true);
    const result = await getPickList(data?.response?.liferayaccesstoken, pickListType.preferences);
    setPreferences(result);
    setLoading(false);
  };

  useEffect(() => {
    getLists();
  }, []);

  const onChangeCountry = (selectedPreference) => {
    let currentPreferences = fields.prefrences ? fields.prefrences.split(',') : [];

    if (currentPreferences.includes(selectedPreference)) {
      currentPreferences = currentPreferences.filter((pref) => pref !== selectedPreference);
    } else {
      currentPreferences.push(selectedPreference);
    }

    const updatedPreferences = currentPreferences.join(',');

    // Update the fields state with the new preferences
    setFields((prevFields) => ({
      ...prevFields,
      prefrences: updatedPreferences,
    }));
  };
  const { isDarkMode } = useTheme();
  const enhanceBioAI = () => {
    return getEnhancedAIBio(talentProfile);
  };

  return (
    <View style={styles.mainFormContainer}>
      <ScrollView style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={styles.scrollViewStyle}>
          {featureFlags.aiFeature ? (
            <EditTextWithAI
              returnKeyType={'default'}
              value={fields?.bio}
              numberOfLines={50}
              multiline
              textAlignVertical={'top'}
              customInputStyles={{
                textAlignVertical: 'top',
                height: '100%',
              }}
              onWriteWithAI={enhanceBioAI}
              onChange={onChangeData('bio')}
              placeholder="Enter bio"
              label="Bio"
              inputViewStyle={{ marginBottom: 24, height: 140 }}
            />
          ) : (
            <EditText
              returnKeyType={'default'}
              value={fields?.bio}
              numberOfLines={50}
              multiline
              textAlignVertical={'top'}
              customInputStyles={{
                textAlignVertical: 'top',
                height: '100%',
              }}
              onChange={onChangeData('bio')}
              placeholder="Enter bio"
              label="Bio"
              inputViewStyle={{ marginBottom: 24, height: 140 }}
            />
          )}

          <Dropdown
            style={{ marginBottom: 16 }}
            placeholder={fields?.prefrences == '' ? 'Select Preferences' : ''}
            data={preferencesList}
            selectedList={fields?.prefrences ? fields?.prefrences.split(',') : null}
            label={'Preferred mode of learning'}
            type="label"
            getSelectedValue={onChangeCountry}
            onClickOfSelectedPreferences={onChangeCountry}
            selectedTextStyle={{ color: fields?.prefrences != null ? 'transparent' : 'black' }}
          />

          <EditText
            value={fields?.email}
            image={AssetsImages.emailIcon}
            onChange={onChangeData('email')}
            placeholder="Enter email"
            label="Email"
            disable
            inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
          />

          <EditText
            value={fields?.teamsId}
            iconStyle={{ width: 20, height: 19 }}
            image={AssetsImages.teams_grey}
            onChange={onChangeData('teamsId')}
            placeholder="https:teams.microsoft.com"
            label="Microsoft Teams"
            inputViewStyle={{ marginBottom: 24 }}
          />
          <EditText
            value={fields?.linkedInURL}
            image={AssetsImages.linkedin}
            onChange={onChangeData('linkedInURL')}
            placeholder="https:www.linkedin.com"
            label="LinkedIn"
            inputViewStyle={{ marginBottom: 24 }}
          />
          <EditText
            value={fields?.facebook}
            iconStyle={{ width: 20, height: 25 }}
            notint
            image={isDarkMode ? AssetsImages.facebookDark : AssetsImages.fb}
            onChange={onChangeData('facebook')}
            placeholder={'https:www.facebook.com'}
            label="Facebook"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            value={fields?.twitter}
            iconStyle={{ width: 16, height: 14 }}
            notint
            image={AssetsImages.twitter}
            onChange={onChangeData('twitter')}
            placeholder="https:www.twitter.com"
            label="Twitter ( X )"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            value={fields?.instagram}
            iconStyle={{ width: 28, height: 28 }}
            notint
            image={AssetsImages.insta}
            onChange={onChangeData('instagram')}
            placeholder="https:www.instagram.com"
            label="Instagram"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            multiline={false}
            numberOfLines={1}
            value={fields?.video}
            iconStyle={{ width: 28, height: 28 }}
            notint
            image={AssetsImages.playIntro}
            onChange={onChangeData('video')}
            placeholder="https:www.youtube.com"
            label={`${t('Intro Video URL')} (YouTube)`}
            inputViewStyle={{ marginBottom: 24 }}
          />
        </KeyboardAwareScrollView>
      </ScrollView>

      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
    </View>
  );
  return (
    <View style={{ flex: 1, backgroundColor: 'blue' }}>
      <View
        style={{
          backgroundColor: 'red',
          right: 0,
          left: 0,
          bottom: 0,
          width: '100%',
          height: 100,
          zIndex: 1,
        }}
      >
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>

      {/* <KeyboardAwareScrollView>
                <ScrollView style={{ flex: 1, backgroundColor: 'yellow', paddingBottom: 1000 }}>

                    <EditText
                        returnKeyType={'default'}
                        value={fields?.bio}
                        numberOfLines={20}
                        multiline

                        customInputStyles={{
                            textAlignVertical: 'top',
                            height: '90%'
                        }}
                        onChange={onChangeData("bio")}
                        placeholder='Enter Bio'
                        label='Bio'
                        inputViewStyle={{ marginBottom: 24, height: 200 }}
                    />
                    <EditText
                        value={fields?.email}
                        image={AssetsImages.emailIcon}
                        onChange={onChangeData("email")}
                        placeholder='Enter email'
                        label='Email'
                        disable
                        inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
                    />

                    <EditText
                        value={fields?.teamsId}
                        iconStyle={{ width: 20, height: 19 }}
                        notint
                        image={AssetsImages.teams}
                        onChange={onChangeData("teamsId")}
                        placeholder='https://teams.microsoft.com/'
                        label='Microsoft Teams'
                        inputViewStyle={{ marginBottom: 24 }}
                    />
                    <EditText
                        value={fields?.linkedInURL}
                        image={AssetsImages.linkedin}
                        onChange={onChangeData("linkedInURL")}
                        placeholder='https://www.linkedin.com/'
                        label='LinkedIn'
                        inputViewStyle={{ marginBottom: 24 }}
                    />
                    <EditText
                        value={fields?.facebook}
                        iconStyle={{ width: 11, height: 20 }}
                        notint
                        image={AssetsImages.fb}
                        onChange={onChangeData("facebook")}
                        placeholder='https://www.facebook.com/'
                        label='Facebook'
                        inputViewStyle={{ marginBottom: 24 }}
                    />

                    <EditText
                        value={fields?.twitter}
                        iconStyle={{ width: 16, height: 14 }}
                        notint
                        image={AssetsImages.twitter}
                        onChange={onChangeData("twitter")}
                        placeholder='https://www.twitter.com/'
                        label='X (Twitter)'
                        inputViewStyle={{ marginBottom: 24 }}
                    />

                    <EditText
                        value={fields?.instagram}
                        iconStyle={{ width: 28, height: 28 }}
                        notint
                        image={AssetsImages.insta}
                        onChange={onChangeData("instagram")}
                        placeholder='https://www.instagram.com/'
                        label='Instagram'
                        inputViewStyle={{ marginBottom: 24 }}
                    />

                    <EditText
                        value={fields?.video}
                        iconStyle={{ width: 28, height: 28 }}
                        notint
                        image={AssetsImages.playIntro}
                        onChange={onChangeData("video")}
                        placeholder='www.youtube.com/'
                        label='Intro Video URL (YouTube)'
                        inputViewStyle={{ marginBottom: 24 }}
                    />


                </ScrollView>
            </KeyboardAwareScrollView> */}
    </View>
  );
};

export const EditDetailsForm = ({ onChange, data, loading }) => {
  const uaeReponse = useSelector((state) => state?.getWFMProfile?.response);
  const { styles } = useStyles();
  const [fields, setFields] = useState<Record<string, object[]>>({
    fullname: uaeReponse?.fullnameEN,
    // fullname: data?.talentProfile?.fullname,
    workExperience: [
      {
        department: getCurrentWorkExperience(data?.talentProfile?.workExperience)?.department,
        jobTitle: getCurrentWorkExperience(data?.talentProfile?.workExperience)?.jobTitle,
        experience: getCurrentWorkExperience(data?.talentProfile?.workExperience)?.experience,
      },
    ],
  });
  const onChangeData = (key) => (value) => {
    const newFields = {
      ...fields,
      workExperience: [
        {
          ...fields?.workExperience?.[0],
          [key]: value,
        },
      ],
    };
    setFields(newFields);
  };
  const onSubmitData = () => {
    const emptyFields = checkEmptyValidation({
      mobile: fields?.fullname,
      department: fields?.workExperience?.[0]?.department,
      jobTitle: fields?.workExperience?.[0]?.jobTitle,
      experience: fields?.workExperience?.[0]?.experience,
    });

    const isAnyChange = checkChange(
      {
        mobile: fields?.fullname,
        department: fields?.workExperience?.[0]?.department,
        jobTitle: fields?.workExperience?.[0]?.jobTitle,
        experience: fields?.workExperience?.[0]?.experience,
      },
      {
        mobile: data?.fullname,
        department: data?.workExperience?.[0]?.department,
        jobTitle: data?.workExperience?.[0]?.jobTitle,
        experience: data?.workExperience?.[0]?.experience,
      },
    );

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        onChange(fields);
      }
    } else {
      Alert.alert('Fields are empty! ' + emptyFields?.join());
    }
  };

  return (
    <View>
      <View style={{ padding: 20 }}>
        <Avatar onChange={(url) => onChange({ picture: url })} />
      </View>
      <EditText
        disable={true}
        notint
        image={AssetsImages.userprofileGray}
        value={fields?.fullname}
        onChange={onChangeData('fullname')}
        placeholder="Enter name"
        label="Full Name"
        inputViewStyle={{ marginBottom: 24 }}
      />
      <EditText
        disable={true}
        iconStyle={{ height: 26, width: 20 }}
        value={fields?.workExperience[0]?.department}
        image={AssetsImages.talentProfileDepartmentIcon}
        onChange={onChangeData('department')}
        placeholder="Enter Entity Name"
        label="Entity Name"
        inputViewStyle={{ marginBottom: 24 }}
      />

      <EditText
        disable={true}
        value={fields?.workExperience[0]?.jobTitle}
        iconStyle={{ width: 24, height: 24 }}
        notint
        image={AssetsImages.luggage}
        onChange={onChangeData('jobTitle')}
        placeholder="Enter Job Title"
        label="Job Title"
        inputViewStyle={{ marginBottom: 24 }}
      />
      <EditText
        disable={true}
        value={fields?.workExperience[0]?.experience}
        image={AssetsImages.calendar}
        onChange={onChangeData('experience')}
        placeholder="Enter Year(s) of Experience"
        label="Year(s) of Experience"
        inputViewStyle={{ marginBottom: 24 }}
      />
      <Button
        isDisabled
        isLoading={loading}
        textStyle={styles.buttonText}
        onPress={onSubmitData}
        testID="submit"
        text={'Save'}
        type="fill"
      />
    </View>
  );
};

const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = time % 60;
  return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
};

const VarifyModal = ({ onDismiss, visible, user, type, token, onClose, visibility, runTimer }) => {
  const [timeRemaining, setTimeRemaining] = useState(120);
  const [timerStarted, setTimerStarted] = useState(false);
  const [otpSheetVisibility, setOtpSheetVisibility] = useState(false);
  const dispatch = useDispatch();
  const { styles } = useStyles();
  useEffect(() => {
    let interval;
    if (timerStarted && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((time) => time - 1);
      }, 1000);
    } else if (timeRemaining <= 0) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [timerStarted, timeRemaining]);

  useEffect(() => {
    if (visible) {
      generateCode();
    }
  }, [visible]);

  const handleStartTimer = () => {
    setTimerStarted(true);
  };

  const verifyCode = async (code) => {
    const url = BASE_URL + API_URL.verifyCode.url;
    try {
      const headers: Record<string, string> = getCustomHeaders(token);
      user = { ...user, type: type === 'mobile' ? '1' : '2', code };
      const result = await customFetch<GenerateCodeType>(url, 'POST', user, {}, headers);
      if (result.status == 'success') {
        Toast.show({
          text: result?.description,
          textStyle: { textAlign: 'left' },
        });
        onDismiss(true);
      } else {
        // onDismiss(true)
        // onClose()
        Alert.alert('Error', 'Please enter valid otp.');
        // Toast.show({
        //     text: result?.description
        // });
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'AboutForm VerifyModal',
        url: url,
        additionalInfo: 'Failed to verify user',
      });
    } finally {
      if (user) trackHttpRequestMetrics(url, 'POST', user, 'AboutForm VerifyModal');
    }
  };

  const generateCode = async () => {
    setTimeRemaining(120);

    try {
      const headers: Record<string, string> = getCustomHeaders(token);

      user = { ...user, type: type === 'mobile' ? '1' : '2' };
      if (type === 'mobile') {
        delete user.email;
      } else {
        delete user.mobileNumber;
      }

      const url = BASE_URL + API_URL.generateCode.url;

      const result = await customFetch<GenerateCodeType>(url, 'POST', user, {}, headers);
      if (result?.status == 'BAD_REQUEST' || result.status == 'INTERNAL_SERVER_ERROR') {
        alert(result?.title);
        return;
      }

      if (result.status == 'success') {
        handleStartTimer();
        runTimer && runTimer();
        setOtpSheetVisibility(true);
      } else {
        alert(result?.description);
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'talentProfileComponents/index.tsx generateCode',
        url: BASE_URL + API_URL.generateCode.url,
        additionalInfo: 'Failed to generate code',
      });
      alert('Something went wrong!');
      expireSession(dispatch);
    }
  };

  const { theme } = useTheme();

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={{ height: 270, justifyContent: 'flex-start' }}
      visiblity={otpSheetVisibility}
      setVisibility={onDismiss}
      children={
        <View style={styles.VerifyModalContainer}>
          <Pressable onPress={onClose} style={[styles.row, { marginBottom: 35 }]}>
            <Icons
              color={theme.tintColor}
              type={IconsType.Ionicons}
              name={'arrow-back'}
              size={20}
            />
            <TextView style={[styles.goBack, { color: theme.textColor }]} text={'Go back'} />
          </Pressable>
          <OTPTextInput
            textInputStyle={[styles.textInputStyle, { color: theme.textColor }]}
            handleTextChange={(e) => {
              if (e.length == 4) verifyCode(e, user.token);
            }}
            offTintColor={Colors.borderGray}
            tintColor={Colors.borderGray}
            inputCount={4}
          />
          <View style={styles.rowJustified}>
            <View style={styles.row}>
              <Image style={styles.clock} source={AssetsImages.clock} />
              <TextView
                style={[styles.resendCode, { color: theme.textColor }]}
                text={'Resend code enables in ' + formatTime(timeRemaining)}
              />
            </View>
            {timeRemaining <= 0 && (
              <Pressable onPress={generateCode} style={styles.row}>
                <Image
                  style={[styles.clock, { tintColor: theme.tintColor }]}
                  source={AssetsImages.refresh}
                />
                <TextView style={styles.resend} type="h6" text={'Resend code'} />
              </Pressable>
            )}
          </View>
        </View>
      }
    />
  );
};

const getUniqueContries = (array) => {
  const uniqueCountries = new Set();

  // Filter the array to include only the first occurrence of each country
  const uniqueArray = array.filter((obj) => {
    if (!uniqueCountries.has(obj.countries)) {
      uniqueCountries.add(obj.countries);
      return true;
    }
    return false;
  });
  return uniqueArray;
};

export const ContactInfoForm = ({ onChange, data, loading }) => {
  const [generateCodeLoading, setGenerateCodeLoading] = useState(false);
  const [countriesData, setCountriesData] = useState({
    countries: [],
    data: [],
    emirates: [],
    area: [],
  });

  const [timeRemaining, setTimeRemaining] = useState(0); // Example starting time
  const [intervalId, setIntervalId] = useState(null);

  const [timeRemainingForPhone, setTimeRemainingForPhone] = useState(0); // Example starting time
  const [intervalIdPhone, setIntervalIdForMobile] = useState(null);
  const { styles } = useStyles();
  const [prefarredEmail, setPrefarredEmail] = useState(data?.talentProfile?.preferredEmail);
  const [prefarredMobile, setPrefarredMobile] = useState(data?.talentProfile?.preferredMobile);
  const [isAnyChange, setIsAnyChange] = useState(false);
  const [visibleVerifyModal, setVisibleVerifyModal] = useState({
    type: 'email',
    visible: false,
  });

  const { theme, isDarkMode } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const { t } = useTranslation();
  const [fields, setFields] = useState<Record<string, string>>({
    mobile: {
      number: getNumber(data?.response?.mobile),
      code: getCode(data?.response?.mobile),
    },
    email: data?.response?.email,
    location: data?.talentProfile?.location,
    country: data.talentProfile?.country,
    emirates: data?.talentProfile?.emirates,
    area: data?.talentProfile?.area,
    address: data?.talentProfile?.address?.toString(),
    // alternateMobile: {
    //     number: getNumber(data?.talentProfile?.alternateMobile),
    //     code: getCode(data?.talentProfile?.alternateMobile)
    // },
    alternateMobile: data?.talentProfile?.alternateMobile,
    alternateEmail: data?.talentProfile?.alternateEmail,
    // landline: {
    //     number: getNumber(data?.talentProfile?.landline),
    //     code: getCode(data?.talentProfile?.landline)
    // },
    landline: data?.talentProfile?.landline,
  });

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  useEffect(() => {
    const isAnyChange = checkChange(
      {
        address: fields?.address,
        alternateMobile: fields?.alternateMobile,
        // alternateMobile: fields?.alternateMobile?.number,
        alternateEmail: fields?.alternateEmail,
        country: fields?.country,
        emirates:
          fields?.emirates === 'Select Emirates' ? data?.talentProfile?.emirates : fields?.emirates,
        area: fields?.area === 'Select Area' ? data?.talentProfile?.area : fields?.area,
        landline: fields?.landline,
        // landline: fields?.landline?.number,
        prefarredEmail: prefarredEmail,
        prefarredMobile: prefarredMobile,
      },
      {
        address: data?.talentProfile?.address,
        alternateMobile: data?.talentProfile?.alternateMobile,
        // alternateMobile: getNumber(data?.talentProfile?.alternateMobile),
        alternateEmail: data?.talentProfile?.alternateEmail,
        country: data.talentProfile?.country,
        emirates: data?.talentProfile?.emirates,
        area: data?.talentProfile?.area,
        landline: data?.talentProfile?.landline,
        // landline: getNumber(data?.talentProfile?.landline),
        prefarredMobile: data?.talentProfile?.preferredMobile,
        prefarredEmail: data?.talentProfile?.preferredEmail,
      },
    );
    if (isAnyChange.length && isAnyChange) {
      setIsAnyChange(true);
    } else {
      setIsAnyChange(false);
    }
  }, [prefarredEmail, prefarredMobile, fields, data?.talentProfile]);

  const startTimer = () => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    setTimeRemaining(60);
    const interval = setInterval(() => {
      setTimeRemaining((time) => {
        if (time > 0) {
          return time - 1;
        } else {
          clearInterval(interval);
          setIntervalId(null);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(interval);
  };

  const startTimerForPhone = () => {
    if (intervalIdPhone) {
      clearInterval(intervalIdPhone);
    }
    setTimeRemainingForPhone(60);
    const interval = setInterval(() => {
      setTimeRemainingForPhone((time) => {
        if (time > 0) {
          return time - 1;
        } else {
          clearInterval(intervalIdPhone);
          setIntervalIdForMobile(null);
          return 0;
        }
      });
    }, 1000);
    setIntervalIdForMobile(interval);
  };

  const onChangeDropdownInput = (key) => (value) => {
    setFields({
      ...fields,
      [key]: {
        ...fields[key],
        number: value,
      },
    });
  };

  const onSubmitData = () => {
    const emptyFields = checkEmptyValidation({
      alternateEmail: fields?.alternateEmail,
    });

    if (!emptyFields?.length) {
      if (isAnyChange) {
        if (emailRegex.test(fields?.alternateEmail)) {
          onChange({ ...fields, preferredMobile: prefarredMobile, preferredEmail: prefarredEmail });
        } else {
          Alert.alert('Email is not correct.', 'Please provide correct email address.');
        }
      }
    } else {
      Alert.alert('Fields are empty! ', `Please provide ${emptyFields?.join()}.`);
    }
  };

  useEffect(() => {
    getLocation();
  }, []);

  const getLocation = async () => {
    const url = `${BASE_URL}o/c/countries?pageSize=-1`;

    const headers = getCustomHeaders(data?.response?.liferayaccesstoken);
    const result = await customFetchGET(url, headers);

    if (result.items?.length) {
      const getCurrentEmirates = result?.items?.filter((obj) => obj.countries === fields?.country);
      const getCurrentArea = result?.items?.filter((obj) => obj.emirates === fields?.emirates);

      setCountriesData({
        ...countriesData,
        data: result.items,
        emirates: getCurrentEmirates,
        countries: getUniqueContries(result.items),
        area: getCurrentArea,
      });
    }
  };

  const onChangeCountry = (e) => {
    if (fields?.country === e) return;
    setFields({
      ...fields,
      country: e,
      emirates: 'Select Emirates',
      area: 'Select Area',
    });

    const getCurrentEmirates = countriesData?.data?.filter((obj) => obj.countries === e);

    setCountriesData({
      ...countriesData,
      emirates: getCurrentEmirates,
      area: [],
    });
  };

  const onChangeEmirates = (e) => {
    setFields({
      ...fields,
      emirates: e,
    });
    const getCurrentArea = countriesData?.data?.filter((obj) => obj.emirates === e);
    setCountriesData({
      ...countriesData,
      area: getCurrentArea,
    });
  };

  const onChangeArea = (e) => {
    if (fields?.area === e) return;
    setFields({
      ...fields,
      area: e,
    });
  };
  const onVerifyButtonPressed = (type) => () => {
    setVisibleVerifyModal({ type, visible: false });
    setTimeout(() => {
      if (emailRegex.test(fields?.alternateEmail)) {
        setVisibleVerifyModal({ type, visible: true });
        setGenerateCodeLoading(true);
        setTimeout(() => {
          setGenerateCodeLoading(false);
        }, 2000);
      } else {
        Alert.alert(t(`Please provide correct ${type} address.`), '', [
          {
            text: t('Ok'),
            onPress: () => {},
          },
        ]);
      }
    }, 0);
  };

  const enableButton = () => {
    if (fields?.country !== data?.talentProfile?.country) {
      let isInValid = false;
      if (fields?.emirates == 'Select Emirates') {
        isInValid = true;
      }
      if (fields?.area == 'Select Area') {
        isInValid = true;
      }
      if (!isInValid) {
        return (
          !isAnyChange ||
          (fields?.alternateMobile !== data?.talentProfile?.alternateMobile &&
            fields?.alternateMobile?.number?.length) ||
          (fields?.alternateEmail !== data?.talentProfile?.alternateEmail &&
            fields?.alternateEmail?.length)
        );
      } else {
        return isInValid;
      }
    } else {
      return (
        !isAnyChange ||
        (fields?.alternateMobile !== data?.talentProfile?.alternateMobile &&
          fields?.alternateMobile?.length) ||
        (fields?.alternateEmail !== data?.talentProfile?.alternateEmail &&
          fields?.alternateEmail?.length)
      );
    }
  };

  const setVisibilityFromModal = () => {
    setVisibleVerifyModal({ ...visibleVerifyModal, visible: !visibleVerifyModal.visible });
  };

  const runTimer = () => {
    visibleVerifyModal.type == 'mobile' ? startTimerForPhone() : startTimer();
  };
  return (
    <View style={styles.mainFormContainer}>
      <ScrollView style={styles.scrollViewStyle}>
        <KeyboardAwareScrollView style={{ marginBottom: 80 }}>
          <EditText
            disable
            editable={false}
            getCountryCode={(code) => {
              setFields({ ...fields, mobile: { ...fields.mobile, code } });
            }}
            codeCountry={fields?.mobile?.code || '971'}
            value={fields?.mobile?.number}
            image={AssetsImages.mobile}
            onChange={onChangeDropdownInput('mobile')}
            placeholder="41312313"
            label="Phone"
            inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
          />

          <EditText
            disable
            value={fields?.email}
            image={AssetsImages.emailIcon}
            onChange={onChangeData('email')}
            placeholder="Enter email"
            label="Email"
            inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
          />

          <Dropdown
            style={{ marginBottom: 16 }}
            placeholder={fields?.country || t('Country')}
            data={countriesData?.countries?.map((item) => ({
              label: item?.countries,
              value: item?.countries,
            }))}
            label={'Country'}
            getSelectedValue={onChangeCountry}
          />

          <Dropdown
            style={{ marginBottom: 16 }}
            getSelectedValue={onChangeEmirates}
            placeholder={fields?.emirates || t('Select Emirates')}
            data={countriesData?.emirates?.map((item) => ({
              label: item?.emirates,
              value: item?.emirates,
            }))}
            label={'Emirate'}
          />

          <EditText
            customInputStyles={{ width: '90%' }}
            value={fields?.area}
            image={AssetsImages.address}
            onChange={onChangeData('area')}
            placeholder="Enter Area"
            label="Area"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            customInputStyles={{ width: '90%' }}
            value={fields?.address}
            image={AssetsImages.address}
            onChange={onChangeData('address')}
            placeholder="Enter Address"
            label="Address"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            editable={true}
            maxLength={12}
            keyboardType="numeric"
            value={fields?.landline || ''}
            image={AssetsImages.landline}
            onChange={onChangeData('landline')}
            placeholder="02 123 4567"
            label="Landline"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            value={fields?.alternateEmail}
            image={AssetsImages.emailIcon}
            onChange={onChangeData('alternateEmail')}
            placeholder="Enter work email"
            label="Work Email"
            keyboardType="email-address"
            inputViewStyle={{ marginBottom: 8 }}
          />

          <View style={styles.preffarredContainer}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Pressable
                onPress={() => {
                  if (!fields?.alternateEmail?.length) {
                    Alert.alert('Please enter work email.');
                    return;
                  }
                  if (fields?.alternateEmail === prefarredEmail) {
                    setPrefarredEmail(fields?.email);
                  } else {
                    setPrefarredEmail(fields?.alternateEmail);
                  }
                }}
              >
                <Image
                  style={[
                    {
                      width: 20,
                      height: 20,
                      resizeMode: 'contain',
                      marginRight: 10,
                      borderRadius: isDarkMode ? 5 : 0,
                    },
                    { borderRadius: isDarkMode ? 5 : 0 },
                    fields?.alternateEmail !== prefarredEmail &&
                      isDarkMode && { tintColor: theme.bgLightDark },
                  ]}
                  source={
                    fields?.alternateEmail === prefarredEmail
                      ? AssetsImages.selectedCheckBox
                      : AssetsImages.uncheck
                  }
                />
              </Pressable>
              <TextView
                style={[styles.preffarredLabel, { color: theme.textColor }]}
                text={'Preferred Work Email'}
              />
            </View>
            {fields?.alternateEmail !== data?.talentProfile?.alternateEmail &&
            fields?.alternateEmail?.length ? (
              <TouchableOpacity
                disabled={Boolean(timeRemaining != 0)}
                onPress={onVerifyButtonPressed('email')}
              >
                {generateCodeLoading &&
                visibleVerifyModal.type == 'email' &&
                (Boolean(timeRemaining !== 0) || generateCodeLoading) ? (
                  <ActivityIndicator />
                ) : (
                  <TextView
                    style={[
                      styles.preffarredLabel,
                      { color: timeRemaining === 0 ? 'rgba(14, 121, 219, 1)' : '#000' },
                      isDarkMode && { color: Colors.activeStateDark },
                    ]}
                    text={timeRemaining === 0 ? 'Verify work email' : '00: ' + timeRemaining}
                  />
                )}
              </TouchableOpacity>
            ) : null}
          </View>

          <EditText
            maxLength={12}
            keyboardType="numeric"
            editable
            value={fields?.alternateMobile}
            image={AssetsImages.mobile}
            onChange={onChangeData('alternateMobile')}
            placeholder="971 50 123 4567"
            label="Alternate Phone"
            inputViewStyle={{ marginBottom: 8 }}
          />

          <View style={styles.preffarredContainer}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Pressable
                onPress={() => {
                  if (!fields?.alternateMobile?.length) {
                    Alert.alert('Please enter alternate mobile number.');
                    return;
                  }
                  if (fields?.alternateMobile === prefarredMobile) {
                    setPrefarredMobile(data?.talentProfile?.mobile);
                  } else {
                    setPrefarredMobile(fields?.alternateMobile);
                  }
                }}
              >
                {/* <Image
                  style={{ width: 20, height: 20, resizeMode: 'contain', marginRight: 10, borderRadius: isDarkMode ? 5 : 0 }}
                  source={
                    fields?.alternateMobile === prefarredMobile
                      ? AssetsImages.selectedCheckBox
                      : (isDarkMode ? AssetsImages.checkBoxDark : AssetsImages.uncheck)
                  }
                /> */}
                <Image
                  style={[
                    {
                      width: 20,
                      height: 20,
                      resizeMode: 'contain',
                      marginRight: 10,
                      borderRadius: isDarkMode ? 5 : 0,
                    },
                    fields?.alternateMobile !== prefarredMobile &&
                      isDarkMode && { tintColor: theme.bgLightDark },
                  ]}
                  source={
                    fields?.alternateMobile === prefarredMobile
                      ? AssetsImages.selectedCheckBox
                      : AssetsImages.uncheck
                  }
                />
              </Pressable>
              <TextView
                style={[styles.preffarredLabel, { color: theme.textColor }]}
                text={'Preferred phone'}
              />
            </View>
            {fields?.alternateMobile !== data?.talentProfile?.alternateMobile &&
            fields?.alternateMobile?.length ? (
              <TouchableOpacity
                disabled={Boolean(timeRemainingForPhone != 0)}
                onPress={onVerifyButtonPressed('mobile')}
              >
                {generateCodeLoading &&
                visibleVerifyModal.type == 'mobile' &&
                (Boolean(timeRemainingForPhone !== 0) || generateCodeLoading) ? (
                  <ActivityIndicator />
                ) : (
                  <TextView
                    style={[
                      styles.preffarredLabel,
                      { color: timeRemainingForPhone === 0 ? 'rgba(14, 121, 219, 1)' : '#000' },
                    ]}
                    text={
                      timeRemainingForPhone === 0
                        ? 'Verify alternate phone'
                        : '00: ' + timeRemainingForPhone
                    }
                  />
                )}
              </TouchableOpacity>
            ) : null}
          </View>

          {visibleVerifyModal.visible ? (
            <VarifyModal
              visibility={setVisibilityFromModal}
              token={data?.response?.liferayaccesstoken}
              user={{
                userId: data?.talentProfile?.userIdonboarding,
                mobileNumber: fields?.alternateMobile,
                email: fields?.alternateEmail,
              }}
              type={visibleVerifyModal?.type}
              onDismiss={(e) => {
                if (e) {
                  if (visibleVerifyModal?.type == 'email') {
                    onChange({
                      ...fields,
                      alternateEmail: fields?.alternateEmail,
                      noSuccessMessage: true,
                      alternateMobile: data?.talentProfile?.alternateMobile,
                    });
                  } else {
                    onChange({
                      ...fields,
                      alternateMobile: fields?.alternateMobile,
                      noSuccessMessage: true,
                      alternateEmail: data?.talentProfile?.alternateEmail,
                    });
                  }
                }
                setVisibleVerifyModal({ ...visibleVerifyModal, visible: false });
              }}
              onClose={() => {
                setVisibleVerifyModal({ ...visibleVerifyModal, visible: false });
              }}
              runTimer={runTimer}
              visible={visibleVerifyModal.visible}
            />
          ) : null}
        </KeyboardAwareScrollView>
      </ScrollView>

      <ThemeBottomButton
        isDisabled={!!enableButton()}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
    </View>
  );
};

const RenderLanguageHeader = ({ onPress, heading, customStyle, loading }) => {
  const { theme, isDarkMode } = useTheme();

  return (
    <View style={[languageHeader.row, customStyle]}>
      <TextView style={[languageHeader.title, { color: theme.textColor }]} text={heading} />

      <View style={languageHeader.rowAbsolute}>
        {!loading ? (
          <TouchableOpacity
            style={[
              languageHeader.iconImage,
              {
                borderColor: theme.borderBackground,
              },
            ]}
            onPress={onPress}
          >
            <Image
              source={AssetsImages.plus}
              style={[
                languageHeader.actionIcon,
                isDarkMode && { tintColor: Colors.activeStateDark },
              ]}
            />
          </TouchableOpacity>
        ) : (
          <View
            style={[
              languageHeader.iconImage,
              {
                borderColor: theme.borderBackground,
              },
            ]}
          >
            <ActivityIndicator size={'small'} color={theme.tintColor} />
          </View>
        )}
      </View>
    </View>
  );
};

export const EditPersonalDetailsForm = ({ onChange, data, loading, getTalentProfile }) => {
  const [maritalList, setList] = useState([]);
  const navigation = useNavigation();
  const { t } = useTranslation();

  const languageArray = data?.talentProfile?.languagesUser
    ? data?.talentProfile?.languagesUser
    : [];
  const [visibility, setVisibility] = useState(false);
  const toggleModal = () => setVisibility(!visibility);
  const [languageId, setLangId] = useState(null);
  const [languageName, setLangName] = useState('');
  const stateLoading = useSelector((state) => state?.getWFMProfile.isLoading);
  const { styles } = useStyles();
  const [fields, setFields] = useState<Record<string, string>>({
    birthdate: data?.talentProfile?.birthdate,
    // birthdate: moment(data?.talentProfile?.birthdate).format('YYYY-MM-DD'),
    gender: data?.talentProfile?.gender,
    maritalStatus: data?.talentProfile?.maritalStatus,
    // nationality: data?.talentProfile?.nationality,
    // emiratesId: data?.talentProfile?.emiratesId,
  });

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const isAnyChange = checkChange(
    {
      // birthdate: fields?.birthdate,
      birthdate: moment(fields?.birthdate).format('YYYY-MM-DD'),
      gender: fields?.gender,
      maritalStatus: fields?.maritalStatus,
      // nationality: fields?.nationality,
      // emiratesId: fields?.emiratesId,
    },
    {
      // birthdate: data?.talentProfile?.birthdate,
      birthdate: moment(data?.talentProfile?.birthdate).format('YYYY-MM-DD'),
      gender: data?.talentProfile?.gender,
      maritalStatus: data?.talentProfile?.maritalStatus,
      // nationality: data?.talentProfile?.nationality,
      // emiratesId: data?.talentProfile?.emiratesId,
    },
  );
  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      birthdate: fields?.birthdate,
      // gender: fields?.gender,
      // maritalStatus: fields?.maritalStatus,
      // nationality: fields?.nationality,
      // emiratesId: fields?.emiratesId,
    });

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        onChange(fields);
      }
    } else {
      Alert.alert('Fields are empty! ' + emptyFields?.join());
    }
  }, [fields]);

  useEffect(() => {
    getList();
  }, []);

  const getList = async () => {
    const result = await getPickList(data?.response?.liferayaccesstoken, 'Maritalstatus');
    if (result?.length) setList(result);
  };

  const onDelete = async () => {
    const sortedLang = languageArray?.filter((res) => res?.id !== languageId);
    onChange({
      languagesUser: sortedLang,
      noSuccessMessage: true,
    });
    toggleModal();
  };

  const date = moment().subtract(15, 'years').toDate();

  const openModal = (res, langId) => {
    toggleModal();
    setLangId(langId);
    setLangName(res?.userLanguage);
  };

  const handleAdd = () => {
    const selectedLang = {
      proficiency: '',
      userLanguage: '',
      r_languagesUser_c_userPersonalDetailsId: data?.talentProfile?.id,
      id: '',
    };

    navigation.navigate('EditLanguage', {
      initialLang: selectedLang,
      liferayToken: data?.response?.liferayaccesstoken,
      languagesUser: data?.talentProfile?.languagesUser,
      fields,
      onChange,
    });
  };

  const handleDelete = (res) => {
    openModal(res, res?.id);
  };

  const handleEdit = (res) => {
    const selectedLang = {
      proficiency: res?.proficiency,
      userLanguage: res?.userLanguage,
      r_languagesUser_c_userPersonalDetailsId: data?.talentProfile?.id,
      id: res?.id,
    };

    navigation.navigate('EditLanguage', {
      initialLang: selectedLang,
      liferayToken: data?.response?.liferayaccesstoken,
      languagesUser: data?.talentProfile?.languagesUser,
      fields,
      onChange,
    });
  };

  return (
    <SafeAreaView style={styles.mainFormContainer}>
      <EditText
        disable={false}
        editable={false}
        notint
        image={AssetsImages.calendar}
        value={fields?.birthdate ? moment(fields?.birthdate).format('DD-MM-YYYY') : ''}
        onChange={onChangeData('birthdate')}
        placeholder="DD-MM-YYYY"
        label="Date Of Birth"
        isCalender
        inputViewStyle={{ marginBottom: 24 }}
        minimumDate={'1950-12-31'}
        maximumDate={date}
      />
      <EditText
        disable
        editable={false}
        notint
        image={AssetsImages.gender}
        value={t(fields?.gender) || 'N/A'}
        // onChange={onChangeData("birthdate")}
        placeholder="Gender"
        label="Gender"
        inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
      />

      <Dropdown
        iconStyle={{ width: 18, height: 22 }}
        icon={AssetsImages.marriedStatus}
        style={{ marginBottom: 24 }}
        placeholder={fields?.maritalStatus || 'Select Marital Status'}
        data={maritalList}
        label={'Marital Status'}
        type="maritalStatus"
        getSelectedValue={onChangeData('maritalStatus')}
        value={fields?.martialStatus}
      />
      <RenderLanguageHeader
        loading={false}
        onPress={handleAdd}
        customStyle={styles.header}
        heading={'Edit Languages'}
      />

      <ScrollView showsVerticalScrollIndicator={false} style={{ marginBottom: 100 }}>
        {data?.talentProfile?.languagesUser?.length
          ? data?.talentProfile?.languagesUser?.map((res, index) => {
              return (
                <LanguageView
                  stateLoading={stateLoading}
                  key={index}
                  res={res}
                  onDelete={() => handleDelete(res)}
                  onEdit={() => handleEdit(res)}
                  desc={res?.proficiency}
                  langauge={res?.userLanguage}
                />
              );
            })
          : null}
      </ScrollView>
      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />

      <ConfirmationModal
        title={languageName}
        type="Language"
        onDelete={onDelete}
        setVisibility={toggleModal}
        visibility={visibility}
      />
    </SafeAreaView>
  );
};

export const EditCV = ({ onChange, data, loading }) => {
  const talentProfile = data?.talentProfile;
  const resume = talentProfile?.resumeCopy;
  const [cvCopy, setCvCopy] = useState(resume);
  const [size, setSize] = useState('');
  const { styles, theme } = useStyles();
  const getFileName = (e) => {
    setSize(e.size);
    setCvCopy(e?.fileName);
  };

  const onPressDelete = () => {
    setCvCopy('');
    // onChange({
    //     resumeCopy: '',
    //     noSuccessMessage: true
    // })
  };

  const isAnyChange = checkChange(
    {
      cvCopy: cvCopy,
    },
    {
      cvCopy: data?.talentProfile?.resumeCopy,
    },
  );

  const onSubmitData = () => {
    if (isAnyChange?.length) {
      // if (cvCopy == '') return alert('Select Your CV')
      onChange({
        resumeCopy: cvCopy,
      });
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <AddAttachment fileName={cvCopy} type={'pdfOnly'} getFileName={getFileName} label="CV" />
      {cvCopy ? (
        <AttachmentView size={size} onPressDelete={onPressDelete} attachmentName={cvCopy} />
      ) : null}
      <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}>
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </View>
  );
};

export const EditEmiratesId = ({ onChange, data, loading }) => {
  const [fields, setFields] = useState({
    emiratesId: data?.talentProfile?.emiratesId,
    emiratesCopy: data?.talentProfile?.emiratesCopy,
    nationality: data?.talentProfile?.nationality,
  });
  const [size, setSize] = useState('');
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const { t } = useTranslation();
  const { styles } = useStyles();
  const [fileName, setFileName] = useState('');
  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };
  const getFileName = (e) => {
    setFields({
      ...fields,
      emiratesCopy: e?.fileName,
    });
    setSize(e.size);
    setFileName(e?.name);
  };

  const onPressDelete = () => {
    setFields({
      ...fields,
      emiratesCopy: '',
    });
    // onChange({
    //     emiratesCopy: '',
    //     noSuccessMessage: true
    // })
  };

  const isAnyChange = checkChange(
    {
      resumeCopy: fields?.emiratesCopy,
    },
    {
      resumeCopy: data?.talentProfile?.emiratesCopy,
    },
  );

  const onSubmitData = () => {
    if (isAnyChange?.length) {
      // if (fields?.emiratesCopy == '') return alert('Select Emirates Copy')
      onChange({
        emiratesCopy: fields?.emiratesCopy,
      });
    }
  };

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView style={styles.scrollViewStyle}>
        <EditText
          disable
          keyboardType="numeric"
          editable={true}
          value={fields?.emiratesId}
          onChange={onChangeData('emiratesId')}
          placeholder="784-1979-1234567-1"
          label="Emirates ID"
          inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
        />
        <EditText
          disable
          keyboardType="numeric"
          editable={true}
          value={fields?.nationality}
          onChange={onChangeData('nationality')}
          placeholder="Nationality"
          label="Nationality"
          inputViewStyle={{ marginBottom: 24, borderColor: 'transparent' }}
        />

        {/* <Dropdown
                disable
                style={{ marginBottom: 30, borderColor: 'transparent' }}
                placeholder={nationality[0].label}
                data={nationality}
                label={"Nationality"}
                getSelectedValue={onChangeData("nationality")}
                value={fields?.nationality}
            /> */}

        <AddAttachment
          type={'emiratescopy'}
          getFileName={getFileName}
          label={`${t('Emirates ID')} ${t('(Front & back)')}`}
        />
        {fields?.emiratesCopy ? (
          <AttachmentView
            fileName={fileName}
            onPressDelete={onPressDelete}
            size={size}
            attachmentName={fields?.emiratesCopy}
          />
        ) : null}
      </ScrollView>
      <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}>
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </View>
  );
};
export const EditPassportDetails = ({ onChange, data, loading }) => {
  const [fields, setFields] = useState({
    passportType: data?.talentProfile?.passportType,
    passportNumber: data?.talentProfile?.passportNumber,
    dateOfExpiry: data?.talentProfile?.dateOfExpiry,
    passportCopy: data?.talentProfile?.passportCopy,
  });
  const [size, setSize] = useState('');
  const [passportTypeList, setPassportType] = useState([]);
  const scrollRef = useRef(null);
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const [fileName, setFileName] = useState('');
  const { styles } = useStyles();
  useEffect(() => {
    getList();
  }, []);

  const getList = async () => {
    const result = await getPickList(data?.response?.liferayaccesstoken, pickListType.passportType);
    if (result?.length) setPassportType(result);
  };

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const getFileName = (e) => {
    setSize(e?.size);
    setFileName(e?.name);
    setFields({
      ...fields,
      passportCopy: e.fileName,
    });
  };

  const onPressDelete = () => {
    setFields({
      ...fields,
      passportCopy: '',
    });
    // onChange({
    //     passportCopy: '',
    //     noSuccessMessage: true
    // })
  };

  const isAnyChange = checkChange(
    {
      passportType: fields?.passportType,
      passportNumber: fields?.passportNumber,
      dateOfExpiry: fields?.dateOfExpiry,
      passportCopy: fields?.passportCopy,
    },
    {
      passportType: data?.talentProfile?.passportType,
      passportNumber: data?.talentProfile?.passportNumber,
      dateOfExpiry: data?.talentProfile?.dateOfExpiry,
      passportCopy: data?.talentProfile?.passportCopy,
    },
  );
  const onSubmitData = () => {
    const PassportCheck = /^\w{6,}$/;

    if (isAnyChange?.length) {
      if (!fields?.passportType) return alert('Select Passport Type');
      if (!PassportCheck.test(fields?.passportNumber))
        return alert('Please enter correct passport number.');
      if (!fields?.dateOfExpiry) return alert('Select Passport Expiry Date');
      // if (!fields?.passportCopy) return alert('Select Passport Copy')
      onChange(fields);
    }
  };

  useEffect(() => {
    if (fields?.passportCopy !== data?.talentProfile?.passportCopy) {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }
  }, [fields?.passportCopy]);

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView ref={scrollRef} style={styles.scrollViewStyle}>
        <Dropdown
          style={{ marginBottom: 16 }}
          placeholder={fields?.passportType || 'Select Passport Type'}
          data={passportTypeList}
          label={'Passport type'}
          type={'Passport Type'}
          getSelectedValue={onChangeData('passportType')}
          value={fields?.passportType}
        />

        <EditText
          editable={true}
          value={fields?.passportNumber}
          onChange={onChangeData('passportNumber')}
          placeholder=""
          label="Passport Number"
          maxLength={24}
          inputViewStyle={{ marginBottom: 24 }}
        />

        <EditText
          minimumDate={moment()}
          editable={false}
          value={fields?.dateOfExpiry ? moment(fields?.dateOfExpiry).format('DD-MM-YYYY') : ''}
          onChange={onChangeData('dateOfExpiry')}
          placeholder="MM-DD-YYYY"
          label="Passport Expiry"
          rightIcon={Images.calendar}
          isCalender
          inputViewStyle={{ marginBottom: 30 }}
        />

        <AddAttachment
          // fileName={fields?.passportCopy}
          getFileName={getFileName}
          label="Passport ID (First Page & Unified Number Page)"
        />
        {fields?.passportCopy ? (
          <AttachmentView
            fileName={fileName}
            onPressDelete={onPressDelete}
            size={size}
            attachmentName={fields?.passportCopy}
          />
        ) : null}
      </ScrollView>

      <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}>
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </View>
  );
};
export const EditFamilyBook = ({ onChange, data, loading }) => {
  const [fields, setFields] = useState({
    tribeName: data?.talentProfile?.tribeName,
    // emiratesFamilyBook: data?.talentProfile?.emirates,
    emiratesFamilyBook: data?.talentProfile?.emiratesFamilyBook,
    khulasitQaidNumber: data?.talentProfile?.khulasitQaidNumber,
    familyBookNumber: data?.talentProfile?.familyBookNumber,
    bookRelation: data?.talentProfile?.bookRelation,
    familyBookCopy: data?.talentProfile?.familyBookCopy,
  });
  const scrollRef = useRef(null);
  const [size, setSize] = useState('');
  const [relationList, setRelation] = useState([]);
  const [emirates, setEmirates] = useState([]);
  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const [fileName, setFileName] = useState('');
  const { t } = useTranslation();
  const { styles } = useStyles();

  const getFileName = (e) => {
    setSize(e?.size);
    setFileName(e?.name);
    setFields({
      ...fields,
      familyBookCopy: e.fileName,
    });
  };

  useEffect(() => {
    getList();
    getCountries();
  }, []);

  const getList = async () => {
    const result = await getPickList(data?.response?.liferayaccesstoken, pickListType.relation);
    if (result?.length) setRelation(result);
  };

  const onPressDelete = () => {
    setFields({
      ...fields,
      familyBookCopy: '',
    });
    // onChange({
    //     passportCopy: '',
    //     noSuccessMessage: true
    // })
  };

  const isAnyChange = checkChange(
    {
      tribeName: fields?.tribeName,
      emiratesFamilyBook: fields?.emiratesFamilyBook,
      khulasitQaidNumber: fields?.khulasitQaidNumber,
      familyBookNumber: fields?.familyBookNumber,
      bookRelation: fields?.bookRelation,
      familyBookCopy: fields?.familyBookCopy,
    },
    {
      tribeName: data?.talentProfile?.tribeName,
      emiratesFamilyBook: data?.talentProfile?.emiratesFamilyBook,
      khulasitQaidNumber: data?.talentProfile?.khulasitQaidNumber,
      familyBookNumber: data?.talentProfile?.familyBookNumber,
      bookRelation: data?.talentProfile?.bookRelation,
      familyBookCopy: data?.talentProfile?.familyBookCopy,
    },
  );
  const onSubmitData = () => {
    if (isAnyChange?.length) {
      // if (!fields?.tribeName) return alert('Select Tribe Name');
      if (!fields?.emiratesFamilyBook) return alert('Select Emirates');
      if (!fields?.khulasitQaidNumber) return alert('Select Khulasit Qauid Number');
      if (!fields?.familyBookNumber) return alert('Select Family Book Number');
      if (!fields?.bookRelation) return alert('Select Book Relation');
      // if (!fields?.familyBookCopy) return alert('Select Family Book Copy')
      onChange(fields);
    }
  };

  const getCountries = async () => {
    const emirates = await getCountriesAndEmirates(data?.response?.liferayaccesstoken);
    if (emirates) setEmirates(emirates);
  };

  useEffect(() => {
    if (fields?.familyBookCopy !== data?.talentProfile?.familyBookCopy) {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }
  }, [fields?.familyBookCopy]);

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      {/* <ScrollView ref={scrollRef} style={styles.scrollViewStyle}> */}
      <ScrollView style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={styles.scrollViewStyle}>
          {/* <EditText
          editable={true}
          labelStyle={styles.labelStyle}
          iconStyle={{ marginEnd: 8, height: 26, width: 22 }}
          value={fields?.tribeName}
          onChange={onChangeData('tribeName')}
          placeholder="Tamim"
          label="Name"
          inputViewStyle={{ marginBottom: 24 }}
        /> */}

          <Dropdown
            style={{ marginBottom: 16 }}
            placeholder={fields?.emiratesFamilyBook || 'Select Emirates'}
            data={emirates}
            label={'Emirates'}
            getSelectedValue={onChangeData('emiratesFamilyBook')}
            value={fields?.emiratesFamilyBook}
          />

          <EditText
            maxLength={14}
            editable={true}
            labelStyle={styles.labelStyle}
            value={fields?.khulasitQaidNumber}
            onChange={onChangeData('khulasitQaidNumber')}
            placeholder=""
            keyboardType="numeric"
            label="Khulasit Qauid No"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <EditText
            maxLength={18}
            editable={true}
            value={fields?.familyBookNumber}
            onChange={onChangeData('familyBookNumber')}
            placeholder=""
            label="Family No"
            keyboardType="numeric"
            inputViewStyle={{ marginBottom: 24 }}
          />

          <Dropdown
            style={{ marginBottom: 30 }}
            placeholder={t('Select Relation')}
            data={relationList}
            type="label"
            label={'Relation to the Family Book Holder'}
            getSelectedValue={onChangeData('bookRelation')}
            value={fields?.bookRelation}
          />

          <AddAttachment getFileName={getFileName} label="Family Book" />

          {fields.familyBookCopy ? (
            <AttachmentView
              fileName={fileName}
              size={size}
              onPressDelete={onPressDelete}
              attachmentName={fields?.familyBookCopy}
            />
          ) : null}
        </KeyboardAwareScrollView>
      </ScrollView>
      <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}>
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </View>
  );
};

export const EditHobbies = ({ onChange, data, loading }) => {
  const [isDisabled, setDisabled] = useState(true);
  const [initalHobbies] = useState(data?.talentProfile?.hobbies.split(','));
  const [hobbiesListTags, setHobbiesListTags] = useState([]);
  const [hobbiesList, setList] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const { styles } = useStyles();

  useEffect(() => {
    if (initalHobbies) {
      setHobbiesListTags(initalHobbies);
    }
  }, [initalHobbies]);

  useEffect(() => {
    if (JSON.stringify(initalHobbies) !== JSON.stringify(hobbiesListTags)) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [hobbiesListTags, initalHobbies]);

  const onSubmitData = () => {
    // if (!hobbiesListTags.length) return alert("Select hobbies")
    const body = {
      hobbies: hobbiesListTags.join(','),
    };
    onChange(body);
    setDisabled(true);
  };

  useEffect(() => {
    getLists();
  }, []);
  const getLists = async () => {
    setLoading(true);
    const result = await getPickList(data?.response?.liferayaccesstoken, pickListType.hobbies);
    setList(result);
    setLoading(false);
  };

  const preSelectedHobbiesArray = useMemo(() => {
    return data?.talentProfile?.hobbies?.split(',').map((item) => ({
      key: item.trim(),
      name: item,
    }));
  }, [data?.talentProfile?.hobbies]);

  const getHobbiesTags = (list) => setHobbiesListTags(list);

  return (
    <View style={styles.mainFormContainer}>
      {isLoading && <ActivityIndicator size={'large'} />}

      <RenderSelectorPreFilled
        preselectedList={preSelectedHobbiesArray}
        getSelectedTags={getHobbiesTags}
        list={hobbiesList}
      />

      {/* <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}> */}
      <ThemeBottomButton
        isDisabled={isDisabled}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
      {/* </View> */}
    </View>
  );
};

export const EditCountriesVisit = ({ onChange, data, loading }) => {
  const [countriesVisit, setCountriesVisited] = useState({
    countriesVisit: data?.talentProfile?.countriesVisit
      ? data?.talentProfile?.countriesVisit.split(',')
      : [],
  });
  const [tags, setTags] = useState(data?.talentProfile?.countriesVisit.split(','));
  const [tagValue, setTagValue] = useState('');
  const [list, setList] = useState([]);
  const [initialTags] = useState(data?.talentProfile?.countriesVisit.split(','));
  const [isDisabled, setDisabled] = useState(true);
  const { styles, theme } = useStyles();
  const removeTag = (index) => {
    const newTags = [...tags];
    newTags.splice(index, 1);
    setTags(newTags);
    setCountriesVisited((prevState) => ({
      ...prevState,
      countriesVisit: newTags,
    }));
  };

  const onSubmitData = () => {
    if (!countriesVisit.countriesVisit.length) return alert('Select Country');
    onChange({ countriesVisit: countriesVisit.countriesVisit.join(',') });
    setDisabled(true);
  };

  useEffect(() => {
    if (JSON.stringify(initialTags) !== JSON.stringify(tags)) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [tags, initialTags]);

  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);

  const handleSearch = (text) => {
    setSearchTerm(text);
    const filteredResults = list.filter((item) =>
      item?.key?.toLowerCase().includes(text.toLowerCase()),
    );
    setSearchResults(filteredResults);
  };

  const isTagInTags = (tag) => tags.includes(tag);

  const addTag = (tagValue) => () => {
    if (tagValue.trim() !== '' && !tags.includes(tagValue)) {
      setTags([...tags, tagValue.trim()]);
      setCountriesVisited((prevState) => ({
        ...prevState,
        countriesVisit: [...prevState.countriesVisit, tagValue.trim()],
      }));

      setTagValue('');
    }
    setTagValue('');
    setSearchTerm('');
    setSearchResults([]);
  };

  useEffect(() => {
    getList();
  }, []);

  const getList = async () => {
    const response = await getPickList(
      data?.response?.liferayaccesstoken,
      pickListType.countriesVisit,
    );
    if (response?.length) setList(response);
  };

  return (
    <Pressable onPress={() => setSearchResults([])} style={{ flex: 1 }}>
      <EditText
        value={searchTerm}
        editable={true}
        onChangeText={handleSearch}
        // onSubmit={addTag}
        // onChangeText={onChangeTags}
        placeholder="Enter Countries Name (Max 5)"
        inputViewStyle={{ marginBottom: 24 }}
      />
      {searchResults?.length ? (
        <View style={styles.suggestionsList}>
          {searchResults?.map((item) => (
            <TouchableOpacity
              key={item?.name}
              style={{ paddingVertical: 5, width: '100%' }}
              onPress={isTagInTags(item.name) ? null : addTag(item.name)}
            >
              <TextView
                key={item?.name}
                style={[styles.suggestionText, isTagInTags(item.name) && { color: 'grey' }]}
                text={item?.name}
              />
            </TouchableOpacity>
          ))}
        </View>
      ) : null}
      <View style={styles.tagContainer}>
        {tags?.map((tag, index) => (
          <View style={styles.tagsStyle} key={index}>
            <TextView style={styles.tagsText} text={tag} />
            <Pressable onPress={() => removeTag(index)}>
              <Image source={AssetsImages.deleteBlack} style={styles.delete} />
            </Pressable>
          </View>
        ))}
      </View>

      <View style={{ position: 'absolute', bottom: 0, width: '100%' }}>
        <Button
          isLoading={loading}
          isDisabled={isDisabled}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </Pressable>
  );
};

export const EditPrefferedModeOfLearning = ({ onChange, data, loading }) => {
  const [interestList, setInterest] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [initalInterests] = useState(data?.talentProfile?.interests?.split(','));
  const [isDisabled, setDisabled] = useState(true);
  const { apiToken, core } = useSession();
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const [allItems, setAllItems] = useState([]);
  const { styles } = useStyles();
  useEffect(() => {
    fetchInterest(apiToken);
  }, []);

  async function fetchInterest(apiToken) {
    setLoading(true);
    const interests = await fetchPreferences(apiToken);
    const categories = interests
      .map((res) => {
        return res?.name;
      })
      .join(',');
    const result = await getSubInterest(categories, apiToken);
    getCategories(result?.interests, categories);
    setLoading(false);
  }

  const getCategories = (categories, allCategories) => {
    const structuredData = [];
    categories?.forEach((interest) => {
      let categoryGroup = structuredData.find((group) => group.category === interest.category);
      if (!categoryGroup) {
        categoryGroup = {
          category: interest.category,
          data: [],
        };
        structuredData.push(categoryGroup);
      }

      if (initalInterests.includes(interest.name)) {
        categoryGroup?.data?.push({
          id: interest.id,
          name: interest.name,
          isSelected: true,
        });
      } else {
        categoryGroup?.data?.push({
          id: interest.id,
          name: interest.name,
          isSelected: false,
        });
      }
    });

    allCategories.split(',').forEach((category) => {
      const exists = structuredData.some((group) => group.category === category);
      if (!exists) {
        structuredData.push({
          category: category,
          data: [],
        });
      }
    });
    if (structuredData) {
      setInterest(structuredData);
    }
  };

  const selectedNames = interestList?.reduce((names, category) => {
    const selectedItems = category.data.filter((item) => item.isSelected).map((item) => item.name);
    return names.concat(selectedItems);
  }, []);

  const handleNavigate = () => {
    onSubmitData(selectedNames.join(','));
    return;
  };
  const onSubmitData = (data) => {
    const body = {
      interests: data,
    };
    onChange(body);
    updateTotoraProfile(apiToken, core?.user?.id, { interests: data })
      .then((res) => {
        setTimeout(() => {
          setDisabled(true);
          setLoading(false);
        }, 2000);
      })
      .catch((err) => {
        setDisabled(true);
        setLoading(false);
      });
  };

  const handleSelectionChange = (selectedItem) => {
    setAllItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((item) => item.id === selectedItem.id);
      if (existingItemIndex > -1) {
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex] = selectedItem;
        return updatedItems;
      } else {
        return [...prevItems, selectedItem];
      }
    });
  };

  const selectedCount = interestList.reduce((count, category) => {
    return count + category.data.filter((item) => item.isSelected).length;
  }, 0);

  useEffect(() => {
    if (JSON.stringify(allItems) !== JSON.stringify(interestList)) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
  }, [allItems, interestList]);

  return (
    <View style={styles.mainFormContainer}>
      {isLoading && <ActivityIndicator size={'large'} />}

      <ScrollView style={[styles.scrollViewStyle, { flex: 1, marginBottom: 140 }]}>
        {interestList?.map((res, index) => {
          return (
            <View key={index} style={{ marginTop: 10 }}>
              <TextView style={styles.heading} text={res?.category} />
              {res.data?.length ? (
                <RenderSelector list={res.data} onSelectionChange={handleSelectionChange} />
              ) : (
                <TextView
                  style={styles.noSubInterest}
                  text="No Sub-Interest available for this category"
                />
              )}
            </View>
          );
        })}
      </ScrollView>

      {/* <View style={[styles.overlayBtn, { backgroundColor: theme.backgroundColor }]}> */}
      <ThemeBottomButton
        isDisabled={selectedCount < 1}
        isLoading={loading}
        onPress={handleNavigate}
        heading={'Save'}
      />
      {/* </View> */}
    </View>
  );
};

export const EditEducationForm = ({ onChange, data, loading, item }) => {
  const [size, setSize] = useState('');
  const [fields, setFields] = useState<Record<string, string>>({
    school: '',
    degreeName: '',
    description: '',
    location: '',
    startDate: '',
    endDate: '',
    educationDocument: '',
    r_education_c_userPersonalDetailsId: data?.talentProfile?.id,
    emiratesId: data?.talentProfile?.emiratesId,
  });
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const { styles } = useStyles();
  const scrollRef = useRef(null);
  useEffect(() => {
    if (item?.id) {
      setFields((prevFields) => ({
        ...prevFields,
        school: item.school || '',
        degreeName: item.degreeName || '',
        description: item.description || '',
        location: item.location || '',
        startDate: item.startDate || '',
        endDate: item.endDate || '',
        educationDocument: item.educationDocument || '',
        r_education_c_userPersonalDetailsId: data?.talentProfile?.id,
        emiratesId: data?.talentProfile?.emiratesId,
      }));
    }
  }, [item]);

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const isAnyChange = checkChange(
    {
      school: fields?.school,
      degreeName: fields?.degreeName,
      description: fields?.description,
      location: fields?.location,
      startDate: fields?.startDate,
      endDate: fields?.endDate,
      educationDocument: fields?.educationDocument,
    },
    {
      school: item?.school || '',
      degreeName: item?.degreeName || '',
      description: item?.description || '',
      location: item?.location || '',
      startDate: item?.startDate || '',
      endDate: item?.endDate || '',
      educationDocument: item?.educationDocument || '',
    },
  );

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      school: fields?.school,
      degreeName: fields?.degreeName,
      // description: fields?.description,
      // location: fields?.location,
      startDate: fields?.startDate,
      endDate: fields?.endDate,
      // educationDocument: fields?.educationDocument
    });
    const updatedEducationArray = data?.talentProfile?.education?.map((edu) => {
      if (edu.id === item.id) {
        return {
          ...edu,
          school: fields.school,
          degreeName: fields.degreeName,
          description: fields.description,
          location: fields.location,
          startDate: fields.startDate,
          endDate: fields.endDate,
          educationDocument: fields.educationDocument,
        };
      }
      return edu;
    });

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        onChange({
          education: item?.id
            ? updatedEducationArray
            : data?.talentProfile?.education
              ? [...data?.talentProfile?.education, fields]
              : [fields],
        });
        if (!item?.id) {
          setTimeout(() => {
            setFields({
              ...fields,
              school: '',
              degreeName: '',
              description: '',
              location: '',
              startDate: '',
              endDate: '',
              educationDocument: '',
            });
          }, 2000);
        }
      }
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      const alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      Alert.alert(t(`${alertMsg}1`), '', [
        {
          text: t('Ok'),
          onPress: () => {},
        },
      ]);
    }
  }, [fields]);

  const getFileName = (e) => {
    setSize(e.size);
    setFields({ ...fields, educationDocument: e.fileName });
  };

  const onPressDelete = () => {
    setFields({ ...fields, educationDocument: '' });
  };

  useEffect(() => {
    if (fields?.educationDocument && fields?.educationDocument !== item.educationDocument) {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }
  }, [fields?.educationDocument]);

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView ref={scrollRef} style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={{ marginBottom: 80 }}>
          <EditText
            value={fields?.school}
            onChange={onChangeData('school')}
            placeholder="Ex: Abu Dhabi University"
            label="School"
            required
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            required
            value={fields?.degreeName}
            onChange={onChangeData('degreeName')}
            placeholder="Ex: Bachelor's"
            label="Degree name"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            value={fields?.description}
            // numberOfLines={20}
            multiline
            customInputStyles={styles.descBox}
            onChange={onChangeData('description')}
            placeholder="Enter Description"
            blurOnSubmit={true}
            textAlignVertical={'top'}
            returnKeyType={'done'}
            onSubmit={() => Keyboard.dismiss()}
            label="Description"
            inputViewStyle={styles.descStyle}
          />

          <EditText
            maxLength={90}
            value={fields.location}
            onChange={onChangeData('location')}
            placeholder="Enter Location"
            label="Location"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            disable={false}
            editable={false}
            notint
            rightIcon={AssetsImages.calendar}
            value={fields.startDate ? moment(fields.startDate).format('DD-MM-YYYY') : ''}
            onChange={(startDate) => {
              setFields({
                ...fields,
                startDate,
                endDate: '',
              });
            }}
            required
            placeholder="DD-MM-YYYY"
            label="Start Date"
            isCalender
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            required
            disable={fields.startDate ? false : true}
            editable={fields.startDate ? false : true}
            notint
            minimumDate={fields.startDate}
            rightIcon={AssetsImages.calendar}
            value={fields?.endDate ? moment(fields?.endDate).format('DD-MM-YYYY') : ''}
            onChange={onChangeData('endDate')}
            placeholder="DD-MM-YYYY"
            label="End Date"
            isCalender={fields.startDate ? true : false}
            inputViewStyle={styles.inputStyle}
          />

          <AddAttachment
            fileName={fields.educationDocument}
            getFileName={getFileName}
            label="Attachment"
          />
          {fields.educationDocument ? (
            <AttachmentView
              size={size}
              onPressDelete={onPressDelete}
              attachmentName={fields.educationDocument}
            />
          ) : null}
        </KeyboardAwareScrollView>
      </ScrollView>

      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
    </View>
  );
};

export const EditTrainingForm = ({ onChange, data, loading, item }) => {
  const [size, setSize] = useState('');
  const [fields, setFields] = useState<Record<string, string>>({
    trainingtitle: '',
    issuingAuthority: '',
    trainingId: '',
    location: '',
    startDate: '',
    expireOn: '',
    trainingAttachment: '',
    r_training_c_userPersonalDetailsId: data?.talentProfile?.id,
    emiratesId: data?.talentProfile?.emiratesId,
  });
  const scrollRef = useRef(null);
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const { t } = useTranslation();
  const { styles } = useStyles();
  useEffect(() => {
    if (item?.id) {
      setFields((prevFields) => ({
        ...prevFields,
        trainingtitle: item.trainingtitle || '',
        issuingAuthority: item.issuingAuthority || '',
        trainingId: item.trainingId || '',
        location: item.location || '',
        startDate: item.startDate || '',
        expireOn: item.expireOn || '',
        trainingAttachment: item.trainingAttachment || '',
        r_training_c_userPersonalDetailsId: data?.talentProfile?.id,
        emiratesId: data?.talentProfile?.emiratesId,
      }));
    }
  }, [item]);

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const isAnyChange = checkChange(
    {
      trainingtitle: fields?.trainingtitle,
      issuingAuthority: fields?.issuingAuthority,
      trainingId: fields?.trainingId,
      location: fields?.location,
      startDate: fields?.startDate,
      expireOn: fields?.expireOn,
      trainingAttachment: fields?.trainingAttachment,
    },
    {
      trainingtitle: item?.trainingtitle || '',
      issuingAuthority: item?.issuingAuthority || '',
      trainingId: item?.trainingId || '',
      location: item?.location || '',
      startDate: item?.startDate || '',
      expireOn: item?.expireOn || '',
      trainingAttachment: item?.trainingAttachment || '',
    },
  );

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      trainingtitle: fields?.trainingtitle,
      issuingAuthority: fields?.issuingAuthority,
      // trainingId: fields?.trainingId,
      // location: fields?.location,
      startDate: fields?.startDate,
      // expireOn: fields?.expireOn,
      // trainingAttachment: fields?.trainingAttachment
    });
    const updatedArray = data?.talentProfile?.training?.map((edu) => {
      if (edu.id === item.id) {
        return {
          ...edu,
          trainingtitle: fields.trainingtitle,
          issuingAuthority: fields.issuingAuthority,
          description: fields.description,
          trainingId: fields.trainingId,
          location: fields.location,
          startDate: fields.startDate,
          expireOn: fields.expireOn,
          trainingAttachment: fields.trainingAttachment,
        };
      }
      return edu;
    });

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        // delete updatedArray?.title;
        // delete fields?.title;

        onChange({
          training: item?.id
            ? updatedArray
            : data?.talentProfile?.training
              ? [...data?.talentProfile?.training, fields]
              : [fields],
        });
        if (!item?.id) {
          setTimeout(() => {
            setFields({
              ...fields,
              trainingtitle: '',
              issuingAuthority: '',
              trainingId: '',
              location: '',
              startDate: '',
              expireOn: '',
              trainingAttachment: '',
            });
          }, 2000);
        }
      }
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      const alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      Alert.alert(t(`${alertMsg}1`), '', [
        {
          text: t('Ok'),
          onPress: () => {},
        },
      ]);
    }
  }, [fields]);

  const getFileName = (e) => {
    setSize(e.size);
    setFields({ ...fields, trainingAttachment: e.fileName });
  };

  const onPressDelete = () => {
    // onChange({
    //     resumeCopy: '',
    //     noSuccessMessage: true
    // })
    setFields({ ...fields, trainingAttachment: '' });
  };

  useEffect(() => {
    if (fields.trainingAttachment && fields.trainingAttachment !== item?.trainingAttachment) {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }
  }, [fields.trainingAttachment]);

  const handleChangeStartDate = (date) => {
    setFields({
      ...fields,
      startDate: date,
      expireOn: '',
    });
  };

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView ref={scrollRef} style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={{ marginBottom: 80 }}>
          <EditText
            value={fields?.trainingtitle}
            onChange={onChangeData('trainingtitle')}
            placeholder={'E.g., Chartered Financial Analyst (CFA)'}
            label="Title"
            inputViewStyle={styles.inputStyle}
            required
          />

          <EditText
            value={fields?.issuingAuthority}
            onChange={onChangeData('issuingAuthority')}
            placeholder="E.g., CFA Institute"
            label="Issuing Authority"
            inputViewStyle={styles.inputStyle}
            required
          />

          <EditText
            returnKeyType={'default'}
            value={fields?.trainingId}
            onChange={onChangeData('trainingId')}
            placeholder="Enter ID"
            label="Training ID"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            value={fields.location}
            onChange={onChangeData('location')}
            placeholder="Enter Location"
            label="Location"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            disable={false}
            editable={false}
            notint
            rightIcon={AssetsImages.calendar}
            value={fields.startDate ? moment(fields.startDate).format('DD-MM-YYYY') : ''}
            // onChange={onChangeData('startDate')}
            onChange={handleChangeStartDate}
            placeholder="DD-MM-YYYY"
            label="Start Date"
            isCalender
            inputViewStyle={styles.inputStyle}
            required
          />

          <EditText
            disable={fields.startDate ? false : true}
            editable={fields.startDate ? false : true}
            notint
            minimumDate={fields.startDate}
            rightIcon={AssetsImages.calendar}
            value={fields?.expireOn ? moment(fields?.expireOn).format('DD-MM-YYYY') : ''}
            onChange={onChangeData('expireOn')}
            placeholder="DD-MM-YYYY"
            label="Expiry Date"
            isCalender={fields.startDate ? true : false}
            inputViewStyle={styles.inputStyle}
          />

          <AddAttachment
            fileName={fields.trainingAttachment}
            getFileName={getFileName}
            label="Attachment"
          />
          {fields.trainingAttachment ? (
            <AttachmentView
              size={size}
              onPressDelete={onPressDelete}
              attachmentName={fields.trainingAttachment}
            />
          ) : null}
        </KeyboardAwareScrollView>
      </ScrollView>

      {/* <View style={styles.overlayBtn}> */}
      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
      {/* </View> */}
    </View>
  );
};

export const EditWorkExperience = ({ onChange, data, loading, item }) => {
  const [fields, setFields] = useState<Record<string, string>>({
    jobTitle: '',
    organizationName: '',
    industry: '',
    location: '',
    workExperienceStartDate: '',
    workExperienceEndDate: '',
    responsibilities: '',
    department: '',
    currentlyWorking: false,
  });
  const { styles } = useStyles();
  useEffect(() => {
    if (item?.id) {
      setTimeout(() => {
        setFields((prevFields) => ({
          ...prevFields,
          jobTitle: item.jobTitle || '',
          organizationName: item.organizationName || '',
          industry: item.industry || '',
          location: item.location || '',
          workExperienceStartDate: item.workExperienceStartDate || '',
          workExperienceEndDate: item.workExperienceEndDate || '',
          responsibilities: item.responsibilities || '',
          department: item.department || '',
          currentlyWorking: item?.currentlyWorking == 'true' ? true : false,
        }));
      }, 0);
    }
  }, [item]);

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const isAnyChange = checkChange(
    {
      jobTitle: fields?.jobTitle,
      organizationName: fields?.organizationName,
      industry: fields?.industry,
      location: fields?.location,
      workExperienceStartDate: fields?.workExperienceStartDate,
      workExperienceEndDate: fields?.workExperienceEndDate,
      responsibilities: fields?.responsibilities,
      department: fields?.department,
      currentlyWorking: fields?.currentlyWorking,
    },
    {
      jobTitle: item?.jobTitle || '',
      organizationName: item?.organizationName || '',
      industry: item?.industry || '',
      location: item?.location || '',
      workExperienceStartDate: item?.workExperienceStartDate || '',
      workExperienceEndDate: item?.workExperienceEndDate || '',
      responsibilities: item?.responsibilities || '',
      department: item?.department || '',
      currentlyWorking: item?.currentlyWorking == 'true' ? true : false,
    },
  );

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      jobTitle: fields?.jobTitle,
      organizationName: fields?.organizationName,
      industry: fields?.industry,
      // location: fields?.location,
      workExperienceStartDate: fields?.workExperienceStartDate,
      department: fields?.department,
      // workExperienceEndDate: fields?.workExperienceEndDate,
    });
    const updatedArray = data?.talentProfile?.workExperience?.map((edu) => {
      if (edu.id === item.id) {
        return {
          ...edu,
          jobTitle: fields.jobTitle,
          organizationName: fields.organizationName,
          industry: fields.industry,
          location: fields.location,
          workExperienceStartDate: fields.workExperienceStartDate,
          workExperienceEndDate: fields.workExperienceEndDate,
          responsibilities: fields.responsibilities,
          department: fields.department,
          currentlyWorking: fields?.currentlyWorking,
        };
      }
      return edu;
    });

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        onChange({
          workExperience: item?.id
            ? updatedArray
            : data?.talentProfile?.workExperience
              ? [
                  ...data?.talentProfile?.workExperience,
                  { ...fields, r_workExperience_c_userPersonalDetailsId: data?.talentProfile?.id },
                ]
              : [{ ...fields, r_workExperience_c_userPersonalDetailsId: data?.talentProfile?.id }],
        });
        if (!item?.id) {
          setTimeout(() => {
            setFields({
              ...fields,
              jobTitle: '',
              organizationName: '',
              industry: '',
              location: '',
              workExperienceStartDate: '',
              workExperienceEndDate: '',
              trainingAttachment: '',
              responsibilities: '',
              department: '',
              currentlyWorking: false,
            });
          }, 2000);
        }
      }
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      const alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      Alert.alert(t(`${alertMsg}1`), '', [
        {
          text: t('Ok'),
          onPress: () => {},
        },
      ]);
    }
  }, [fields]);

  // const date = moment().subtract(15, 'years').toDate();
  const currentDate = new Date();
  const specificDate = moment(currentDate, 'YYYY-MM-DD');
  const date = specificDate.toDate();
  const { featureFlags } = useFeatureFlags();
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };

  const onPressCurrentWorking = () => {
    if (fields?.currentlyWorking) {
      setFields({
        ...fields,
        currentlyWorking: false,
      });
    } else {
      setFields({
        ...fields,
        currentlyWorking: true,
      });
    }
  };

  const handleChangeStartDate = (date) => {
    setFields({
      ...fields,
      workExperienceStartDate: date,
      workExperienceEndDate: '',
    });
  };

  const { isDarkMode } = useTheme();
  const enhanceResponsibilities = () => {
    return getEnhancedAIResponsibilities(JSON.stringify(fields));
  };

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={styles.scrollViewStyle}>
          <EditText
            required
            value={fields?.jobTitle}
            onChange={onChangeData('jobTitle')}
            placeholder="Enter Job Title"
            label="Job title"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            required
            value={fields?.organizationName}
            onChange={onChangeData('organizationName')}
            placeholder="Enter Organization Name"
            label="Organization name"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            required
            returnKeyType={'default'}
            value={fields?.industry}
            onChange={onChangeData('industry')}
            placeholder="Enter Industry Name"
            label="Industry"
            inputViewStyle={styles.inputStyle}
          />
          <EditText
            required
            returnKeyType={'default'}
            value={fields?.department}
            onChange={onChangeData('department')}
            placeholder="Enter Department Name"
            label="Department"
            inputViewStyle={styles.inputStyle}
          />

          <EditText
            value={fields.location}
            onChange={onChangeData('location')}
            placeholder="Enter location"
            label="Location"
            inputViewStyle={styles.inputStyle}
          />

          <Pressable onPress={onPressCurrentWorking} style={styles.listItem}>
            <Image
              style={[
                styles.skillCheck,
                !fields?.currentlyWorking && isDarkMode && { tintColor: theme.bgLightDark },
              ]}
              source={
                fields?.currentlyWorking ? AssetsImages.selectedCheckBox : AssetsImages.uncheck
              }
            />
            <TextView
              style={[styles.currentlyWorking, { color: theme.textColor }]}
              text={'I am currently working in this role'}
            />
          </Pressable>

          <EditText
            disable={false}
            editable={false}
            required
            notint
            rightIcon={AssetsImages.calendar}
            value={
              fields.workExperienceStartDate
                ? moment(fields.workExperienceStartDate).format('DD-MM-YYYY')
                : ''
            }
            onChange={handleChangeStartDate}
            placeholder="DD-MM-YYYY"
            label="Start Date"
            isCalender
            inputViewStyle={styles.inputStyle}
            minimumDate={new Date(new Date().setFullYear(new Date().getFullYear() - 100))}
            // minimumDate={moment()}
            maximumDate={new Date()}
          />
          <EditText
            // disable={isPresent && fields.workExperienceStartDate}
            disable={!fields?.currentlyWorking ? false : true}
            // editable={!isPresent}
            editable={fields.workExperienceStartDate != ''}
            notint
            maximumDate={date}
            minimumDate={fields.workExperienceStartDate}
            rightIcon={AssetsImages.calendar}
            value={
              fields?.currentlyWorking
                ? 'Present'
                : fields?.workExperienceEndDate
                  ? moment(fields?.workExperienceEndDate).format('DD-MM-YYYY')
                  : ''
            }
            onChange={onChangeData('workExperienceEndDate')}
            placeholder={fields?.currentlyWorking ? 'Present' : 'DD-MM-YYYY'}
            label="End Date"
            isCalender={
              fields?.currentlyWorking ? false : fields.workExperienceStartDate ? true : false
            }
            inputViewStyle={styles.inputStyle}
          />

          {featureFlags.aiFeature ? (
            <EditTextWithAI
              onWriteWithAI={enhanceResponsibilities}
              returnKeyType={'default'}
              value={item?.responsibilities}
              numberOfLines={40}
              multiline
              customInputStyles={styles.descBox}
              onChange={onChangeData('responsibilities')}
              placeholder="Enter details of your resposibility"
              label="Responsibility"
              inputViewStyle={styles.descStyle}
              textAlignVertical="top"
            />
          ) : (
            <EditText
              returnKeyType={'default'}
              value={fields?.responsibilities}
              numberOfLines={40}
              multiline
              customInputStyles={styles.descBox}
              onChange={onChangeData('responsibilities')}
              placeholder="Enter details of your resposibility"
              label="Responsibility"
              inputViewStyle={styles.descStyle}
              textAlignVertical="top"
            />
          )}
        </KeyboardAwareScrollView>
      </ScrollView>
      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Save'}
      />
    </View>
  );
};
export const EditAchievement = ({ onChange, data, loading, item }) => {
  const [fields, setFields] = useState<Record<string, string>>({
    title: '',
    date: '',
    description: '',
    attachment: '',
  });
  const scrollRef = useRef(null);
  const [size, setSize] = useState('');
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const [fileName, setFileName] = useState('');
  const { styles } = useStyles();
  useEffect(() => {
    if (item?.id) {
      setFields((prevFields) => ({
        ...prevFields,
        title: item.title || '',
        date: item.date || '',
        description: item.description || '',
        attachment: item.attachment || '',
      }));
    }
  }, [item]);

  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };

  const isAnyChange = checkChange(
    {
      title: fields?.title,
      date: fields?.date,
      description: fields?.description,
      attachment: fields.attachment,
    },
    {
      title: item?.title || '',
      date: item?.date || '',
      description: item?.description || '',
      attachment: item?.attachment || '',
    },
  );

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      title: fields?.title,
      date: fields?.date,
    });
    const updatedArray = data?.talentProfile?.achievements?.map((edu) => {
      if (edu.id === item.id) {
        return {
          ...edu,
          title: fields.title,
          date: fields.date,
          description: fields.description,
          attachment: fields.attachment,
        };
      }
      return edu;
    });

    if (!emptyFields?.length) {
      if (isAnyChange?.length) {
        onChange({
          achievements: item?.id
            ? updatedArray
            : data?.talentProfile?.achievements
              ? [...data?.talentProfile?.achievements, fields]
              : [fields],
        });
        if (!item?.id) {
          setTimeout(() => {
            setFields({
              ...fields,
              title: '',
              date: '',
              description: '',
              attachment: '',
            });
          }, 2000);
        }
      }
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      const alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      Alert.alert('Please Enter ' + alertMsg);
    }
  }, [fields]);

  const getFileName = (e) => {
    setSize(e.size);
    setFields({ ...fields, attachment: e.fileName });
    setFileName(e?.name);
  };

  const onPressDelete = () => {
    setFields({ ...fields, attachment: '' });
  };

  useEffect(() => {
    if (fields?.attachment && fields?.attachment !== item?.attachment) {
      scrollRef?.current?.scrollToEnd({ animated: true });
    }
  }, [fields?.attachment]);

  return (
    <View style={[{ flex: 1 }, backgroundColor]}>
      <ScrollView ref={scrollRef} style={[styles.scrollViewStyle, { marginTop: 30 }]}>
        <KeyboardAwareScrollView style={{ marginBottom: 60 }}>
          <EditText
            value={fields?.title}
            onChange={onChangeData('title')}
            placeholder="E.g., Chartered Financial Analyst (CFA)"
            label="Achievements Title"
            inputViewStyle={styles.inputStyle}
            returnKeyType={'default'}
            required
          />
          <EditText
            returnKeyType={'default'}
            value={fields?.description}
            numberOfLines={20}
            multiline
            textAlignVertical={'top'}
            customInputStyles={styles.descBox}
            onChange={onChangeData('description')}
            placeholder="Enter Details"
            label="Achievements Description"
            inputViewStyle={styles.descStyle}
          />

          <EditText
            disable={false}
            editable={false}
            notint
            rightIcon={AssetsImages.calendar}
            value={fields.date ? moment(fields.date).format('DD-MM-YYYY') : ''}
            onChange={onChangeData('date')}
            placeholder="DD-MM-YYYY"
            label="Date"
            minimumDate={new Date(new Date().setFullYear(new Date().getFullYear() - 100))}
            isCalender
            inputViewStyle={styles.inputStyle}
            required
          />

          <AddAttachment
            fileName={fields.attachment}
            getFileName={getFileName}
            label="Attachment"
          />
          {fields.attachment ? (
            <AttachmentView
              fileName={fileName}
              size={size}
              onPressDelete={onPressDelete}
              attachmentName={fields.attachment}
            />
          ) : null}
        </KeyboardAwareScrollView>
      </ScrollView>
      <View style={styles.overlayBtn}>
        <Button
          isDisabled={!isAnyChange?.length}
          isLoading={loading}
          textStyle={styles.buttonText}
          onPress={onSubmitData}
          testID="submit"
          text={'Save'}
          type="fill"
        />
      </View>
    </View>
  );
};

export const AddRecommendationform = ({ onChange, data, loading, item }) => {
  const [fields, setFields] = useState<Record<string, string>>({
    askMessage: '',
    askUserId: data?.talentProfile?.id,
    giverMessage: '',
    username: '',
    relationship: '',
    statusRecommend: 'false',
  });
  const [selectedUsername, setSelectedUsername] = useState('');
  const [username, setUsername] = useState('');
  const [emailTo, setEmailTo] = useState('');
  const [relationList, setRelation] = useState([]);
  const dispatch = useDispatch();
  const [relationLoading, setRelationLoading] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const { styles } = useStyles();
  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };
  const { t } = useTranslation();
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };
  const name = capitalizeFirstLetter(username?.toLowerCase());

  const isAnyChange = checkChange(
    {
      askMessage: fields?.askMessage,
      askUserId: fields?.askUserId,
      giverMessage: fields?.giverMessage,
      username: fields.username,
      relationship: fields.relationship,
      statusRecommend: fields.statusRecommend,
    },
    {
      askMessage: item?.askMessage || '',
      askUserId: item?.askUserId || '',
      giverMessage: item?.giverMessage || '',
      username: item?.username || '',
      relationship: item?.relationship || '',
      statusRecommend: item?.statusRecommend,
    },
  );

  // useEffect(() => {
  //     if (username) {
  //         getList()
  //     }
  // }, [username])

  const getList = async (name) => {
    setRelationLoading(true);
    const result = await getPickList(
      data?.response?.liferayaccesstoken,
      pickListType.askRecommendationList,
    );

    if (result?.length) {
      const replacedArray = result?.map((obj) => {
        const newObj = { ...obj };
        newObj.label = newObj.label.replace('<NAME>', name);
        newObj.name = newObj.name.replace('<NAME>', '');
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace('<NAME>', name);

        const userName = talentProfile?.firstName ?? '';
        const youRegex = /you/gi;
        newObj.label = newObj.label.replace(youRegex, userName);
        newObj.name = newObj.name.replace(youRegex, userName);
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace(youRegex, userName);

        const wereRegex = /were/gi;
        newObj.label = newObj.label.replace(wereRegex, 'was');
        newObj.name = newObj.name.replace(wereRegex, 'was');
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace(wereRegex, 'was');

        return newObj;
      });
      setRelation(replacedArray);
      setRelationLoading(false);
    } else {
      setRelationLoading(false);
    }
  };

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      username: fields?.username,
      relationship: fields?.relationship,
    });

    if (!emptyFields?.length) {
      dispatch(setLoading(true));
      dispatch(setActionType('askFor'));
      addRecommendation(
        {
          askMessage: fields?.askMessage,
          askUserId: data?.talentProfile?.id,
          giverMessage: '',
          relationship: fields?.relationship,
          statusRecommend: 'false',
          giverUserId: selectedUsername,
        },
        token,
      )
        .then((res) => {
          requestRecommendationNotifier(res);
        })
        .catch((err) => {
          dispatch(setLoading(false));
        });
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      const alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      Alert.alert(t(`${alertMsg}1`), '', [
        {
          text: t('Ok'),
          onPress: () => {},
        },
      ]);
    }
  }, [fields]);

  const requestRecommendationNotifier = (res) => {
    const body = {
      personWhoRequest: data?.talentProfile?.firstName,
      personWhoRequestEmail: data?.talentProfile?.email,
      recommender: username,
      recommenderEmail: emailTo,
    };
    recommendationRequestNotify(body, token).then((response) => {
      dispatch(setLoading(false));
      if (res?.askUserId) {
        dispatch(isSubmitSuccess(true));
      } else {
        Toast.show({
          text: 'Something went wrong, while processing your request',
          textStyle: { textAlign: 'left' },
        });
      }
    });
  };

  const { usernameLoading, visible, suggestions, onSearchUsers, setVisible } = useReferCourse();

  const getUsersSuggestions = async () => {
    if (username.length < 2) return;
    await onSearchUsers(username);
  };

  const onUserCrossIconPress = () => {
    if (selectedUsername) {
      setSelectedUsername('');
      setUsername('');
      setFields({
        ...fields,
        relationship: '',
        username: '',
      });
      setRelation([]);
    } else {
      getUsersSuggestions();
    }
  };

  return (
    <View style={styles.mainFormContainer}>
      <ScrollView style={styles.scrollViewStyle}>
        {/* <KeyboardAwareScrollView > */}
        {/* <KeyboardAvoidingView behavior={Platform.OS == 'ios' ? 'padding' : 'height'}  > */}

        <View style={{ position: 'relative' }}>
          <EditText
            required
            editable={true}
            label={'Username'}
            placeholder={'Who do you want to ask?'}
            labelStyle={styles.labelStyle}
            value={username}
            rightIconLoading={usernameLoading}
            rightIcon={selectedUsername ? AssetsImages.cross_x : AssetsImages.ic_search}
            onChange={async (txt) => {
              setUsername(txt);
              // if (txt == "") setVisible(false);
              setSelectedUsername('');
              await onSearchUsers(txt);
            }}
            onSubmit={getUsersSuggestions}
            rightIconOnPress={onUserCrossIconPress}
          />
          <View style={{ marginHorizontal: 20 }}>
            {suggestions?.length > 0 && visible && (
              <SuggestionsList
                suggestions={suggestions}
                onUserItemPress={(item) => {
                  setFields({
                    ...fields,
                    username: item.id,
                  });
                  setUsername(item?.name);
                  setVisible(false);
                  setTimeout(() => setSelectedUsername(item?.id), 0);
                  getList(item?.name);
                  setEmailTo(item.email);
                }}
              />
            )}
          </View>
        </View>
        <Spacer height={30} />
        <Dropdown
          required
          label="Relation"
          loading={relationLoading}
          disable={!selectedUsername}
          placeholder={t('Select Relation')}
          data={relationList}
          type="relation"
          getSelectedValue={onChangeData('relationship')}
          value={fields?.bookRelation}
          style={{ marginBottom: 30 }}
        />

        <EditText
          required
          placeholder={'Please enter your message here'}
          label={'Message'}
          value={fields.askMessage}
          customInputStyles={styles.descBox}
          textAlignVertical="top"
          inputViewStyle={[{ height: 120 }]}
          multiline
          blurOnSubmit={true}
          returnKeyType={'done'}
          onSubmit={() => Keyboard.dismiss()}
          labelStyle={styles.labelStyle}
          onChange={(askMessage) => setFields({ ...fields, askMessage })}
        />
      </ScrollView>
      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'Request Recommendation'}
      />
    </View>
  );
};
export const WriteRecommendation = ({ onChange, data, loading, item }) => {
  const [fields, setFields] = useState<Record<string, string>>({
    askMessage: item?.askMessage || '',
    askUserId: item?.askUserId || '',
    giverMessage: '',
    giverUserId: item?.giverUserId || '',
    relationship: item?.relationship || '',
    statusRecommend: 'true',
  });
  const [emailTo, setEmailTo] = useState(
    (item && item?.askUser?.length && item?.askUser[0]?.email) || '',
  );
  const [selectedUsername, setSelectedUsername] = useState(item?.askUserId || '');
  const [username, setUsername] = useState(
    (item?.askUser?.length && item?.askUser[0]?.fullname) || '',
  );
  // const [username] = useState(item?.giverUser?.length && item?.giverUser[0]?.firstName || '');
  const [relationList, setRelation] = useState([]);
  const dispatch = useDispatch();
  const onChangeData = (key) => (value) => {
    setFields({ ...fields, [key]: value });
  };
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const { theme } = useTheme();
  const backgroundColor = { backgroundColor: theme.backgroundColor };

  const name = capitalizeFirstLetter(
    item?.giverUser?.length ? item?.giverUser[0]?.fullname?.toLowerCase() : username,
  );
  const [relationLoading, setRelationLoading] = useState(false);
  const { t } = useTranslation();
  const { styles } = useStyles();
  const isAnyChange = checkChange(
    {
      askMessage: fields?.askMessage,
      askUserId: fields?.askUserId,
      giverMessage: fields?.giverMessage,
      giverUserId: fields.giverUserId,
      relationship: fields.relationship,
      statusRecommend: fields.statusRecommend,
    },
    {
      askMessage: item?.askMessage || '',
      askUserId: item?.askUserId || '',
      giverMessage: item?.giverMessage || '',
      giverUserId: item?.giverUserId || '',
      relationship: item?.relationship || '',
      statusRecommend: item?.statusRecommend,
    },
  );

  useEffect(() => {
    if (username) {
      getList(username);
    }
  }, [username, username]);

  useEffect(() => {
    if (!item?.id) {
      setFields({
        ...fields,
        giverUserId: data?.talentProfile?.id,
      });
    }
  }, [item]);

  const getList = async (name) => {
    setRelationLoading(true);
    const result = await getPickList(
      data?.response?.liferayaccesstoken,
      pickListType.writeRecommendationList,
    );
    if (result?.length) {
      const replacedArray = result?.map((obj) => {
        const newObj = { ...obj };

        newObj.label = newObj.label.replace('<NAME>', name);
        newObj.name = newObj.name.replace('<NAME>', '');
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace('<NAME>', name);

        const userName = talentProfile?.firstName ?? '';
        const youRegex = /you/gi;
        newObj.label = newObj.label.replace(youRegex, userName);
        newObj.name = newObj.name.replace(youRegex, userName);
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace(youRegex, userName);

        const wereRegex = /were/gi;
        newObj.label = newObj.label.replace(wereRegex, 'was');
        newObj.name = newObj.name.replace(wereRegex, 'was');
        newObj.name_i18n['en-US'] = newObj.name_i18n['en-US'].replace(wereRegex, 'was');

        return newObj;
      });
      setRelation(replacedArray);
    }
    setRelationLoading(false);
  };

  const onSubmitData = useCallback(() => {
    const emptyFields = checkEmptyValidation({
      askUserId: fields?.askUserId,
      relationship: fields?.relationship,
      giverMessage: fields?.giverMessage,
    });

    if (!emptyFields?.length) {
      dispatch(setActionType('sendRecommendation'));
      dispatch(setLoading(true));
      if (item?.id) {
        updateRecommendation(fields, item?.id, token)
          .then((res) => {
            if (res?.status == 'BAD_REQUEST') {
              Toast.show({
                text: res?.title,
                textStyle: { textAlign: 'left' },
              });
              dispatch(setLoading(false));
              return;
            }

            notifyRecommender(res);
          })
          .catch((err) => {
            dispatch(setLoading(false));
          });
      } else {
        addRecommendation(fields, token)
          .then((res) => {
            requestRecommendationNotifier(res);
          })
          .catch((err) => {
            dispatch(setLoading(false));
          });
      }
    } else {
      const splittedField = splitCamelCase(emptyFields[0]);
      let alertMsg = splittedField?.charAt(0).toUpperCase() + splittedField?.slice(1);
      alertMsg =
        alertMsg == 'Ask User Id'
          ? 'User Name'
          : alertMsg == 'Giver Message'
            ? 'Message'
            : alertMsg;
      Alert.alert('Please Enter ' + alertMsg);
    }
  }, [fields]);

  const requestRecommendationNotifier = (res) => {
    const body = {
      personWhoRequest: data?.talentProfile?.firstName,
      personWhoRequestEmail: data?.talentProfile?.email,
      recommender: username,
      recommenderEmail: emailTo,
    };
    recommendationRequestNotify(body, token).then((response) => {
      dispatch(setLoading(false));
      if (response?.status == 'BAD_REQUEST') {
        Toast.show({
          text: 'Something went wrong, while processing your request',
          textStyle: { textAlign: 'left' },
        });
        return;
      }
      if (res?.askUserId) {
        dispatch(isSubmitSuccess(true));
      } else {
        Toast.show({
          text: 'Something went wrong, while processing your request',
          textStyle: { textAlign: 'left' },
        });
      }
    });
  };

  const notifyRecommender = (res) => {
    const body = {
      emailTo: emailTo,
      userToName: username,
      recommender: data?.talentProfile?.firstName,
      recommenderEmail: data?.talentProfile?.email,
    };
    addRecommendation_notifier(data?.talentProfile?.id, body, token).then((response) => {
      dispatch(setLoading(false));
      if (res?.askUserId) {
        dispatch(isSubmitSuccess(true));
      } else {
        Toast.show({
          text: 'Something went wrong, while processing your request',
          textStyle: { textAlign: 'left' },
        });
      }
    });
  };

  const { usernameLoading, visible, suggestions, onSearchUsers, setVisible } = useReferCourse();

  const getUsersSuggestions = async () => {
    await onSearchUsers(username);
  };

  const onUserCrossIconPress = () => {
    if (selectedUsername) {
      setSelectedUsername('');
      setUsername('');
      setFields({
        ...fields,
        relationship: '',
      });
      setRelation([]);
    } else {
      getUsersSuggestions();
    }
  };

  return (
    <View style={styles.mainFormContainer}>
      <ScrollView style={styles.scrollViewStyle}>
        {/* <KeyboardAvoidingView behavior={Platform.OS == 'ios' ? 'padding' : 'height'}  > */}
        <View style={{ position: 'relative' }}>
          <EditText
            required
            editable={true}
            label={'Username'}
            placeholder={'Who do you want to ask?'}
            labelStyle={styles.labelStyle}
            value={username}
            rightIconLoading={usernameLoading}
            rightIcon={selectedUsername ? AssetsImages.cross_x : AssetsImages.ic_search}
            onChange={async (txt) => {
              setUsername(txt);
              if (txt == '') setVisible(false);
              setSelectedUsername('');
              await onSearchUsers(txt);
            }}
            onSubmit={getUsersSuggestions}
            rightIconOnPress={onUserCrossIconPress}
          />
          <View style={{ marginHorizontal: 20 }}>
            {suggestions?.length > 0 && visible && (
              <SuggestionsList
                suggestions={suggestions}
                onUserItemPress={(item) => {
                  setFields({
                    ...fields,
                    askUserId: item.id,
                  });
                  setUsername(item?.name);
                  setVisible(false);
                  setTimeout(() => setSelectedUsername(item?.id), 0);
                  getList(item?.name);
                  setEmailTo(item.email);
                  if (item?.email == undefined) {
                    setEmailTo(item?.name);
                  }
                }}
              />
            )}
          </View>
        </View>
        <Spacer height={30} />
        <Dropdown
          required
          loading={relationLoading}
          disable={!selectedUsername}
          placeholder={fields?.relationship ? fields?.relationship : t('Select Relation')}
          data={relationList}
          label={'Relation'}
          type="relation"
          getSelectedValue={onChangeData('relationship')}
          value={fields?.relationship}
          style={{ marginBottom: 30 }}
        />

        <EditText
          required
          placeholder={'Please enter your message here'}
          label={'Message'}
          value={fields.giverMessage}
          customInputStyles={styles.descBox}
          textAlignVertical="top"
          inputViewStyle={[{ height: 120 }]}
          multiline
          blurOnSubmit={true}
          onSubmit={() => Keyboard.dismiss()}
          labelStyle={styles.labelStyle}
          onChange={(giverMessage) => setFields({ ...fields, giverMessage })}
        />
      </ScrollView>
      <ThemeBottomButton
        isDisabled={!isAnyChange?.length}
        isLoading={loading}
        onPress={onSubmitData}
        heading={'send Recommendation'}
      />
    </View>
  );
};

const LanguageView = ({
  langauge,
  desc,
  onEdit,
  onDelete,
  res,
  isLoading,
  editLoading,
  selectedLanguage,
  stateLoading,
}) => {
  const { theme, isDarkMode } = useTheme();
  const { styles } = useStyles();

  // const deleteItem=()=()=>onDelete()
  return (
    <View style={[styles.rowJustified, { marginHorizontal: 20 }]}>
      <View style={styles.languageView}>
        <TextView
          style={[styles.langaugeText, { color: theme.textColor }]}
          text={capitalizeFirstLetter(langauge)}
        />
        <TextView
          style={[styles.langDesc, { color: theme.textColor }]}
          text={capitalizeFirstLetter(desc)}
        />
      </View>

      <View style={styles.row}>
        <Pressable disabled={stateLoading} onPress={onEdit}>
          {res?.id === selectedLanguage && editLoading ? (
            <View
              style={{ backgroundColor: '#000', padding: 5, borderRadius: 50, marginStart: 10 }}
            >
              <ActivityIndicator size={'small'} color={'#fff'} />
            </View>
          ) : (
            <Image source={AssetsImages.editBlack} style={styles.actionBtn} />
          )}
        </Pressable>
        {!isLoading ? (
          <Pressable disabled={stateLoading} onPress={onDelete}>
            <Image source={AssetsImages.deleteBlack} style={styles.actionBtn} />
          </Pressable>
        ) : (
          <View style={{ backgroundColor: '#000', padding: 5, borderRadius: 50, marginStart: 10 }}>
            <ActivityIndicator size={'small'} color={'#fff'} />
          </View>
        )}
      </View>
    </View>
  );
};
//common components
export const AddAttachment = ({
  onPressAttachment,
  label,
  getFileName,
  type,
  fileName,
  containerStyle,
  labelDescription,
  labelStyle,
  fileLengthText,
  fileLabel,
  customfileOptions,
  labelDescriptionStyles,
  titleStyle,
}) => {
  const [isLoading, setLoading] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const emiratesId = useSelector((state) => state?.getWFMProfile?.talentProfile?.emiratesId);
  const stateLoading = useSelector((state) => state?.getWFMProfile?.isLoading);
  const { theme, isDarkMode } = useTheme();
  const [imageOptions, setImageOptions] = useState(false);
  const { styles } = useStyles();
  const getFile = (e) => {
    setLoading(false);
    if (e) {
      getFileName(e);
    }
  };

  const openFileView = async () => {
    setLoading(true);
    const response = await pickDocument(token, getFile, type, emiratesId);
    getFile(response);
  };

  const navigateToFun = (item) => () => {
    toggleVisibility();
    setTimeout(() => {
      if (item.id == '1') openCameraCropper();
      if (item.id == '2') openGallery();
      if (item.id == '3') openFileView();
    }, 500);
  };

  const openGallery = async () => {
    setLoading(true);
    const response = await pickImage(token, getFile, 'profilepicture', emiratesId);
    getFile(response);
  };

  const openCameraCropper = async () => {
    setLoading(true);
    const response = await pickImageFromCamera(token, getFile, 'profilepicture', emiratesId);
    getFile(response);
  };

  const toggleVisibility = async () => {
    type == 'pdfOnly' ? openFileView() : setImageOptions(!imageOptions);
  };

  return (
    <View style={containerStyle}>
      <TextView
        style={[styles.label, titleStyle, { color: theme.textColor, opacity: 1 }]}
        text={label}
      />
      {labelDescription && (
        <TextView
          style={[styles.labelDescription, labelDescriptionStyles, { color: theme.textColor }]}
          text={labelDescription}
        />
      )}
      <Pressable disabled={stateLoading}>
        <Pressable
          disabled={stateLoading}
          onPress={toggleVisibility}
          style={[
            styles.dashView,
            {
              borderColor: theme.borderBackground,
            },
          ]}
        >
          {isLoading ? (
            <ActivityIndicator color={'black'} />
          ) : (
            <Image
              source={AssetsImages.folder}
              style={[styles.upload, { tintColor: theme.tintColor }]}
            />
          )}

          <TextView
            style={styles.selectAttachment}
            text={fileLabel ? fileLabel : 'Select Attachment file'}
          />
          <TextView
            style={[styles.format, { color: theme.textColor }]}
            text={
              fileLengthText
                ? fileLengthText
                : type == 'pdfOnly'
                  ? 'PDF ( max. 5MB)'
                  : 'JPG,PNG OR PDF (MAX. 5MB)'
            }
          />
        </Pressable>
      </Pressable>

      <BottomSheet
        visiblity={imageOptions}
        setVisibility={setImageOptions}
        children={
          <View style={styles.sheetContainer}>
            {(customfileOptions ? customfileOptions : fileOptions)?.map((item, index) => {
              return (
                <TouchableOpacity
                  onPress={navigateToFun(item)}
                  key={index}
                  style={[
                    styles.missingContainer,
                    {
                      borderBottomColor: theme.borderBackground,
                    },
                  ]}
                >
                  <View style={commonStyle.rowAlign}>
                    <Image
                      source={item.icon}
                      style={[styles.icon, isDarkMode && { tintColor: Colors.activeStateDark }]}
                    />
                    <TextView
                      style={[
                        styles.missingSectionText,
                        isDarkMode && { color: Colors.activeStateDark },
                      ]}
                      text={item?.title}
                    />
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        }
      />
    </View>
  );
};
//common components

export const AttachmentView = ({
  onPressAttachment,
  size,
  days,
  attachmentName,
  onPressDelete,
  type,
  fileName,
}) => {
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const stateLoading = useSelector((state) => state?.getWFMProfile?.isLoading);
  const navigation = useNavigation();
  const imageSource = attachmentName?.includes('pdf')
    ? AssetsImages.pdf
    : AssetsImages.imagePlaceholder;

  const match = attachmentName?.match(fileNameRegex);
  const extension = attachmentName?.split('.').pop();
  const { styles } = useStyles();
  const openAttachment = async () => {
    const match = attachmentName?.match(fileNameRegex);

    if (!attachmentName) return;
    if (!attachmentName?.includes('pdf')) {
      setTimeout(() => {
        navigation.navigate('AttachmentView', attachmentName);
      }, 100);
    } else {
      navigation.navigate('ViewCertificates', {
        downloadLink: attachmentName,
        isInApp: false,
        fileName: match?.length ? match[1] + '.' + extension : 'Download.pdf',
      });
    }
  };

  const [isLoading, setLoading] = useState(false);
  const { theme } = useTheme();
  return (
    <Pressable
      disabled={isLoading || stateLoading}
      onPress={openAttachment}
      style={styles.attachmentView}
    >
      <View style={[styles.attachmentContainers]}>
        <View style={styles.row}>
          <Image source={imageSource} style={styles.pdf} />
          <View style={{ width: '75%' }}>
            <TextView
              numberOfLines={2}
              style={[styles.attachmentName, { color: theme.textColor }]}
              text={
                fileName ? fileName : match?.length ? match[1] + '.' + extension : attachmentName
              }
            />
          </View>
          {isLoading ? <ActivityIndicator /> : null}
        </View>

        <TouchableOpacity disabled={isLoading || stateLoading} onPress={onPressDelete}>
          <Image
            source={AssetsImages.trash}
            style={[styles.trash, { tintColor: theme.tintColor }]}
          />
        </TouchableOpacity>
      </View>
    </Pressable>
  );
};
