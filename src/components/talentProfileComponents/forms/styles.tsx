import fonts from '../../../utils/fonts';
import { Colors, Dimen } from '../../../theme';
import { Platform, StyleSheet } from 'react-native';
import fontSize, { isPad } from '../../../utils/fontSize';
import { I18nManager } from 'react-native';
import { lineHeight } from '@utils/constants';
import { useTheme } from '@theme/ThemeProvider';

const useStyles = () => {
  const { theme, isDarkMode } = useTheme();

  const styles = StyleSheet.create({
    alternateText: {
      fontFamily: fonts.medium,
      paddingHorizontal: 20,
      fontSize: fontSize.xlarge,
      marginBottom: 24,
    },
    labelStyle: {
      fontFamily: fonts.regular,
      fontSize: fontSize.small,
    },
    inputStyle: {
      marginBottom: 24,
    },
    descStyle: { marginBottom: 24, height: 120 },
    descBox: {
      textAlignVertical: 'top',
      maxHeight: '90%',
    },
    inputDropDownStyle: {
      marginBottom: 8,
      borderLeftWidth: 0,
      borderRightWidth: 1,
      borderRadius: 0,
      paddingHorizontal: 0,
      marginHorizontal: 0,
    },
    buttonText: {
      fontFamily: fonts.regular,
      fontSize: fontSize.medium
    },
    header: {
      paddingHorizontal: 20,
      marginTop: 20,
      marginBottom: 10,
    },
    rowJustified: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginHorizontal: 7,
      marginVertical: 10,
      paddingBottom: 10,
      // backgroundColor: '#fff'
    },
    languageView: {},
    actionBtn: {
      width: isPad ? 40 : 30,
      height: isPad ? 40 : 30,
      resizeMode: 'contain',
      marginLeft: 10,
      marginVertical: Platform.OS == 'ios' ? 15 : 0,
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    langDesc: {
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
      opacity: 0.7,
      marginTop: Platform.OS == 'ios' ? 8 : 0,
      textAlign: 'left'
    },
    langaugeText: {
      fontFamily: fonts.medium,
      fontSize: isPad ? fontSize.large15 : fontSize.semiMedium,
      textAlign: 'left'
    },
    dashView: {
      borderStyle: 'dashed',
      borderWidth: 2,
      borderColor: Colors.borderGray,
      marginHorizontal: 20,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      height: Dimen.height * 0.16,
    },
    upload: {
      width: isPad ? 40 : 25,
      height: isPad ? 40 : 25,
      resizeMode: 'contain',
      marginBottom: 10,
    },
    selectAttachment: {
      color: 'rgba(14, 121, 219, 1)',
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    },
    format: {
      color: Colors.black,
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
      marginTop: Platform.OS == 'ios' ? 5 : 0,
    },
    attachmentView: {
      marginTop: 20,
    },
    attachmentContainers: {
      // borderWidth: 1,
      // borderColor: Colors.borderGray,
      marginHorizontal: 10,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'space-between',
      flexDirection: 'row',
      paddingHorizontal: 10,
    },
    trash: {
      width: isPad ? 30 : 25,
      height: isPad ? 30 : 25,
      resizeMode: 'contain',
    },
    pdf: {
      width: isPad ? 60 : 30,
      // backgroundColor: 'red'
      height: isPad ? 70 : 40,
      resizeMode: 'contain',
      marginRight: 10,
    },
    size: {
      color: 'rgba(30, 30, 30, 1)',
      fontSize: isPad ? fontSize.large : fontSize.semiMini,
      marginRight: 10,
      fontFamily: fonts.regular,
    },
    uploaded: {
      color: 'rgba(30, 30, 30, 1)',
      fontSize: isPad ? fontSize.large : fontSize.semiMini,
      marginRight: 10,
      fontFamily: fonts.regular,
    },
    attachmentName: {
      fontFamily: fonts.medium,
      fontSize: isPad ? fontSize.large : fontSize.semiMedium,
      color: 'black',
      padding: 0,
      textAlign: 'left'
    },
    label: {
      marginHorizontal: 20,
      marginBottom: 10,
      color: Colors.black,
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
      textAlign: 'left',
      lineHeight: lineHeight(isPad ? fontSize.semiMedium1 : fontSize.medium)
    },
    labelDescription: {
      marginHorizontal: 20,
      marginBottom: 10,
      color: Colors.black,
      opacity: 0.5,
      fontFamily: fonts.regular,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.xmini,
      textAlign: 'left',
    },
    tagsStyle: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: 5,
      borderColor: Colors.borderGray,
      borderWidth: 1,
      padding: Platform.OS == 'ios' ? 7.5 : 3,
      borderRadius: 20,
      paddingHorizontal: Platform.OS == 'ios' ? 15 : 10,
      marginVertical: 5,
    },
    tagContainer: {
      marginHorizontal: 10,
      flexDirection: 'row',
      alignItems: 'center',
      flexWrap: 'wrap',
    },
    suggestionsList: {
      marginHorizontal: 20,
      flexWrap: 'wrap',
      marginTop: -10,
      backgroundColor: 'white',
      position: 'absolute',
      width: '90%',
      zIndex: 11111,
      top: 65,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,

      elevation: 5,
      padding: 10,
      borderRadius: 5,
    },
    delete: {
      width: 20,
      height: 20,
      resizeMode: 'contain',
    },
    tagsText: {
      marginRight: 10,
      fontFamily: fonts.regular,
      fontSize: fontSize.semiSmall,
      color: 'black',
      textTransform: 'capitalize',
    },
    heading: {
      marginHorizontal: 18,
      marginTop: 20,
      marginVertical: 10,
      textAlign: 'left',
      color: theme.textColor
    },
    subHeading: {
      marginHorizontal: 18,
      lineHeight: 20,
      opacity: 0.8,
    },
    select: {
      marginHorizontal: 18,
      textAlign: 'left',
      fontFamily: fonts.regular,
      fontSize: fontSize.large,

    },
    scrollView: {
      paddingBottom: 120,
      flex: 1,
    },
    scrollViewStyle: {
      flex: 1,
      marginBottom: 80,
      zIndex: 0,
    },
    overlayBtn: {
      position: 'absolute',
      bottom: Platform.OS == 'ios' ? 0 : 20,
      width: '100%',
      height: 100,
      borderTopWidth: 2,
      borderTopColor: theme.bgDark,
      alignItems: 'center',
      justifyContent: 'center',
      // backgroundColor: 'white',
      backgroundColor: theme.bgLightDark

    },
    detailsHeading: {
      fontFamily: fonts.semiBold,
      fontSize: fontSize.xxxlarge,
      marginTop: 20,
      color: 'black',
      marginHorizontal: 20,
      marginBottom: 30,
      textAlign: 'left'
    },
    detailsDesc: {
      fontFamily: fonts.regular,
      fontSize: fontSize.small,
      marginTop: 20,
      color: 'black',
      marginBottom: 60,
      lineHeight: 24,
    },
    suggestionText: {
      borderBottomColor: Colors.borderGray,
      borderBottomWidth: 1,
      width: '100%',
      paddingBottom: 10,
      fontFamily: fonts.regular,
      fontSize: fontSize.small,
    },
    preffarredContainer: {
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 24,
    },
    preffarredLabel: {
      fontFamily: fonts.regular,
      fontSize: I18nManager.isRTL ? fontSize.mini : fontSize.small,
      textTransform: 'capitalize',
      lineHeight: lineHeight(isPad ? fontSize.semiMedium1 : fontSize.small),
    },
    VerifyModalContainer: {
      alignSelf: 'center',
      borderRadius: 10,
      paddingVertical: 20,
    },
    textInputStyle: {
      borderColor: Colors.borderGray,
      borderWidth: 1,
      borderBottomWidth: 1,
      width: '20%',
      borderRadius: 15,
      height: 80,
      fontSize: fontSize.xxxxxxlarge,
      fontFamily: fonts.regular,
    },
    clock: {
      width: 15,
      height: 15,
      marginRight: 5,
    },
    resend: {
      color: Colors.blue,
      fontFamily: fonts.medium,
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.xxmini,
    },
    goBack: {
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.small,
      color: Colors.black,
      marginStart: 10,
    },

    resendCode: {
      fontSize: isPad ? fontSize.semiMedium1 : fontSize.xxmini,
      color: Colors.black,
      marginStart: 10,
    },
    listItem: {
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      paddingBottom: 20
    },
    skillCheck: {
      width: isPad ? 25 : 20,
      height: isPad ? 25 : 20,
      resizeMode: 'contain',
    },
    currentlyWorking: {
      fontFamily: fonts.regular,
      color: 'rgba(51, 51, 51, 1)',
      fontSize: fontSize.small,
      marginStart: 10
    },
    noSubInterest: {
      fontFamily: fonts.thin,
      fontSize: fontSize.small,
      marginHorizontal: 20,
      color: theme.textColor
    },
    sheetContainer: {
      backgroundColor: 'transparent',
      paddingHorizontal: 20,
      paddingBottom: 60,
    },
    missingContainer: {
      borderBottomColor: '#f5f5f5',
      paddingTop: 15,
      borderBottomWidth: 1,
      justifyContent: 'space-between',
      flexDirection: 'row',
      alignItems: 'center',
    },
    icon: {
      width: 20,
      height: 20,
      resizeMode: 'contain',
      marginRight: 10,
      marginTop: 2,
    },
    missingSectionText: {
      textTransform: 'capitalize',
      fontFamily: fonts.medium,
      fontSize: fontSize.semiMedium,
      marginVertical: 10,
    },
    mainFormContainer: {
      flex: 1,
      backgroundColor: theme.backgroundColor
      // backgroundColor: theme.bgLightDark
    }
  });

  return { styles, theme }

}

export default useStyles;

export const languageHeader = StyleSheet.create({
  itemContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    paddingHorizontal: 20,
    paddingVertical: 13,
  },
  aboutHeader: {
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: fontSize.xlarge,
    marginRight: 10,
    color: '#000',
    fontFamily: fonts.medium,
  },
  contentContainer: {
    paddingVertical: 10,
  },
  componentContainer: {
    marginBottom: 5,
  },
  componentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  addIcon: {
    width: 35,
    height: 35,
    marginHorizontal: 10,
    resizeMode: 'contain',
  },
  actionIcon: {
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
  },
  circle: {
    width: 35,
    height: 35,
    marginHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    justifyContent: 'space-between',
  },
  rowAbsolute: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 40,
  },
  actionIconContainer: {
    borderWidth: 1,
    borderRadius: 50,
    borderColor: Colors.borderGray,
  },
  contactInfo: {
    marginHorizontal: 20,
    backgroundColor: Colors.creamColorGradient,
    borderRadius: 20,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editIcon: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 50,
    borderColor: Colors.borderGray,
  },
  contactInfoText: {
    fontFamily: fonts.semiBold,
    fontSize: fontSize.large,
  },
  iconImage: {
    width: isPad ? 40 : 30,
    height: isPad ? 40 : 30,
    borderWidth: 1,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: Colors.borderGray,
  },
  field: {
    opacity: 0.7,
    fontSize: fontSize.medium,
    marginStart: 10,
    color: Colors.black,
  },
  value: {
    fontSize: fontSize.medium,
    marginStart: 10,
    color: Colors.black,
  },
  alternateHeading: {
    marginVertical: 10,
    marginTop: 30,
  },
  ViewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 22,
  },
  aboutContainer: {
    paddingHorizontal: 20,
  },
  fill: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    flex: 1,
  },


})