import React, { useState } from 'react';
import { Image, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { TextView } from '../../../../src/components';
import { AssetsImages, Colors, Icons, Images } from '../../../../src/theme';
import { IconsType } from '../../../../src/theme/Icons';
import { LabelConfig } from '../../../../src/theme/labelConfig';
import fontSize from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
const detail =
  'As a mentor, my primary role was to facilitate the learning and development of my mentee for their career growth. I provided guidance regarding acquiring new skills, deepen existing knowledge, and adapt to the evolving demands of the chosen field.';
const { showMore, showLess } = LabelConfig.talentProfile.about;
const { myMentees } = LabelConfig.talentProfile.mentoringExp;
const array = [Images.face1, Images.face2, Images.face1];

const MentoringExperience = () => {
  const [courseContentExpended, setCourseContentExpended] = useState(false);
  const onExpend = () => setCourseContentExpended(!courseContentExpended);

  return (
    <View>
      <View style={styles.container}>
        <Pressable style={styles.borderCircle}>
          <Image source={AssetsImages.adsg} style={styles.companyImage} />
        </Pressable>

        <View style={styles.detailContainer}>
          <TextView style={styles.heading} text={'Mentor: Talent Management'} />
          <TextView style={styles.desc} text="Abu Dhabi Investment Authority." />
          <TextView style={styles.year} text="Jan 2010 - May 2019, Abu Dhabi, UAE" />
          <TextView style={styles.detail} text={detail} />
          <TextView style={styles.myMentees} text={myMentees} />
          <View style={styles.row}>
            <View style={styles.imageStack}>
              {array?.map((res, index) => {
                return (
                  <Image
                    style={[
                      styles.mentorImage,
                      {
                        marginLeft: index > 0 ? -20 : 0,
                      },
                    ]}
                    source={res}
                  />
                );
              })}
            </View>
            <TextView
              style={styles.names}
              text="Abeer Aldahash, Khaled Ahmed, M. Fahad bin Khalil"
            />
          </View>
          <TouchableOpacity onPress={onExpend} style={styles.viewMoreContainer}>
            <TextView style={styles.ViewMore} text={!courseContentExpended ? showMore : showLess} />
            <Icons type={IconsType.FontAwesome5} name={'chevron-down'} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default MentoringExperience;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 20,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: 32,
    height: 32,
  },
  detailContainer: {
    marginHorizontal: 10,
    width: '80%',
  },
  borderCircle: {
    borderColor: '#DBDBDB',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 5,
  },
  heading: {
    fontFamily: fonts.semiBold,
    fontSize: fontSize.large,
    color: Colors.black,
  },
  desc: {
    fontFamily: fonts.semiBold,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 5,
    lineHeight: 22,
  },
  dept: {
    fontFamily: fonts.light,
    fontSize: fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 15,
  },
  year: {
    fontFamily: fonts.light,
    fontSize: fontSize.mini,
    color: Colors.black,
    marginTop: 10,
    opacity: 0.7,
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.medium,
    marginRight: 5,
  },
  mentorImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
});
