import PerformanceItem from '../performanceItem';
import { TextView } from '../../../components/common';
import React, { useEffect, useState } from 'react';
import {
  Image,
  StyleSheet,
  View,
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { useSelector } from 'react-redux';
import { sortArray } from '../../../utils/constants';
import commonStyle from '../../../utils/commonStyle';
import AssetsImages from '../../../theme/AssetsImages';
import { fontSize, fonts } from '../../../utils';
import Colors from '../../../theme/Colors';
import { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';

const Performance = () => {
  const [performance, setPerformance] = useState([1, 2]);
  const [isExpend, setIsExpend] = useState(false);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { theme } = useTheme()

  useEffect(() => {
    if (talentProfile?.performance) {
      setIsExpend(false);
      setPerformance(sortArray(talentProfile?.performance, 'year', true)?.slice(0, isPad ? 3 : 2));
    }
  }, [talentProfile, talentProfile?.performance]);

  useEffect(() => {
    if (isExpend) {
      setPerformance(sortArray(talentProfile?.performance, 'year', true));
    } else {
      setPerformance(sortArray(talentProfile?.performance, 'year', true)?.slice(0, isPad ? 3 : 2));
    }
  }, [isExpend]);

  const onExpend = () => setIsExpend(!isExpend);

  const onViewRef = React.useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  });

  const viewConfigRef = React.useRef({ viewAreaCoveragePercentThreshold: 50 });

  return (
    <View>
      {!talentProfile?.performance?.length ? (
        <TextView style={[commonStyle.noInfo, { color: theme.textColor }]} text="No information available" />
      ) : null}

      {/* {talentProfile?.performance?.length ? (
        <View style={styles.scoreCard}>
          <Image style={styles.scoreImage} source={AssetsImages.highScore} />
          <TextView style={[styles.score, { color: theme.textColor }]} text="High Score" />
          <Image style={styles.scoreImage} source={AssetsImages.lowScore} />
          <TextView style={[styles.score, { color: theme.textColor }]} text="Low Score" />
        </View>
      ) : null} */}
      <View style={styles.container}>
        <FlatList
          data={performance}
          renderItem={({ item }) => <PerformanceItem item={item} />}
          horizontal
          showsHorizontalScrollIndicator={false}
          scrollEventThrottle={16}
          pagingEnabled
          onViewableItemsChanged={onViewRef.current}
          viewabilityConfig={viewConfigRef.current}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <View style={styles.indicatorContainer}>
        {performance?.map((_, index) => (
          <View
            key={index}
            style={[styles.indicator, { opacity: index === currentIndex ? 1 : 0.2 }]}
          />
        ))}
      </View>
    </View>
  );
};

export default Performance;

const styles = StyleSheet.create({
  container: {
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  scoreCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  scoreImage: {
    height: isPad ? 25 : 20,
    width: isPad ? 25 : 20,
    resizeMode: 'contain',
    marginRight: 10,
  },
  score: {
    marginRight: 20,
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.black,
    marginHorizontal: 2,
  },
});
