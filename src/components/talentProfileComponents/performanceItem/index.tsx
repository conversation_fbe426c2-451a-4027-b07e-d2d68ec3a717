import { isPad } from '@utils/fontSize';
import React from 'react';
import { I18nManager, Image, Platform, StyleSheet, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { Line } from 'react-native-svg';
import { TextView } from '../../../components/common';
import { AssetsImages, Dimen, fontSize, fonts } from '../../../theme';
import Colors from '../../../theme/Colors';
import CircularProgress from 'react-native-circular-progress-indicator';

const PerformanceItem = ({ item }) => {
  const isRTL = I18nManager.isRTL;
  return (
    <View style={styles.performanceContainer}>
      <View style={{ width: '62%', justifyContent: 'space-between',paddingRight:isRTL? 20:0 }}>
        <View>
          <TextView style={styles.performanceApp} text={'Performance Appraisal'} />
          <TextView text={`${item?.year}`} style={styles.heading} />
        </View>

        <View style={styles.ratingRow}>
          <View style={styles.ratingContainer}>
            <Image style={styles.cup} source={AssetsImages.cup} />
          </View>
          <TextView style={styles.ratingText} text="Rating: 03" />
        </View>
      </View>

      <View style={{ width: '38%' }}>
        <View style={styles.progressView}>
          <CircularProgress
            value={Number(item.rating)}
            activeStrokeWidth={18}
            inActiveStrokeWidth={18}
            titleColor="black"
            radius={isPad ? 100 : 60}
            inActiveStrokeColor="#e8e8e8"
            showProgressValue={false}
            activeStrokeColor={'#C2B9F5'}
            activeStrokeSecondaryColor={'#C3F2F5'}
          />
        </View>
      </View>
    </View>
  );
};

export default PerformanceItem;

const styles = StyleSheet.create({
  performanceContainer: {
    marginVertical: 15,
    borderRadius: 5,
    paddingVertical: 20,
    backgroundColor: 'rgba(245, 245, 245, 1)',
    marginRight: 10,
    flexDirection: 'row',
    width: Dimen.width / 1.3,
    paddingHorizontal: 10,
    paddingEnd: 15
  },
  heading: {
    fontFamily: fonts.thin,
    color: Colors.black,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.xxxxlarge,
  },
  comment: {
    position: 'absolute',
    marginHorizontal: 25,
    textAlign: 'center',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.large : fontSize.xxmini,
    width: isPad ? 120 : 80,
  },
  performanceApp: {
    fontFamily: fonts.medium,
    color: Colors.black,
    fontSize: fontSize.medium,
    marginBottom: 5,
  },
  ratingContainer: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
    backgroundColor: 'rgba(242, 163, 8, 0.2)',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  cup: {
    width: 15,
    height: 15,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    color: 'rgba(204, 135, 0, 1)',
    fontFamily: fonts.regular,
    fontSize: fontSize.small,
  },
  progressView: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  barOuter: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -10,
  },
  barInner: {
    backgroundColor: 'white',
    height: 60,
    width: 60,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressText: {
    color: Colors.black,
    fontFamily: fonts.thin,
    fontSize: fontSize.semiSmall,
    textAlign: 'center',
  },
});
