import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Image, Platform, Pressable, StyleSheet, View } from 'react-native';
import { API_URL, BEARER_TOKEN } from '../../../api/urls';
import { ConfirmationModal, TextView } from '../../../components/common';
import Colors from '../../../theme/Colors';
import { fontSize, fonts } from '../../../utils';
import { RenderAvatar } from '../skills';
import { Switch } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import AssetsImages from '../../../theme/AssetsImages';
import { useDispatch, useSelector } from 'react-redux';
import { setActionType } from '../../../totara/actions/getWFMProfileAction';
import { deleteRecommendation, updateRecommendation } from '../../../api/server_requests';
import { Toast } from 'native-base';
import { isPad } from '@utils/fontSize';
import { useTheme } from '@theme/ThemeProvider';
import { ImageAuth } from '@components';
import { LabelConfig } from '@theme';
import { BASE_URL } from '@api/api_client';
import { formatDateBasedOnLocale } from '@utils/AppUtils';
import { t } from 'i18next';
import { getUrl, lineHeightForAndroidLTR, lineHeightForAndroidLTRMed } from '@utils/constants';
const { recieved, sent } = LabelConfig.recommendation;

const RecommendationItem = ({ res, viewAll, tab, getAllRecommendations, index, list }) => {
  const [isLoading, setLoading] = useState(false);
  const [isPublished, setIspublished] = useState(res?.publish);
  const navigation = useNavigation();
  const toggleModal = () => setVisibility(!visibility);
  const [visibility, setVisibility] = useState(false);
  const [item, setItem] = useState(null);
  const dispatch = useDispatch();
  const token = useSelector((state) => state.getWFMProfile.response?.liferayaccesstoken);
  const { theme } = useTheme();
  const color = { color: theme.textColor };

  const toggleSwitch = () => {
    setLoading(true);
    const data = {
      askMessage: res?.askMessage,
      askUserId: res?.askUserId,
      giverMessage: res?.giverMessage,
      giverUserId: res?.giverUserId,
      relationship: res?.relationship.replace(/,/g, ''),
      statusRecommend: res?.statusRecommend,
      publish: !isPublished,
    };

    setIspublished(!isPublished);
    updateRecommendation(data, res?.id, token).then((res) => {
      if (res?.askUserId) {
        getAllRecommendations();
        setTimeout(() => {
          setLoading(false);
          setIspublished(!isPublished);
        }, 2000);
      } else {
        setLoading(false);
        Toast.show({
          text: 'Error while processing...',
          textStyle: { textAlign: 'left' },
        });
      }
    });
  };

  const onPressOfRecommendation = () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Write a recommendation',
      type: 'Give_Recommendation',
      item: res,
    });
  };

  const message = tab == recieved || tab == sent ? res?.giverMessage : res?.askMessage;
  const userName = tab == recieved ? res?.giverUser[0]?.fullname : res?.askUser[0]?.fullname;
  const picture = tab == recieved ? res?.giverUser[0]?.picture : res?.askUser[0]?.picture;

  const onDelete = () => {
    setLoading(true);
    deleteRecommendation(item?.id, token).then((res) => {
      getAllRecommendations();
      setLoading(false);
    });
    toggleModal();
  };

  const openModal = (item) => () => {
    toggleModal();
    setItem(item);
    dispatch(setActionType('delete'));
  };

  const { isDarkMode } = useTheme();
  return (
    <View>
      <View
        style={[
          styles.container,
          {
            paddingBottom: !viewAll ? 10 : index == list.length - 1 ? 50 : 20,
            // paddingEnd: 10
          },
        ]}
      >
        <View style={{ flexDirection: 'row', flex: 1, paddingStart: 5 }}>
          <RenderPicture customStyle={styles.borderCircle} picture={picture} />

          <View style={styles.detailView}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
              }}
            >
              <TextView
                style={[styles.heading, color, { flex: 1 }, lineHeightForAndroidLTR]}
                text={userName || 'N/A'}
              />
              {viewAll && tab == 'Received' && (
                <>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      overflow: 'hidden',
                      justifyContent: 'flex-end',
                      flex: 0.5,
                      marginTop: -2,
                    }}
                  >
                    {isLoading ? (
                      <ActivityIndicator />
                    ) : (
                      <TextView
                        style={[styles.status, color]}
                        text={!isPublished ? t(`Off1`) : t('On1')}
                      />
                    )}
                    <Switch
                      style={styles.switchStyle}
                      trackColor={{ false: '#CBD9E7', true: Colors.skyBlue }}
                      thumbColor={isPublished ? 'white' : 'white'}
                      ios_backgroundColor={'#CBD9E7'}
                      onValueChange={toggleSwitch}
                      value={isPublished}
                    />
                  </View>
                </>
              )}
            </View>
            {res?.askUser[0]?.length ? (
              <TextView style={[styles.desc, color]} text={res?.askUser[0]?.jobTitle} />
            ) : null}
            <View style={styles.rowAlign}>
              <TextView
                style={[styles.year, color]}
                text={formatDateBasedOnLocale(res?.dateCreated, 'Do MMMM, YYYY')}
              />
              {res?.relationship?.length < 20 ? (
                <>
                  <View style={styles.dot} />
                  <TextView style={[styles.role, color]} text={res?.relationship} />
                </>
              ) : null}
            </View>
            {res?.relationship && res?.relationship?.length > 20 ? (
              <View style={{ marginTop: 8 }}>
                <TextView style={[styles.role, color]} text={res?.relationship?.trim()} />
              </View>
            ) : null}
            {message ? <TextView style={[styles.details, color]} text={message} /> : null}
            {/* {(!viewAll || tab == 'pending') ? <TextView style={styles.details} text={res?.askMessage} /> : null} */}
            {viewAll && tab == 'Pending' && (
              <Pressable
                onPress={onPressOfRecommendation}
                style={[
                  styles.recommendButton,
                  isDarkMode && { backgroundColor: theme.bgLightDark },
                ]}
              >
                <TextView style={styles.recommendBtn} text="Recommend" />
              </Pressable>
            )}
          </View>
        </View>

        {viewAll && tab == 'pending' && (
          <>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {isLoading ? (
                <ActivityIndicator />
              ) : (
                <Pressable onPress={openModal(res)}>
                  <Image style={styles.actionBtn} source={AssetsImages.deleteBlack} />
                </Pressable>
              )}
            </View>
          </>
        )}
      </View>
      <ConfirmationModal onDelete={onDelete} setVisibility={toggleModal} visibility={visibility} />
    </View>
  );
};

export default RecommendationItem;

const RenderPicture = ({ id, picture, customStyle }) => {
  const [image, setImage] = useState('');
  const token = useSelector((state) => state.getWFMProfile.response?.liferayaccesstoken);

  useEffect(() => {
    if (picture) {
      getUrl(picture, token).then((res) => {
        setImage(res);
      });
    }
  }, [id, picture]);

  return <ImageAuth loaderSize={'small'} uri={image} style={customStyle} />;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 20,
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  detailView: {
    marginStart: 10,
    flex: 1,
    // width: '62%',
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: 32,
    height: 32,
  },

  borderCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    width: 50,
    height: 50,
    backgroundColor: '#f5f5f5',
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.semiMedium,
    color: Colors.black,
    textAlign: 'left',
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.semiSmall,
    color: Colors.black,
    marginTop: 5,
    lineHeight: 22,
    textAlign: 'left',
  },
  dept: {
    fontFamily: fonts.light,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 15,
    textAlign: 'left',
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'left',
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
    textAlign: 'left',
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.semiBold,
    fontSize: fontSize.medium,
    marginRight: 5,
  },
  mentorImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
    textAlign: 'left',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
    textAlign: 'left',
  },
  certificate: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    marginTop: 10,
  },
  dot: {
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: 'black',
    marginHorizontal: 10,
    opacity: 0.5,
  },
  role: {
    textTransform: 'uppercase',
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    opacity: 0.7,
    flex: 1,
    textAlign: 'left',
  },
  rowAlign: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  details: {
    marginTop: 10,
    color: Colors.black,
    opacity: 0.8,
    lineHeight: 22,
    textAlign: 'left',
  },
  askFor: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    padding: 10,
  },
  askForBtn: {
    marginTop: 20,
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 320,
  },
  tabsView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  recievedBtn: {
    borderWidth: 1,
    backgroundColor: 'black',
    marginRight: 10,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendBtn: {
    borderWidth: 1,
    borderColor: 'black',
    marginRight: 10,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recieve: {
    color: 'white',
    fontFamily: fonts.medium,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  send: {
    color: 'black',
    fontFamily: fonts.medium,
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  status: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginEnd: 5,
    marginTop: Platform.OS == 'ios' ? 0 : -2,
    textAlign: 'left',
  },
  recommendButton: {
    backgroundColor: 'black',
    width: 120,
    borderRadius: 25,
    height: 30,
    marginTop: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recommendBtn: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: fontSize.semiSmall,
    marginTop: -2,
  },
  actionBtn: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  switchStyle: {
    transform: [
      { scaleX: Platform.OS == 'android' ? 1 : isPad ? 0.9 : 0.7 },
      { scaleY: Platform.OS == 'android' ? 1 : isPad ? 0.9 : 0.7 },
    ],
    marginTop: -1,
  },
});
