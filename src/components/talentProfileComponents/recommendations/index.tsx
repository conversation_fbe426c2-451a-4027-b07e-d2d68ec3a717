import { useIsFocused } from '@react-navigation/native';
import { useTheme } from '@theme/ThemeProvider';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, I18nManager, Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';
import { Loader, ShowMore, TalentProfileEmptyState, TextView } from '../../../../src/components';
import { Colors, LabelConfig } from '../../../../src/theme';
import {
  getPendingRecommendations,
  getRecievedRequest,
  getSentRequest,
} from '../../../api/server_requests';
import { convertNumbersToArabicNumerals, marginTop, sortArray } from '../../../utils/constants';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import RecommendationItem from '../recommendationItem';
import { Platform } from 'react-native';
import { HEIGHT } from '@totara/components/AnimatedHeader';
const { recieved, sent, pending } = LabelConfig.recommendation;

const Recommendations = ({
  style,
  viewAll,
  selectedTab,
  sentRequestLength,
  isLoading,
  tabLoading,
}) => {
  const [contentExpended, setContentExpended] = useState(false);
  const [tab, setTab] = useState(recieved);
  const [sentRequest, setSentRequest] = useState([]);
  const [recievedRequest, setRecievedRequest] = useState([]);
  const [pendingRequest, setPendingRequest] = useState([]);
  const userId = useSelector((state) => state?.getWFMProfile?.talentProfile?.id);
  const isFocused = useIsFocused();
  const [recommendation, setRecommendation] = useState([]);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);
  const { theme, isDarkMode } = useTheme()

  const handleTab = (inputtab) => () => {
    if (inputtab == tab) return
    setTab(inputtab);
    selectedTab && selectedTab(inputtab);
    setContentExpended(false);
  };

  const onExpend = () => setContentExpended(!contentExpended);

  useEffect(() => {
    setContentExpended(false);
    getAllRecommendations();
  }, [isFocused]);

  const getAllRecommendations = () => {
    getRecievedRequest(userId, token)
      .then((res) => {
        if (res?.items?.length) setRecievedRequest(sortArray(res?.items, 'dateCreated', viewAll));
        else {
          sentRequestLength && sentRequestLength(null);
        }
      })
      .catch((err) => {
        sentRequestLength && sentRequestLength(null);
      });
    getSentRequest(userId, token)
      .then((res) => {
        if (res?.items?.length) {
          setSentRequest(sortArray(res?.items, 'dateCreated', true));
          sentRequestLength && sentRequestLength(res?.items.length);
        } else {
          sentRequestLength && sentRequestLength(null);
        }
      })
      .catch((err) => {
        sentRequestLength && sentRequestLength(null);
      });
    getPendingRecommendations(userId, token)
      .then((res) => {
        if (res?.items?.length) setPendingRequest(sortArray(res?.items, 'dateCreated', true));
        else {
          sentRequestLength && sentRequestLength(null);
          setPendingRequest([]);
        }
      })
      .catch((err) => {
        sentRequestLength(null);
      });
  };

  useEffect(() => {
    switch (tab) {
      case recieved:
        setRecommendation(viewAll ? recievedRequest : recievedRequest.slice(0, 1));
        break;
      case pending:
        setRecommendation(viewAll ? pendingRequest : pendingRequest.slice(0, 1));
        break;
      case sent:
        setRecommendation(viewAll ? sentRequest : sentRequest.slice(0, 1));
        break;
      default:
        setRecommendation(viewAll ? recievedRequest : recievedRequest.slice(0, 1));
        break;
    }
  }, [tab, sentRequest.length, pendingRequest.length, recievedRequest.length]);

  useEffect(() => {
    if (!viewAll) {
      if (contentExpended) {
        if (tab == recieved) setRecommendation(recievedRequest);
        if (tab == sent) setRecommendation(sentRequest);
        if (tab == pending) setRecommendation(pendingRequest);
      } else {
        if (tab == recieved) setRecommendation(recievedRequest.slice(0, 1));
        if (tab == sent) setRecommendation(sentRequest.slice(0, 1));
        if (tab == pending) setRecommendation(pendingRequest.slice(0, 1));
      }
    }
  }, [contentExpended]);

  const value = tab == recieved ? recieved : tab == pending ? pending : sent;
  const scrollStyle = { marginBottom: tab == pending ? 0 : viewAll ? 80 : 10, paddingBottom: viewAll && (tab == pending) ? 0 : 0 };

  const selectedTabStyle = [styles.recievedBtn, isDarkMode && { backgroundColor: Colors.activeStateDark }]
  const unSelected = [styles.sendBtn, isDarkMode && { backgroundColor: viewAll ? theme.backgroundColor : theme.bgDark, borderColor: theme.bgLightDark }]
  const selectedTextStyle = [styles.recieve, isDarkMode && { color: 'black' }]
  const unSelectedTextStyle = [styles.send, isDarkMode && { color: 'white' }]
  const selectedCount = [styles.light, isDarkMode && { backgroundColor: 'black' }]
  const unSelectedCount = [styles.dark, isDarkMode && { backgroundColor: Colors.activeStateDark }]
  const selectedCountText = [styles.countText, !isDarkMode && { color: 'black' }]
  const UnselectedCountText = [styles.countText, isDarkMode && { color: 'black' }]

  return (
    <View style={[scrollStyle, style, isDarkMode && { backgroundColor: viewAll ? theme.backgroundColor : theme.bgDark, }, { flex: 1 }]}>
      <View style={styles.tabsView}>
        <Pressable
          onPress={handleTab(recieved)}
          style={tab == recieved ? selectedTabStyle : unSelected}
        >
          <TextView style={tab == recieved ? selectedTextStyle : unSelectedTextStyle} text={recieved} />
          <View style={tab == recieved ? selectedCount : unSelectedCount}>
            <TextView
              style={[tab != recieved ? UnselectedCountText : selectedCountText, { bottom: (I18nManager.isRTL ? (Platform.OS === "android" ? 3 : 2) : (!I18nManager.isRTL ? 1 : 0)) }]}
              text={convertNumbersToArabicNumerals(recievedRequest?.length || '0')}
            />
          </View>
        </Pressable>

        <Pressable
          onPress={handleTab(sent)}
          style={tab == sent ? selectedTabStyle : unSelected}
        >
          <TextView style={tab == sent ? selectedTextStyle : unSelectedTextStyle} text={sent} />
          <View style={tab == sent ? selectedCount : unSelectedCount}>
            <TextView
              style={[tab != sent ? UnselectedCountText : selectedCountText, { bottom: (I18nManager.isRTL ? (Platform.OS === "android" ? 3 : 1) : (!I18nManager.isRTL ? 1 : 0)) }]}
              text={convertNumbersToArabicNumerals(sentRequest?.length || '0')}
            />
          </View>
        </Pressable>
        <Pressable
          onPress={handleTab(pending)}
          style={tab == pending ? selectedTabStyle : unSelected}
        >
          <TextView style={tab == pending ? selectedTextStyle : unSelectedTextStyle} text={pending} />
          <View style={tab == pending ? selectedCount : unSelectedCount}>
            <TextView
              style={[tab != pending ? UnselectedCountText : selectedCountText, { bottom: (I18nManager.isRTL ? (Platform.OS === "android" ? 3 : 2) : (!I18nManager.isRTL ? 1 : 0)) }]}
              text={convertNumbersToArabicNumerals(pendingRequest?.length || '0')}
            />
          </View>
        </Pressable>
      </View>
      {(tabLoading || isLoading) ? <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', height: HEIGHT }}>
        <ActivityIndicator color={isDarkMode ? 'white' : 'black'} size={"large"} />
      </View> : null}
      {/* {!recommendation?.length
        &&
        !isLoading && !tabLoading ? (
        <TextView style={[styles.noInfo, { color: theme.textColor }]}
          text={`No ${value} Recommendation Available`} />
      ) : null} */}


      {!recommendation?.length
        &&
        !isLoading && !tabLoading &&
        <TalentProfileEmptyState
          hideButton
        // onPress={customOnPress}
        />}

      {(!isLoading && !tabLoading) && (
        <ScrollView style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}>
          {recommendation?.map((res, index) => (
            <RecommendationItem
              key={res.id}
              getAllRecommendations={getAllRecommendations}
              viewAll={viewAll}
              tab={tab}
              list={recommendation}
              index={index}
              res={res}
            />
          ))}
        </ScrollView>)}
      {
        !viewAll &&
          (tab == recieved ?
            recievedRequest?.length > 1 :
            tab == sent ?
              sentRequest.length > 1 : pendingRequest?.length > 1) ? (
          <ShowMore style={styles.showMore}
            onExpend={onExpend}
            isExpend={contentExpended} />
        ) : null
      }
    </View >
  );
};

export default Recommendations;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 20,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
  },
  detailContainer: {
    marginHorizontal: 10,
    width: '80%',
  },
  borderCircle: {
    borderColor: '#DBDBDB',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
  },
  desc: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginTop: 5,
    lineHeight: 22,
  },
  dept: {
    fontFamily: fonts.light,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 15,
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    marginTop: 10,
    opacity: 0.7,
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
    marginTop: 10,
  },
  dot: {
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: 'black',
    marginHorizontal: 10,
    opacity: 0.5,
  },
  role: {
    textTransform: 'uppercase',
    fontSize: fontSize.small,
    fontFamily: fonts.regular,
  },
  rowAlign: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  details: {
    marginTop: 10,
    color: Colors.black,
    opacity: 0.8,
    lineHeight: 22,
  },
  askFor: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    padding: 10,
  },
  askForBtn: {
    marginTop: 20,
    borderColor: 'black',
    borderWidth: 1,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 320,
  },
  tabsView: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  recievedBtn: {
    borderWidth: 1,
    backgroundColor: 'black',
    marginEnd: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    height: 35,
    flexDirection: 'row',
    paddingHorizontal: 10,
    borderColor: 'rgba(187, 187, 187, 1)',
  },
  sendBtn: {
    borderWidth: 1,
    borderColor: 'rgba(187, 187, 187, 1)',
    marginEnd: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    height: 35,
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  recieve: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.large : fontSize.xxmini,
    textTransform: 'capitalize',
  },
  countText: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: fontSize.custom(Platform.OS == 'android' ? 10 : 9),
    marginTop: Platform.OS == 'android' ? .5 : 1
  },
  send: {
    color: 'black',
    fontFamily: fonts.medium,
    fontSize: fontSize.xxmini,
    textTransform: 'capitalize'
  },
  showMore: {
    marginHorizontal: 65,
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: isPad ? fontSize.semiMedium : fontSize.xxmini,
    textAlign: 'left'
  },
  dark: {
    backgroundColor: Colors.black,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    marginStart: 10,
  },
  light: {
    backgroundColor: 'white',
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    marginStart: 10,
  },
});
