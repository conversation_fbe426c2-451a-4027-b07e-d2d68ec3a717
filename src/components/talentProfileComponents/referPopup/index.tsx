import React, { useEffect, useState } from 'react';
import { Platform, StyleSheet, View, BackHandler } from 'react-native';
import ListItem from '../../AddToListBottomSheet/ListItem';
import AssetsImages from '../../../theme/AssetsImages';
import Colors from '../../../theme/Colors';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../totara/reducers';
import { setBottomSheetVisible } from '../../../totara/reducers/referralHistoryBottomSheetReducer';
import { navigate } from '../../../navigation/navigationService';
import BottomSheet from '../../../components/bottomSheet';
import fonts from '@utils/fonts';
import fontSize, { isPad } from '@utils/fontSize';

const ReferBottomSheetPopup = ({ history = true }) => {
  const dispatch = useDispatch();
  const { visible } = useSelector((state: RootState) => state.referralHistoryBottomSheetReducer);

  const onCloses = () => setBottomSheetVisible(dispatch, false);

  const onPress = () => {
    if (history) {
      setBottomSheetVisible(dispatch, false);

      setTimeout(() => navigate('ReferHistory'), 50);
    }
  };

  return (
    <BottomSheet
      backdropColor={'rgba(0,0,0,0.2)'}
      customStyles={{ justifyContent: 'flex-start' }}
      visiblity={visible}
      setVisibility={() => setBottomSheetVisible(dispatch, false)}
      children={
        <View style={styles.body}>
          <ListItem image={AssetsImages.referral} text={'Referral History'} onPress={onPress} />
        </View>
      }
    />
  );
};

export default ReferBottomSheetPopup;

const styles = StyleSheet.create({
  line: { borderBottomWidth: 0.5, borderBottomColor: Colors.grayline },
  bottomSheet: { paddingHorizontal: 0 },
  textStyle: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xxlarge : fontSize.semiMedium1,
    color: Colors.black,
  },
  iconStyle: {
    width: isPad ? 35 : 24,
    height: isPad ? 35 : 24,
    resizeMode: 'contain',
  },
});
