import React, { useEffect, useState } from 'react';
import { I18nManager, Image, Platform, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useSelector } from 'react-redux';
import { ShowMore, TextView } from '../../../../src/components';
import { AssetsImages, Colors } from '../../../../src/theme';
import { getEndorsement } from '../../../api/server_requests';
import { getUrl } from '../../../utils/constants';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import Icons, { IconsType } from '../../../../src/theme/Icons';
import { LabelConfig } from '../../../theme/labelConfig';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { useTheme } from '@theme/ThemeProvider';
import { commonStyle } from '@utils';

const { showMore, showLess } = LabelConfig.talentProfile.about;
const Skills = ({ onPress }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const skills = talentProfile?.skills ? talentProfile?.skills?.split(',') : []
  const [isExpend, setIsExpend] = useState(false);
  const onExpend = () => setIsExpend(!isExpend);
  const [skillArray, setSkillArray] = useState([]);
  const { isDarkMode, theme } = useTheme();
  const backgroundColor = { backgroundColor: isDarkMode ? 'rgba(109, 163, 215, 0.1)' : 'rgba(242, 246, 249, 1)', }
  const color = { color: theme.textColor };
  const [tab, setTab] = useState('skills');
  const verifiedSkillsLabel = 'verifiedSkills'
  const skillsTabLabel = 'skills';
  const skillsJson = talentProfile?.skillsJson || "[]";
  const parsedSkills = JSON.parse(skillsJson)
  const skillsToSelect = tab == skillsTabLabel ? skills : parsedSkills

  const verifiedSkills = skillsToSelect?.map(skill => ({
    "skillName": skill?.skillName ? skill?.skillName : skill,
    "level": skill?.level || '',
    "talentId": skill?.talentId || '',
    "isVerified": skill?.isVerified || false
  }));

  useEffect(() => {
    if (isExpend) {
      setSkillArray(verifiedSkills);
    } else {
      setSkillArray(verifiedSkills?.slice(0, 4));
    }
  }, [isExpend]);

  const handleTab = (tab) => () => {
    setTab(tab);
    onPress(tab)
  };

  useEffect(() => {

    if (tab == skillsTabLabel) {
      setSkillArray(verifiedSkills.slice(0, 4))
    }
    else {
      setSkillArray(verifiedSkills.filter(item => item.isVerified).slice(0, 4))
    }
    setIsExpend(false)


  }, [tab, talentProfile?.skills])

  const selectedTab = [styles.recievedBtn, isDarkMode && { backgroundColor: Colors.activeStateDark }]
  const unSelected = [styles.sendBtn, isDarkMode && { backgroundColor: theme.bgDark, borderColor: theme.bgLightDark }]
  const selectedTextStyle = [styles.recieve, isDarkMode && { color: 'black' }]
  const unSelectedTextStyle = [styles.send, isDarkMode && { color: 'white' }]
  const selectedCount = [styles.light, isDarkMode && { backgroundColor: 'black' }]
  const unSelectedCount = [styles.dark, isDarkMode && { backgroundColor: Colors.activeStateDark }]
  const selectedCountText = [styles.countText, !isDarkMode && { color: 'black' }]
  const UnselectedCountText = [styles.countText, isDarkMode && { color: 'black' }]
  return (
    <View>
      <View style={styles.tabsView}>
        <Pressable
          onPress={handleTab(skillsTabLabel)}
          style={tab == skillsTabLabel ? selectedTab : unSelected}
        >
          <TextView style={tab == skillsTabLabel ? selectedTextStyle : unSelectedTextStyle} text={skillsTabLabel} />
          <View style={tab == skillsTabLabel ? selectedCount : unSelectedCount}>
            <TextView
              style={tab != skillsTabLabel ? UnselectedCountText : selectedCountText}
              text={skills?.length}
            />
          </View>
        </Pressable>

        <Pressable
          onPress={handleTab(verifiedSkillsLabel)}
          style={tab == verifiedSkillsLabel ? selectedTab : unSelected}
        >
          <TextView style={tab == verifiedSkillsLabel ? selectedTextStyle : unSelectedTextStyle} text={'Verified Skills'} />
          <View style={tab == verifiedSkillsLabel ? selectedCount :
            unSelectedCount}>
            <TextView
              style={tab != verifiedSkillsLabel ? UnselectedCountText : selectedCountText}
              text={parsedSkills?.filter(item => item?.isVerified)?.length}
            />
          </View>
        </Pressable>
      </View>
      {!skillArray?.length ? (
        <TextView style={[styles.noInfo, { color: theme.textColor }]} text="No information available" />
      ) : null}
      <View style={styles.container}>
        {skillArray?.map((res) => (
          <Pressable style={[styles.item, backgroundColor]} key={res?.skillName}>
            <View style={commonStyle.rowAlign}>
              <TextView style={[styles.itemTile, color]} type="h5" text={res?.skillName} />
              {res?.isVerified ? <Image source={AssetsImages.verifiedLink} style={styles.verifiedLink} /> : null}
            </View>
            {tab != verifiedSkillsLabel ?
              <RenderEndorsement
                skill={res?.skillName} /> : null}
          </Pressable>
        ))}
      </View>
      {skillArray?.length >= 4 && (
        <ShowMore
          isExpend={isExpend}
          style={styles.viewMoreContainer}
          onExpend={onExpend}
        />
      )}
    </View>
  );
};

export default React.memo(Skills);

const RenderEndorsement = React.memo(({ skill }) => {
  const profile = useSelector((state) => state?.getWFMProfile);
  const token = profile?.response?.liferayaccesstoken;
  const navigation = useNavigation();
  const [endorsement, setEndorement] = useState(null);
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const [avatarList, setAvatarList] = useState([]);
  const isFocused = useIsFocused();
  const { isDarkMode, theme } = useTheme();
  const backgroundColor = { backgroundColor: isDarkMode ? theme.backgroundColor : 'rgba(242, 246, 249, 1)', }
  const color = { color: theme.textColor };
  const tintColor = { tintColor: theme.tintColor };

  useEffect(() => {
    getEndorsement(skill, profile?.response?.id, token).then((response) => {
      setEndorement(response?.endorsedByprofile);
      setAvatarList(response?.endorsedByprofile?.filter((item) => item?.pubEndorsed));
    });
  }, [talentProfile, isFocused]);

  const navigateToEndoresment = () => navigation.navigate('Endorsement', skill);

  return (
    <Pressable
      onPress={navigateToEndoresment}
      style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 5 }}
    >
      <View style={{ flexDirection: 'row' }}>
        {/* {avatarList?.length ? avatarList?.slice(0, 2)?.map((item, index) => {
          return <RenderAvatar
            content={item?.endorserProfile?.profile?.picture}
            token={token}
            index={index}
          />
        }) : null} */}
        <Image style={[styles.endoresmentProfiles, tintColor]} source={AssetsImages.endoresmentProfiles} />
      </View>
      <View style={commonStyle.rowAlign}>
        <TextView style={[styles.endorsement, color]} text={(I18nManager.isRTL ? " " : '') + (avatarList?.length || '0') + (!I18nManager.isRTL ? " " : '')} />
        <TextView style={[styles.endorsement, color]} text={'Endorsement'} />

      </View>
    </Pressable>
  );
});

export const RenderAvatar = React.memo(({ content, token, index, customStyle }) => {
  const [image, setImage] = useState('');

  useEffect(() => {
    if (content) {
      getUrl(content, token).then((res) => {
        setImage(res);
      });
    }
  }, []);

  return (
    <FastImage
      style={[
        styles.mentorImage,
        {
          marginLeft: index === 0 ? 0 : -8,
          zIndex: index * 1000,
          marginRight: 5,
        },
        customStyle,
      ]}
      source={image ? { uri: image } : AssetsImages.soulvite}
    />
  );
});

const styles = StyleSheet.create({
  skillView: {},
  skillName: {},
  item: {
    padding: 5,
    borderRadius: 5,
    marginRight: 10,
    marginTop: 15,
    paddingHorizontal: isPad ? 16 : 10,
    backgroundColor: 'rgba(242, 246, 249, 1)',
  },
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  itemTile: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.large : fontSize.medium,
    marginVertical: isPad ? 8 : 5,
    textTransform: 'capitalize',
  },
  row: {
    // alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageStack: {
    flexDirection: 'row',
    marginLeft: 10,
  },
  mentorImage: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    borderRadius: 20,
    backgroundColor: 'black',
  },
  endorsement: {
    fontSize: isPad ? fontSize.small : fontSize.mini,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'left'
  },
  checkSkills: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  addedSkillsCheck: {
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  maxSkills: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: 'black',
    marginStart: 5,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginHorizontal: 5
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
    textAlign: 'left'

  },
  endoresmentProfiles: {
    width: 15,
    height: 15,
    marginRight: 5,
  },
  tabsView: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  recievedBtn: {
    borderWidth: 1,
    backgroundColor: 'black',
    marginRight: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    height: 35,
    flexDirection: 'row',
    paddingHorizontal: 10,
    borderColor: 'rgba(187, 187, 187, 1)',
  },
  sendBtn: {
    borderWidth: 1,
    borderColor: 'rgba(187, 187, 187, 1)',
    marginRight: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    height: 35,
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  recieve: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.large : fontSize.xxmini,
    textTransform: 'capitalize',
    marginTop: Platform.OS == 'android' ? -2 : 0
  },
  send: {
    color: 'black',
    fontFamily: fonts.medium,
    fontSize: fontSize.xxmini,
    textTransform: 'capitalize',
    marginTop: Platform.OS == 'android' ? -2 : 0
  },
  countText: {
    color: 'white',
    fontFamily: fonts.medium,
    fontSize: fontSize.custom(Platform.OS == 'android' ? 10 : 8.5),
    marginTop: Platform.OS == 'android' ? -1.5 : -.5
  },
  dark: {
    backgroundColor: Colors.black,
    width: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    marginStart: 10,
  },
  light: {
    backgroundColor: 'white',
    width: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    marginStart: 10,
  },
  verifiedLink: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginLeft: 10
  }
});