import { getProfileData } from '@api/totaraApis';
import { useIsFocused } from '@react-navigation/native';
import useGetLearningPrograms from '@screens/myLearningPathways/api/useGetLearningPrograms';
import { useTheme } from '@theme/ThemeProvider';
import { useSession } from '@totara/core';
import React, { useEffect, useState } from 'react';
import { FlatList, Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { LearningPathWayItem, Seperator, TextView } from '../..';
import { ItemSeparator } from '../../../screens/myLearningPathways/RecommendedLearningScreen';
import { Colors } from '../../../theme';
import Icons, { IconsType } from '../../../theme/Icons';
import { LabelConfig } from '../../../theme/labelConfig';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';


const { showMore, showLess } = LabelConfig.talentProfile.about;
const keyExtractor = (item, index) => index.toString();

const SkillsToLearn = () => {
  const [isExpend, setIsExpend] = useState(false);
  const onExpend = () => setIsExpend(!isExpend);
  const [skillArray, setSkillArray] = useState([]);
  const { theme } = useTheme();
  const { recommendedPathways } = useGetLearningPrograms();
  const { apiToken } = useSession();
  const [skillsIwantToLearn, setSkillsToLearn] = useState(null)
  const isFocused = useIsFocused();

  useEffect(() => {
    getProfile()
  }, [isFocused])

  const getProfile = async () => {
    const profile = await getProfileData({ apiToken })
    const skillsList = profile?.skills.split(',')
    if (skillsList?.length) {
      setSkillsToLearn(skillsList?.slice(0, 4))
      setSkillArray(skillsList)
    }
  }
  useEffect(() => {
    if (isExpend) {
      setSkillsToLearn(skillArray);
    } else {
      setSkillsToLearn(skillArray?.slice(0, 4));
    }
  }, [isExpend]);

  return (
    <View>
      <View style={styles.container}>
        {!skillsIwantToLearn?.length ? (
          <TextView
            style={[styles.noInfo, { color: theme.textColor }]}
            text="No information available"
          />
        ) : null}
        <TextView
          style={[styles.personalizeSkills, { color: theme.textColor }]}
          text="We will use these skills to personalize the learning recommendation on your homepage"
        />

        {skillsIwantToLearn?.map((res) => (
          <Pressable style={[styles.item]} key={res}>
            <TextView
              style={[styles.itemTile, { color: theme.textColor }]}
              type="h5"
              text={res}
            />
          </Pressable>
        ))}
      </View>

      {skillArray?.length > 4 && (
        <TouchableOpacity onPress={onExpend} style={styles.viewMoreContainer}>
          <TextView style={styles.ViewMore} text={!isExpend ? showMore : showLess} />
          <Icons type={IconsType.FontAwesome5} name={isExpend ? 'chevron-up' : 'chevron-down'} />
        </TouchableOpacity>
      )}

      <Seperator mv={20} />

      {recommendedPathways?.list?.length ?
        <TextView
          style={[styles.suggestedCourses, { color: theme.textColor }]}
          text="Suggested courses for you"
        /> : null}

      <FlatList
        style={styles.flatlistStyle}
        data={recommendedPathways?.list}
        renderItem={suggestedCourses}
        ItemSeparatorComponent={ItemSeparator}
        keyExtractor={keyExtractor}
      />
    </View>
  );
};

export default React.memo(SkillsToLearn);

export const suggestedCourses = ({ item }) => {
  const lessons = item?.total_lesson > 1 ? ' Lessons' : ' Lesson';
  return (
    <LearningPathWayItem
      itemStyle={styles.itemStyle}
      showRating
      showMore
      isDetail
      showPeopleJoined={item?.user_completed > 0}
      peopleJoined={item?.user_completed}
      name={item?.title}
      months={item?.months || '0'}
      courses={item?.total_course}
      overDue={item?.due_date}
      provider={item?.curator_name ? item?.curator_name : 'Gov Academy'}
      providerLogo={item?.curator_logo}
      progress={item?.progress || '0'}
      image={item?.image}
      rating={item.rating}
      totalContent={item?.type == 'course' ? (item?.total_lesson + lessons) : item?.total_course + ' Courses'}
      duration={item.duration}
      type={item.type}
      showFav
      item={{
        ...item,
        courseid: item?.id
      }}
    />
  )
}

const styles = StyleSheet.create({
  item: {
    padding: 5,
    borderRadius: 5,
    marginRight: 10,
    marginTop: 15,
    paddingHorizontal: isPad ? 16 : 10,
    backgroundColor: Colors.skillContainer,
  },
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  itemTile: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.large : fontSize.medium,
    marginVertical: isPad ? 8 : 5,
    textTransform: 'capitalize',
  },
  row: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  endorsement: {
    fontSize: isPad ? fontSize.small : fontSize.mini,
    color: Colors.black,
    opacity: 0.7,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
    textAlign: 'left',
  },
  personalizeSkills: {
    marginVertical: 5,
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'rgba(86, 86, 86, 1)',
    fontSize: fontSize.small,
    paddingEnd: 10,
    textAlign: 'left'
  },
  suggestedCourses: {
    fontFamily: fonts.medium,
    textTransform: 'uppercase',
    fontSize: fontSize.small,
    marginBottom: 20,
    textAlign: 'left',
  },
  flatlistStyle: {
    marginBottom: 30,
  },
  itemStyle: {
    width: '100%',
    alignSelf: 'center',
    marginVertical: 0,
    marginRight: 0,
    paddingHorizontal: 0,
  },
});
