import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  I18nManager,
  Image,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { ConfirmationModal, ShowMore, TalentProfileEmptyState, TextView } from '../../../../src/components';
import { AssetsImages, Colors, Icons, Images } from '../../../../src/theme';
import { IconsType } from '../../../../src/theme/Icons';
import { LabelConfig } from '../../../../src/theme/labelConfig';
import fontSize, { isPad } from '../../../utils/fontSize';
import fonts from '../../../utils/fonts';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { getUrl } from '../../../utils/constants';
import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { setActionType, updateProfile } from '../../../totara/actions/getWFMProfileAction';
import { RenderDocument } from '../education';
import { useTheme } from '@theme/ThemeProvider';
import { formatDateBasedOnLocale } from '@utils/AppUtils';
import { useTranslation } from 'react-i18next';
const { showMore, showLess } = LabelConfig.talentProfile.about;

type Props = {
  viewAll?: boolean;
  viewEdit?: boolean;
  customStyle?: Object;
};

const Training = ({ viewAll, customStyle, viewEdit }: Props) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const userProfile = useSelector((state) => state?.getWFMProfile?.response);

  const [isExpend, setIsExpend] = useState(false);
  const [training, setTraining] = useState([]);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [item, setItem] = useState(null);
  const toggleModal = () => setVisibility(!visibility);
  const [visibility, setVisibility] = useState(false);
  const stateLoading = useSelector((state) => state?.getWFMProfile.isLoading);
  const { theme } = useTheme();

  useEffect(() => {
    if (talentProfile?.training) {
      const array = talentProfile?.training.sort((a, b) => {
        return new Date(b.startDate) - new Date(a.startDate);
      });
      setTraining(viewAll ? array : array?.slice(0, 1));
      setIsExpend(false);
    }
  }, [talentProfile, talentProfile?.training]);

  useEffect(() => {
    if (!viewAll) {
      if (isExpend) {
        const array = talentProfile?.training?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setTraining(array);
      } else {
        const array = talentProfile?.education?.sort((a, b) => {
          return new Date(b.startDate) - new Date(a.startDate);
        });
        setTraining(talentProfile?.training?.slice(0, 1));
      }
    }
  }, [isExpend]);

  const onExpend = () => setIsExpend(!isExpend);
  const onEdit = (item) => () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Edit training',
      type: 'Edit_Training',
      item,
    });
    dispatch(setActionType('edit'));
  };

  const scrollStyle = { marginBottom: viewAll ? 140 : 10 };

  const onDelete = () => {
    const filteredArray = talentProfile?.training.filter((element) => element.id != item.id);
    const fields = {
      training: filteredArray,
    };
    dispatch(updateProfile(talentProfile.id, fields, userProfile?.liferayaccesstoken, navigation));
    toggleModal();
  };

  const openModal = (item) => () => {
    toggleModal();
    setItem(item);
    dispatch(setActionType('delete'));
  };
  const onPress = () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Add training and certificate',
      type: 'Edit_Training',
    });
    dispatch(setActionType('add'));
  };


  const color = { color: theme.textColor }
  const { t } = useTranslation();
  return (
    <View style={customStyle}>


      <ScrollView showsVerticalScrollIndicator={false} style={scrollStyle}>
        {!talentProfile?.training?.length &&
          <TalentProfileEmptyState
            onPress={onPress}
          />}

        {training?.map((item, index) => {
          return (
            <View key={item.id}>
              <View style={styles.container}>
                <Image source={AssetsImages.training} style={styles.borderCircle} />

                <View style={styles.detailContainer}>
                  <View style={styles.rowEducation}>
                    <View style={{ flex: 1 }}>
                      <TextView style={[styles.heading, color]} text={item.trainingtitle} />
                      <TextView style={[styles.desc, color]} text={item.issuingAuthority} />
                    </View>
                    {viewAll && (
                      <View style={{ flexDirection: 'row' }}>
                        <Pressable disabled={stateLoading} onPress={onEdit(item)}>
                          <Image source={AssetsImages.editBlack} style={styles.actionBtn} />
                        </Pressable>
                        <Pressable
                          disabled={stateLoading}
                          onPress={openModal(item)}
                        // onPress={onDelete(item)}
                        >
                          <Image source={AssetsImages.deleteBlack} style={styles.actionBtn} />
                        </Pressable>
                      </View>
                    )}
                  </View>
                  <View style={styles.duration}>
                    <TextView
                      style={[styles.year, color]}
                      text={formatDateBasedOnLocale(item.startDate)}
                    />
                    {item.expireOn ? (
                      <>
                        <View style={styles.dot} />
                        <TextView
                          style={[styles.year, color]}
                          text={'Expires on: ' + moment(item.expireOn).format('DD-MM-YYYY')}
                        />
                      </>
                    ) : null}
                  </View>
                  {item.trainingId && item.trainingId?.length < 20 ? (
                    <>
                      <TextView
                        style={[styles.year, { marginTop: 10 }, color]}
                        text={t('ID') + ': ' + item.trainingId}
                      />
                    </>
                  ) : null}
                  {item.location ? <TextView
                    style={[styles.year, { marginTop: 10 }, color]}
                    text={item.location}
                  /> : null}
                  {item.trainingAttachment && !viewAll ? (
                    <RenderDocument fileName={item.trainingAttachment} />
                  ) : null}
                </View>
              </View>
              {(isExpend || viewAll) && training?.length - 1 != index && (
                <RenderBorder />
              )}
            </View>
          );
        })}

        {!viewAll && talentProfile?.training?.length > 1 && (
          <ShowMore
            isExpend={isExpend}
            onExpend={onExpend} />
        )}

        <ConfirmationModal
          title={item?.trainingtitle}
          type="training"
          onDelete={onDelete}
          setVisibility={toggleModal}
          visibility={visibility}
        />
      </ScrollView>

    </View>
  );
};

export default React.memo(Training);


export const RenderBorder = () => {
  const { theme } = useTheme()
  return (
    <View style={{
      width: '100%',
      height: 1,
      marginVertical: 15,
      backgroundColor: theme.borderBackground,
    }}>

    </View>

  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    borderBottomColor: 'rgba(0, 0, 0, 1)',
  },
  actionBtn: {
    width: isPad ? 35 : 30,
    height: isPad ? 35 : 30,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  border: {
    width: '100%',
    height: 1,
    marginVertical: 15,
    backgroundColor: 'black',
    opacity: 0.1,
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: 20,
    height: 20,
  },
  detailContainer: {
    marginStart: 10,
    flex: 1,
  },
  borderCircle: {
    borderColor: '#DBDBDB',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    textAlign: 'left'
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    marginVertical: (I18nManager.isRTL && Platform.OS == 'ios') ? 0 : 10,
    textAlign: 'left'
  },
  dept: {
    fontFamily: fonts.light,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    marginTop: 15,
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    opacity: 0.7,
    textAlign: 'left',
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
    textAlign: 'left'
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    marginHorizontal: 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: 120,
    height: 80,
    marginVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 10,
  },
  dot: {
    width: 2.5,
    height: 2.5,
    backgroundColor: 'rgba(147, 147, 147, 1)',
    borderRadius: 2.5,
    marginHorizontal: 5,
  },
  rowEducation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
    textAlign: 'left'
  },
});
