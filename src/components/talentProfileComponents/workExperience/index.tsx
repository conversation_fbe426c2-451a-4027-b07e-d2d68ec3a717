import React, { useEffect, useState } from 'react';
import {
  I18nManager,
  Image,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@theme/ThemeProvider';
import { formatDateBasedOnLocale } from '@utils/AppUtils';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import {
  ConfirmationModal,
  ShowMore,
  TalentProfileEmptyState,
  TextView,
} from '../../../../src/components';
import { AssetsImages, Colors } from '../../../../src/theme';
import {
  setActionType,
  setLoading,
  updateProfile,
} from '../../../totara/actions/getWFMProfileAction';
import fonts from '../../../utils/fonts';
import fontSize, { isPad } from '../../../utils/fontSize';

const WorkExperience = ({ viewAll, customStyle }) => {
  const talentProfile = useSelector((state) => state?.getWFMProfile?.talentProfile);
  const [visibility, setVisibility] = useState(false);
  const [isExpend, setIsExpend] = useState(false);
  const [workExperience, setWorkExp] = useState([]);
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [item, setItem] = useState(null);
  const dispatch = useDispatch();
  const toggleModal = () => setVisibility(!visibility);
  const stateLoading = useSelector((state) => state?.getWFMProfile.isLoading);
  const token = useSelector((state) => state.getWFMProfile.response?.liferayaccesstoken);

  useEffect(() => {
    if (talentProfile?.workExperience) {
      const array = talentProfile?.workExperience.sort((a, b) => {
        return new Date(b.workExperienceStartDate) - new Date(a.workExperienceStartDate);
      });
      setIsExpend(false);
      setWorkExp(viewAll ? array : array?.slice(0, 1));
    }
  }, [talentProfile, talentProfile?.workExperience]);

  useEffect(() => {
    if (!viewAll) {
      if (isExpend) {
        const array = talentProfile?.workExperience?.sort((a, b) => {
          return new Date(b.workExperienceStartDate) - new Date(a.workExperienceStartDate);
        });
        setWorkExp(array);
      } else {
        const array = talentProfile?.workExperience?.sort((a, b) => {
          return new Date(b.workExperienceStartDate) - new Date(a.workExperienceStartDate);
        });
        setWorkExp(array?.slice(0, 1));
      }
    }
  }, [isExpend]);

  const onExpend = () => setIsExpend(!isExpend);

  const onEdit = (item) => () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Add / Edit Work Experience',
      type: 'Edit_Work_Experience',
      item,
      edit: true,
    });
    dispatch(setActionType('edit'));
  };

  const scrollStyle = { marginBottom: viewAll ? 140 : 10 };

  const onDelete = () => {
    dispatch(setLoading(true));
    toggleModal();
    const indexToRemove = workExperience.findIndex(({ id }) => id === item?.id);
    const updatedWorkExperience = workExperience.filter((_w, index) => index !== indexToRemove);
    dispatch(updateProfile(talentProfile.id, { workExperience: updatedWorkExperience }, token));
  };

  const openModal = (item) => () => {
    toggleModal();
    setItem(item);
    dispatch(setActionType('delete'));
  };

  const color = { color: theme.textColor };
  const { t } = useTranslation();

  const customOnPress = () => {
    navigation?.navigate('EditFormTelentProfile', {
      title: 'Add Work Experience',
      type: 'Edit_Work_Experience',
    });
  };

  return (
    <View style={customStyle}>
      <ScrollView showsVerticalScrollIndicator={false} style={scrollStyle}>
        {!talentProfile?.workExperience?.length && (
          <TalentProfileEmptyState onPress={customOnPress} />
        )}

        {workExperience?.map((item, index) => {
          return (
            <View key={item.id}>
              <View style={[styles.container]}>
                <Pressable style={styles.borderCircle}>
                  <Image source={AssetsImages.book} style={styles.companyImage} />
                </Pressable>

                <View style={styles.detailContainer}>
                  <View style={styles.rowEducation}>
                    <View style={{ flex: 1 }}>
                      <TextView style={[styles.heading, color]} text={item?.jobTitle} />
                      <TextView
                        style={[styles.desc, color]}
                        text={item.industry + ' | ' + item.organizationName}
                      />
                      <TextView style={[styles.dept, color]} text={item.department} />
                    </View>
                    {viewAll && (
                      <View style={{ flexDirection: 'row' }}>
                        <Pressable disabled={stateLoading} onPress={onEdit(item)}>
                          <Image source={AssetsImages.editBlack} style={styles.actionBtn} />
                        </Pressable>
                        <Pressable disabled={stateLoading} onPress={openModal(item)}>
                          <Image source={AssetsImages.deleteBlack} style={styles.actionBtn} />
                        </Pressable>
                      </View>
                    )}
                  </View>
                  <View style={styles.duration}>
                    <TextView
                      style={[styles.year, color]}
                      text={
                        formatDateBasedOnLocale(item?.workExperienceStartDate) +
                        ' - ' +
                        (item?.currentlyWorking == 'true'
                          ? t('Present')
                          : formatDateBasedOnLocale(item?.workExperienceEndDate))
                      }
                    />
                    {item?.location && item?.location?.length < 20 ? (
                      <>
                        <View style={[styles.dot, { marginTop: 10 }]} />
                        <TextView style={[styles.year, color]} text={item.location} />
                      </>
                    ) : null}
                  </View>

                  {item?.location?.length > 20 ? (
                    <>
                      <TextView style={[styles.year, color]} text={item.location} />
                    </>
                  ) : null}
                  {item.responsibilities ? (
                    <TextView style={[styles.description, color]} text={item.responsibilities} />
                  ) : null}
                </View>
              </View>
              {(isExpend || viewAll) && workExperience?.length - 1 != index && (
                <View style={[styles.border, { backgroundColor: theme.borderBackground }]} />
              )}
            </View>
          );
        })}
      </ScrollView>

      {talentProfile?.workExperience?.length > 1 && !viewAll && (
        <ShowMore isExpend={isExpend} onExpend={onExpend} />
      )}

      <ConfirmationModal
        title={item?.jobTitle}
        type="experience"
        onDelete={onDelete}
        setVisibility={toggleModal}
        visibility={visibility}
      />
    </View>
  );
};

export default WorkExperience;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingTop: 20,
    borderBottomColor: 'rgba(0, 0, 0, 1)',
  },
  pdfImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: fontSize.small,
    textAlign: 'left',
  },
  actionBtn: {
    width: isPad ? 35 : 30,
    height: isPad ? 35 : 30,
    resizeMode: 'contain',
    marginLeft: 10,
  },
  border: {
    width: '100%',
    height: 1,
    marginVertical: 15,
    backgroundColor: 'black',
  },
  row: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: 10,
  },
  imageView: {
    flex: 1,
  },
  companyImage: {
    resizeMode: 'contain',
    width: isPad ? 30 : 20,
    height: isPad ? 30 : 20,
  },
  detailContainer: {
    marginStart: 10,
    flex: 1,
  },
  borderCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: isPad ? 55 : 40,
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    marginRight: 5,
    backgroundColor: '#E9E2DD',
  },
  heading: {
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.xlarge : fontSize.medium,
    color: Colors.black,
    textAlign: 'left',
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium1 : fontSize.medium,
    color: Colors.black,
    marginVertical: I18nManager.isRTL && Platform.OS == 'ios' ? 0 : 10,
    textAlign: 'left',
  },
  dept: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: '#1E1E1ECC',
    textAlign: 'left',
  },
  year: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.medium : fontSize.xmini,
    color: Colors.black,
    opacity: 0.7,
    marginTop: I18nManager.isRTL && Platform.OS == 'ios' ? 0 : 10,
  },
  description: {
    fontFamily: fonts.regular,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    color: Colors.black,
    marginTop: 15,
    textAlign: 'left',
  },
  detail: {
    fontFamily: fonts.regular,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginVertical: 15,
    lineHeight: 22,
    opacity: 0.8,
  },
  reporting: {
    fontFamily: fonts.medium,
    fontSize: fontSize.medium,
    color: Colors.black,
    marginTop: 15,
    lineHeight: 22,
  },
  viewMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginHorizontal: isPad ? 70 : 55,
  },
  ViewMore: {
    color: Colors.black,
    fontFamily: fonts.medium,
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    marginRight: 5,
  },
  mentorImage: {
    width: isPad ? 55 : 40,
    height: isPad ? 55 : 40,
    resizeMode: 'contain',
    borderRadius: 20,
  },
  imageStack: {
    flexDirection: 'row',
  },
  myMentees: {
    fontFamily: fonts.medium,
    fontSize: fontSize.large,
    color: 'black',
  },
  names: {
    fontSize: fontSize.medium,
    flexWrap: 'wrap',
    width: '70%',
    marginStart: 10,
    color: Colors.black,
  },
  certificate: {
    width: 120,
    height: 80,
    marginVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: Colors.borderGray,
    borderWidth: 0.5,
  },
  duration: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  dot: {
    width: 2.5,
    height: 2.5,
    backgroundColor: 'rgba(147, 147, 147, 1)',
    borderRadius: 2.5,
    marginHorizontal: 5,
  },
  rowEducation: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
});
