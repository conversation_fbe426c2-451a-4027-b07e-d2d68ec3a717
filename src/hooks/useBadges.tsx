import { useSession } from '../totara/core';
import { useEffect, useState } from 'react';
import { getBadges } from '../../src/api/totaraApis';
import { useIsFocused } from '@react-navigation/native';

const useBadges = () => {
  const { apiToken, core } = useSession();
  const [loading, setLoading] = useState(true);
  const [expendCriteria, setExpendCriteria] = useState(false);
  const [badges, setBadges] = useState<BadgesData>();
  const [selectedCriteria, setSelectedCriteria] = useState<Badge>();
  const isFocused = useIsFocused();

  useEffect(() => {
    (async () => {
      const response = await getBadges({ apiToken, userid: core?.user?.id }); // TODO: will remove later
      setLoading(false);
      if (response) setBadges(response);
    })();
  }, [isFocused]);

  return {
    loading,
    expendCriteria,
    badges,
    selectedCriteria,
    setSelectedCriteria,
    setExpendCriteria,
  };
};

export default useBadges;
