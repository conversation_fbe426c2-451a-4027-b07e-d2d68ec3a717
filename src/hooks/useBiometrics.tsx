/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { sendPublicKeyToServer, verifySignatureWithServer } from '@api/server_requests';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { store } from '@totara/store';
import { ASYNC_STORAGE_KEYS, BIOMETRICS_ERRORS } from '@utils/constants';
import crashReportLogger from '@utils/crashlyticsLogger';
import ReactNativeBiometrics, { BiometryTypes } from 'react-native-biometrics';
import uuid from 'react-native-uuid';

enum BiometricLoginEnum {
  ENABLED = 'Enabled',
}

const useBiometrics = () => {
  const [availableBiometryType, setAvailableBiometry] = useState<keyof typeof BiometryTypes | null>(
    null,
  );

  const createRNBInstance = () => {
    if (Platform.OS === 'ios') {
      return new ReactNativeBiometrics({ allowDeviceCredentials: true });
    }
    return new ReactNativeBiometrics();
  };

  const rnBiometrics = createRNBInstance();

  useEffect(() => {
    checkAvailableBiometryType();
  }, []);

  const checkAvailableBiometryType = async (): Promise<boolean> => {
    try {
      const { available, biometryType } = await rnBiometrics.isSensorAvailable();
      if (available && biometryType === BiometryTypes.TouchID) {
        setAvailableBiometry(BiometryTypes.TouchID);
        return true;
      } else if (available && biometryType === BiometryTypes.FaceID) {
        setAvailableBiometry(BiometryTypes.FaceID);
        return true;
      } else if (available && biometryType === BiometryTypes.Biometrics) {
        setAvailableBiometry(BiometryTypes.Biometrics);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometrics checkAvailableBiometryType',
        additionalInfo: 'Failed to check biometrics',
      });
      throw error;
    }
  };

  const getOrCreateDeviceId = async () => {
    let deviceId = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.BIOMETRICS.DEVICE_ID);
    if (!deviceId) {
      deviceId = uuid.v4(); // ⇨ '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'
      await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.BIOMETRICS.DEVICE_ID, deviceId);
    }
    return deviceId;
  };

  const turnBiometricsOn = async (): Promise<boolean> => {
    try {
      const email = store.getState().getWFMProfile.response?.email;
      if (!email) {
        throw new Error('Email not found');
      }
      await AsyncStorage.setItem('email', email);
      const { publicKey } = await rnBiometrics.createKeys();

      const deviceId = await getOrCreateDeviceId();
      const success = await sendPublicKeyToServer(publicKey, deviceId);

      if (success)
        await AsyncStorage.setItem(ASYNC_STORAGE_KEYS.BIOMETRICS.LOGIN, BiometricLoginEnum.ENABLED);

      return success;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometrics turnBiometricsOn',
        additionalInfo: 'Failed to turn biometrics on',
      });
      throw error;
    }
  };

  const turnBiometricsOff = async (): Promise<boolean> => {
    try {
      const deletedKeys = await deleteKeys();
      if (deletedKeys) await AsyncStorage.removeItem(ASYNC_STORAGE_KEYS.BIOMETRICS.LOGIN);
      return deletedKeys;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometrics turnBiometricsOff',
        additionalInfo: 'Failed to turn biometrics off',
      });
      throw error;
    }
  };

  const signBiometricChallenge = async (challenge: string): Promise<string> => {
    try {
      const { signature, error } = await rnBiometrics.createSignature({
        promptMessage: 'Sign in',
        payload: challenge,
      });
      if (error) {
        throw new Error(error);
      }
      if (!signature) {
        throw new Error(BIOMETRICS_ERRORS.LOCAL.SIGNATURE_FAILED);
      }
      return signature;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometrics signBiometricChallenge',
        additionalInfo: 'Failed to generate signature',
      });
      throw error;
    }
  };

  const getBiometricsChallenge = (email: string): string => {
    const epochTimeSeconds = Math.round(new Date().getTime() / 1000).toString();
    const challenge = epochTimeSeconds + ' challenge ' + email;
    return challenge;
  };

  const authenticateWithBiometrics = async (email: string): Promise<any> => {
    try {
      const challenge: string = getBiometricsChallenge(email);
      const signature: string = await signBiometricChallenge(challenge);

      const deviceId = await getOrCreateDeviceId();
      return await verifySignatureWithServer(email, challenge, signature, deviceId);
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometris authenticateWithBiometrics',
        additionalInfo: 'Failed to login with biometrics',
      });
      throw error;
    }
  };

  const biometricKeysExist = async (): Promise<boolean> => {
    try {
      const { keysExist } = await rnBiometrics.biometricKeysExist();
      return keysExist;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometris biometricKeysExist',
        additionalInfo: 'Failed to check for biometric keys',
      });
      throw error;
    }
  };

  const deleteKeys = async (): Promise<boolean> => {
    // private method - don't forget to clear ASYNC_STORAGE_KEYS.BIOMETRICS.LOGIN key if you use this method directly.
    try {
      const { keysDeleted } = await rnBiometrics.deleteKeys();
      return keysDeleted;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'useBiometrics deleteKeys',
        additionalInfo: 'Failed to delete biometric keys',
      });
      throw error;
    }
  };

  return {
    authenticateWithBiometrics,
    availableBiometryType,
    turnBiometricsOn,
    turnBiometricsOff,
    biometricKeysExist,
  };
};

export default useBiometrics;
