import { Toast } from 'native-base';
import { onBookSlot } from '../../src/api/totaraApis';
import { useSession } from '../totara/core';
import { useState } from 'react';
import constants from '../../src/api/constants';
import { useDispatch } from 'react-redux';
import { displayFixedSnackbar, FixedSnackbarTypeEnum } from '@totara/reducers/fixedSnackbarReducer';

const useBookingSlot = ({ params }) => {
  const { apiToken } = useSession();
  const [isLoading, setLoading] = useState(false);
  const [isSuccessBooked, setSuccessBooked] = useState(false);
  const dispatch = useDispatch();
  

  const [selectedSlot, setSelectedSlot] = useState(
    params?.id
      ? {
          id: params?.id,
          slot: params?.slot,
          fullDate: params?.fDate,
        }
      : {
          id: '',
          slot: '',
          fullDate: '',
        },
  );

  const onSubmitBookingSlot = async ({ courseId, slotid, userId, requiresManagerApproval = false }) => {
    setLoading(true);
    const requestParams = {
      wstoken: apiToken,
      wsfunction: constants.BOOKING_SLOT,
      moodlewsrestformat: 'json',
      // userid: userId,
      courseid: courseId,
      slotid: slotid,
      edit: params?.id ? 1 : 0,
      previous_slot_id: params?.id ?? 0,
    };
    const data = await onBookSlot(requestParams);
    if (data?.success) {
      setLoading(false);
      setSuccessBooked(true);
      if (requiresManagerApproval) {
        return true;
      } else {
        Toast.show({ text: data?.message, textStyle: { textAlign: 'left' } });
        return true;
      }
    } else {
      if (data?.message === 'Cancelling this request slot is not possible right now.') {
        // temporary if, we are waiting for BE to change message text!
        setLoading(false);
        dispatch(displayFixedSnackbar({ message: 'Cancelation prior 48h to the scheduled time slot is not possible', type: FixedSnackbarTypeEnum.Error }))
        return false;
      } else {
        setLoading(false);
        Toast.show({ text: data?.message, textStyle: { textAlign: 'left' } });
        return false;
      }
    }
  };

  const isSelectedSlot = params?.id == selectedSlot?.id;

  return {
    isSelectedSlot,
    isSuccessBooked,
    selectedSlot,
    isLoading,
    setSelectedSlot,
    setSuccessBooked,
    onSubmitBookingSlot,
  };
};

export default useBookingSlot;
