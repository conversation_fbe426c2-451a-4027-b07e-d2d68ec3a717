import React, { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import { coreCourse } from '../totara/features/currentLearning/course/api';
import { courseDetails } from '../../src/api/totaraApis';
import { useSession } from '../totara/core';
import { userOwnProfile } from '../totara/features/profile/api';
import { setLastOpenedCourse, setNewCourseList } from '../totara/reducers/lastOpenedCourseReducer';
import { useDispatch } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import { enrolmentInfoQuery } from '../totara/features/enrolment/api';
import { Toast } from 'native-base';
import { setReferCourse } from '../totara/reducers/referCourseReducer';
import { setBottomSheetVisible } from '../totara/reducers/addToListBottomSheetReducer';
import crashReportLogger from '@utils/crashlyticsLogger';

const useCategoryDetails = ({ item }) => {
  const queryVariables = { courseid: item?.courseid };
  const { apiToken } = useSession();
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [course, setCourse] = useState<any>(null); // TODO: will remove any later
  const [refreshing, setRefreshing] = useState(false);
  const [enrolRefreshing, setEnrolRefreshing] = useState(false);

  const { data: userData } = useQuery(userOwnProfile, {
    notifyOnNetworkStatusChange: true,
  });

  const { data: coursesData, refetch: coreCourseRefetch } = useQuery(coreCourse, {
    variables: queryVariables,
    notifyOnNetworkStatusChange: true,
  });

  const { data: enrolmentInfo } = useQuery(enrolmentInfoQuery, {
    variables: queryVariables,
    fetchPolicy: 'no-cache',
  });

  const getCourseDetails = async () => {
    setRefreshing(true);
    const data = await courseDetails({
      courseId: item?.courseid,
      apiToken,
      userId: userData?.profile?.id,
    });
    if (data) {
      setCourse(data);
    }
    setRefreshing(false);
  };

  const onRefresh = React.useCallback(() => {
    if (refreshing) return;
    setTimeout(() => {
      getCourseDetails();
      coreCourseRefetch({ variables: queryVariables })
        .then(() => {
          setRefreshing(false);
          setEnrolRefreshing(false);
          Toast.show({
            text: 'You have successfully enrolled to this course',
            textStyle: { textAlign: 'left' },
          });
          setNewCourseList(dispatch, item);
          setLastOpenedCourse(dispatch, item?.courseid);
        })
        .catch((error) => {
          crashReportLogger(error as Error, {
            component: 'useCategoryDetails',
            additionalInfo: 'Failed to enroll to course'
          })
          console.error(error);
          setRefreshing(false);
          setEnrolRefreshing(false);
        });
    }, 2000);
  }, [coreCourseRefetch, queryVariables]);

  const onMorePress = () => {
    setReferCourse(dispatch, {
      courseid: item?.courseid,
      name: course?.title || '',
      image: item?.image,
    });
    setBottomSheetVisible(dispatch, true);
  };

  useEffect(() => {
    getCourseDetails();
    setLastOpenedCourse(dispatch, item?.courseid);
  }, [isFocused]);

  const courseTotaraData = coursesData?.mobile_course?.course;

  const { sectionTabs, isInstructorLedCourse } = useMemo(() => {
    const isInstructorLedCourse =
      course?.coursetype == 'Instructor-led' || course?.coursetype == 'Blended';
    const sectionTabs = [
      'Info',
      'Agenda',
      'Lessons',
      'Reviews',
      isInstructorLedCourse ? 'Location' : null,
    ].filter(Boolean);
    return { sectionTabs: sectionTabs as string[], isInstructorLedCourse };
  }, [course?.coursetype]);

  return {
    course,
    coursesData,
    courseTotaraData,
    queryVariables,
    refreshing,
    enrolmentInfo,
    enrolRefreshing,
    sectionTabs,
    isInstructorLedCourse,
    setEnrolRefreshing,
    onMorePress,
    onRefresh,
    coreCourseRefetch,
    setRefreshing,
  };
};

export default useCategoryDetails;
