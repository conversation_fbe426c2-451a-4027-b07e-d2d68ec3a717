import React, { useEffect, useMemo, useState } from 'react';
import { useQuery } from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useIsFocused } from '@react-navigation/native';
import useOfflineStatus from '@screens/OfflineCoursesSpace/hooks/useOfflineStatus.tsx';
import { LabelConfig } from '@theme';
import { displayFixedSnackbar, FixedSnackbarTypeEnum } from '@totara/reducers/fixedSnackbarReducer';
import { convertNumbersToArabicNumerals } from '@utils/constants';
import { LOCALE } from '@utils/localization';
import moment from 'moment';
import { Toast } from 'native-base';
import { useDispatch } from 'react-redux';
import i18n from '../../i18n';
import { getLatestCourseDetails, onCancelSlot } from '../../src/api/totaraApis';
import { CourseDetailsTypes } from '../../src/models/CourseType';
import { useSession } from '../totara/core';
import { coreCourse } from '../totara/features/currentLearning/course/api';
import { enrolmentInfoQuery } from '../totara/features/enrolment/api';
import { userOwnProfile } from '../totara/features/profile/api';
import { setBottomSheetVisible } from '../totara/reducers/addToListBottomSheetReducer';
import { setLastOpenedCourse } from '../totara/reducers/lastOpenedCourseReducer';
import { setReferCourse } from '../totara/reducers/referCourseReducer';

const { info, Activities, Location, Agenda } = LabelConfig.courseDetail;

const useCategoryDetails = ({ item, routeParams = null }: { item: any; routeParams?: any }) => {
  const currentCourseId = routeParams?.courseid || item?.courseid;
  const queryVariables = { courseid: currentCourseId };
  const { apiToken } = useSession();
  const [editSlotView, setEditSlotView] = useState(false);
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [course, setCourse] = useState<CourseDetailsTypes>();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [enrolRefreshing, setEnrolRefreshing] = useState(false);
  const { isConnected } = useOfflineStatus();
  const [offlineContent, setOfflineContent] = useState([]);

  useEffect(() => {
    if (isFocused) {
      (async function () {
        setOfflineContent(!isConnected ? await getOfflineScormActivities() : []);
      })();
    }
  }, [isFocused, isConnected]);

  const { data: courseGraphQL, refetch: coreCourseRefetch } = useQuery(coreCourse, {
    variables: queryVariables,
    notifyOnNetworkStatusChange: true,
  });

  const { data: userData } = useQuery(userOwnProfile, {
    notifyOnNetworkStatusChange: true,
  });

  const { data: enrolmentInfo } = useQuery(enrolmentInfoQuery, {
    variables: queryVariables,
    fetchPolicy: 'no-cache',
  });

  const getCourseDetails = React.useCallback(async () => {
    const data = await getLatestCourseDetails({
      courseId: currentCourseId,
      apiToken,
      userId: userData?.profile?.id,
    });
    if (data) {
      setCourse(data);
    }
  }, [currentCourseId, apiToken, userData?.profile?.id]);

  const loadCourseData = React.useCallback(async () => {
    await getCourseDetails();
    coreCourseRefetch();
    setLastOpenedCourse(dispatch, currentCourseId);
  }, [getCourseDetails, coreCourseRefetch, dispatch, currentCourseId]);

  const onRefresh = React.useCallback(() => {
    if (refreshing) return;
    setRefreshing(true);
    setTimeout(() => {
      getCourseDetails();
      coreCourseRefetch();
      setRefreshing(false);
    }, 1000);
  }, [currentCourseId, getCourseDetails, coreCourseRefetch]);

  const onMorePress = () => {
    setReferCourse(dispatch, {
      courseid: currentCourseId,
      name: course?.course_name || '',
      image: course?.course_image,
    });
    setBottomSheetVisible(dispatch, true);
  };

  useEffect(() => {
    loadCourseData();
  }, [isFocused, currentCourseId, loadCourseData]);

  const onCancelBookingSlot = async ({ slotid, requiresManagerApproval = false }) => {
    setLoading(true);
    const data = await onCancelSlot({
      apiToken,
      slotid,
      courseid: currentCourseId,
      userid: userData?.profile?.id,
    });

    if (data?.success) {
      if (requiresManagerApproval) {
        dispatch(displayFixedSnackbar({ message: data?.message }));
      } else {
        Toast.show({ text: data?.message, textStyle: { textAlign: 'left' } });
      }
      setEditSlotView(false);
      getCourseDetails();
    } else {
      if (data?.message === 'Cancelling this request slot is not possible right now.') {
        // temporary if, we are waiting for BE to change message text!
        dispatch(
          displayFixedSnackbar({
            message: 'Cancelation prior 48h to the scheduled time slot is not possible',
            type: FixedSnackbarTypeEnum.Error,
          }),
        );
      } else {
        Toast.show({ text: data?.message, textStyle: { textAlign: 'left' } });
      }
    }
    setLoading(false);
  };

  const getFormatedDate = (date) => {
    if (!date) return undefined;

    const dateSplit = date.split(' ')[0];
    return moment(dateSplit).locale(LOCALE).format('DD MMMM');
  };

  const getOfflineScormActivities = async () => {
    const offlineData = (await AsyncStorage.getItem('myDownloadsDetailsActivities')) || '[]';
    return (
      JSON.parse(offlineData).find((it) => it.courseid?.toString() === currentCourseId?.toString())
        ?.activities || []
    );
  };

  const startDate = getFormatedDate(course?.seat_date_info?.startdate);
  // const endDate = getFormatedDate(course?.slot_end_date_time);

  const content = useMemo(
    () =>
      courseGraphQL?.mobile_course?.course?.sections
        ?.map((it) => {
          if (it?.data?.length > 0) {
            return it;
          }
        })
        ?.filter(Boolean),
    [courseGraphQL, isFocused],
  );
  const enrolment = useMemo(
    () => enrolmentInfo?.enrolmentInfo?.enrolmentOptions.filter((val) => val.type == 'self'),
    [enrolmentInfo],
  );

  const { sectionTabs, isInstructorLedCourse } = useMemo(() => {
    const isInstructorLedCourse = course?.courseType == 'Instructor-led';
    const sectionTabs =
      (item?.isNewJoiner ?? false)
        ? [
            info,
            // content?.length > 0 && convertNumbersToArabicNumerals(Activities || '0'),
            isInstructorLedCourse && course?.available_slots?.length ? Location : null,
          ].filter(Boolean)
        : [
            info,
            isInstructorLedCourse ? course?.course_agenda?.length > 0 && Agenda : false,
            content?.length > 0 && convertNumbersToArabicNumerals(Activities || '0'),
            course?.reviews?.length > 0 &&
              `${i18n.t('Reviews')} (${convertNumbersToArabicNumerals(course?.reviews?.length || '0')})`,
            course?.available_slots?.length > 0 && isInstructorLedCourse && Location,
          ].filter(Boolean);
    return { sectionTabs: sectionTabs as string[], isInstructorLedCourse };
  }, [course?.courseType, content, course, isFocused]);

  const getActivityCount = useMemo(() => {
    if (!content || !Array.isArray(content)) return 0;

    let count = 0;
    content?.map((it) => {
      return (count += it?.data?.length);
    });
    return count;
  }, [content]);

  return {
    editSlotView,
    content,
    startDate,
    endDate: '',
    userProfileId: userData?.profile?.id,
    course,
    queryVariables,
    refreshing,
    enrolment,
    enrolRefreshing,
    sectionTabs,
    isInstructorLedCourse,
    isNewJoiner: item?.isNewJoiner ?? false,
    isLoading,
    setEditSlotView,
    setEnrolRefreshing,
    onMorePress,
    onRefresh,
    setRefreshing,
    onCancelBookingSlot,
    offlineContent,
    isConnected,
    getActivityCount,
    apiToken,
  };
};

export default useCategoryDetails;
