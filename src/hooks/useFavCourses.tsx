import { useSession } from '../totara/core';
import { addToFavorite, courseDetails, getCoursesProgress, getLatestCourseDetails } from '../api/totaraApis';
import { RootState } from '../totara/reducers';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Toast } from 'native-base';
import { useTranslation } from 'react-i18next';
import { openFavouritesSheet, removeFavCourse } from '@totara/reducers/personalListReducer';
import { useFeatureFlags } from '@utils/FeatureFlagsProvider';
import { addCourseToFavs, fetchFavCourses, loadFavCourse, loadFavCourses, removeCourseFromFavs, savePersonalCourse } from '@totara/reducers/favoriteCourses';
import { CourseDetailsTypes, FavoriteCourse, PersonalCourse } from '@models/CourseType';

const useFavCourses = () => {
  const dispatch = useDispatch();
  const { apiToken, core } = useSession();
  const { data, cache, ids, isFetched, isLoading, loadingCourseId } = useSelector((state: RootState) => state.favoriteCourses);
  const { t } = useTranslation();
  const { featureFlags } = useFeatureFlags();

  const fetchData = () => {
    dispatch(loadFavCourses(true));
    const config = {
      apiToken: apiToken,
      userId: global?.userData?.id,
    };
    getCoursesProgress(config)
      .then((response) => {
        dispatch(fetchFavCourses(response?.savedcourses || []));
      })
      .catch((e) => {
        dispatch(loadFavCourses(false));
      });
  };

  useEffect(() => {
    if (!isFetched && !isLoading) {
      fetchData();
    }
  }, [isFetched, isLoading]);

  const cachePersonalCourse = async (details: any, latestDetails: CourseDetailsTypes | undefined) => {
    if (details && latestDetails) {
      const course: PersonalCourse = {
        id: Number(latestDetails.courseId),
        title: details.title,
        image: latestDetails.course_image ?? '',
        type: details.coursetype,
        isenrolled: details.isenrolled,
        progress: latestDetails.progress,
        rating: details.rating,
        status: details.status,
        total_lesson: latestDetails.lessons?.length?.toString(),
        course_day: '',
        course_type: details.coursetype,
        curator_logo: latestDetails.course_curatorlogo ?? '',
        creator: '',
        due_date: latestDetails.slot_end_date_time,
        course_due_date: latestDetails.slot_end_date_time,
        duration: latestDetails.duration,
        user_completed: latestDetails.completed_users,
      }
      dispatch(savePersonalCourse(course));
    }
  }

  const fetchCourse = async (id: number, isFav: boolean): Promise<FavoriteCourse | null> => {
    if (!cache[id]) {
      const details = await courseDetails({
        courseId: id,
        apiToken,
        userId: core?.user.id,
      });
      const latestDetails = await getLatestCourseDetails({
        courseId: id,
        apiToken,
        userId: core?.user.id,
      });
      cachePersonalCourse(details, latestDetails);
  
      if (details && latestDetails) {
        const course: FavoriteCourse = {
          category: {
            id,
            name: '',
          },
          courseid: id,
          title: details.title,
          image: latestDetails.course_image ?? '',
          coursetype: details.coursetype,
          isenrolled: details.isenrolled,
          rating: details.rating,
          ///////////////
          course_day: 'Multi Day',
          course_due_date: latestDetails.slot_end_date_time,
          duration: latestDetails.duration,
          component_type: '',
          course_curatorlogo: latestDetails.course_curatorlogo ?? '',
          errormessage: null,
          description: latestDetails.course_description,
          course_curatorname: latestDetails.course_curatorname,
          is_favorite: isFav,
          /////////////////////
          language: '',
          total_lessons: latestDetails.lessons?.length ?? 0,
          /////////////////////
          points: 0,
          reviews: latestDetails.reviews,
        }
        return course;
      }
      return null;
    }
   return cache[id];
  }

  const isAddedToFav = (id: number): boolean => ids.includes(id);

  const addToFav = async (id: number) => {
    if (!loadingCourseId) {
      dispatch(loadFavCourse({ id }));
      const course = await fetchCourse(id, true);
      if (course) {
        dispatch(addCourseToFavs(course));
        await addToFavorite({
          apiToken: apiToken,
          courseid: id,
          // userId: global?.userData?.id,
          userId: core?.user?.id,
          status: true,
        });
        if (featureFlags.personalLists) {
          dispatch(openFavouritesSheet({ courseId: id }))
        } else {
          Toast.show({ text: t('Course added to favorite'), textStyle: { textAlign: 'left' } });
        }
      }      
      dispatch(loadFavCourse({ id: null }));
    }
  };

  const removeFromFav = async (id: number) => {
    if (!loadingCourseId) {
      dispatch(loadFavCourse({ id }));
      dispatch(removeCourseFromFavs({ id }));
      dispatch(removeFavCourse({courseId: id }))
      await addToFavorite({
        apiToken: apiToken,
        courseid: id,
        // userId: global?.userData?.id,
        userId: core?.user?.id,
        status: false,
      });
      Toast.show({ text: t('Course removed from favorite'), textStyle: { textAlign: 'left' } }); 
      dispatch(loadFavCourse({ id: null }));
    }
  };

  return {
    data,
    isLoading,
    isFetched,
    loadingCourseId,
    isAddedToFav,
    addToFav,
    removeFromFav,
  };
};

export default useFavCourses;
