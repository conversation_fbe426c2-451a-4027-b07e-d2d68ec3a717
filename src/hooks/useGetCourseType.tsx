import { getCourseType } from '@api/totaraApis';
import { useSession } from '@totara/core';
import { setLoaderVisibility } from '@totara/reducers/modalLoaderReducer';
import React from 'react';
import { useDispatch } from 'react-redux';

export const useGetCourseType = () => {
  const { apiToken } = useSession();
  const dispatch = useDispatch();

  const onGetCourseType = async (id) => {
    setLoaderVisibility(dispatch, true);
    const res = await getCourseType({ apiToken, id });
    setLoaderVisibility(dispatch, false);
    return res;
  };

  return { onGetCourseType };
};
