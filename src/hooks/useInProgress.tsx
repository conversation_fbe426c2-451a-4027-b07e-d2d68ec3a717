import { useEffect, useState } from 'react';
import { TOTARA_BASE_URL } from '@api/api_client';
import { useSession } from '@totara/core';
import crashReportLogger from '@utils/crashlyticsLogger';
import axios from 'axios';

const useInProgress = () => {
  const [inProgress, setInProgress] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const { apiToken } = useSession();

  useEffect(() => {
    fetchInProgress();
  }, []);

  const fetchInProgress = async () => {
    setLoading(true);
    const apiUrl = TOTARA_BASE_URL;
    const requestData = {
      wstoken: apiToken,
      wsfunction: 'wstotara_fetch_mylearningdata',
      moodlewsrestformat: 'json',
    };

    const params = new URLSearchParams(requestData).toString();

    try {
      const response = await axios.get(`${apiUrl}?${params}`);
      setInProgress(response.data?.inprogresscourses);
      setLoading(false);
      return response.data?.courses;
    } catch (e) {
      crashReportLogger(e as Error, {
        component: 'useInProgress fetchInProgress',
        url: apiUrl,
        additionalInfo: 'Failed to fetch progess',
      });
    }
  };
  return {
    inProgress,
    isLoading,
  };
};

export default useInProgress;
