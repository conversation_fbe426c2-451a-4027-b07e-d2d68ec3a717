import { useEffect } from 'react';
import { getCoursesProgress } from '../api/totaraApis';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../totara/reducers';
import { useSession } from '../totara/core';
import { useIsFocused } from '@react-navigation/native';
import CoursesType from '../models/CourseType';
import { setLastOpenedCourse, setOpenedCourses } from '../totara/reducers/lastOpenedCourseReducer';
import AsyncStorage from '@react-native-async-storage/async-storage';

const propertiesToCheck = [
  'requiredcourses',
  'completedcourses',
  // "savedcourses",
  'inprogresscourses',
];

const useLastOpenedCourseProgress = () => {
  const { apiToken } = useSession();
  const dispatch = useDispatch();
  const isFocus = useIsFocused();
  const { userData } = useSelector((state: RootState) => state.userProfileData);
  const { coursesList, lastOpenedCourseItem } = useSelector(
    (state: RootState) => state.lastOpenedCourseReducer,
  );

  useEffect(() => {
    getCoursesProgress({
      apiToken: apiToken,
      userId: userData?.id,
    })
      .then((response) => {
        let arr: CoursesType[] = [];
        propertiesToCheck.forEach((property) => {
          if (response?.[property] && response[property]?.length > 0) {
            arr.push(...response[property]);
          }
        });
        setOpenedCourses(dispatch, arr);
        if (response?.inprogresscourses && response?.inprogresscourses?.length > 0) {
          setInitialInProgressItem({ item: response?.inprogresscourses[0] });
        }
      })
      .catch((e) => console.warn(e));
  }, [isFocus, userData?.id]);

  const setInitialInProgressItem = async ({ item }) => {
    if (Object.keys(lastOpenedCourseItem).length == 0) {
      setLastOpenedCourse(dispatch, item?.courseid);
    }
  };

  return { coursesList, lastOpenedCourseItem };
};

export default useLastOpenedCourseProgress;
