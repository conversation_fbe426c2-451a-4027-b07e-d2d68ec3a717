import { useState } from 'react';
import { useSession } from '@totara/core';
import { checkIfUserHasLineManager } from '@api/totaraApis';
import { UserHasLineManagerResponse } from '@models/CourseType';
import { useDispatch } from 'react-redux';
import { openBottomSheet } from '@totara/reducers/bottomSheetReducer';

interface Props {
  requiresManagerApproval: boolean;
}

const useManagerApproval = ({ requiresManagerApproval }: Props) => {
  const { apiToken } = useSession();
  const dispatch = useDispatch();
  const [isLoading, setLoading] = useState<boolean>(false);
  
  const checkForLineManager = async (): Promise<boolean> => {
    if (!requiresManagerApproval) {
      return true;
    }
    
    setLoading(true);
    const response: UserHasLineManagerResponse | undefined = await checkIfUserHasLineManager({ apiToken });
    if (!response) {
      console.log("Failed to fetch line manager information.");
      setLoading(false);
      return false;
    }

    if (!response.success && response.message == "Your line manager hasn't been assigned yet. Please reach out to the admin team for assistance.") {
      dispatch(openBottomSheet({ modalKey: 'NoLineManager' }));
      setLoading(false);
      return false;
    }

    setLoading(false);
    return true;
  }

  return {
    checkForLineManager,
    isLoading,
  }
};

export default useManagerApproval;
