import { Toast } from 'native-base';
import { getMentorSlotsDetails, onBookMentorSession } from '../../src/api/totaraApis';
import { useSession } from '../totara/core';
import { useEffect, useState } from 'react';

const useBookingSlot = ({ id, tab }) => {
  const { apiToken, core } = useSession();
  const [isLoading, setLoading] = useState(false);
  const [loadingSlots, setLoadingSlots] = useState(true);
  const [isSuccessBooked, setSuccessBooked] = useState(false);
  const [availableSlots, setAvailableSlots] = useState<any>([]);
  const [selectedSlot, setSelectedSlot] = useState({
    id: '',
    slot: '',
    fullDate: '',
    date: '',
  });
  const onSubmitBookingSlot = async () => {
    setLoading(true);
    const data = await onBookMentorSession({
      apiToken,
      userid: core?.user?.id,
      slot_id: selectedSlot.id,
      tab,
    });
    if (data?.status) {
      setSuccessBooked(true);
    } else {
      Toast.show({ text: data?.message, textStyle: { textAlign: 'left' } });
    }
    setLoading(false);
  };

  useEffect(() => {
    (async () => {
      setLoadingSlots(true);
      const response = await getMentorSlotsDetails({ apiToken, mentorId: id, tab });
      if (response && response?.length > 0) {
        const obj = {};
        response.map((it) => {
          if (!obj.hasOwnProperty(it.slot_date)) {
            obj[it.slot_date] = [it];
          } else {
            obj[it.slot_date].push(it);
          }
        });

        var result = Object.keys(obj).map((key) => {
          return {
            date: key,
            slots: obj[key],
          };
        });
        setAvailableSlots(result);
      }
      setLoadingSlots(false);
    })();
  }, []);

  return {
    loadingSlots,
    isSuccessBooked,
    isLoading,
    availableSlots,
    selectedSlot,
    setSuccessBooked,
    onSubmitBookingSlot,
    setSelectedSlot,
  };
};

export default useBookingSlot;
