import { useIsFocused } from '@react-navigation/native';
import { getCoachDetails } from '../api/totaraApis';
import { useSession } from '../totara/core';
import { useEffect, useState } from 'react';

const useCoachDetails = ({ tab, coachId }) => {
  const [loading, setLoading] = useState(true);
  const [response, setResponse] = useState(undefined);
  const { apiToken } = useSession();
  const isFocused = useIsFocused();

  useEffect(() => {
    (async () => {
      const response = await getCoachDetails({ apiToken, coachId, tab });
      if (!response?.exception) {
        setResponse(response);
        setLoading(false);
      }
    })();
  }, [tab, isFocused]);

  return {
    loading,
    response,
  };
};

export default useCoachDetails;
