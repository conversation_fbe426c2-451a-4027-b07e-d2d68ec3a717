import { fetchPreferences, getCoachList } from '../api/totaraApis';
import { useSession } from '../totara/core';
import { useEffect, useState } from 'react';
import event, { EVENT_LISTENER, Events } from '@totara/lib/event';
import axios from 'axios';
import { TOTARA_BASE_URL } from '@api/api_client';
import { I18nManager } from 'react-native';

const useMentors = ({ tab }) => {
  const [loading, setLoading] = useState(true);
  const [list, setList] = useState([]);
  const [selectedTab, setSelectedTab] = useState(tab);
  const [filteredList, setFilteredList] = useState([]);
  const { apiToken } = useSession();
  const [categories, setCategories] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [coachspecializationArray, setCoachspecializationArray] = useState([]);
  const [refreshCall, setRefreshCall] = useState(false);

  const refreshApiCall = () => setRefreshCall(true);


  const setTab = (e) => {
    setSelectedTab(e)
  }

  const setQuery = (e) => {
    setSearchQuery(e)
  }


  const getCoahcingFilters = async () => {
    const apiUrl = TOTARA_BASE_URL;
    const params = {
      wstoken: apiToken,
      wsfunction: 'wstotara_get_coach_specialization',
      moodlewsrestformat: 'json',
      language: I18nManager.isRTL ? 'ar' : 'en',

    };

    const response = await axios.get(apiUrl, { params })
    setCategories(response.data?.coachspecialization);
    const filtersSorted = response.data?.coachspecialization?.split(',').map((item, index) => {
      return { id: index, name: item };
    })
    setCoachspecializationArray(filtersSorted)
  }

  useEffect(() => {
    const eventEmit = event.addListener(EVENT_LISTENER, (param) => {
      if (param.event === Events.RefreshMentorList) {
        refreshApiCall();
      }
    });

    return () => eventEmit.remove();
  }, []);

  useEffect(() => {
    (async () => {
      if (categories?.length || refreshCall) {
        setLoading(true);
        const response = await getCoachList({ apiToken, coachspecialization: categories, name: searchQuery });
        const filteredResponse = await getCoachList({ apiToken, coachspecialization: selectedTab, name: searchQuery });

        if (selectedTab) {
          setFilteredList(filteredResponse)
        }
        else {
          setFilteredList(response)
        }
        if (!response?.exception) {
          setList(response);
          setLoading(false);
          setRefreshCall(false);
        }
      }
    })();
  }, [selectedTab, categories, refreshCall, searchQuery]);

  useEffect(() => {
    getCoahcingFilters();
  }, []);

  return {
    loading,
    list: list ?? [],
    categories,
    coachspecializationArray,
    filteredList,
    setTab,
    setQuery
  };
};

export default useMentors;
