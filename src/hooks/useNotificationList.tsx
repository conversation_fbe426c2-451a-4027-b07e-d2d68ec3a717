import { useIsFocused, useNavigation } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { getNotificationList, markNotificationAsRead } from '../api/totaraApis';
import { useSession } from '../totara/core';
import { navigate, navigationRef } from '../navigation/navigationService';
import moment from 'moment';
import { extractId } from '../utils/constants';
import { isSearchBarAvailableForCurrentPlatform } from 'react-native-screens';

const useNotificationList = () => {
  const isFocused = useIsFocused();
  const { apiToken, core } = useSession();
  const [list, setList] = useState([]);
  const [unreadCount, setUnreadNotificatins] = useState(0);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    getList();
  }, [isFocused]);

  const getList = () => {
    setLoading(true);
    getNotificationList(apiToken, core?.user?.id)
      .then((res) => {
        if (res?.notifications) {
          const unreadCount = res?.notifications?.filter((res) => res?.timeread == null);
          setUnreadNotificatins(unreadCount.length);
          setList(res?.notifications);
          setLoading(false);
        }
      })
      .catch((err) => setLoading(false));
  };
  const read = (item) => {
    const courseId = extractId(item?.fullmessage);
    if (item?.timeread) {
      navigateToRespective(item, courseId);
      setLoading(false);
      return;
    }
    setLoading(true);

    markNotificationAsRead(apiToken, core?.user?.id, item.id)
      .then((res) => {
        setLoading(false);
        if (res?.status) {
          getList();
          navigateToRespective(item, courseId);
        }
      })
      .catch((err) => setLoading(false));
  };

  const navigateToRespective = (item, courseId) => {
    const jsonObject = JSON.parse(item?.data);
    const imageUrl = jsonObject?.image;

    if (item?.type == 'announcement') {
      (navigationRef.current ?? navigation)?.navigate('AnnoucementDetail', {
        name: item?.subject,
        subject: item?.fullmessage,
        image: imageUrl,
        created: moment(item.timepushed, 'DD MMM YYYY, HH:mm').format('DD MMMM YYYY'),
        title: item?.subject,
      });
    }
    if (item?.type == 'program') {
      (navigationRef.current ?? navigation)?.navigate('ProgramDetails', {
        id: item?.itemid,
      });
    }
    if (item?.type == 'course') {
      (navigationRef.current ?? navigation)?.navigate(
        item?.course_days == 'Multi Day' && item?.course_type != 'Self-Paced'
          ? 'MultipleSession'
          : 'CourseDetail',
        {
          courseid: item?.itemid,
          title: item?.subject,
          image: imageUrl,
        },
      );
    }
    if (item?.type == 'streak_notification') {
      (navigationRef.current ?? navigation).navigate('StreakHistory');
    }
  };

  return { read, list, unreadCount, loading };
};

export default useNotificationList;
