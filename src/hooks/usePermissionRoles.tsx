import { useEffect, useLayoutEffect, useState } from 'react';
import { getRolesPermission } from '../../src/api/server_requests';
import { useNavigation } from './index';
import { useIsFocused } from '@react-navigation/native';

const usePermissionRoles = () => {
  const [isRecommendedEnabled, setRecommendedEnabled] = useState(false);
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  // useLayoutEffect(() => {
  //   setTimeout(() => {
  //     getRolesPermission().then((response) => {
  //       setRecommendedEnabled(
  //         response?.rolesPermission?.functionPermissions?.recommend
  //       );
  //     });
  //   }, 1000);
  // }, []);

  const onPress = () => {
    navigation.navigate('RecommendLeader');
  };

  return { onPress, isRecommendedEnabled };
};

export default usePermissionRoles;
