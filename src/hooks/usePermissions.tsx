import { useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import deviceInfoModule from 'react-native-device-info';
import {
  request,
  openSettings,
  RESULTS,
  requestMultiple,
  checkMultiple,
  PERMISSIONS,
} from 'react-native-permissions';

export const REQUIRED_PERMISSIONS = {
  audio: {
    android:
      Number(deviceInfoModule.getSystemVersion()) >= 13
        ? [
          PERMISSIONS.ANDROID.RECORD_AUDIO,
          PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
          PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
          PERMISSIONS.ANDROID.READ_MEDIA_AUDIO,
        ]
        : [
          PERMISSIONS.ANDROID.RECORD_AUDIO,
          PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
          PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
        ],
    ios: [PERMISSIONS.IOS.MICROPHONE],
  },
  video: {
    android:
      Number(deviceInfoModule.getSystemVersion()) >= 13
        ? [
          PERMISSIONS.ANDROID.READ_MEDIA_VIDEO,
          PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
          PERMISSIONS.ANDROID.READ_MEDIA_AUDIO,
          PERMISSIONS.ANDROID.CAMERA,
        ]
        : [PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE, PERMISSIONS.ANDROID.CAMERA],
    ios: [PERMISSIONS.IOS.MICROPHONE, PERMISSIONS.IOS.CAMERA],
  },
};

const usePermissions = () => {
  const [isPermissionEnabled, setPermissionEnabled] = useState(false);

  const requestPermission = useCallback(async (permissionType) => {
    const result = await request(permissionType);
    const isPermissionEnabled = handlePermissionStatus(result, permissionType);
    if (!isPermissionEnabled) {
      showPermissionBlockedAlert();
      return false;
    }
    return true;
  }, []);

  const requestMultiplePermission = useCallback(async (permissionType, showAlert = true) => {
    const result = await requestMultiple(permissionType);
    for (const res of Object.keys(result)) {
      const isPermissionEnabled = handlePermissionStatus(result[res], res);
      if (!isPermissionEnabled) {
        setPermissionEnabled(false);
        if (showAlert) {
          showPermissionBlockedAlert();
        }
        return false;
      }
    }

    setPermissionEnabled(true);
    return true;
  }, []);

  const handlePermissionStatus = useCallback(
    (status, permissionType) => {
      switch (status) {
        case RESULTS.UNAVAILABLE:
          break;
        case RESULTS.DENIED:
          requestPermission(permissionType).then((result) =>
            handlePermissionStatus(result, permissionType),
          );
          break;
        case RESULTS.LIMITED:
          break;
        case RESULTS.GRANTED:
          return true;
        case RESULTS.BLOCKED:
          return false;
      }
    },
    [requestPermission],
  );

  const showPermissionBlockedAlert = () => {
    Alert.alert(
      `Alert`,
      `Please enable permissions`,
      [
        {
          text: 'Cancel',
        },
        {
          text: 'Settings',
          onPress: () => openSettings(),
        },
      ],
      { cancelable: false },
    );
  };

  return { isPermissionEnabled, requestPermission, requestMultiplePermission };
};

export default usePermissions;
