import { useSession } from '../totara/core';
import { useEffect, useState } from 'react';
import { getProgramDetails, onProgramEnrol } from '../../src/api/totaraApis';
import { Toast } from 'native-base';

const useProgramsDetails = ({ id }) => {
  const { apiToken, core } = useSession();
  const [loading, setLoading] = useState(true);
  const [programData, setProgramsData] = useState<ProgramDetailsType>();
  const [isLoading, setEnrollLoading] = useState(false);
  const [successEnrolled, setSuccessEnrolled] = useState(false);
  const getDetails = async () => {
    const response = await getProgramDetails({
      apiToken,
      userid: core?.user?.id,
      programid: id,
    });
    setLoading(false);
    if (response) setProgramsData(response);
  };

  useEffect(() => {
    getDetails();
  }, []);

  const onEnrollPress = async () => {
    setEnrollLoading(true);
    const response = await onProgramEnrol({ apiToken, programid: id });
    setSuccessEnrolled(true);
    getDetails();
    setEnrollLoading(false);
  };

  return {
    successEnrolled,
    setSuccessEnrolled,
    apiToken,
    loading,
    programData,
    isLoading,
    onEnrollPress,
  };
};

export default useProgramsDetails;
