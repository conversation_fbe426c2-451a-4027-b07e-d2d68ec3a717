import { TOTARA_BASE_URL } from "@api/api_client";
import { useSession } from "@totara/core";
import crashReportLogger from "@utils/crashlyticsLogger";
import axios from "axios";
import React, { useEffect, useState, useCallback } from "react";
import { I18nManager } from "react-native";

const useRecommended = () => {
    const [recommendedCourses, setRecommended] = useState(null);
    const [isLoading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const { apiToken } = useSession();


    const fetchRecommendedCourses = useCallback(async () => {
        setLoading(true);


        const apiUrl = TOTARA_BASE_URL;
        const requestData = {
            wstoken: apiToken,
            wsfunction: 'wstotara_get_onboardingcourses',
            moodlewsrestformat: 'json',
            // language: I18nManager.isRTL ? 'ar' : 'en', // Uncomment if language setting is required
        };
        const params = new URLSearchParams(requestData).toString();

        try {
            const response = await axios.get(`${apiUrl}?${params}`);
            setRecommended(response.data?.courses);
        } catch (error) {
            crashReportLogger(error as Error, {
                component: 'useRecomended hook',
                additionalInfo: 'Failed to fetch recommended courses'
            })
            console.error("Failed to fetch recommended courses:", error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [apiToken]);


    useEffect(() => {
        fetchRecommendedCourses();
    }, [fetchRecommendedCourses]);


    const refreshRecommendedCourses = async () => {
        fetchRecommendedCourses();
        setRefreshing(true);
    };

    return {
        recommendedCourses,
        isLoading,
        refreshing,
        refreshRecommendedCourses,
    };
};

export default useRecommended;
