import { useState } from 'react';
import { getUsernames, onSubmitReferCourse } from '../api/server_requests';
import { Toast } from 'native-base';
import { useSelector } from 'react-redux';
import { RootState } from '../totara/reducers';
import { useTheme } from '@theme/ThemeProvider';
import { Colors } from '@theme';

const useReferCourse = () => {
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [visible, setVisible] = useState(false);
  const [usernameLoading, setUsernameLoading] = useState(false);
  const [selectedUsername, setSelectedUsername] = useState('');
  const [username, setUsername] = useState('');
  const token = useSelector(
    (state: RootState) => state?.getWFMProfile?.response?.liferayaccesstoken,
  );
  const user = useSelector((state: RootState) => state?.getWFMProfile?.response);

  const { referCourse } = useSelector((state: RootState) => state.referCourseReducer);
  const [isSuccess, setSuccess] = useState(false);
  const { isDarkMode } = useTheme()
  const onReferSubmit = async ({
    email,
    username,
    comment,
  }: {
    email?: string;
    username?: string;
    comment?: string;
  }) => {
    setLoading(true);
    let body = {
      courseId: referCourse?.courseid,
      courseName: referCourse?.name,
      courseImage: referCourse?.image,
      referBy: global.liferayUser.id,
    };

    if (email) {
      body['email'] = email;
    } else {
      body['referTo'] = username;
    }

    if (comment) {
      body['comment'] = comment;
    }
    const result = await onSubmitReferCourse({ body, token });
    setLoading(false);
    if (result?.requestStatus?.status == 'SUCCESS') {
      setSuccess(true);
    } else {
      Toast.show({
        text: result?.title,
        position: 'bottom',
        type: 'danger',
        textStyle: { textAlign: 'left' },
        style: isDarkMode && { backgroundColor: Colors.notificationRedForDark }
      });
    }
  };

  const onSearchUsers = async (text: string) => {
    if (text.length > 2) setUsernameLoading(true);
    const response = await getUsernames(text, 'fullname', token);
    setUsernameLoading(false);
    if (response?.length) {
      setSuggestions(response);
      setVisible(true);

      return response;
    } else {
      setSuggestions([]);
      setVisible(false);
      return [];
    }
  };

  const onSearchUserByEmail = async (text: string, listVisibility = true) => {
    setUsernameLoading(true);
    const response = await getUsernames(text, 'email', token);

    setUsernameLoading(false);
    if (response.length) {
      setSuggestions(response);
      setVisible(listVisibility);

      return response;
    } else {
      setSuggestions([]);
      setVisible(false);

      if (listVisibility)
        Toast.show({
          text: 'No record found',
          position: 'bottom',
          type: 'danger',
          textStyle: { textAlign: 'left' },
        });
      return [];
    }
  };

  const onUserCrossIconPress = async () => {
    if (selectedUsername) {
      setSelectedUsername('');
      setUsername('');
    } else {
      await onSearchUsers(username);
    }
  };

  const onUserCrossIconPressEmail = async () => {
    if (selectedUsername) {
      setSelectedUsername('');
      setUsername('');
    } else {
      await onSearchUserByEmail(username);
    }
  };

  return {
    selectedUsername,
    username,
    usernameLoading,
    visible,
    onReferSubmit,
    isSuccess,
    setSuccess,
    loading,
    onSearchUsers,
    suggestions,
    setVisible,
    onUserCrossIconPress,
    setUsername,
    setSelectedUsername,
    onSearchUserByEmail,
    onUserCrossIconPressEmail,
  };
};

export default useReferCourse;
