import { useEffect, useState } from 'react';
import { getReferralHistory, onReadReferralStatus } from '../api/server_requests';
import { useSelector } from 'react-redux';

const useReferralHistory = ({ type }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [noRecord, setNoRecord] = useState(false);
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    const response = await getReferralHistory({ type, token });
    if (response?.items?.length > 0) {
      setData(response?.items);
    } else {
      setNoRecord(true);
    }
    setLoading(false);
    setRefreshLoading(false);
  };

  const onRefresh = () => {
    setRefreshLoading(true);
    getData();
  };

  const onViewCourse = (referralCourseId) => {
    const body = {
      referCourseId: referralCourseId,
      readStatus: true,
    };
    onReadReferralStatus({ body, token });
  };

  return {
    data,
    loading,
    noRecord,
    refreshLoading,
    onRefresh,
    onViewCourse,
  };
};

export default useReferralHistory;
