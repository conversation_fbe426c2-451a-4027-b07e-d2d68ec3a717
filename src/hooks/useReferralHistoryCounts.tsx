import { useEffect, useState } from 'react';
import { getReferralHistoryCount } from '../api/server_requests';
import { useIsFocused } from '@react-navigation/native';
import { useSelector } from 'react-redux';

const useReferralHistoryCounts = () => {
  const [data, setData] = useState([]);
  const isFocused = useIsFocused();
  const token = useSelector((state) => state?.getWFMProfile?.response?.liferayaccesstoken);

  useEffect(() => {
    (async () => {
      const response = await getReferralHistoryCount(token);
      setData(response);
    })();
  }, [isFocused]);

  return {
    received: data?.received ?? 0,
    sent: data?.sent ?? 0,
  };
};

export default useReferralHistoryCounts;
