/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef } from 'react';
import perf from '@react-native-firebase/perf';
import { useFocusEffect, useRoute } from '@react-navigation/native';

const useScreenPerformanceMonitor = () => {
  const route = useRoute();
  const screenName = route.name;
  const traceName = `${screenName}_LoadTime`;
  const screenLoadTrace = useRef<any>(null);

  const initialize = useCallback(() => {
    screenLoadTrace.current = perf().newTrace(traceName);
    screenLoadTrace.current.start();
  }, [traceName]);

  useFocusEffect(
    useCallback(() => {
      initialize();

      return () => {
        if (screenLoadTrace.current) {
          screenLoadTrace.current.stop();
          screenLoadTrace.current = null;
        }
      };
    }, [initialize]),
  );

  return { initialize };
};

export default useScreenPerformanceMonitor;
