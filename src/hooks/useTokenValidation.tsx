import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { TokenValidationService } from '../utils/TokenValidationService';

export const useTokenValidation = (navigation) => {
  const dispatch = useDispatch();

  const checkTokenExpiration = useCallback(async () => {
    await TokenValidationService.checkTokenExpiration(dispatch, navigation);
  }, [dispatch]);

  const clearTokenData = useCallback(async () => {
    await TokenValidationService.clearTokenData(dispatch, navigation);
  }, [dispatch]);

  return { checkTokenExpiration, clearTokenData };
};