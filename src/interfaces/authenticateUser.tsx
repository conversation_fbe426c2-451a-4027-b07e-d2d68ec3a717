export interface authenticateUser {
  email: string;
  expiresin: number;
  firstnameEN: string;
  fullnameEN: string;
  gender: string;
  isNew: boolean;
  lastnameEN: string;
  liferayaccesstoken: string;
  mobile: string;
  nationalityAR: string;
  nationalityEN: string;
  refreshtoken: string;
  scope: string;
  uaepasstoken: string;
  userId: number;
  userType: string;
}

export interface UAEPassCodeResponseType {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  refresh_token: string;
  status: string;
}

export interface UAEPassCodeReqType {
  accessToken: string;
  mobileNumber: string;
  userId: number | string;
  type: number;
  email: string;
}

export interface UAEPassOTPReqType {
  accessToken: string;
  mobileNumber: string;
  userId: number | string;
  type: number;
  email: string;
  code: string;
}

export interface UAEPassOTPResponseType {
  description: string;
  status: string;
}
