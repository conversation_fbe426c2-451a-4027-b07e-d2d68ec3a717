export interface RenderListItemProps {
  Icon: string;
  Text: string;
}

export type RenderListItemPropsType = {
  item: RenderListItemProps;
  index: number;
};

export type ScreenRouteProp = RouteProp<RootStackParamList, 'Categories'>;
export type ScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Categories'>;

export type CategoriesScreenProps = {
  route: ScreenRouteProp;
  navigation: ScreenNavigationProp;
};
