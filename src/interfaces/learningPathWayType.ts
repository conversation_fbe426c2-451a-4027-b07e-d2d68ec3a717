export type LearningPathwayType = {
  name: string;
  months: string;
  courses: string;
  overDue: string;
  provider: string;
  id: number;
  backgroundColor?: string;
  progress?: any;
  showInProgress?: boolean;
  isDetail?: boolean;
  itemStyle?: any;
  showPeopleJoined?: boolean;
  showMore?: boolean;
  image?: string;
  rating: number;
  showRating?: boolean;
  showCurrentModule?: boolean;
  image?: string;
  providerLogo?: string
  rating?: any
  totalContent?: string
  duration?: string;
  isPersonal?: boolean
  type?: string
  item?: any;
  showFav?: boolean;
  peopleJoined?: number;
  isSelectMode?: boolean;
  onSelect?: () => void;
  onUnselect?: () => void;
  onMorePress?: () => void;
};
