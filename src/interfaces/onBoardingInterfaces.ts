export interface GenerateCodeType {
  description?: string;
  status?: string;
}

export interface RenderPreferredContactsProps {
  setVisibility?: React.Dispatch<React.SetStateAction<boolean>>;
  setCount?: React.Dispatch<React.SetStateAction<number>>;
  toggleModal?: () => void;
  setVerifyEmailView?: React.Dispatch<React.SetStateAction<boolean>>;
  originalEmail?: string;
  alternateEmail?: string;
  selectedPrefferedPhone?: any;
  onChangeEmail?: any;
  onPress?: any;
  getCountryCode?: any;
  codeCountry?: string;
  isLoading?: boolean;
  originalPhone?: string;
  alternatePhone?: string;
  info?: Object;
  email?: string;
}
