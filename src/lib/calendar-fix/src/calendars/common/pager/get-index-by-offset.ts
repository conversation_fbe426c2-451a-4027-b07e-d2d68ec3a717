import { I18nManager, Platform } from 'react-native';


export const getIndexByOffset = (
  offset: number,
  option: {
    length: number;
    indexMin?: number;
    indexMax: number;
  },
): number => {
  const { indexMax, indexMin = 0, length } = option;
  if (length <= 0) {
    if (__DEV__) {
      throw new Error('The length cannot be <= 0');
    }
    return 0;
  }

  const calc = Math.trunc((offset + length / 2) / length);
  if (calc < indexMin) {
    return indexMin;
  } else if (calc > indexMax) {
    return indexMax;
  } else {
    return calc;
  }
};

// RTL-aware scroll offset calculation for scroll events only
export const getIndexByOffsetRTL = (
  offset: number,
  option: {
    length: number;
    indexMin?: number;
    indexMax: number;
  },
): number => {
  const { indexMax, indexMin = 0, length } = option;
  if (length <= 0) {
    if (__DEV__) {
      throw new Error('The length cannot be <= 0');
    }
    return Math.max(indexMin, Math.floor(indexMax / 2)); // Return middle index instead of 0
  }

  let adjustedOffset = offset;
  if (I18nManager.isRTL) {
    const totalWidth = indexMax * length;
    adjustedOffset = totalWidth - offset - length;
    adjustedOffset = Math.max(0, adjustedOffset);
  }

  const calc = Math.trunc((adjustedOffset + length / 2) / length);
  
  // Enhanced bounds checking to prevent index 0 on Android Arabic
  if (calc < indexMin) {
    return indexMin;
  } else if (calc >= indexMax) {
    // Use >= to prevent out of bounds
    return indexMax - 1;
  } else if (calc === 0 && I18nManager.isRTL && Platform.OS === 'android') {
    // Android Arabic RTL fix: Never return index 0, return a safe middle index instead
    return Math.max(1, Math.floor(indexMax / 2));
  } else {
    return calc;
  }
};
