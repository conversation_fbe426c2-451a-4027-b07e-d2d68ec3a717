import React, {createContext, PropsWithChildren, useMemo} from 'react';
import dayjs, {ConfigType} from 'dayjs';
import {createRequiredContextValueHook} from '../../../utils/react-hooks';
type DayjsLocale = string | ILocale;
export type Locale = DayjsLocale;
type LocaleVal = {
  localedDayjs: (config?: ConfigType) => dayjs.Dayjs;
  weekDays: string[];
  months: string[];
  weekStart: number;
};
const LocaleContext = createContext<LocaleVal | undefined>(undefined);
type LocaleProviderProps = PropsWithChildren<{
  locale: Locale | undefined;
}>;
const LocaleProvider = ({locale, children}: LocaleProviderProps) => {
  // Force English locale for all internal calculations to prevent Android Arabic issues
  // while keeping display elements in the requested locale
  const localedDayjs = useMemo<(config?: ConfigType) => dayjs.Dayjs>(() => {
    // Always use English locale for date calculations to ensure consistency across platforms
    return (config?: ConfigType) => dayjs(config).locale('en');
  }, []);
  
  const result = useMemo<LocaleVal>(() => {
    // Use the requested locale for UI elements (months, weekdays, week start)
    const displayLocale = locale !== undefined ? locale : 'en';
    const displayDayjs = dayjs().locale(displayLocale);
    const localeData = displayDayjs.localeData();
    
    return {
      localedDayjs, // This uses English for calculations
      months: localeData.months(), // This uses requested locale for display
      weekDays: localeData.weekdaysMin(), // This uses requested locale for display
      weekStart: localeData.firstDayOfWeek(), // This uses requested locale for display
    };
  }, [locale]);
  
  return <LocaleContext.Provider value={result} children={children} />;
};
export default LocaleProvider;
export const useLocale = createRequiredContextValueHook(
  LocaleContext,
  'useLocale',
  'LocaleProvider',
);
export const useLocaledDayjs = () => {
  return useLocale().localedDayjs;
};
export type LDayjs = ReturnType<typeof useLocaledDayjs>;
