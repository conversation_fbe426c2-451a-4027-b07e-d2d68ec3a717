import dayjs from 'dayjs';
import memoizeOne from 'memoize-one';
import {fDay, setNoon} from '../../../utils/day';
import type {LDayjs, MonthPageIndex} from '../../common';
const normalizePageStart = (day: dayjs.Dayjs) => {
  return setNoon(day.startOf('month').utc(true));
};
const normalizePageEnd = (day: dayjs.Dayjs) => {
  return setNoon(day.endOf('month').utc(true));
};
export const getMonthPageStartOrDefault = (
  propValue: MonthPageIndex | dayjs.Dayjs | string | undefined,
  ldayjs: LDayjs,
) => {
  return normalizePageStart(
    (() => {
      if (propValue === undefined) {
        return ldayjs().subtract(2, 'year'); // Reduced from 5 to 2 years to prevent index 0 issues
      } else if (dayjs.isDayjs(propValue)) {
        return propValue;
      } else if (typeof propValue === 'string') {
        return ldayjs(propValue);
      } else {
        const {year, month} = propValue;
        return ldayjs().year(year).month(month);
      }
    })(),
  );
};
export const getMonthPageEndOrDefault = (
  propValue: MonthPageIndex | dayjs.Dayjs | string | undefined,
  ldayjs: LDayjs,
) => {
  return normalizePageEnd(
    (() => {
      if (propValue === undefined) {
        return ldayjs().add(2, 'year'); // Reduced from 5 to 2 years for better performance
      } else if (dayjs.isDayjs(propValue)) {
        return propValue;
      } else if (typeof propValue === 'string') {
        return ldayjs(propValue);
      } else {
        const {year, month} = propValue;
        return ldayjs().year(year).month(month);
      }
    })(),
  );
};
export const createMonthIndexes = memoizeOne(
  (start: dayjs.Dayjs, end: dayjs.Dayjs): MonthPageIndex[] => {
    end = end.add(1, 'month');
    if (start.isSameOrAfter(end, 'month')) {
      return [];
    }
    const result: MonthPageIndex[] = [];
    const monthCount = end.diff(start, 'month');
    for (let i = 0; i < monthCount; ++i) {
      result.push({
        year: start.year(),
        month: start.month(),
      });
      start = start.add(1, 'month');
    }
    return result;
  },
  (newInputs, lastInputs) => {
    return newInputs.every((arg1, index) => {
      return fDay(arg1) === fDay(lastInputs[index]!);
    });
  },
);
export const getMonthPageArrayIndex = (
  indexes: ReadonlyArray<MonthPageIndex>,
  {year, month}: MonthPageIndex,
) => {
  const foundIndex = indexes.findIndex(
    (pIndex) => pIndex.year === year && pIndex.month === month,
  );
  
  // If month not found, find the closest month instead of returning -1
  if (foundIndex === -1) {
    // Find the index closest to the target date using dayjs with 'en' locale
    const targetDate = dayjs().locale('en').year(year).month(month).startOf('month');
    let closestIndex = 0;
    let closestDiff = Infinity;
    
    indexes.forEach((pageIndex, index) => {
      const pageDate = dayjs().locale('en').year(pageIndex.year).month(pageIndex.month).startOf('month');
      const diff = Math.abs(targetDate.valueOf() - pageDate.valueOf());
      if (diff < closestDiff) {
        closestDiff = diff;
        closestIndex = index;
      }
    });
    
    return closestIndex;
  }
  
  return foundIndex;
};
