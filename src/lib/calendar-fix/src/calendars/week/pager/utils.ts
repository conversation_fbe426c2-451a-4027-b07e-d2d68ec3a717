import dayjs from 'dayjs';
import type {WeekPageIndex, LDayjs} from '../../common';
import memoizeOne from 'memoize-one';
import {fDay, setNoon, WEEK_LENGTH} from '../../../utils/day';
const normalizePageStart = (day: dayjs.Dayjs) => {
  return setNoon(day.startOf('week').utc(true));
};
const normalizePageEnd = (day: dayjs.Dayjs) => {
  return setNoon(day.endOf('week').utc(true));
};
export const getWeekPageStartOrDefault = (
  propValue: WeekPageIndex | dayjs.Dayjs | string | undefined,
  ldayjs: LDayjs,
) => {
  return normalizePageStart(
    (() => {
      if (propValue === undefined) {
        return ldayjs().subtract(2, 'year'); // Reduced from 5 to 2 years to prevent index 0 issues
      } else if (dayjs.isDayjs(propValue)) {
        return propValue;
      } else if (typeof propValue === 'string') {
        return ldayjs(propValue);
      } else {
        const {year, dayOfYear} = propValue;
        return ldayjs().year(year).dayOfYear(dayOfYear);
      }
    })(),
  );
};
export const getWeekPageEndOrDefault = (
  propValue: WeekPageIndex | dayjs.Dayjs | string | undefined,
  ldayjs: LDayjs,
) => {
  return normalizePageEnd(
    (() => {
      if (propValue === undefined) {
        return ldayjs().add(2, 'year'); // Reduced from 5 to 2 years for better performance
      } else if (dayjs.isDayjs(propValue)) {
        return propValue;
      } else if (typeof propValue === 'string') {
        return ldayjs(propValue);
      } else {
        const {year, dayOfYear} = propValue;
        return ldayjs().year(year).dayOfYear(dayOfYear);
      }
    })(),
  );
};
export const createWeekIndexes = memoizeOne(
  (start: dayjs.Dayjs, end: dayjs.Dayjs): WeekPageIndex[] => {
    if (start.isSameOrAfter(end, 'week')) {
      return [];
    }
    const endYear = end.year();
    const result: WeekPageIndex[] = [];
    while (start.year() < endYear) {
      const dayCountInYear = start.endOf('year').dayOfYear();
      const year = start.year();
      const dayOfYear = start.dayOfYear();
      const weekCount = Math.trunc((dayCountInYear - dayOfYear) / WEEK_LENGTH);
      for (let i = 0; i <= weekCount; ++i) {
        result.push({
          year,
          dayOfYear: dayOfYear + WEEK_LENGTH * i,
        });
      }
      start = start.add(weekCount + 1, 'week');
    }
    const year = start.year();
    const dayOfYear = start.dayOfYear();
    const weekCount = Math.trunc((end.dayOfYear() - dayOfYear) / WEEK_LENGTH);
    for (let i = 0; i <= weekCount; ++i) {
      result.push({
        year,
        dayOfYear: dayOfYear + WEEK_LENGTH * i,
      });
    }
    return result;
  },
  (newInputs, lastInputs) => {
    return newInputs.every((arg1, index) => {
      return fDay(arg1) === fDay(lastInputs[index]!);
    });
  },
);
export const getWeekPageIndexNumber = (
  indexes: ReadonlyArray<WeekPageIndex>,
  {year, dayOfYear}: WeekPageIndex,
) => {
  const foundIndex = indexes.findIndex(
    (pIndex) => pIndex.year === year && pIndex.dayOfYear === dayOfYear,
  );
  
  // If week not found, find the closest week instead of returning -1
  if (foundIndex === -1) {
    // Find the index closest to the target date using dayjs with 'en' locale
    const targetDate = dayjs().locale('en').year(year).dayOfYear(dayOfYear);
    let closestIndex = 0;
    let closestDiff = Infinity;
    
    indexes.forEach((pageIndex, index) => {
      const pageDate = dayjs().locale('en').year(pageIndex.year).dayOfYear(pageIndex.dayOfYear);
      const diff = Math.abs(targetDate.valueOf() - pageDate.valueOf());
      
      if (diff < closestDiff) {
        closestDiff = diff;
        closestIndex = index;
      }
    });
    
    return closestIndex;
  }
  
  return foundIndex;
};
