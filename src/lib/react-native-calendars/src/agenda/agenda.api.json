{"name": "Agenda", "description": "Agenda component", "images": ["https://github.com/wix/react-native-calendars/blob/master/demo/assets/agenda.gif?raw=true"], "extends": ["CalendarList", "FlatList"], "extendsLink": ["https://github.com/wix/react-native-calendars/blob/master/src/calendar-list/index.tsx", "https://reactnative.dev/docs/flatlist"], "example": "https://github.com/wix/react-native-calendars/blob/master/example/src/screens/agenda.tsx", "props": [{"name": "items", "type": "AgendaSchedule", "description": "the list of items that have to be displayed in agenda. If you want to render item as empty date the value of date key has to be an empty array []. If there exists no value for date key it is considered that the date in question is not yet loaded"}, {"name": "loadItemsForMonth", "type": "(data: DateData) => void", "description": "Handler which gets executed when items for a certain month should be loaded (month became visible)"}, {"name": "onDayChange", "type": "(data: DateData) => void", "description": "Handler which gets executed when day changes while scrolling agenda list"}, {"name": "onCalendarToggled", "type": "(enabled: boolean) => void", "description": "Handler which gets executed when the calendar is opened or closed"}, {"name": "selected", "type": "string", "description": "initially selected day"}, {"name": "renderKnob", "type": "() => JSX.Element", "description": "Replace default agenda's knob with a custom one"}, {"name": "hideKnob", "type": "boolean", "description": "Whether to hide the knob"}, {"name": "showClosingKnob", "type": "boolean", "description": "Whether the knob should always be visible (when hideKnob = false)"}, {"name": "showOnlySelectedDayItems", "type": "boolean", "description": "Whether to show items only for the selected date"}, {"name": "renderEmptyData", "type": "() => JSX.Element", "description": "Replace default ActivityIndicator with a custom one"}]}