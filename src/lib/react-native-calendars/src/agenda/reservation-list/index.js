import isFunction from 'lodash/isFunction';
import PropTypes from 'prop-types';
import XDate from 'xdate';
import React, { useState, useEffect, useRef } from 'react';
import { ActivityIndicator, View, FlatList } from 'react-native';
import { extractReservationProps } from '../../componentUpdater';
import { sameDate } from '../../dateutils';
import { toMarkingFormat } from '../../interface';
import styleConstructor from './style';
import Reservation from './reservation';

const ReservationList = (props) => {
  const {
    items,
    selectedDay: initialSelectedDay,
    topDay,
    onDayChange,
    showOnlySelectedDayItems,
    renderEmptyData,
    onScroll,
    onScrollBeginDrag,
    onScrollEndDrag,
    onMomentumScrollBegin,
    onMomentumScrollEnd,
    refreshControl,
    refreshing,
    onRefresh,
    reservationsKeyExtractor,
  } = props;

  const style = styleConstructor(props.theme);
  const [reservations, setReservations] = useState([]);
  const heights = useRef([]);
  const selectedDay = useRef(initialSelectedDay);
  const scrollOver = useRef(true);
  const list = useRef();

  useEffect(() => {
    // updateDataSource(getReservations(props).reservations);
  }, []);

  useEffect(() => {
    if (topDay && !sameDate(topDay, props.topDay)) {
      setReservations([]);
      updateReservations(props);
    } else {
      updateReservations(props);
    }
  }, [props]);

  const updateDataSource = (reservations) => {
    setReservations(reservations);
  };

  const updateReservations = (props) => {
    const { selectedDay: newSelectedDay, showOnlySelectedDayItems } = props;
    const reservations = getReservations(props);

    if (
      !showOnlySelectedDayItems &&
      list.current &&
      !sameDate(newSelectedDay, selectedDay.current)
    ) {
      let scrollPosition = 0;
      for (let i = 0; i < reservations.scrollPosition; i++) {
        scrollPosition += heights.current[i] || 0;
      }
      scrollOver.current = false;
      list.current.scrollToOffset({
        offset: scrollPosition,
        animated: true,
      });
    }

    selectedDay.current = newSelectedDay;
    updateDataSource(reservations.reservations);
  };

  const getReservationsForDay = (iterator, props) => {
    const day = iterator.clone();
    const res = props.items?.[toMarkingFormat(day)];
    if (res && res.length) {
      return res.map((reservation, i) => ({
        reservation,
        date: i ? undefined : day,
      }));
    } else if (res) {
      return [{ date: iterator.clone() }];
    } else {
      return false;
    }
  };

  const getReservations = (props) => {
    const { selectedDay, showOnlySelectedDayItems } = props;
    if (!props.items || !selectedDay) {
      return { reservations: [], scrollPosition: 0 };
    }

    let reservations = [];
    if (reservations.length) {
      const iterator = reservations[0].date?.clone();
      if (iterator) {
        while (iterator.getTime() < selectedDay.getTime()) {
          const res = getReservationsForDay(iterator, props);
          if (!res) {
            reservations = [];
            break;
          } else {
            reservations = reservations.concat(res);
          }
          iterator.addDays(1);
        }
      }
    }

    const scrollPosition = reservations.length;
    const iterator = selectedDay.clone();
    if (showOnlySelectedDayItems) {
      const res = getReservationsForDay(iterator, props);
      if (res) {
        reservations = res;
      }
      iterator.addDays(1);
    } else {
      for (let i = 0; i < 31; i++) {
        const res = getReservationsForDay(iterator, props);
        if (res) {
          reservations = reservations.concat(res);
        }
        iterator.addDays(1);
      }
    }

    return { reservations, scrollPosition };
  };

  const handleScroll = (event) => {
    const yOffset = event.nativeEvent.contentOffset.y;
    onScroll?.(yOffset);

    let topRowOffset = 0;
    let topRow;
    for (topRow = 0; topRow < heights.current.length; topRow++) {
      if (topRowOffset + heights.current[topRow] / 2 >= yOffset) {
        break;
      }
      topRowOffset += heights.current[topRow];
    }

    const row = reservations[topRow];
    if (!row) return;
    const day = row.date;
    if (day) {
      if (!sameDate(day, selectedDay.current) && scrollOver.current) {
        selectedDay.current = day.clone();
        onDayChange?.(day.clone());
      }
    }
  };

  const onListTouch = () => {
    scrollOver.current = true;
  };

  const onRowLayoutChange = (index, event) => {
    heights.current[index] = event.nativeEvent.layout.height;
  };

  const handleMoveShouldSetResponderCapture = () => {
    onListTouch();
    return false;
  };

  const renderRow = ({ item, index }) => {
    const reservationProps = extractReservationProps(props);
    return (
      <View onLayout={(event) => onRowLayoutChange(index, event)}>
        <Reservation
          index={index}
          reservations={reservations}
          {...reservationProps}
          item={item.reservation}
          date={item.date}
        />
      </View>
    );
  };

  const keyExtractor = (item, index) => {
    return reservationsKeyExtractor?.(item, index) || `${item?.reservation?.day}${index}`;
  };

  if (!items || (initialSelectedDay && !items[toMarkingFormat(initialSelectedDay)])) {
    if (isFunction(renderEmptyData)) {
      return renderEmptyData?.();
    }
    return <ActivityIndicator style={style.indicator} color={props.theme?.indicatorColor} />;
  }

  return (
    <FlatList
      ref={list}
      style={props.style}
      contentContainerStyle={style.content}
      data={reservations}
      renderItem={renderRow}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={200}
      onMoveShouldSetResponderCapture={handleMoveShouldSetResponderCapture}
      onScroll={handleScroll}
      refreshControl={refreshControl}
      refreshing={refreshing}
      onRefresh={onRefresh}
      onScrollBeginDrag={onScrollBeginDrag}
      onScrollEndDrag={onScrollEndDrag}
      onMomentumScrollBegin={onMomentumScrollBegin}
      onMomentumScrollEnd={onMomentumScrollEnd}
    />
  );
};

ReservationList.displayName = 'ReservationList';
ReservationList.propTypes = {
  ...Reservation.propTypes,
  items: PropTypes.object,
  selectedDay: PropTypes.instanceOf(XDate),
  topDay: PropTypes.instanceOf(XDate),
  onDayChange: PropTypes.func,
  showOnlySelectedDayItems: PropTypes.bool,
  renderEmptyData: PropTypes.func,
  onScroll: PropTypes.func,
  onScrollBeginDrag: PropTypes.func,
  onScrollEndDrag: PropTypes.func,
  onMomentumScrollBegin: PropTypes.func,
  onMomentumScrollEnd: PropTypes.func,
  refreshControl: PropTypes.element,
  refreshing: PropTypes.bool,
  onRefresh: PropTypes.func,
  reservationsKeyExtractor: PropTypes.func,
};
ReservationList.defaultProps = {
  refreshing: false,
  selectedDay: new XDate(true),
};

export default ReservationList;
