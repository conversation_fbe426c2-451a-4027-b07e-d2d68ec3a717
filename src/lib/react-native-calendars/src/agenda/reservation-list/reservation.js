import isFunction from 'lodash/isFunction';
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { View, Text, ImageBackground } from 'react-native';
import { isToday } from '../../dateutils';
import { getDefaultLocale } from '../../services';
import { RESERVATION_DATE } from '../../testIDs';
import styleConstructor from './style';
import fonts from '@utils/fonts';
import fontSize, { isPad } from '@utils/fontSize';
import AssetsImages from '@theme/AssetsImages';
import { TextView } from '../../../../../components';
import { Dimensions } from 'react-native';

const windowSize = Dimensions.get('window');

class Reservation extends Component {
  static displayName = 'Reservation';
  static propTypes = {
    date: PropTypes.any,
    item: PropTypes.any,
    theme: PropTypes.object,
    rowHasChanged: PropTypes.func,
    renderDay: PropTypes.func,
    renderItem: PropTypes.func,
    renderEmptyDate: PropTypes.func,
  };
  style;
  constructor(props) {
    super(props);
    this.style = styleConstructor(props.theme);
  }
  shouldComponentUpdate(nextProps) {
    const d1 = this.props.date;
    const d2 = nextProps.date;
    const r1 = this.props.item;
    const r2 = nextProps.item;
    let changed = true;
    if (!d1 && !d2) {
      changed = false;
    } else if (d1 && d2) {
      if (d1.getTime() !== d2.getTime()) {
        changed = true;
      } else if (!r1 && !r2) {
        changed = false;
      } else if (r1 && r2) {
        if ((!d1 && !d2) || (d1 && d2)) {
          if (isFunction(this.props.rowHasChanged)) {
            changed = this.props.rowHasChanged(r1, r2);
          }
        }
      }
    }
    return changed;
  }
  renderDate() {
    const { item, date, renderDay } = this.props;
    if (isFunction(renderDay)) {
      return renderDay(date, item);
    }
    const today = date && isToday(date) ? this.style.today : undefined;
    const dayNames = getDefaultLocale().dayNamesShort;
    if (date) {
      return (
        <View
          style={[
            this.style.day,
            {
              paddingEnd: 15,
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 10,
            },
          ]}
          testID={RESERVATION_DATE}
        >
          <Text
            allowFontScaling={false}
            style={[
              this.style.dayNum,
              today,
              {
                fontSize: isPad ? fontSize.large : fontSize.semiSmall,
                color: '#000',
                textAlign: 'right',
                fontFamily: fonts.medium,
              },
            ]}
          >
            {date.getDate()}
          </Text>
          <Text
            allowFontScaling={false}
            style={[
              this.style.dayText,
              today,
              {
                fontSize: isPad ? fontSize.large : fontSize.semiSmall,
                color: '#000',
                textAlign: 'right',
                fontFamily: fonts.medium,
              },
            ]}
          >
            {dayNames ? dayNames[date.getDay()] : undefined}
          </Text>
        </View>
      );
    }
    return <View style={this.style.day} />;
  }
  render() {
    const { item, date, renderItem, renderEmptyDate } = this.props;
    let content;
    if (item) {
      const firstItem = date ? true : false;
      if (isFunction(renderItem)) {
        content = renderItem(item, firstItem);
      }
    } else if (isFunction(renderEmptyDate)) {
      content = renderEmptyDate(date);
    }

    const Reservations = this?.props?.reservations;
    const index = this?.props?.index;

    return (
      <View style={this.style.container}>
        {this.renderDate()}

        <View style={this.style.innerContainer}>
          {content}
          {/* {item?.tag !== "no event" ? (
            content
          ) : (
            <ImageBackground
              resizeMode="cover"
              source={AssetsImages.linebg}
              style={{
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
                height: 70,
                width: "100%",
              }}
            >
              {Reservations?.[index - 1]?.reservation?.tag !== "no event" && (
                <TextView
                  style={{
                    color: "#000",
                    textAlign: "center",
                    fontFamily: fonts.medium,
                    fontSize: fontSize.large,
                  }}
                  text="You have a spare day"
                />
              )}
            </ImageBackground>
          )} */}
        </View>
      </View>
    );
  }
}
export default Reservation;
