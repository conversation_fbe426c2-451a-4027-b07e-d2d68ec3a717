import { StyleSheet } from 'react-native';
import * as defaultStyle from '../../style';
// import { fonts, fontSize } from "../../../../../theme";
export default function styleConstructor(theme = {}) {
  const appStyle = { ...defaultStyle, ...theme };
  return StyleSheet.create({
    container: {
      flexDirection: 'row',
      width: '90%',
    },
    innerContainer: {
      //   flex: 1,
      borderLeftWidth: 1,
      borderColor: 'rgba(237, 233, 233, 1)',
      paddingStart: 10,
      width: '100%',
    },
    dayNum: {
      color: appStyle.agendaDayNumColor,
    },
    dayText: {
      fontSize: 14,
      fontWeight: appStyle.textDayFontWeight,
      fontFamily: appStyle.textDayFontFamily,
      color: appStyle.agendaDayTextColor,
      backgroundColor: 'rgba(0,0,0,0)',
      //   marginTop: -5,
    },
    day: {
      width: 63,
      alignItems: 'center',
      justifyContent: 'flex-start',
      //   marginTop: 32,
    },
    today: {
      color: appStyle.agendaTodayColor,
    },
    indicator: {
      //   marginTop: 80,
    },
    ...(theme['stylesheet.agenda.list'] || {}),
  });
}
