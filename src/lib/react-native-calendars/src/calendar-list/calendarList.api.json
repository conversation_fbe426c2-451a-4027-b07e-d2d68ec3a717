{"name": "CalendarList", "description": "Calendar list component", "images": ["https://github.com/wix/react-native-calendars/blob/master/demo/assets/calendar-list.gif?raw=true"], "extends": ["Calendar", "FlatList"], "extendsLink": ["https://github.com/wix/react-native-calendars/blob/master/src/calendar/index.tsx", "https://reactnative.dev/docs/flatlist"], "example": "https://github.com/wix/react-native-calendars/blob/master/example/src/screens/calendarsList.tsx", "props": [{"name": "pastScrollRange", "type": "number", "description": "Max amount of months allowed to scroll to the past", "default": "50"}, {"name": "futureScrollRange", "type": "number", "description": "Max amount of months allowed to scroll to the future", "default": "50"}, {"name": "calendarStyle", "type": "ViewStyle", "description": "Specify style for calendar container element"}, {"name": "calendarHeight", "type": "number", "description": "Dynamic calendar height", "default": "360"}, {"name": "calendarWidth", "type": "number", "description": "Used when calendar scroll is horizontal, (when pagingEnabled = false)", "default": "Device width"}, {"name": "staticHeader", "type": "boolean", "description": "Whether to use a fixed header that doesn't scroll (when horizontal = true)"}, {"name": "showScrollIndicator", "type": "boolean", "description": "Whether to enable or disable vertical / horizontal scroll indicator", "default": "false"}]}