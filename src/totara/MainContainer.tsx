/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2021 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD’s customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */

import React, { useEffect } from 'react';
import { AppState, Linking, Platform } from 'react-native';
import { useMutation, useQuery } from '@apollo/client';
import { useGetCourseType } from '@hooks/useGetCourseType.tsx';
import { navigate } from '@navigation/navigationService';
import messaging from '@react-native-firebase/messaging';
import { useNavigation } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AIAssistant from '@screens/AIAssistant';
import AIChatHistory from '@screens/AIChatHistory';
import AssessmentScreen from '@screens/Assessment/AssessmentScreen';
import PrinciplesYouWelcomeScreen from '@screens/Assessment/components/PrinciplesYouWelcomeScreen/index.tsx';
import CaseStudyBankList from '@screens/CaseStudyBanks';
import { CommunitiesScreen } from '@screens/Communities/index.tsx';
import CommunityDetailsScreen from '@screens/CommunityDetails/index.tsx';
import CommunityStartDiscussion from '@screens/CommunityStartDiscussion/index.tsx';
import MeetYourPeerDetails from '@screens/MeetYourPeerDetails/index.tsx';
import MeetYourPearsList from '@screens/MeetYourPeersList/index.tsx';
import MultipleSession from '@screens/MultipleSession';
import ProgramDetailsCertificatesScreen from '@screens/ProgramDetails/ProgramDetailsCertificatesScreen.tsx';
import ProgramDetailsCoursesScreen from '@screens/ProgramDetails/ProgramDetailsCoursesScreen.tsx';
import ProgramDetailsScreen from '@screens/ProgramDetails/ProgramDetailsScreen.tsx';
import SelectCohort from '@screens/SelectCohort';
import LoginMethod from '@screens/Settings/LoginMethod/index.tsx';
import AboutStack from '@totara/features/about/AboutStack';
import { scormStack } from '@totara/features/activities/scorm/ScormActivity';
import { registerPushNotifications } from '@totara/lib/notificationService';
import crashReportLogger from '@utils/crashlyticsLogger.tsx';
import { analyticsService } from 'analytics/AnalyticsService';
import { showMessage } from 'react-native-flash-message';
import { useDispatch, useSelector } from 'react-redux';
import { updateFcmToken } from '../../src/api/server_requests';
import MyNetworkCommentsSection from '../../src/screens/MyNetwork/components/CommentsSections';
import { updateTotoraProfile } from '../api/totaraApis';
import {
  AchievementList,
  AnnouncementDetail,
  AttachmentView,
  Biometrics,
  Calendar,
  Categories,
  CategoryDetail,
  CategoryListing,
  ContactUs,
  DiscoverListing,
  EarnedBadge,
  EditFormTelentProfile,
  EducationList,
  Endorsement,
  FaqsDetail,
  ImageViewer,
  InProgressCourses,
  MentorShip,
  MentorShipDetails,
  MyBadges,
  MyCourseList,
  MyFavorites,
  MyLearningPathways,
  MyLearningRecords,
  MyLearningRecordsCertificates,
  MyNetwork,
  NewJoinerScreen,
  Notification,
  RecommendationList,
  ReferHistory,
  RequiredLearning,
  Search,
  SelectedCategoryResults,
  SelectedSubCategoryResults,
  SelectedSubToSubCategoryResults,
  Settings,
  SkillIWantToLearn,
  SkillList,
  StreakHistory,
  Streaks,
  SupportCenter,
  SupportCenterDetailScreen,
  SupportCenterFilter,
  SupportCenterMyRequestDetailScreen,
  SupportCenterMyRequestScreen,
  SupportCenterOld,
  TrainingList,
  UpdateLearningGoals,
  ViewCertificates,
  WorkExperienceList,
} from '../screens';
import useOfflineStatus from '../screens/OfflineCoursesSpace/hooks/useOfflineStatus.tsx';
import { OfflineStack } from '../screens/OfflineCoursesSpace/navigation/index.tsx';
import MyDownloadsScreen from '../screens/OfflineCoursesSpace/screens/MyDownloads/index.tsx';
import CuratorFilterScreen from '../screens/Search/CuratorFilterScreen.tsx';
import SearchFilterScreen from '../screens/Search/SearchFilterScreen.tsx';
import { tokenSent, updateToken } from './actions/notification';
import Header from './components/Header';
import TotaraNavigationOptions from './components/NavigationOptions';
import { useSession } from './core';
import WebViewCourseStack from './features/activities/webview/WebViewCourseStack';
import WebViewStack from './features/activities/webview/WebViewStack';
import HistoryScreen from './features/currentLearning/HistoryScreen';
import { EnrolmentModal } from './features/enrolment/EnrolmentModal';
import FindLearningWebViewWrapper from './features/findLearning/FindLearningWebViewWrapper';
import { OverviewModal } from './features/findLearning/OverviewModal';
import { mutationForToken, notificationsQuery } from './features/notifications/api';
import { cardModalOptions, horizontalAnimation, navigateByRef, NAVIGATION } from './lib/navigation';
import { translate } from './locale';
import { RootState } from './reducers';
import TabContainer from './TabContainer';
import EditLanguageScreen from '@screens/EditFormTelentProfile/EditLanguageScreen.tsx';
import { NewCalendarScreen } from '@screens';

const { SCORM_STACK_ROOT, ABOUT, WEBVIEW_ACTIVITY, COURSE_WEBVIEW_ACTIVITY } = NAVIGATION;

const Stack = createStackNavigator();

const StackRequiredLearning = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: ({ current, next, inverted, layouts }) => {
          const translateX = current.progress.interpolate({
            inputRange: [0, 1],
            outputRange: [layouts.screen.width, 0],
          });
          return {
            cardStyle: {
              transform: [
                {
                  translateX,
                },
              ],
            },
          };
        },
      }}
      initialRouteName="MyLearningPathways"
    >
      <Stack.Screen
        name="MyLearningPathways"
        component={MyLearningPathways}
        options={{ headerShown: false }}
      />
      <Stack.Screen name="CourseDetail" component={CategoryDetail} />
      <Stack.Screen name="NewCalendar" component={NewCalendarScreen} options={{ headerShown: false }} />
      <Stack.Screen
        name="NewJoinerPrograms"
        component={NewJoinerScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

const detaultScreenOptions = TotaraNavigationOptions({
  backTitle: translate('general.back'),
});
const detaultScreenOptionsHistory = TotaraNavigationOptions({});

const MainContainer = () => {
  const notificationState = useSelector((state: RootState) => state.notificationReducer);
  const { client } = useQuery(notificationsQuery);
  const [sendToken] = useMutation(mutationForToken);
  const dispatch = useDispatch();
  const { apiToken, core } = useSession();
  const { isConnected } = useOfflineStatus();
  const { onGetCourseType } = useGetCourseType();

  const getUrl = async ({ url }) => {
    try {
      if (url == null) return;

      let queryParams = {};
      let urlParts = url.split('?');
      let path = urlParts[0];

      if (urlParts.length > 1) {
        const queryString = urlParts[1];
        queryParams = queryString.split('&').reduce((acc, item) => {
          if (item.includes('=')) {
            const [key, value] = item.split('=');
            acc[key] = value;
          } else {
            const [key, value] = item.split('/');
            if (key && value) {
              acc[key] = value;
            }
          }
          return acc;
        }, {});
      }

      const marketingParams = {};
      const utmParams = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
        'referrer',
        'gclid',
        'fbclid',
      ];

      let hasMarketingParams = false;
      utmParams.forEach((param) => {
        if (queryParams[param]) {
          marketingParams[param] = queryParams[param];
          hasMarketingParams = true;
        }
      });

      if (hasMarketingParams) {
        await analyticsService.logEvent('deeplink_marketing_params', marketingParams);
      }

      if (!queryParams?.courseid) {
        return;
      }

      const item = await onGetCourseType(queryParams?.courseid);

      const isMultiDay =
        (item?.course_day == 'Multi Day' && item?.course_type != 'Self-paced') ||
        (item?.course_day == 'Multi Day' && item?.course_type != 'Self-Paced');

      const route =
        item?.course_type == 'Self-paced' || item?.course_type == 'Self-Paced'
          ? 'CourseDetail'
          : isMultiDay
            ? 'MultipleSession'
            : 'CourseDetail';

      if (queryParams?.action == 'attendance') {
        navigate(route, {
          courseid: queryParams?.courseid,
          seminarid: queryParams?.seminarid,
          sessionid: queryParams?.sessionid,
        });
      } else if (queryParams?.courseid) {
        navigate(route, { courseid: queryParams.courseid });
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'mainContainer getUrl',
        additionalInfo: 'Error occured wiht deep link handling',
      });
    }
  };

  useEffect(() => {
    Linking.getInitialURL()
      .then((url) => {
        if (url) {
          getUrl({ url });
        }
      })
      .catch((err) => {
        crashReportLogger(err as Error, {
          component: 'MainContainer',
          additionalInfo: 'Failed to fetch initial URL',
        });
      });

    const subscription = Linking.addEventListener('url', (event) => {
      getUrl({ url: event.url });
    });
    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    if (core?.user?.id && notificationState?.token) {
      updateProfile(notificationState.token);
    }
  }, [core, notificationState]);

  const { markFCMTokenSent } = require('../utils/notifications/notificationPermissions');

  const handleNotificationReceived = (remoteMessage) => {
    if (remoteMessage) {
      console.log('>>> handleNotificationReceived Main Container', JSON.stringify(remoteMessage));
      fetchNotifications(client);
      if (remoteMessage?.data?.notification === '1') {
        navigateByRef('Notifications', {});
      }
    }
  };

  const fetchNotifications = (client) => {
    if (client) {
      client.query({
        query: notificationsQuery,
        fetchPolicy: 'network-only',
        errorPolicy: 'ignore',
      });
    }
  };

  useEffect(() => {
    subscribeTopic();
  }, []);

  const subscribeTopic = () => {
    messaging()
      .subscribeToTopic('all')
      .then(() => console.warn('Subscribed to topic!'));
  };

  useEffect(() => {
    if (notificationState?.tokenSent) return;

    registerPushNotifications()
      .then((token) => {
        sendToken({ variables: { token } })
          .then((success) => {
            if (success) {
              dispatch(updateToken({ token: token }));
              dispatch(tokenSent({ tokenSent: true }));
              updateProfile(token);
            } else {
              crashReportLogger({} as Error, {
                component: 'MainContainer sendToken',
                additionalInfo: 'Failed to update FCM token',
              });
            }
          })
          .catch((err) => {
            crashReportLogger(err as Error, {
              component: 'MainContainer sendToken catch',
              additionalInfo: 'Failed to update FCM token',
            });
          });
      })
      .catch((err) => {
        crashReportLogger(err as Error, {
          component: 'MainContainer registerPushNotifications',
          additionalInfo: 'Failed to update FCM token',
        });
      });
  }, []);

  const talentProfile = useSelector((state) => state?.getWFMProfile?.response);

  const updateProfile = async (fcmToken) => {
    if (fcmToken) {
      try {
        await updateTotoraProfile(apiToken, core?.user?.id, { fcmtoken: fcmToken });
        if (core?.user?.email) {
          const data = {
            deviceToken: fcmToken,
            email: talentProfile?.email,
          };
          await updateFcmToken(data, talentProfile?.liferayaccesstoken);
        }
        await markFCMTokenSent(true);
      } catch (error) {
        crashReportLogger(error as Error, {
          component: 'MainContainer updateProfile',
          additionalInfo: 'Failed to update FCM token',
        });
        console.error('Error updating FCM token:', error);
      }
    }
  };

  useEffect(() => {
    messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        fetchNotifications(client);
        handleNotificationReceived(remoteMessage);
      });

    messaging().onTokenRefresh((token) => {
      dispatch(updateToken({ token: token }));
    });

    const unsubscribe = messaging().onMessage((message) => {
      console.log('>>> onMessage Main Container', JSON.stringify(message));
      fetchNotifications(client);
      const data = {
        description:
          message?.notification?.body ||
          '<p></p><p>The Abu Dhabi School of Government (ADSG), the leading government platform for developing human capital in Abu Dhabi, a subsidiary of the Department of Government Support, and Abu Dhabi Global Market Academy (ADGM Academy) have launched the ‘Investment Foundation program, which seeks to equip Abu Dhabi government employees with knowledge and skills related to finance, ethics, and investment practices.</p><p>Targeting Abu Dhabi government employees working in the financial sector whose practical experience ranges between one and five years, the program features a blend of interactive face-to-face training and remote learning, as well as group work, open debates, and tutorial support.</p><br><p></p>',
        type: message?.data?.type || 'new_released_course',
        courseId: message?.data?.courseid || '408',
        image: message?.data?.image || '',
        created: message?.data?.date,
      };

      showMessage({
        message: message?.notification?.title,
        description: data,
      });
    });

    return unsubscribe;
  }, []);

  if (!isConnected) {
    return <OfflineStack />;
  }

  return (
    <Stack.Navigator
      initialRouteName="TabContainer"
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: ({ current, next, inverted, layouts }) => {
          const translateX = current.progress.interpolate({
            inputRange: [0, 1],
            outputRange: [layouts.screen.width, 0],
          });
          return {
            cardStyle: {
              transform: [
                {
                  translateX,
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="TabContainer" component={TabContainer} />
      <Stack.Screen name={NAVIGATION.MY_DOWNLOADS} component={MyDownloadsScreen} />
      <Stack.Screen name="CourseDetail" component={CategoryDetail} />
      <Stack.Screen
        name="MyLearningPathways"
        component={StackRequiredLearning}
        options={{ headerShown: false }}
      />
      <Stack.Screen name="MultipleSession" component={MultipleSession} />
      <Stack.Screen name="SelectCohort" component={SelectCohort} />
      <Stack.Screen name="CommunityDetailsScreen" component={CommunityDetailsScreen} />
      <Stack.Screen
        name="AIAssistant"
        component={AIAssistant}
        options={({ navigation }) => ({
          headerShown: false,
          tabBarStyle: { display: 'none' },
        })}
      />
      <Stack.Screen
        name="AIChatHistory"
        component={AIChatHistory}
        options={({ navigation }) => ({
          headerShown: false,
          tabBarStyle: { display: 'none' },
        })}
      />
      <Stack.Screen name={NAVIGATION.MY_NETWORK_COMMUNITIES} component={CommunitiesScreen} />
      <Stack.Screen
        name={NAVIGATION.MY_NETWORK_START_DISCUSSION}
        component={CommunityStartDiscussion}
      />

      <Stack.Screen name="ProgramDetailsCourses" component={ProgramDetailsCoursesScreen} />
      <Stack.Screen name="ProgramDetails" component={ProgramDetailsScreen} />
      <Stack.Screen name="EarnedBadge" component={EarnedBadge} />
      <Stack.Screen name="MyBadges" component={MyBadges} />
      <Stack.Screen
        name="ProgramDetailsCertificates"
        component={ProgramDetailsCertificatesScreen}
      />
      <Stack.Screen name="MentorShipDetails" component={MentorShipDetails} />
      <Stack.Screen name="MentorShip" component={MentorShip} />
      <Stack.Screen
        name="ProgramDetailsViewCertificates"
        component={ViewCertificates}
        initialParams={{
          backgroundColor: 'white',
        }}
      />
      <Stack.Screen
        name={NAVIGATION.MY_NETWORK_COMMENTS_SECTION}
        component={MyNetworkCommentsSection}
      />
      <Stack.Screen name={NAVIGATION.ASSESSMENT} component={PrinciplesYouWelcomeScreen} />
      <Stack.Screen name={NAVIGATION.ASSESSMENT_TEST} component={AssessmentScreen} />
      <Stack.Screen
        name="HistoryScreen"
        component={HistoryScreen}
        options={({ navigation }) => ({
          header: () => (
            <Header
              navigation={navigation}
              title={translate('current_learning.history')}
              nullProp={undefined}
              isProfile={undefined}
            />
          ),
          headerShown: true,
        })}
      />
      <Stack.Screen name={SCORM_STACK_ROOT} component={scormStack} />
      <Stack.Screen name={WEBVIEW_ACTIVITY} component={WebViewStack} />
      <Stack.Screen name={ABOUT} component={AboutStack} />
      <Stack.Screen
        name={NAVIGATION.FIND_LEARNING_OVERVIEW}
        component={OverviewModal}
        options={cardModalOptions}
      />
      <Stack.Screen
        name={NAVIGATION.ENROLMENT_MODAL}
        component={EnrolmentModal}
        options={cardModalOptions}
      />
      <Stack.Screen
        name={NAVIGATION.FIND_LEARNING_WEBVIEW}
        component={FindLearningWebViewWrapper}
        options={({ route }: any) => ({
          ...horizontalAnimation,
          ...detaultScreenOptions,
          gestureDirection: 'horizontal',
          headerTitle: route.params.title,
        })}
      />
      <Stack.Screen name="Notification" component={Notification} />
      <Stack.Screen name="Calendar" component={NewCalendarScreen} options={{ headerShown: false }} />
      <Stack.Screen name={'UpdateLearningGoals'} component={UpdateLearningGoals} />
      <Stack.Screen name={'StreakHistory'} component={StreakHistory} />
      <Stack.Screen name={'Streaks'} component={Streaks} />
      <Stack.Screen
        name={NAVIGATION.COURSE_WEBVIEW_ACTIVITY}
        component={WebViewCourseStack}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen name="InProgressCourses" component={InProgressCourses} />
      <Stack.Screen name="ViewCertificates" component={ViewCertificates} />
      <Stack.Screen name="RequiredLearning" component={RequiredLearning} />
      <Stack.Screen name="MyLearningRecords" component={MyLearningRecords} />
      <Stack.Screen name="CaseStudyBanksList" component={CaseStudyBankList} />
      <Stack.Screen
        name="MyLearningRecordsCertificates"
        component={MyLearningRecordsCertificates}
      />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="SearchFilter" component={SearchFilterScreen} />
      <Stack.Screen name="CuratorFilterScreen" component={CuratorFilterScreen} />
      <Stack.Screen name="CategoryListing" component={CategoryListing} />
      <Stack.Screen name="Categories" component={Categories} />
      <Stack.Screen name="NewSettings" component={Settings} />
      <Stack.Screen name="LoginMethod" component={LoginMethod} />
      <Stack.Screen name="SupportCenter" component={SupportCenter} />
      <Stack.Screen name="SupportCenterOld" component={SupportCenterOld} />
      <Stack.Screen name="SupportCenterDetailScreen" component={SupportCenterDetailScreen} />
      <Stack.Screen name="SupportCenterMyRequestScreen" component={SupportCenterMyRequestScreen} />
      <Stack.Screen
        name="SupportCenterMyRequestDetailScreen"
        component={SupportCenterMyRequestDetailScreen}
      />
      <Stack.Screen name="SupportCenterFilter" component={SupportCenterFilter} />
      <Stack.Screen name="FaqsDetail" component={FaqsDetail} />
      <Stack.Screen name="ContactUs" component={ContactUs} />
      <Stack.Screen name="MyFavorites" component={MyFavorites} />
      <Stack.Screen name="AttachmentView" component={AttachmentView} />
      <Stack.Screen name="EducationList" component={EducationList} />
      <Stack.Screen name="TrainingList" component={TrainingList} />
      <Stack.Screen name="WorkExperienceList" component={WorkExperienceList} />
      <Stack.Screen name="AchievementList" component={AchievementList} />
      <Stack.Screen name="SkillList" component={SkillList} />
      <Stack.Screen name="AnnoucementDetail" component={AnnouncementDetail} />
      <Stack.Screen name="SkillIWantToLearn" component={SkillIWantToLearn} />
      <Stack.Screen name="ReferHistory" component={ReferHistory} />
      <Stack.Screen name="Endorsement" component={Endorsement} />
      <Stack.Screen name="RecommendationList" component={RecommendationList} />
      <Stack.Screen name="ImageViewer" component={ImageViewer} />
      <Stack.Screen name="MyCourseList" component={MyCourseList} />
      <Stack.Screen
        name="NewJoinerPrograms"
        component={NewJoinerScreen}
        options={{ headerShown: false }}
      />

      <Stack.Screen
        name={'EditFormTelentProfile'}
        component={EditFormTelentProfile}
        options={({ navigation }) => ({
          headerShown: false,
        })}
      />
      <Stack.Screen name="EditLanguage" component={EditLanguageScreen} />
      <Stack.Screen name="DiscoverListing" component={DiscoverListing} />
      <Stack.Screen name="SelectedCategoryResults" component={SelectedCategoryResults} />

      <Stack.Screen name="SelectedSubCategoryResults" component={SelectedSubCategoryResults} />
      <Stack.Screen
        name="SelectedSubToSubCategoryResults"
        component={SelectedSubToSubCategoryResults}
      />
      <Stack.Screen name={NAVIGATION.MEET_YOUR_PEERS_LIST} component={MeetYourPearsList} />
      <Stack.Screen name={NAVIGATION.MEET_YOUR_PEER_DETAILS} component={MeetYourPeerDetails} />
    </Stack.Navigator>
  );
};

export default MainContainer;
