/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2021 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD's customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  I18nManager,
  StyleSheet as NativeStyleSheet,
  Platform,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';
import LoaderModal from '@components/common/LoaderModal.tsx';
import ReferPopup from '@components/talentProfileComponents/referPopup/index.tsx';
import { useTokenValidation } from '@hooks/useTokenValidation.tsx';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { createStackNavigator, StackCardInterpolationProps } from '@react-navigation/stack';
import RequestSlot from '@screens/CategoryDetail/RequestSlot.tsx';
import RateScreen from '@screens/MentorShip/RateScreen.tsx';
import CreatePersonalListScreen from '@screens/myLearningPathways/CreatePersonalListScreen/index.tsx';
import EditPersonalListScreen from '@screens/myLearningPathways/EditPersonalListScreen/index.tsx';
import FavouritesBottomSheet from '@screens/myLearningPathways/FavouritesBottomSheet.tsx/index.tsx';
import PersonalListScreen from '@screens/myLearningPathways/PersonalList/index.tsx';
import SelectCourses from '@screens/myLearningPathways/SelectCourses/index.tsx';
import FixedSnackbar from '@screens/MyNetwork/components/Toast/FixedSnackbar.tsx';
import OnboardingPager from '@screens/onboardingWizard/OnboardingPager.tsx';
import { onboardingDataMap } from '@screens/onboardingWizard/OnboardingWizardData.tsx';
import AdditionalDocuments from '@screens/Scholarship/AdditionalDocuments/index.tsx';
import ScholarshipApplication from '@screens/Scholarship/Application/index.tsx';
import ScholarshipDetails from '@screens/Scholarship/ScholarshipDetails/index.tsx';
import ScholarshipSearchFilters from '@screens/Scholarship/ScholarshipSearchFilters/index.tsx';
import ScholarshipsList from '@screens/Scholarship/ScholarshipsList/index.tsx';
import { useTheme } from '@theme/ThemeProvider';
import { RootState } from '@totara/reducers';
import { ASYNC_STORAGE_KEYS, YesNoEnum } from '@utils/constants.tsx';
import crashReportLogger from '@utils/crashlyticsLogger.tsx';
import { createInterceptors } from '@utils/createInterceptors.tsx';
import { useFeatureFlags } from '@utils/FeatureFlagsProvider';
import { NOTIFICATION_TYPES, NotificationType } from '@utils/notifications/notificationConstants';
import axios from 'axios';
import { showMessage } from 'react-native-flash-message';
import RNShake from 'react-native-shake';
import { useDispatch, useSelector } from 'react-redux';
import { analyticsService } from '../analytics/AnalyticsService.tsx';
import AddToListBottomSheet from '../components/AddToListBottomSheet';
import BottomSheet from '../components/bottomSheet';
import { TextView } from '../components/common';
import InternetInfo from '../components/InternetInfo';
import { navigationRef } from '../navigation/navigationService';
import {
  Biometrics,
  BookSession,
  BookSlot,
  Categories,
  Discover,
  EnterOtp,
  Faqs,
  MyLearningHome,
  NewCalendarScreen,
  OnBoardingSteps,
  RecommendLeader,
  RecommendSubmit,
  RecordVideo,
  ReferCourse,
  UserCredentials,
  VerifyMobile,
  Welcome,
  WriteReview,
  WriteReviewOld,
} from '../screens/index';
import BiometricLogin from '../screens/newOnBoardingFlow/biometricLogin/BiometricLogin.tsx';
import { getSupportCenterData } from './actions/supportCenterActions';
import BrowserLogin from './auth/manual/browser/BrowserLogin';
import EmailLogin from './auth/manual/native/EmailLogin.tsx';
import EmailOtpLogin from './auth/manual/native/EmailOtpLogin';
import NativeLoginNew from './auth/manual/native/NativeLoginNew';
import VerifyOtpLogin from './auth/manual/native/VerifyEmailOtpLogin';
import SiteUrl from './auth/manual/SiteUrl';
import WebviewLogin from './auth/manual/webview/WebviewLogin';
import Header from './components/Header';
import { useSession } from './core';
import WebViewCourseStack from './features/activities/webview/WebViewCourseStack.tsx';
import HistoryScreen from './features/currentLearning/HistoryScreen';
import FindLearning from './features/findLearning/FindLearning';
import { NAVIGATION } from './lib/navigation';
import { translate } from './locale';
import SessionContainer from './SessionContainer';
import TabContainer from './TabContainer';
import { TotaraTheme } from './theme/Theme';
import {
  handleNavigateToSupportCenter,
  handleReportBugOnly,
  handleTakeScreenshotAndReport,
} from './utils/shakeHandlers';

const navigationTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: TotaraTheme.viewContainer.backgroundColor!,
  },
};

const Stack = createStackNavigator();
const ONBOARDING_KEY = 'onboardingStatuses';

const RootContainer = () => {
  const dispatch = useDispatch();
  const { getSavedTheme, isDarkMode, theme } = useTheme();

  const routeNameRef = useRef<string | null>(null);
  const [currentRouteName, setCurrentRouteName] = useState<string | null>(null);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [currentScreenOnboardingData, setCurrentScreenOnboardingData] = useState([]);
  const { apiToken } = useSession();
  const { featureFlags } = useFeatureFlags();

  const lastShakeRef = useRef<number | null>(null);

  const [showShakeOptionsSheet, setShowShakeOptionsSheet] = useState(false);

  const supportCenterToken = useSelector((state: RootState) => state.supportCenterData.token);

  const { checkTokenExpiration } = useTokenValidation(navigationRef.current);

  useEffect(() => {
    if (supportCenterToken === null) {
      dispatch(getSupportCenterData(I18nManager.isRTL ? 'ar' : 'en'));
    }
  }, [dispatch, supportCenterToken]);

  useEffect(() => {
    const { requestInterceptor, responseInterceptor, setupFetchWrapper } =
      createInterceptors(dispatch);

    // Setup fetch wrapper
    const cleanupFetchWrapper = setupFetchWrapper();

    return () => {
      // Cleanup interceptors and fetch wrapper
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
      cleanupFetchWrapper();
    };
  }, [dispatch]);

  useEffect(() => {
    messaging().onNotificationOpenedApp((remote) => {
      const message = {
        data: {
          description:
            remote?.notification?.body ||
            '<p></p><p>The Abu Dhabi School of Government (ADSG), the leading government platform for developing human capital in Abu Dhabi, a subsidiary of the Department of Government Support, and Abu Dhabi Global Market Academy (ADGM Academy) have launched the ‘Investment Foundation program, which seeks to equip Abu Dhabi government employees with knowledge and skills related to finance, ethics, and investment practices.</p><p>Targeting Abu Dhabi government employees working in the financial sector whose practical experience ranges between one and five years, the program features a blend of interactive face-to-face training and remote learning, as well as group work, open debates, and tutorial support.</p><br><p></p>',
          type: remote?.data?.type || 'new_released_course',
          courseId: remote?.data?.courseid || '408',
          image: remote?.data?.image || '',
          created: remote?.data?.date || new Date().toISOString(),
        },
      };

      setTimeout(() => {
        const messageType = message?.data?.type as NotificationType;

        switch (messageType) {
          case NOTIFICATION_TYPES.ANNOUNCEMENT:
            navigationRef.current.navigate('AnnoucementDetail', {
              name: remote?.notification?.title,
              subject: remote?.notification?.body,
              image: message?.data?.image,
            });
            break;
          case NOTIFICATION_TYPES.NEW_RELEASED_COURSE:
          case NOTIFICATION_TYPES.COURSE_ASSIGNMENT:
          case NOTIFICATION_TYPES.SEMINAR_REMINDER:
          case NOTIFICATION_TYPES.SEMINAR_BOOKING:
          case NOTIFICATION_TYPES.CERTIFICATE_COMPLETION:
            navigationRef.current.navigate('CourseDetail', {
              courseid: message?.data?.courseId,
              title: remote?.notification?.title,
              image: message?.data?.image,
            });
            break;
          default:
            crashReportLogger({} as Error, {
              component: 'RootContainer onNotificationOpenedApp',
              additionalInfo: `Unhandled message type: ${messageType}`,
            });
            break;
        }
      }, 500);
    });

    const unsubscribe = messaging().onMessage((message) => {});

    return unsubscribe;
  }, []);

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (message) => {
      const data = {
        description:
          message?.notification?.body ||
          '<p></p><p>The Abu Dhabi School of Government (ADSG), the leading government platform for developing human capital in Abu Dhabi, a subsidiary of the Department of Government Support, and Abu Dhabi Global Market Academy (ADGM Academy) have launched the ‘Investment Foundation program, which seeks to equip Abu Dhabi government employees with knowledge and skills related to finance, ethics, and investment practices.</p><p>Targeting Abu Dhabi government employees working in the financial sector whose practical experience ranges between one and five years, the program features a blend of interactive face-to-face training and remote learning, as well as group work, open debates, and tutorial support.</p><br><p></p>',
        type: message?.data?.type || 'new_released_course',
        courseId: message?.data?.courseid || '408',
        image: message?.data?.image || '',
        created: message?.data?.date,
      };
      showMessage({
        message: message?.notification?.title || 'Notification',
        description: message?.notification?.body || '',
      });
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    getSavedTheme();
  }, []);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        if (!currentRouteName) return;

        const flag = await AsyncStorage.getItem(ASYNC_STORAGE_KEYS.BIOMETRICS.SEEN_FEATURE);
        if (featureFlags?.biometrics && apiToken && currentRouteName === 'Home') {
          if (!flag || flag === YesNoEnum.NO) {
            navigationRef.current?.navigate('BiometricLogin', { nextRoute: 'SessionContainer' });
          }
        }

        if (!featureFlags?.biometrics || (flag && flag === YesNoEnum.YES)) {
          const storedStatuses = await AsyncStorage.getItem(ONBOARDING_KEY);
          const onboardingStatuses = storedStatuses ? JSON.parse(storedStatuses) : {};

          const data = onboardingDataMap[currentRouteName!] || [];

          if (Array.isArray(data) && data.length > 0 && !onboardingStatuses[currentRouteName!]) {
            setCurrentScreenOnboardingData(data);
            setShowOnboarding(true);
          } else {
            setShowOnboarding(false);
          }
        }
      } catch (error) {
        crashReportLogger(error as Error, {
          component: 'RootContainer checkOnboardingStatus',
          additionalInfo: 'Failed to read onboarding statuses',
        });
      }
    };

    checkOnboardingStatus();
  }, [currentRouteName, featureFlags?.biometrics, apiToken]);

  const handleOnboardingFinish = async () => {
    try {
      const storedStatuses = await AsyncStorage.getItem(ONBOARDING_KEY);
      const onboardingStatuses = storedStatuses ? JSON.parse(storedStatuses) : {};
      onboardingStatuses[currentRouteName!] = true;
      await AsyncStorage.setItem(ONBOARDING_KEY, JSON.stringify(onboardingStatuses));
      setShowOnboarding(false);
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'RootContainer handleOnboardingFinish',
        additionalInfo: 'Failed to save onboarding status',
      });
    }
  };

  useEffect(() => {
    if (apiToken) {
      const subscription = RNShake.addListener(() => {
        const now = Date.now();
        if (lastShakeRef.current && now - lastShakeRef.current < 3000) {
          return;
        }
        lastShakeRef.current = now;
        setShowShakeOptionsSheet(true);
      });

      return () => {
        if (subscription) {
          subscription.remove();
        }
      };
    } else {
      crashReportLogger({} as Error, {
        component: 'RootContainer useEffect',
        additionalInfo: 'User not authenticated, RNShake listener not active.',
      });
      return () => {};
    }
  }, [apiToken]);

  if (showOnboarding) {
    return <OnboardingPager data={currentScreenOnboardingData} onFinish={handleOnboardingFinish} />;
  } else {
    return (
      <View style={{ flex: 1, backgroundColor: theme.backgroundColor }}>
        <NavigationContainer
          ref={navigationRef}
          theme={{
            ...navigationTheme,
            colors: { ...DefaultTheme.colors, background: theme.backgroundColor },
          }}
          onStateChange={async () => {
            const previousRouteName = routeNameRef.current;
            const currentRoute = navigationRef.current?.getCurrentRoute();
            if (currentRoute) {
              const currentRouteNameLocal = currentRoute.name;
              if (previousRouteName !== currentRouteNameLocal) {
                await checkTokenExpiration();
                await analyticsService.logScreenView(currentRouteNameLocal, currentRouteNameLocal);
                setCurrentRouteName(currentRouteNameLocal);
              }
              routeNameRef.current = currentRouteNameLocal;
            } else {
              routeNameRef.current = null;
            }
          }}
        >
          <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
          <Stack.Navigator
            initialRouteName="SessionContainer"
            screenOptions={{
              gestureEnabled: false,
              headerShown: false,
              cardStyleInterpolator: ({ current, layouts }: StackCardInterpolationProps) => {
                const translateX = current.progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [layouts.screen.width, 0],
                });
                return {
                  cardStyle: {
                    transform: [
                      {
                        translateX,
                      },
                    ],
                  },
                };
              },
            }}
          >
            {featureFlags?.biometrics ? (
              <Stack.Screen name="BiometricLogin" component={BiometricLogin} />
            ) : null}
            <Stack.Screen name="BookSession" component={BookSession} />
            <Stack.Screen name="WriteReview" component={WriteReview} />
            <Stack.Screen name="WriteReviewOld" component={WriteReviewOld} />
            <Stack.Screen name="RateScreen" component={RateScreen} />
            <Stack.Screen name="BookSlot" component={BookSlot} />
            <Stack.Screen name="RequestSlot" component={RequestSlot} />
            <Stack.Screen name="RecommendLeader" component={RecommendLeader} />
            <Stack.Screen name="RecommendSubmit" component={RecommendSubmit} />
            <Stack.Screen name="RecordVideo" component={RecordVideo} />
            <Stack.Screen name="Faqs" component={Faqs} />
            <Stack.Screen name="ReferCourse" component={ReferCourse} />
          <Stack.Screen name="Calendar" component={NewCalendarScreen} options={{ headerShown: false }}/>

            <Stack.Screen name="MyLearningHome" component={MyLearningHome} />
            <Stack.Screen name="PersonalListScreen" component={PersonalListScreen} />
            <Stack.Screen name="CreatePersonalListScreen" component={CreatePersonalListScreen} />
            <Stack.Screen name="SelectCourses" component={SelectCourses} />
            <Stack.Screen name="EditPersonalListScreen" component={EditPersonalListScreen} />
            <Stack.Screen name={'EmailLogin'} component={EmailLogin} />
            <Stack.Screen name="SessionContainer" component={SessionContainer} />
            <Stack.Screen name="TabContainer" component={TabContainer} />
            <Stack.Screen name="WelcomeOnBoard" component={Welcome} />
            <Stack.Screen name="UserCredentials" component={UserCredentials} />
            <Stack.Screen name="EnterOtp" component={EnterOtp} />
            <Stack.Screen name="VerifyMobile" component={VerifyMobile} />
            <Stack.Screen name="OnBoardingSteps" component={OnBoardingSteps} />
            <Stack.Screen name="Biometrics" component={Biometrics} />
            <Stack.Screen name={NAVIGATION.SCHOLARSHIP_LIST} component={ScholarshipsList} />
            <Stack.Screen
              name={NAVIGATION.SCHOLARSHIP_SEARCH_FILTERS}
              component={ScholarshipSearchFilters}
            />
            <Stack.Screen name={NAVIGATION.EMAIL_OTP_LOGIN} component={EmailOtpLogin} />
            <Stack.Screen name={NAVIGATION.VERIFY_OTP_LOGIN} component={VerifyOtpLogin} />
            <Stack.Screen
              name="ScholarshipDetails"
              component={ScholarshipDetails}
              initialParams={{ route: { params: { tab: 'info', status: 'NOT_APPLICABLE' } } }}
            />
            <Stack.Screen
              name={NAVIGATION.COURSE_WEBVIEW_ACTIVITY}
              component={WebViewCourseStack}
            />

            <Stack.Screen
              name={NAVIGATION.FIND_LEARNING}
              component={FindLearning}
              options={{
                headerStyle: {
                  backgroundColor: '#e7305b',
                },
              }}
            />
            <Stack.Screen name="Categories" component={Categories} />
            <Stack.Screen name={NAVIGATION.SITE_URL} component={SiteUrl} />
            <Stack.Screen
              name="HistoryScreen"
              component={HistoryScreen}
              options={({ navigation }) => ({
                header: () => (
                  <Header
                    navigation={navigation}
                    title={translate('current_learning.history')}
                    nullProp={undefined}
                    isProfile={undefined}
                  />
                ),
                headerShown: true,
              })}
            />
            <Stack.Screen name={NAVIGATION.NATIVE_LOGIN} component={NativeLoginNew} />
            <Stack.Screen name={NAVIGATION.WEBVIEW_LOGIN} component={WebviewLogin} />
            <Stack.Screen name={NAVIGATION.BROWSER_LOGIN} component={BrowserLogin} />
            <Stack.Screen name={NAVIGATION.DISCOVERY} component={Discover} />
            <Stack.Screen
              name={NAVIGATION.SCHOLARSHIP_APPLICATION}
              component={ScholarshipApplication}
            />
            <Stack.Screen name={NAVIGATION.ADDITIONAL_DOCUMENTS} component={AdditionalDocuments} />
          </Stack.Navigator>
        </NavigationContainer>

        <AddToListBottomSheet />
        <FavouritesBottomSheet />
        <InternetInfo />
        <FixedSnackbar />
        <LoaderModal />
        <ReferPopup />

        <BottomSheet visiblity={showShakeOptionsSheet} setVisibility={setShowShakeOptionsSheet}>
          <View style={bottomSheetStyles.bottomSheetContent}>
            <TouchableOpacity
              style={bottomSheetStyles.optionButton}
              onPress={() =>
                handleTakeScreenshotAndReport(navigationRef.current, setShowShakeOptionsSheet)
              }
            >
              <TextView
                style={[bottomSheetStyles.optionText, { color: theme.textColor }]}
                text={'Take A Screen shot and report a bug'}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={bottomSheetStyles.optionButton}
              onPress={() => handleReportBugOnly(navigationRef.current, setShowShakeOptionsSheet)}
            >
              <TextView
                style={[bottomSheetStyles.optionText, { color: theme.textColor }]}
                text={'Report a Bug'}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={bottomSheetStyles.optionButton}
              onPress={() =>
                handleNavigateToSupportCenter(navigationRef.current, setShowShakeOptionsSheet)
              }
            >
              <TextView
                style={[bottomSheetStyles.optionText, { color: theme.textColor }]}
                text={'Support Center'}
              />
            </TouchableOpacity>
          </View>
        </BottomSheet>
      </View>
    );
  }
};

const bottomSheetStyles = NativeStyleSheet.create({
  optionButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  optionText: {
    fontSize: 16,
    ...(Platform.OS == 'ios' && { transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }),
  },
  bottomSheetContent: {
    paddingTop: 10,
  },
});

export default RootContainer;
