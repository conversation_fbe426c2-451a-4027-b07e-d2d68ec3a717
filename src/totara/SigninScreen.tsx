// /**
//  * This file is part of Totara Enterprise.
//  *
//  * Copyright (C) 2020 onwards Totara Learning Solutions LTD
//  *
//  * Totara Enterprise is provided only to Totara Learning Solutions
//  * LTD’s customers and partners, pursuant to the terms and
//  * conditions of a separate agreement with Totara Learning
//  * Solutions LTD or its affiliate.
//  *
//  * If you do not have an agreement with Totara Learning Solutions
//  * LTD, you may not access, use, modify, or distribute this software.
//  * Please contact [<EMAIL>] for more information.
//  */
import React, { useContext } from 'react';
import {
  View,
  Text,
  SectionList,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ThemeContext } from '@totara/theme';
import { fontSizes, fontWeights, iconSizes, margins, paddings } from '@totara/theme/constants';
import { Image } from 'react-native';
import { TotaraTheme } from './theme/Theme';

const SigninScreen = ({ navigation }) => {
  const DeviceHeight = Dimensions.get('window').height;
  const DeviceWidth = Dimensions.get('window').width;

  return (
    <>
      <View style={styles.container}>
        <View style={styles.gradient}>
          <View style={styles.content}>
            <Text style={styles.textRegister}>Sign In</Text>
            <TouchableOpacity
              style={styles.skip}
              onPress={() => navigation.navigate('TabContainer')}
            >
              <Text style={styles.textContinue}>skip</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={{ backgroundColor: '#003435' }}>
          <View
            style={{
              backgroundColor: TotaraTheme.colorAccent,
              height: DeviceHeight / 1.16,
              borderTopEndRadius: 25,
              borderTopStartRadius: 25,
            }}
          >
            <ScrollView showsVerticalScrollIndicator={false}>
              <View
                style={{
                  marginTop: margins.margin2XXL,
                  width: DeviceWidth / 1.35,
                  alignSelf: 'center',
                }}
              >
                <Text style={styles.welcometext}>Welcome to My Navigator</Text>
                <Image
                  style={styles.poster}
                  source={require('@resources/images/learning/SiginPoster.png')}
                />
                <TouchableOpacity
                  style={styles.signInButton}
                  onPress={() => navigation.navigate('TabContainer')}
                >
                  <Image
                    style={styles.fingerPrint}
                    source={require('@resources/images/learning/FingerPrint.png')}
                  />
                  <Text style={styles.btnText}>Sign in with UAE PASS</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingBottom: paddings.padding3XL,
    backgroundColor: '#003435',
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textRegister: {
    fontSize: 25,
    color: TotaraTheme.colorAccent,
    fontWeight: fontWeights.fontWeightNormal,
    textAlign: 'center',
  },
  textContinue: {
    fontSize: 14,
    color: TotaraTheme.colorAccent,
    fontWeight: fontWeights.fontWeightNormal,
  },
  skip: {
    alignSelf: 'center',
    position: 'absolute',
    right: 20,
    bottom: 5,
  },
  welcometext: {
    alignSelf: 'center',
    fontSize: 26,
    fontWeight: fontWeights.fontWeightNormal,
    lineHeight: 33,
    textAlign: 'center',
  },
  poster: {
    width: 295,
    height: 313,
    marginTop: 87,
  },
  signInButton: {
    marginTop: 87,
    backgroundColor: '#000',
    height: 44,
    borderRadius: 34,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: margins.marginSXL,
  },
  fingerPrint: {
    height: iconSizes.sizeM,
    width: iconSizes.sizeM,
    marginRight: margins.marginXS,
  },
  btnText: {
    color: TotaraTheme.colorAccent,
    fontSize: fontSizes.fontSizeMX,
    fontWeight: fontWeights.fontWeightSemiBold,
  },
});

export default SigninScreen;
