// /**
//  * This file is part of Totara Enterprise.
//  *
//  * Copyright (C) 2020 onwards Totara Learning Solutions LTD
//  *
//  * Totara Enterprise is provided only to Totara Learning Solutions
//  * LTD’s customers and partners, pursuant to the terms and
//  * conditions of a separate agreement with Totara Learning
//  * Solutions LTD or its affiliate.
//  *
//  * If you do not have an agreement with Totara Learning Solutions
//  * LTD, you may not access, use, modify, or distribute this software.
//  * Please contact [<EMAIL>] for more information.
//  */
import React, { useContext, useEffect } from 'react';
import {
  View,
  Text,
  SectionList,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  ImageBackground,
  StatusBar,
} from 'react-native';
import { ThemeContext } from '@totara/theme';
import { fontSizes, fontWeights, margins } from '@totara/theme/constants';
import { Image } from 'react-native';
import { TotaraTheme } from '@totara/theme/Theme';
import { RouteProp, useRoute } from '@react-navigation/native';
import { WebViewProps } from 'react-native-webview';
import LinearGradient from 'react-native-linear-gradient';
import { deviceScreen } from '@totara/lib/tools';
import { translate } from './locale';

const SplashScreen = ({ navigation }) => {
  const DeviceHeight = Dimensions.get('window').height;
  const DeviceWidth = Dimensions.get('window').width;

  useEffect(() => {
    // Wait for the specified time (e.g., 2000 milliseconds) and then navigate to the desired screen
    const timeout = setTimeout(() => {
      // Replace 'MainScreen' with the name of the screen you want to navigate to
      navigation.replace('SigninScreen');
    }, 2000);
    return () => {
      // Clear the timeout if the component unmounts before the timeout completes
      clearTimeout(timeout);
    };
  }, [navigation]);

  return (
    <View style={{ flex: 1 }}>
      <StatusBar />
      <View style={{ width: '100%', height: DeviceHeight / 1.23 }}>
        {/* <Image
          style={{
            width: "100%",
            height: DeviceHeight / 1.23,
            position: "absolute",
            bottom: 0,
            backgroundColor: "transparent"
          }}
          source={require("@resources/images/learning/Splash.png")}
        /> */}
        <LinearGradient
          colors={['#00494A', '#238E8F']}
          start={{ x: 0, y: 1 }}
          end={{ x: 0, y: 0 }}
          style={{ width: '100%', height: DeviceHeight / 1.23 }}
        >
          <View style={{ alignItems: 'center' }}>
            <Image
              style={{
                width: DeviceWidth,
                height: 180,
                tintColor: '#40cbcc',
                resizeMode: 'stretch',
              }}
              source={require('@resources/images/learning/SplashScreen.png')}
            />
            <Text style={styles.text}>{translate('splash.my_navigator')}</Text>
          </View>
        </LinearGradient>
      </View>
      <View style={{ justifyContent: 'center', flex: 1, alignItems: 'center' }}>
        <Image
          style={{ width: 252, height: 45, alignSelf: 'center' }}
          source={require('@resources/images/learning/logoBlack.png')}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: fontSizes.fontSizeXL1,
    color: TotaraTheme.colorNeutral1,
    fontWeight: fontWeights.fontWeightSemiBold,
    lineHeight: 50,
  },
});

export default SplashScreen;
