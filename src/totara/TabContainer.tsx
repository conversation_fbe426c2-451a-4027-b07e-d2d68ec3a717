import React, { useEffect } from 'react';
import { createMaterialBottomTabNavigator } from '@react-navigation/material-bottom-tabs';
import {
  AppState,
  Dimensions,
  Image,
  ImageSourcePropType,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { useTheme } from '@theme/ThemeProvider';
import CurrentLearningStack from './features/currentLearning';
import { TAB_TEST_IDS } from './lib/testIds';
import { getLocale, translate } from '@totara/locale';
import { AssetsImages, Colors, fontSize } from '../theme';
import fonts from '../utils/fonts';
import deviceInfoModule from 'react-native-device-info';
import { HomeStack, MyLearningHomeStack } from './features/currentLearning/CurrentLearningStack';
import { coming_soon_opacity } from '../utils/AppUtils';
import { isPad } from '@utils/fontSize';
import { NAVIGATION } from '@totara/lib/navigation.ts';
import { MyNetworkStack } from '../screens/MyNetwork/navigation';
import { t } from 'i18n-js';
import { useTranslation } from 'react-i18next';
import useOfflineStatus from '../screens/OfflineCoursesSpace/hooks/useOfflineStatus.tsx';
import NetInfo from "@react-native-community/netinfo";
import { OfflineStack } from '@screens/OfflineCoursesSpace/navigation/index.tsx';
import { Home } from '@screens/index.tsx';
import { LineHeight } from '@utils/index.tsx';

const { height } = Dimensions.get('window');

const tabBarHeight = height < 668 ? 62 : 54;
const Tab = createMaterialBottomTabNavigator();
const TabContainer = ({ navigation }) => {
  const { theme, isDarkMode } = useTheme();
  const isArabic = getLocale() === 'ar';

  const TabBarIconBuilder = ({
    image,
    focused,
    color,
    title,
    disabled,
  }: {
    image: iconImageProps;
    focused: boolean;
    color: string;
    title: string;
    disabled: boolean;
  }) => {
    const iconColor = isDarkMode ? '#FFFFFF' : '#000000';
    const { t, i18n } = useTranslation();

    return (
      <View
        style={[
          {
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
          },
          disabled && { opacity: coming_soon_opacity },
        ]}
      >
        <Image
          resizeMode="contain"
          source={focused ? image.solid : image.regular}
          style={{
            width: isPad ? 30 : 22,
            height: isPad ? 30 : 20,
            tintColor: (isDarkMode && focused) ? '#f5f5f5' :
              (isDarkMode && !focused) ? '#A5a5a5' :
                !focused ? '#A5a5a5' : Colors.black,
          }}
        />

        <Text
          style={{
            color: (isDarkMode && focused) ?
              Colors.bottomBarDark : (isDarkMode && !focused) ?
                '#A5a5a5' : !focused ? '#A5a5a5' : Colors.black,
            width: '100%',
            textAlign: 'center',
            fontSize: fontSize.h6,
            fontFamily: focused ? fonts.semiBold : fonts.medium,
            textTransform: 'capitalize',
            marginTop: 5,
            lineHeight: LineHeight.h6_LH
            // lineHeight: lineHeight(isPad ? fontSize.semiMedium : fontSize.mini)
          }}
          numberOfLines={1}
        >
          {t(title)}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.backgroundColor }}>
      <View style={{ backgroundColor: theme.backgroundColor, flex: 1, }}>
        <Tab.Navigator
          barStyle={[
            styles.tabbar,
            {
              backgroundColor: isDarkMode ? theme.bgDark : 'white',
            },
            isDarkMode && { borderTopColor: theme.bgLightDark, borderWidth: 0.6, borderRightColor: theme.bgDark, borderLeftColor: theme.bgDark },
            {
              shadowColor: "black",
              shadowOffset: {
                width: 0,
                height: 20,
              },
              shadowOpacity: 0.58,
              shadowRadius: 16.00,

              elevation: 24,
            }
          ]}
          shifting={true}
          labeled={false}
        >

          <Tab.Screen
            name="Home"
            component={HomeStack}
            options={{
              tabBarAccessibilityLabel: translate('current_learning.action_primary'),
              tabBarIcon: ({ focused, color }: { focused: boolean; color: string }) => (
                <TabBarIconBuilder
                  color={color}
                  focused={focused}
                  image={tabBarIconImages.home}
                  title={'Home'}
                  disabled={false}
                />
              ),
              tabBarTestID: TAB_TEST_IDS.CURRENT_LEARNING,
            }}
            listeners={({ navigation }) => ({
              tabPress: (e) => { },
            })}
          />
          <Tab.Screen
            name="MyLearningHome"
            component={MyLearningHomeStack}
            options={{
              tabBarAccessibilityLabel: translate('current_learning.action_primary'),
              tabBarIcon: ({ focused, color }: { focused: boolean; color: string }) => (
                <TabBarIconBuilder
                  color={color}
                  focused={focused}
                  image={tabBarIconImages.current_learning}
                  title={'My Learning'}
                  disabled={false}
                />
              ),
              tabBarTestID: TAB_TEST_IDS.CURRENT_LEARNING,
            }}
            listeners={({ navigation }) => ({
              tabPress: (e) => { },
            })}
          />

          <Tab.Screen
            name="Learning"
            component={CurrentLearningStack}
            options={{
              tabBarAccessibilityLabel: translate('current_learning.action_primary'),
              tabBarIcon: ({ focused, color }: { focused: boolean; color: string }) => (
                <TabBarIconBuilder
                  color={color}
                  focused={focused}
                  image={tabBarIconImages.discover}
                  title={'Discover'}
                  disabled={false}
                />
              ),
              tabBarTestID: TAB_TEST_IDS.CURRENT_LEARNING,
            }}
          />
          {isArabic ? (
            <Tab.Screen
              name="Learning"
              component={CurrentLearningStack}
              options={{
                tabBarAccessibilityLabel: translate('current_learning.action_primary'),
                tabBarIcon: ({ focused, color }: { focused: boolean; color: string }) => (
                  <TabBarIconBuilder
                    color={color}
                    focused={focused}
                    image={tabBarIconImages.current_learning}
                    title={translate('bottomtab_title.home.title')}
                  />
                ),
                tabBarTestID: TAB_TEST_IDS.CURRENT_LEARNING,
              }}
            />
          ) : (
            <Tab.Screen
              name={NAVIGATION.MY_NETWORK}
              component={MyNetworkStack}
              options={{
                tabBarAccessibilityLabel: translate('user_profile.title'),
                tabBarIcon: ({ focused, color }: { focused: boolean; color: string }) => (
                  <TabBarIconBuilder
                    color={color}
                    focused={focused}
                    image={tabBarIconImages.profile}
                    title={'Social'}
                    disabled={false}
                  />
                ),
                tabBarTestID: TAB_TEST_IDS.PROFILE,
              }}
            />
          )}
        </Tab.Navigator>
      </View>
    </SafeAreaView>
  );
};

type iconImageProps = {
  solid: ImageSourcePropType;
  regular: ImageSourcePropType;
};

const tabBarIconImages: {
  current_learning: iconImageProps;
  find_learning: iconImageProps;
  downloads: iconImageProps;
  notifications: iconImageProps;
  profile: iconImageProps;
  all_course: iconImageProps;
  home: iconImageProps;
  discover: iconImageProps;
} = {
  home: {
    solid: AssetsImages.newHomeIcon,
    regular: AssetsImages.newHomeIcon,
  },
  current_learning: {
    solid: AssetsImages.myLearningIcon,
    regular: AssetsImages.myLearningIcon,
  },
  find_learning: {
    solid: require('@resources/icons/tabbar/SearchIcon.png'),
    regular: require('@resources/icons/tabbar/SearchIcon.png'),
  },
  downloads: {
    solid: require('@resources/icons/tabbar/MyLearningsIcons.png'),
    regular: require('@resources/icons/tabbar/MyLearningsIcons.png'),
  },
  notifications: {
    solid: require('@resources/icons/tabbar/WalletIcon.png'),
    regular: require('@resources/icons/tabbar/WalletIcon.png'),
  },
  profile: {
    solid: AssetsImages.socialIcon,
    regular: AssetsImages.socialIcon,
  },
  all_course: {
    solid: AssetsImages.s1,
    regular: AssetsImages.s_gary,
  },
  discover: {
    solid: AssetsImages.discoverIcon,
    regular: AssetsImages.discoverIcon,
  },
};

const styles = StyleSheet.create({
  tabbar: {
    height: Platform.OS == 'ios' ? (deviceInfoModule.hasNotch() ? 54 : isPad ? 75 : tabBarHeight) : 60,
    position: 'absolute',
    paddingTop: isPad ? 10 : 5,
  },
});

export default TabContainer;
