/**
 * This file is part of Totara Enterprise.
 *
 * Copyright (C) 2020 onwards Totara Learning Solutions LTD
 *
 * Totara Enterprise is provided only to Totara Learning Solutions
 * LTD’s customers and partners, pursuant to the terms and
 * conditions of a separate agreement with Totara Learning
 * Solutions LTD or its affiliate.
 *
 * If you do not have an agreement with Totara Learning Solutions
 * LTD, you may not access, use, modify, or distribute this software.
 * Please contact [<EMAIL>] for more information.
 */
import storage from '@react-native-async-storage/async-storage';
import { AnyAction, applyMiddleware, createStore } from 'redux';
import { createLogger } from 'redux-logger';
import { persistReducer, persistStore } from 'redux-persist';
import thunk, { ThunkMiddleware } from 'redux-thunk';
import rootReducer, { RootState } from './reducers';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: [
    'sessionReducer',
    'resourceReducer',
    'notificationReducer',
    'getWFMProfile',
    'lastOpenedCourseReducer',
    'notificationReducer',
    'calendarReducer',
    'getStreakData',
    'personalLists',
  ],
};

const persistedReducer = persistReducer<RootState>(persistConfig, rootReducer);

const createMiddleware = (logger: boolean) => {
  const middleware = [thunk as ThunkMiddleware<RootState, AnyAction>];
  if (logger) {
    middleware.push(createLogger({ collapsed: true, level: 'info' }));
  }
  return applyMiddleware(...middleware);
};

const store = createStore(persistedReducer, createMiddleware(__DEV__));
const persistor = persistStore(store);
export { persistor, store };
