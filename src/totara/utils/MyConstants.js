import { Platform } from 'react-native';
import { ENV } from '../../api/api_client';
import { I18nManager } from 'react-native';

const UAE_PASS_BASE_URL_STG = 'https://stg-id.uaepass.ae';
const UAE_PASS_BASE_URL_PROD = 'https://id.uaepass.ae';

export const BASE_URL = ENV == 'prod' ? UAE_PASS_BASE_URL_PROD : UAE_PASS_BASE_URL_STG;

export const AUTH_URL = BASE_URL + '/idshub/authorize';
export const PROFILE_URL = BASE_URL + '/idshub/userinfo';
export const ACCESS_TOKEN_URL = BASE_URL + '/idshub/token?grant_type=authorization_code';
export const SELF_CARE = 'https://stg-selfcare.uaepass.ae/';
export const LOGOUT_UAEPASS = BASE_URL + `/idshub/logout?redirect_uri=${SELF_CARE}`;

export const ENV_UAEPASS = ENV == 'prod' ? 'production' : 'staging'; // or production // default staging
export const APP_URL =
  ENV == 'prod' ? 'https://learn.adsg.gov.ae' : 'https://adsgstaging.elearning.ae';

const REDIRECT_URL_PROD = 'https://workspace.dge.gov.ae/api/user/uae-pass/return';
const REDIRECT_URL_STG = 'https://stg.workspace.dge.gov.ae/api/user/uae-pass/return';
const REDIRECT_URL_DEV = 'https://google.com/';

export const REDIRECT_URL =
  ENV === 'prod' ? REDIRECT_URL_PROD : ENV == 'stg' ? REDIRECT_URL_STG : REDIRECT_URL_DEV;

export const UAE_PASS_CLIENT_ID_MOBILE = 'dgegovlearningw_web_stage'; // stg
export const UAE_PASS_CLIENT_ID_MOBILE_PROD = 'dgemylearning_mob_prod'; // prod

export const UAE_PASS_CLIENT_ID_SANDBOX = 'sandbox_stage';
export const UAE_PASS_CLIENT_ID_WEB_STG = 'dgegovwmsw_web_stage';
export const UAE_PASS_CLIENT_ID_WEB_PROD = 'dgeworkspace_web_prod';

export const UAE_PASS_CLIENT_ID =
  ENV === 'prod'
    ? UAE_PASS_CLIENT_ID_WEB_PROD
    : ENV === 'stg'
      ? UAE_PASS_CLIENT_ID_WEB_STG
      : UAE_PASS_CLIENT_ID_SANDBOX;

const UAE_PASS_SECRET_ID_STG = 'QDGtVPagCdEX7GEY';
const UAE_PASS_SECRET_ID_PROD = '';

export const UAE_PASS_SECRET_ID =
  ENV === 'prod'
    ? UAE_PASS_SECRET_ID_PROD
    : ENV === 'stg'
      ? UAE_PASS_SECRET_ID_STG
      : UAE_PASS_CLIENT_ID_SANDBOX;

export const UAE_PASS_CLIENT_SECRET_DEV = 'fkUNvRXUaQSqxUgrs5wcoThu';
export const UAE_PASS_CLIENT_SECRET_PROD = '';

export const UAE_PASS_CLIENT_SECRET =
  ENV === 'prod'
    ? UAE_PASS_CLIENT_SECRET_PROD
    : ENV === 'stg'
      ? UAE_PASS_SECRET_ID_STG
      : UAE_PASS_CLIENT_SECRET_DEV;

export const RESPONSE_TYPE = 'code';
export const SCOPE =
  ENV === 'stg' || ENV === 'prod'
    ? 'urn:uae:digitalid:profile:general'
    : 'urn:uae:digitalid:profile';

export const LANGUAGE = 'en';

export const ACR_VALUES_MOBILE = 'urn:digitalid:authentication:flow:mobileondevice';
export const ACR_VALUES_WEB = 'urn:safelayer:tws:policies:authentication:level:low';
export const UAE_PASS_PACKAGE_ID = ENV == 'prod' ? 'ae.uaepass.mainapp' : 'ae.uaepass.mainapp.stg';
export const UAE_PASS_QA_PACKAGE_ID = 'ae.uaepass.mainapp.qa';
export const URL_SCHEME = 'mynavigatorapp';
export const HOST_SUCCESS = Platform.OS == 'ios' ? 'uaePassSuccess' : 'success';
export const HOST_FAILURE = Platform.OS == 'ios' ? 'uaePassFail' : 'failure';
export const TOTARA_PASSWORD = 'TestPassword123!';

export const updateURLParameter = (url, param, paramVal) => {
  var newAdditionalURL = '';
  var tempArray = url.split('?');
  var baseURL = tempArray[0];
  var additionalURL = tempArray[1];
  var temp = '';
  if (additionalURL) {
    tempArray = additionalURL.split('&');
    for (var i = 0; i < tempArray.length; i++) {
      if (tempArray[i].split('=')[0] != param) {
        newAdditionalURL += temp + tempArray[i];
        temp = '&';
      }
    }
  }

  var rows_txt = temp + '' + param + '=' + paramVal;
  return baseURL + '?' + newAdditionalURL + rows_txt;
};

export const getParamsFromUrl = (url) => {
  let regex = /[?&]([^=#]+)=([^&#]*)/g,
    params = {},
    match;
  while ((match = regex.exec(url))) {
    params[match[1]] = match[2];
  }

  return params;
};

export const ContactSupport = '+971501025751';
export const aboutUrl = 'https://www.dge.gov.ae/en/what-we-do/programs';
export const privacyPolicyUrl = 'https://www.dge.gov.ae/en/policies-and-legislations';
export const terms =
  'https://www.dge.gov.ae/en/terms-of-use#:~:text=DGE%20Application%20Privacy%20Policy%20Scope%20of%20the%20Application%20This%20policy';
export const linkedinUrl = 'https://ae.linkedin.com/company/department-of-government-enablement';
export const learningPathwayItemHeight = I18nManager.isRTL ? 205 : 195;

export const getGovernmentEntityFromEmail = (email) => {
  const delim1 = '@';
  const delim2 = '.';
  return email.split(delim1).pop().split(delim2)[0];
};
