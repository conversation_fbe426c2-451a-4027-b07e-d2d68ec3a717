import { captureScreen } from 'react-native-view-shot';
import React from 'react';

// A more specific type could be used if RootStackParamList is available from your navigation setup
type NavigationRefType = {
  navigate: (screen: string, params?: any) => void;
} | null;

export const handleTakeScreenshotAndReport = (
  navigation: NavigationRefType,
  setShowShakeOptionsSheet: React.Dispatch<React.SetStateAction<boolean>>
) => {
  setShowShakeOptionsSheet(false);
  // Add a small delay to allow the bottom sheet to dismiss fully on iOS
  setTimeout(() => {
    captureScreen({
      format: 'png',
    })
      .then(
        (uri) => {
          console.log('Image saved to', uri);
          if (navigation) {
            navigation.navigate('SupportCenterDetailScreen', { screenshotUri: uri, type: 'bug' });
          }
        },
        (error) => {
          console.error('Oops, screenshot failed', error);
          if (navigation) {
            navigation.navigate('SupportCenterDetailScreen', { type: 'bug' });
          }
        },
      )
      .catch(error => {
        console.error('Exception in screenshot handler:', error);
        if (navigation) {
          navigation.navigate('SupportCenterDetailScreen', { type: 'bug' });
        }
      });
  }, 300); // 300ms delay
};

export const handleReportBugOnly = (
  navigation: NavigationRefType,
  setShowShakeOptionsSheet: React.Dispatch<React.SetStateAction<boolean>>
) => {
  setShowShakeOptionsSheet(false);
  if (navigation) {
    navigation.navigate('SupportCenterDetailScreen', { type: 'bug' });
  }
};

export const handleNavigateToSupportCenter = (
  navigation: NavigationRefType,
  setShowShakeOptionsSheet: React.Dispatch<React.SetStateAction<boolean>>
) => {
  setShowShakeOptionsSheet(false);
  if (navigation) {
    navigation.navigate('SupportCenter');
  }
}; 