// Support Center API v2 TypeScript Interfaces

export interface ApiResponse<T> {
  data: T;
  pagination: any | null;
  operationResponse: OperationResponse;
}

export interface OperationResponse {
  success: boolean;
  message: string | null;
  errorCode: string | null;
  errorDescription: string | null;
  validationErrors: ValidationError[] | null;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface KeyValuePair {
  key: string;
  value: string;
}

export interface Service extends KeyValuePair {}

export interface Category extends KeyValuePair {
  services: Service[];
}

export interface CaseType extends KeyValuePair {
  categories: Category[];
}

export interface CaseStatus extends KeyValuePair {}

export interface CaseComment {
  createdAt: string;
  commentBody: string;
  status: CaseStatus;
}

export interface CaseAttachment {
  id?: string;
  name?: string;
  url?: string;
  size?: number;
}

export interface SupportCase {
  number: string;
  serviceNumber: string;
  title: string;
  description: string;
  type: CaseType;
  category: Category;
  serviceName: Service;
  status: CaseStatus;
  comments: CaseComment[];
  attachments: CaseAttachment[];
  daysAgo: number;
  createdAt: string;
  updatedAt: string;
  emiratesId: string;
}

export interface CreateCaseRequest {
  emiratesId: string;
  englishFirstName: string;
  englishLastName: string;
  mobile: string;
  emailAddress: string;
  prefCommChannel: number;
  languagePreference: number;
  caseTitle: string;
  caseDescription: string;
  caseType: KeyValuePair;
  caseCategory: KeyValuePair;
  serviceName: KeyValuePair;
  region: KeyValuePair;
  area: KeyValuePair;
}

export interface CreateCaseResponse {
  caseNumber: string;
  serviceNumber: string;
  status: CaseStatus;
}

export interface CreateCommentRequest {
  emiratesId: string;
  caseNumber: string;
  commentBody: string;
}

export interface CreateAttachmentRequest {
  emiratesId: string;
  caseNumber: string;
  attachmentName: string;
  attachmentFileBytes: string;
  attachmentFileSize: number;
  attachmentExtension: string;
}

// Redux State Interfaces
export interface SupportCenterState {
  loading: boolean;
  token: string | null;
  data: CaseType[];
  myRequests: SupportCase[];
  filters: any[];
}

// API Response Type Aliases
export type AuthResponse = ApiResponse<AuthTokens>;
export type ReferencesResponse = ApiResponse<CaseType[]>;
export type CasesResponse = ApiResponse<SupportCase[]>;
export type CaseDetailResponse = ApiResponse<SupportCase>;
export type CreateCaseApiResponse = ApiResponse<CreateCaseResponse>;
export type CreateCommentResponse = ApiResponse<null>;
export type CreateAttachmentResponse = ApiResponse<null>;
