/* eslint-disable @typescript-eslint/no-explicit-any */
import moment from 'moment';
import { I18nManager, PermissionsAndroid, Platform } from 'react-native';
import momentTimeZone from 'moment-timezone';
import momentWithLocales from 'moment/min/moment-with-locales'; // Import with alias for locale-specific functionality
import { convertNumbersToArabicNumerals } from './constants';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { IS_ARABIC, LOCALE } from './localization';
import crashReportLogger from './crashlyticsLogger';

export const fontFamily = (type: 'ST-MEDIUM' | 'ST-MEDIUM-ITALIC') => {
  switch (type) {
    case 'ST-MEDIUM':
      return Platform.OS == 'ios' ? 'SFProText-Medium' : 'SF-Pro-Text-Medium';

    case 'ST-MEDIUM-ITALIC':
      return Platform.OS == 'ios' ? 'SFProText-Medium' : 'SF-Pro-Text-MediumItalic';
  }
};

export const coming_soon_opacity = 1;

export const getName = (length: number) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
};

export const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes < 1 ? '00' : minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

export const checkPermissionAndroidMic = async () => {
  try {
    const grants = await PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    ]);

    if (
      grants['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
      grants['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
      grants['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED
    ) {
      return true;
    } else {
      return false;
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'AppUtils.tsx',
      additionalInfo: 'Failed to check permission for Android'
    })
    return false;
  }
};

export const checkPermissionAndroidCamera = async () => {
  try {
    const grants = await PermissionsAndroid.requestMultiple([
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      PermissionsAndroid.PERMISSIONS.CAMERA,
    ]);

    if (
      grants['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
      grants['android.permission.CAMERA'] === PermissionsAndroid.RESULTS.GRANTED
    ) {
      return true;
    } else {
      return false;
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'AppUtils.tsx',
      additionalInfo: 'Failed to check permissions Android camera'
    })
    return false;
  }
};

export function mapDurationToMinutes(duration) {
  switch (duration) {
    case '0 – 1 Hours':
      return { durationfrom: 0, durationto: 60 };
    case '1 – 3 Hours':
      return { durationfrom: 60, durationto: 180 };
    case '3 – 6 Hours':
      return { durationfrom: 180, durationto: 360 };
    case '6 – 20 Hours':
      return { durationfrom: 360, durationto: 1200 };
    case '20+ Hours':
      return { durationfrom: 1200, durationto: Number.MAX_SAFE_INTEGER };
    default:
      // Handle other cases as needed
      return { durationfrom: 0, durationto: 0 };
  }
}

export const logInDevelopment = (...args) => { };

export const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export function getTimeAgo(timestamp) {
  // const currentDate = new Date();
  // const targetDate = new Date(timestamp);
  // const timeDifference = currentDate - targetDate;
  // const seconds = Math.floor(timeDifference / 1000); // milliseconds to seconds
  // if (seconds < 60) {
  //   return `${seconds} seconds ago`;
  // } else if (seconds < 3600) {
  //   const minutes = Math.floor(seconds / 60);
  //   return `${minutes} minutes ago`;
  // } else if (seconds < 86400) {
  //   const hours = Math.floor(seconds / 3600);
  //   return `${hours} hours ago`;
  // } else {
  //   const days = Math.floor(seconds / 86400);
  //   return `${days} day${days > 1 ? "s" : ""} ago`;
  // }
  return moment(moment.utc(timestamp).format())
    .locale('en')
    .fromNow();
}

export const getFileName = (img: string) => {
  const localUri = img;
  const filename: any = localUri.split('/').pop();
  const match: any = /\.(\w+)$/.exec(filename);
  const type = match ? `audio/${match[1]}` : 'audio';
  const image = img;
  const obj = {
    name: 'audio' + new Date().getTime() + '.' + match[1],
    type: type,
    // uri: Platform.OS === "android" ? image : image.replace("file://", ""),
    uri: image,
  };
  return obj;
};

export const getFullNameInitials = (fullName: string) => {
  const fullNameArray = fullName?.split(' ');
  const firstNameInitial = fullNameArray[0]?.charAt(0)?.toUpperCase();
  const lastNameInitial = fullNameArray[fullNameArray.length - 1]?.charAt(0).toUpperCase();
  return firstNameInitial + lastNameInitial;
};

export function formatBytes(bytes: number, decimals = 1) {
  if (!+bytes) return `${convertNumbersToArabicNumerals('0')} Bytes`;

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${convertNumbersToArabicNumerals(parseFloat((bytes / Math.pow(k, i)).toFixed(dm)))} ${sizes[i]}`;
}

export const getTimeWithSystemTZ = (time, format, formatMin?: any) => {
  const dateTime = time?.split('T');
  const d = dateTime?.[0];
  const t = dateTime?.[1]?.split('.')?.[0];
  const dubaiTimeWithDate = momentTimeZone.tz(`${d}T${t}`, 'Asia/Dubai').locale(LOCALE);
  const utcTime = dubaiTimeWithDate.clone().tz('UTC').locale(LOCALE);
  const systemDefaultTz = utcTime.clone().tz(momentTimeZone.tz.guess());
  const formattedUtcTime = utcTime.format('YYYY-MM-DDTHH:mm:ssZ');
  const formattedTime = systemDefaultTz.locale(LOCALE).format(format);
  if (formatMin) {
    const formattedTime = systemDefaultTz.locale(LOCALE).format('hh');
    const formattedTimeMin = systemDefaultTz.locale(LOCALE).format(formatMin);
    return `${formattedTime}:${formattedTimeMin}`;
  }
  return formattedTime;
};
export const strippedHTMLString = (text: string) =>
  text?.length ? text?.replace(/(<([^>]+)>)/gi, '') : '';

export const formatDateBasedOnLocale = (date, format = 'MMM YYYY') => {
  const locale = 'en';
  momentWithLocales?.locale(locale);
  return momentWithLocales(date)?.locale(locale)?.format(format);
};
export const MONTHS = {
  Jan: 'يناير',
  Feb: 'فبراير',
  Mar: 'مارس',
  Apr: 'إبريل',
  May: 'مايو',
  Jun: 'يونيو',
  July: 'يوليو',
  Aug: 'أغسطس',
  Sep: 'سبتمبر',
  Oct: 'أكتوبر',
  Nov: 'نوفمبر',
  Dec: 'ديسمبر',
};

export const DAYS = {
  Sun: 'الأحد',
  Mon: 'الاثنين',
  Tue: 'الثلاثاء',
  Wed: 'الأربعاء',
  Thu: 'الخميس',
  Fri: 'الجمعة',
  Sat: 'السبت',
};


export const CoachStatus = {
  userCanRate: 'User can Rate the coach',
  userCannotRate: 'User cannot rate the coach, Because the user does not attend slot or slot time not completed',
  userAlreadyRated: 'User already rated the coach',
}

export function generateRandomString() {
  const length = 10;
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return `${result}${new Date().getTime()}`;
}
export const getAppLocale = async (): Promise<'en' | 'ar'> => {
  try {
    const storedLanguage = await AsyncStorage.getItem('currentLanguage');
    const cleaned = storedLanguage?.trim().toLowerCase();

    if (cleaned === 'arabic' || cleaned === 'ar') {
      return 'ar';
    }

    return 'en';
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'AppUtils.tsx',
      additionalInfo: 'Failed to fetch language from AsyncStorage'
    })
    return 'en';
  }
};
export const getRandom10 = (arr)=> {
  const shuffled = [...arr];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled.slice(0, 10);
}

export function formatNumbers(input: string) {
  if (!input || !IS_ARABIC) {
    return input;
  }
  const str = `${input}`;

  const numbersMap = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  return I18nManager.isRTL ? str.replace(/[0-9]/g, (digit) => numbersMap[digit]) : str;
}