import React, { createContext, FC, useContext, useEffect, useState } from 'react';
import { APIFeature, fetchAPIFeatureFlags } from '@api/apiFeatureFlagsService';
import remoteConfig from '@react-native-firebase/remote-config';
import { useSession } from '@totara/core';
import { RootState } from '@totara/reducers';
import crashReportLogger from '@utils/crashlyticsLogger';
import { useSelector } from 'react-redux';
import { apiDefaults, frbsDefaults, MIN_FF_FETCH_INTERVAL_MS } from './constants';

type FeatureFlagsContextType = {
  featureFlags: Record<string, boolean>;
  refreshFeatures?: () => Promise<void>;
  isLoading?: boolean;
};

type FeatureFlagsProviderProps = {
  children?: React.ReactNode;
};

export const FeatureFlagsContext = createContext<FeatureFlagsContextType>({
  featureFlags: {},
  isLoading: false,
});

export const FeatureFlagsProvider: FC<FeatureFlagsProviderProps> = ({ children }) => {
  const [featureFlags, setFeatureFlags] = useState({ ...frbsDefaults, ...apiDefaults });
  const [isLoading, setIsLoading] = useState(false);
  const { apiToken } = useSession();
  const talentProfile = useSelector((state: RootState) => state?.getWFMProfile?.response);

  const setDefaultValuesFrbs = async () => {
    try {
      await remoteConfig().setDefaults(frbsDefaults);
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'FeatureFlagsProvider setDefaultValuesFrbs',
        additionalInfo: 'Failed to set Remote Config defaults',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchApiFeatureFlags = async () => {
    setIsLoading(true);
    try {
      const features = await fetchAPIFeatureFlags(talentProfile.email);
      const featuresApi = { ...apiDefaults };
      features.forEach((feature: APIFeature) => {
        featuresApi[feature.description] = true;
      });

      return featuresApi;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'FeatureFlagsProvider fetchApiFeatureFlags',
        additionalInfo: `Failed to fetch API feature flags for email: ${talentProfile.email}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFrbsFeatureFlags = async () => {
    setIsLoading(true);
    try {
      const featuresFrbs = {};
      // Configure fetch interval
      await remoteConfig().setConfigSettings({
        minimumFetchIntervalMillis: MIN_FF_FETCH_INTERVAL_MS,
      });

      await remoteConfig().fetchAndActivate();
      const remoteFlags = remoteConfig().getAll();
      if (Object.entries(remoteFlags).length) {
        for (const [flag, value] of Object.entries(remoteFlags)) {
          featuresFrbs[flag] = value.asBoolean();
        }
      }
      return featuresFrbs;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'FeatureFlagsProvider fetchFrbsFeatureFlags',
        additionalInfo: 'Failed to fetch and activate Remote Config',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshFeatures = async () => {
    const flags = await fetchAndSetFeatureFlags();
    setFeatureFlags(flags);
  };

  const fetchAndSetFeatureFlags = async () => {
    const frbsFeatureFlags = (await fetchFrbsFeatureFlags()) || {};
    let apiFeatureFlags;
    if (apiToken && talentProfile?.email) {
      apiFeatureFlags = (await fetchApiFeatureFlags()) || {};
    }
    const combinedFeatureFlags = { ...frbsFeatureFlags, ...apiFeatureFlags };
    combinedFeatureFlags['aiFeature'] = frbsFeatureFlags['aiFeature'] || apiFeatureFlags['AI'];
    return combinedFeatureFlags;
  };

  useEffect(() => {
    // Set Remote Config default values
    setDefaultValuesFrbs();
    async function prepareFlags() {
      const flags = await fetchAndSetFeatureFlags();
      setFeatureFlags(flags);
    }
    prepareFlags();
  }, [apiToken, talentProfile?.email]);

  return (
    <FeatureFlagsContext.Provider value={{ featureFlags, refreshFeatures, isLoading }}>
      {children}
    </FeatureFlagsContext.Provider>
  );
};

export const useFeatureFlags = (): FeatureFlagsContextType => {
  const context = useContext(FeatureFlagsContext);
  if (!context) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagsProvider');
  }
  return context;
};
