import crashReportLogger from '../utils/crashlyticsLogger';
import { confirmedLogout } from '@totara/actions/getWFMProfileAction';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class TokenValidationService {
  static async checkTokenExpiration(dispatch: any, navigation: any) {
    try {
      const expiryTimestampString = await AsyncStorage.getItem('tokenExpiry');
      const expiryTimestamp = expiryTimestampString ? JSON.parse(expiryTimestampString!) : 0;
      const currentTime = Math.floor(Date.now() / 1000);
      if (currentTime >= expiryTimestamp) {
        await TokenValidationService.clearTokenData(dispatch, navigation);
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'TokenValidationService',
        additionalInfo: 'Failed to check token expiration',
      });
    }
  }

  static async clearTokenData(dispatch: any, navigation) {
    try {
      dispatch(confirmedLogout(navigation));
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'TokenValidationService',
        additionalInfo: 'Failed to clear token data',
      });
    }
  }
}