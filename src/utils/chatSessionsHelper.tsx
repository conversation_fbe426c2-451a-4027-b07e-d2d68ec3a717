import AsyncStorage from '@react-native-async-storage/async-storage';
import crashReportLogger from './crashlyticsLogger';

const CHAT_LIST_KEY = 'CHAT_SESSION_LIST';

export const saveChatMessage = async (sessionId, messages) => {
  await AsyncStorage.setItem(`chat_${sessionId}`, JSON.stringify(messages));
};
export const saveAIChatMessage = async (sessionId, messages) => {
  await AsyncStorage.setItem(`chat_ai_${sessionId}`, JSON.stringify(messages));
};

export const loadChatMessage = async (sessionId) => {
  const messages = await AsyncStorage.getItem(`chat_${sessionId}`);
  return messages ? JSON.parse(messages) : [];
};
export const loadAIChatMessage = async (sessionId) => {
  const messages = await AsyncStorage.getItem(`chat_ai_${sessionId}`);
  return messages ? JSON.parse(messages) : [];
};

export const getChatSessions = async () => {
  const sessions = await AsyncStorage.getItem(CHAT_LIST_KEY);
  return sessions ? JSON.parse(sessions) : [];
};

export const addChatSession = async (session) => {
  const sessions = await getChatSessions();
  const newSessions = [session, ...sessions];
  await AsyncStorage.setItem(CHAT_LIST_KEY, JSON.stringify(newSessions));
};

export const groupSessionsByTime = (sessions) => {
  const groups = {};
  const now = new Date();

  const isSameDay = (a, b) =>
    a.getFullYear() === b.getFullYear() &&
    a.getMonth() === b.getMonth() &&
    a.getDate() === b.getDate();

  const isYesterday = (date) => {
    const yest = new Date();
    yest.setDate(now.getDate() - 1);
    return isSameDay(date, yest);
  };

  const daysBetween = (a, b) => {
    const diff = Math.abs(a - b);
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  };

  const formatMonth = (date) => {
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return monthNames[date.getMonth()];
  };

  sessions.forEach((session) => {
    const createdAt = new Date(session.createdAt);
    let label = '';

    if (isSameDay(createdAt, now)) {
      label = 'Today';
    } else if (isYesterday(createdAt)) {
      label = 'Yesterday';
    } else {
      const daysAgo = daysBetween(now, createdAt);

      if (daysAgo <= 7) {
        label = 'Previous 7 Days';
      } else if (daysAgo <= 30) {
        label = 'Previous 30 Days';
      } else if (createdAt.getFullYear() === now.getFullYear()) {
        label = formatMonth(createdAt);
      } else if (createdAt.getFullYear() === now.getFullYear() - 1) {
        label = 'Last Year';
      } else {
        label = 'Older';
      }
    }

    if (!groups[label]) groups[label] = [];
    groups[label].push(session);
  });

  // Optional: define a specific order for labels
  const labelOrder = [
    'Today',
    'Yesterday',
    'Previous 7 Days',
    'Previous 30 Days',
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
    'Last Year',
    'Older',
  ];

  return labelOrder
    .filter((label) => groups[label])
    .map((label) => ({
      title: label,
      data: groups[label],
    }));
};

export const deleteChatSession = async (sessionId) => {
  try {
    // Remove chat messages for that session
    await AsyncStorage.removeItem(`chat_${sessionId}`);

    // Get existing session list
    const sessions = await getChatSessions();

    // Filter out the deleted session
    const updatedSessions = sessions.filter((session) => session.id !== sessionId);

    // Save updated session list
    await AsyncStorage.setItem(CHAT_LIST_KEY, JSON.stringify(updatedSessions));

    return true;
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'chatSessionsHelper.tsx',
      additionalInfo: 'Failed to delete chat session',
    });
    return false;
  }
};
