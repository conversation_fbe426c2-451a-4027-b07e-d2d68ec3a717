import { I18nManager, Platform, StyleSheet } from 'react-native';
import fonts from './fonts';
import fontSize, { isPad } from './fontSize';
import { Colors } from '@theme';
import { isAndroid } from '@totara/lib/tools';

export default StyleSheet.create({
  noInfo: {
    marginTop: 10,
    fontFamily: fonts.regular,
    color: 'black',
    fontSize: isPad ? fontSize.semiMedium : fontSize.small,
    textAlign: 'left'
  },
  showMore: {
    marginHorizontal: 65,
  },
  switchStyle: {
    transform: [
      { scaleX: Platform.OS == 'android' ? 1 : 0.7 },
      { scaleY: Platform.OS == 'android' ? 1 : 0.7 },
    ],
    marginTop: -1,
  },
  rowAlign: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowJustified: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mt1: { marginTop: 1 },
  mt2: { marginTop: 2 },
  mt3: { marginTop: 3 },
  mt4: { marginTop: 4 },
  mt5: { marginTop: 5 },
  mt10: { marginTop: 10 },
  mt15: { marginTop: 15 },
  mt20: { marginTop: 20 },
  dot: {
    width: 5,
    height: 5,
    borderRadius: 5,
    backgroundColor: 'rgba(30, 30, 30, 0.5)',
    marginHorizontal: 10,
  },
  bottomView: {
    bottom: 20,
    position: 'absolute',
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
  },
  learningPathWayLoader: {
    backgroundColor: 'transparent',
    alignSelf: 'center',
    borderBottomColor: Colors.borderColorSt,
    borderBottomWidth: 1,
    height: 100,
    maxHeight: 100,
    borderRadius: 0,
    width: '85%',
    marginStart: 0,
    paddingStart: 0,
    marginHorizontal: 20
  },
  landingBackgroundColorLight: {
    flex: 1,
    backgroundColor: Colors.landingBackgroundLightColor
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    paddingStart: 0,
  },
  textDirection: {
    textAlign: "left",
    ...(I18nManager.isRTL && {
      direction: "ltr",
      textAlign: isAndroid ? "left" : "right",
    })
  }
});
