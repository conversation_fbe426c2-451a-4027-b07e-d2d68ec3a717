import { StyleSheet } from "react-native";
import { useTheme } from "@theme/ThemeProvider";
import { typography } from "./typography";
import fonts, { fontWeights } from "./fonts";

const useCommonThemeStyle = () => {
    const { theme, isDarkMode } = useTheme();

    const themeStyle = StyleSheet.create({
        backgroundOverlayColor: {
            backgroundColor: theme.backgroundColor,
            borderTopColor: theme.bgDark
        },
        darkBackgroundColor: {
            backgroundColor: theme.backgroundColor
        },
        container: {
            flex: 1,
            backgroundColor: theme.backgroundColor
        },
        themedTextColor: {
            color: theme.textColor
        },
        h1: {
            ...typography.h1,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h1Light: {
            ...typography.h1,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h1Bold: {
            ...typography.h1,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h2: {
            ...typography.h2,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h2Light: {
            ...typography.h2,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h2Bold: {
            ...typography.h2,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h3: {
            ...typography.h3,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h3Light: {
            ...typography.h3,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h3Bold: {
            ...typography.h3,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h4: {
            ...typography.h4,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h4Light: {
            ...typography.h4,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h4Bold: {
            ...typography.h4,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h5: {
            ...typography.h5,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h5Light: {
            ...typography.h5,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h5Bold: {
            ...typography.h5,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h6: {
            ...typography.h6,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h6Light: {
            ...typography.h6,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h6Bold: {
            ...typography.h6,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h7: {
            ...typography.h7,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h7Light: {
            ...typography.h7,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h7Bold: {
            ...typography.h7,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        h8: {
            ...typography.h8,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        h8Light: {
            ...typography.h8,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        h8Bold: {
            ...typography.h8,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        pageHeading: {
            ...typography.pageHeading,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        pageHeadingLight: {
            ...typography.pageHeading,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        pageHeadingBold: {
            ...typography.pageHeading,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        title: {
            ...typography.title,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        titleLight: {
            ...typography.title,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        titleBold: {
            ...typography.title,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        subTitle: {
            ...typography.subTitle,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        subTitleLight: {
            ...typography.subTitle,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        subTitleBold: {
            ...typography.subTitle,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        smallTitle: {
            ...typography.smallTitle,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        smallTitleLight: {
            ...typography.smallTitle,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        smallTitleBold: {
            ...typography.smallTitle,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        runningText: {
            ...typography.runningText,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        runningTextLight: {
            ...typography.runningText,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        runningTextBold: {
            ...typography.runningText,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        smallText: {
            ...typography.smallText,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        smallTextLight: {
            ...typography.smallText,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        smallTextBold: {
            ...typography.smallText,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        tinyText: {
            ...typography.tinyText,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        tinyTextLight: {
            ...typography.tinyText,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        tinyTextBold: {
            ...typography.tinyText,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
        tinySmallText: {
            ...typography.tinySmallText,
            fontWeight: fontWeights.regular,
            fontFamily: fonts.regular,
            color: theme.thunder100
        },
        tinySmallTextLight: {
            ...typography.tinySmallText,
            fontWeight: fontWeights.light,
            fontFamily: fonts.light,
            color: theme.thunder100
        },
        tinySmallTextBold: {
            ...typography.tinySmallText,
            fontWeight: fontWeights.bold,
            fontFamily: fonts.bold,
            color: theme.thunder100
        },
    });

    return { themeStyle, theme, isDarkMode };
}

export default useCommonThemeStyle;
