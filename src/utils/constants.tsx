/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-unsafe-optional-chaining */
import { BASE_URL, BASIC_AUTH_CRED } from './../api/api_client';
import {AssetsImages, Colors, fontSize, Images, LabelConfig} from '../theme';
import DocumentPicker from 'react-native-document-picker';
import { API_URL } from './../api/urls';
import axios, { AxiosResponse } from 'axios';
import base64 from 'react-native-base64';
import { launchImageLibrary } from 'react-native-image-picker';
import { Toast } from 'native-base';
import { setAPITokenRequest } from './../totara/actions/session';
import event, { EVENT_LISTENER, Events } from './../totara/lib/event';
import { I18nManager, Platform } from 'react-native';
import { store } from '@totara/store';
import ImagePicker from 'react-native-image-crop-picker';
import moment from 'moment';
import i18n from '../../i18n';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CourseProgress from '@models/CourseProgress';
import { navigate } from '@navigation/navigationService';
import { SubItem } from '@components';
import React from 'react';
import crashReportLogger from './crashlyticsLogger';
import { trackHttpRequestMetrics } from './trackHttpRequestMetrics';

export const isOnlyNumbers = (value) => /^[0-9]+(\.[0-9]+)?$/.test(value);

export const data = [
  { label: 'Item 1', value: '1' },
  { label: 'Item 2', value: '2' },
  { label: 'Item 3', value: '3' },
  { label: 'Item 4', value: '4' },
  { label: 'Item 5', value: '5' },
  { label: 'Item 6', value: '6' },
  { label: 'Item 7', value: '7' },

  { label: 'Item 8', value: '8' },
];

export const learningModeData = [
  { label: 'Physical Workshops', isSelected: false },
  { label: 'One to One (mentor)', isSelected: false },
  { label: 'Articles', isSelected: false },
  { label: 'Classrooms', isSelected: false },
  { label: 'Audiobooks', isSelected: false },
  { label: 'eBooks', isSelected: false },
  { label: 'Group Learning', isSelected: false },
  { label: 'Books', isSelected: false },
  { label: 'Online', isSelected: false },
  { label: 'Videos', isSelected: false },
];
export const interestAray = [
  { label: 'Technology', isSelected: false },
  { label: 'Design', isSelected: false },
  { label: 'Innovation', isSelected: false },
  { label: 'Data Science', isSelected: false },
  { label: 'Information Technology', isSelected: false },
  { label: 'Self Development', isSelected: false },
  { label: 'Future', isSelected: false },
  { label: 'Collaboration', isSelected: false },
  { label: 'Cloud Computing', isSelected: false },
  { label: 'Communication', isSelected: false },
];

export const onBoarding = [
  {
    image: AssetsImages.onBoarding1,
    heading: 'Unlock a new\nlearning experience ',
    subHeading: 'Embark on a new learning adventure and reach your true potential with us',
  },
  {
    image: Images.launchScreen,
    heading: 'Smooth learning with\ntrusted mentors ',
    subHeading: 'Guided by experienced mentors, your learning journey will be expectional.',
  },
];

export const homeCategories = [
  {
    Icon: 'hanshake',
    Text: 'Business',
  },
  {
    Icon: 'Isolation_Mode',
    Text: 'Design',
  },
  {
    Icon: 'view_in_ar',
    Text: 'Data',
  },
  {
    Icon: 'hanshake',
    Text: 'Business',
  },
  {
    Icon: 'hanshake',
    Text: 'Business',
  },
];

export const learningPaths = [
  {
    Icon: 'hanshake',
    Text: 'Lorem Ipsum',
    courses: '12',
    backgroundColor: 'rgba(125, 161, 196, 0.40)',
    hours: '80',
  },
  {
    Icon: 'Isolation_Mode',
    Text: 'Lorem Ipsum',
    courses: '09',
    backgroundColor: 'rgba(199, 183, 170, 0.40)',
    hours: '62',
  },
];

export const releaseData = [
  {
    image: AssetsImages.release1,
    title: 'Lorem Ipsum',
  },
  {
    image: AssetsImages.release2,
    title: 'Lorem Ipsum',
  },
];

export const categories = [
  {
    Icon: 'hanshake',
    Text: 'Business Intelligence',
  },
  {
    Icon: 'Isolation_Mode',
    Text: 'Cloud Computing',
  },
  {
    Icon: 'view_in_ar',
    Text: 'Experience & Visual Design',
  },
];

export const categoryListing = [
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'Gamification: Motivation Psychology & The Art of Engagement',
    tag: 'e-learning',
    hours: '2.5 hours',
    date: '13 December',
    rating: '2.3',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
  {
    url: 'officeimage',
    text: 'User Experience Design Fundamentals',
    tag: 'On premise',
    hours: '8 hours',
    date: '13 December',
    rating: '5',
  },
];

export const categoryDetail = {
  info: {
    courseContent: [
      'Understand the importance of ethical behavior in the workplace',
      'Essentials of work place ethics',
      'Skills for navigating complex situations',
      'Significance of inclusivity in workplace',
      'Understand the importance of ethical behavior in the workplace',
      'Essentials of work place ethics',
      'Skills for navigating complex situations',
      'Significance of inclusivity in workplace',
    ],
  },
  lessons: [
    {
      heading: 'Introduction',
      des: 'Summary of the course',
    },
    {
      heading: 'Foundations of Ethical Conduct',
      des: 'Five key principles of design ethically',
    },
    {
      heading: 'Code of Conduct Elements',
      des: 'Practices & procedures to follow to be complaint.',
    },
    {
      heading: 'Ethical Decision-Making',
      des: 'Taking decisions with leadership',
    },
    {
      heading: 'Introduction',
      des: 'Summary of the course',
    },
    {
      heading: 'Foundations of Ethical Conduct',
      des: 'Five key principles of design ethically',
    },
    {
      heading: 'Code of Conduct Elements',
      des: 'Practices & procedures to follow to be complaint.',
    },
    {
      heading: 'Ethical Decision-Making',
      des: 'Taking decisions with leadership',
    },
  ],
  reviews: {
    rating: [80, 60, 35, 20, 10],
    userReviews: [
      {
        url: 'officeimage',
        name: 'Alhussain Madani',
        rating: 4.5,
        time: '2 days ago',
        comment:
          'As someone relatively new to the concept of workplace ethics, this session provided a solid introduction. I appreciate the practical approach,especially the real-world case studies',
      },
      {
        url: 'officeimage',
        name: 'Alhussain Madani',
        rating: 4.5,
        time: '2 days ago',
        comment:
          'As someone relatively new to the concept of workplace ethics, this session provided a solid introduction. I appreciate the practical approach,especially the real-world case studies',
      },
      {
        url: 'officeimage',
        name: 'Alhussain Madani',
        rating: 4.5,
        time: '2 days ago',
        comment:
          'As someone relatively new to the concept of workplace ethics, this session provided a solid introduction. I appreciate the practical approach,especially the real-world case studies',
      },
      {
        url: 'officeimage',
        name: 'Alhussain Madani',
        rating: 4.5,
        time: '2 days ago',
        comment:
          'As someone relatively new to the concept of workplace ethics, this session provided a solid introduction. I appreciate the practical approach,especially the real-world case studies',
      },
    ],
  },
};

export const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

export const parseAndFormatDuration = (duration) => {
  if (!isOnlyNumbers(duration)) {
    return duration;
  }

  const { t } = useTranslation();
  return Number(duration) >= 2
    ? `${convertNumbersToArabicNumerals(duration)} ${t('hours')}`
    : `${convertNumbersToArabicNumerals(duration)} ${t('hour')}`;
};

const formatHour = (hour) => {
  return i18n.t(hour === 1 ? 'hour' : 'hours');
};

const formatMinute = (minute) => {
  return i18n.t(minute > 1 ? 'mins' : 'min');
};

const parseDurationToHours = (duration) => {
  return duration;
};
export const pickImage = async (token, getFileName, docType, emiratesId) => {
  try {
    const result = await launchImageLibrary({
      mediaType: 'photo',
      selectionLimit: 1,
      maxWidth: 300,
      maxHeight: 300,
      quality: 0.5,
      includeBase64: true,
    });
    if (result) {
      if (result?.assets?.length) {
        const name = result?.assets[0]?.fileName;
        const type = result?.assets[0]?.type;
        const uri = result?.assets[0]?.uri;
        const size = result?.assets[0]?.fileSize;
        const formData = new FormData();

        formData.append('emiratesId', emiratesId);
        formData.append('file', {
          uri: uri,
          type,
          name: name,
        });

        const file = await uploadObsDoc(formData, token, docType);

        return {
          fileName: file?.fileName,
          size,
          uri,
          name,
          base64: file?.imageUrl,
          base64String:result?.assets[0]?.base64
        };
      }
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'constants.tsx',
      additionalInfo: 'Failed to pick image'
    })
    // if (DocumentPicker.isCancel(err)) {
    // }
    return null;
  }
};

export const pickImageFromCamera = async (token, getFileName, docType, emiratesId) => {
  try {
    const result = await ImagePicker.openCamera({
      width: 300,
      height: 400,
      cropping: true,
    });
    if (result) {
      if (result) {
        const imagePath = result?.path;
        const fileName = imagePath?.split('/').pop();
        const name = fileName;
        const type = result?.mime;
        const uri = result?.path;
        const size = result?.size;
        const formData = new FormData();

        formData.append('emiratesId', emiratesId);
        formData.append('file', {
          uri: uri,
          type,
          name: name,
        });

        const file = await uploadObsDoc(formData, token, docType);

        return {
          fileName: file?.fileName,
          size,
          uri,
          name,
          base64: file?.imageUrl,
        };
      }
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'constants.tsx',
      additionalInfo: 'Failed to pick image from camera'
    })
    return null;
  }
};

export const pickDocument = async (token, getFileName, docType, emiratesId) => {
  try {
    const result = await DocumentPicker.pick({
      type: docType == 'pdfOnly' ? [DocumentPicker.types.pdf] : [DocumentPicker.types.pdf],
    });
    if (result) {
      const { name, size, type, uri } = result[0];
      if (size > 5000120) return alert('File Size Limit Exceeded.');
      const formData = new FormData();

      formData.append('emiratesId', emiratesId);
      formData.append('file', {
        uri: uri,
        type,
        name: name,
      });

      const file = await uploadObsDoc(formData, token, docType);

      return {
        fileName: file?.fileName,
        size,
        uri,
        name,
        base64: file?.imageUrl,
      };
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'constants.tsx',
      additionalInfo: 'Failed to pick document'
    })
    // if (DocumentPicker.isCancel(err)) {
    // }
    return null;
  }
};

export const uploadObsDoc = async (formData, token, docType) => {
  const url = BASE_URL + API_URL.documentUpload.url + docType;
  let result: AxiosResponse | undefined;
  try {
    result = await axios.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${token}`,
      },
    });

    if (result?.data?.description) {
      const imageUrl = await getUrl(result?.data?.fileName, token);

      return {
        fileName: result?.data?.fileName,
        imageUrl,
      };
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'constants uploadObsDoc',
      additionalInfo: 'Failed to upload documet'
    });
  } finally {
    if (result) trackHttpRequestMetrics(url, 'POST', result, 'constants uploadObsDoc');
  }
};

export const getUrl = async (fileName, token) => {
  if (fileName) {
    const basee64 = await getBase64Url(fileName, token);
    return basee64;
  }
};

export const getCurrentWorkExperience = (records) => {
  if (records?.length) {
    return records[0];
  } else {
    return [];
  }
};

export const getBase64Url = async (imageName, token) => {
  if (imageName) {
    const url = BASE_URL + API_URL.getObsDoc.url + `/${imageName}`;
    let result: AxiosResponse | undefined;
    try {
      result = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (result?.data?.contentUrl) {
        return result?.data?.contentUrl;
      }
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'constants getBase64Url',
        additionalInfo: 'Failed to get base64Url'
      });
    } finally {
      if (result) trackHttpRequestMetrics(url, 'GET', result, 'constants getBase64Url');
    }
  }
};

export function addEllipsesInCenter(text, maxLength) {
  if (text?.length <= maxLength) {
    return text;
  }

  const ellipsisLength = 3;
  const remainingLength = maxLength - ellipsisLength;
  const startLength = Math.ceil(remainingLength / 3);
  const endLength = Math.floor(remainingLength / 2);

  return text?.substring(0, startLength) + '...' + text?.substring(text?.length - endLength);
}

export const getCustomHeaders = (token?: string, NoBearer?: boolean) => {
  const BasicAuth =
    'Basic ' + base64.encode(`${BASIC_AUTH_CRED.username}:${BASIC_AUTH_CRED.password}`);
  let headers;
  if (token) {
    headers = {
      Authorization: 'Bearer ' + token,
      'Content-Type': 'application/json',
    };
  } else {
    headers = {
      Authorization: BasicAuth,
      'Content-Type': 'application/json',
    };
  }

  return headers;
};

export const getHeaders = (token?: string) => {
  const ntoken = store.getState().getWFMProfile.response?.liferayaccesstoken;

  const headers = {
    Authorization: 'Bearer ' + ntoken,
    'Content-Type': 'application/json',
  };

  return headers;
};

export const pickListType = {
  interest: 'Interests',
  hobbies: 'Hobbies',
  countriesVisit: 'CountriesVisits',
  passportType: 'PassportType',
  relation: 'Relationship',
  preferences: 'Preferences',
  skills: 'skills',
  askRecommendationList: 'RecommendationRequestRelations',
  writeRecommendationList: 'RecommendationWriteRelations',
  recommendInterest: 'RecommendInterest',
};

export const documents = {
  profilePicture: 'profilepicture',
  video: 'video',
  resume: 'resume',
  emiratesCopy: 'emiratescopy',
  familyBook: 'familybook',
  passport: 'passport',
  educationCertificate: 'educationcertificate',
  training: 'training',
  achievement: 'achievement',
  volunteer: 'volunteer',
  assessment: 'assessment',
};

export const socialMedia = [
  {
    icon: AssetsImages.in_grey,
    url: 'linkedInURL',
  },
  {
    icon: AssetsImages.fb_grey,
    url: 'facebook',
  },
  {
    icon: AssetsImages.insta_grey,
    url: 'instagram',
  },
  {
    icon: AssetsImages.x_grey,
    url: 'twitter',
  },
  {
    icon: AssetsImages.teams_grey,
    url: 'teamsId',
  },
  {
    icon: AssetsImages.mail_grey,
    url: 'email',
  },
];

export const aboutComponentsTabsButtons = [
  'Personal Details',
  'CV',
  'Emirates ID',
  'Passport Details',
  'Family Book',
];

export const isDisabled = true;

export const hitSlop = {
  right: 50,
  left: 50,
  top: 50,
  bottom: 50,
};

export const comingSoon = () =>
  Toast.show({ text: 'Coming soon', textStyle: { textAlign: 'left' } });

export const expireSession = async (dispatch) => {
  dispatch(setAPITokenRequest(undefined));
  await AsyncStorage.removeItem('logindate');
  await AsyncStorage.removeItem('tokenExpiry');
  event.emit(EVENT_LISTENER, { event: Events.Logout });
  dispatch(setAPITokenRequest(undefined));
};

export const nestedFields =
  'profileinsights,workExperience,mentoringExperience,personalDocuments,workExperience,familyBook,passportDetails,languagesUser,education,skill,training,achievements,assessments,performance';

export const telentProfileSections = [
  {
    title: 'About',
    route: 'EditFormTelentProfile',
    heading: 'Edit About',
    type: 'About',
    icon: AssetsImages.aboutIcon,
    showCollapse: true,
    showWarning: true,
    showEdit: true,
  },
  {
    title: 'Work Experience',
    route: 'WorkExperienceList',
    icon: AssetsImages.workExpIcon,
    showCollapse: true,
    showWarning: true,
    showEdit: true,
  },
  {
    title: 'Education',
    route: 'EducationList',
    icon: AssetsImages.educationIcon,
    showCollapse: true,
    showWarning: true,
    showEdit: true,
  },
  {
    title: 'Trainings & Certificates',
    route: 'TrainingList',
    icon: AssetsImages.trainingIcon,
    showCollapse: true,
    showWarning: true,
    showEdit: true,
  },
  {
    title: 'Recommendations',
    route: 'RecommendationList',
    icon: AssetsImages.recommendationIcon,
    showCollapse: true,
    showWarning: false,
    showEdit: true,
  },
  {
    title: 'Assessments',
    showCollapse: true,
    icon: AssetsImages.trainingIcon,
    showEdit: true,
    showWarning: false,
  },
];

export function splitCamelCase(str) {
  return str.split(/(?=[A-Z])/).join(' ');
}

export function capitalizeFirstLetter(string) {
  return string?.charAt(0).toUpperCase() + string?.slice(1);
}

export const sortArray = (list, key, viewAll) => {
  if (viewAll) {
    return list?.sort((a, b) => {
      return new Date(b[key]) - new Date(a[key]);
    });
  } else {
    return list
      ?.filter((item) => item.publish)
      .sort((a, b) => {
        return new Date(b[key]) - new Date(a[key]);
      });
  }
};

export const extractId = (fullmessage) => {
  const urlPattern = /https:\/\/\S+\/course\/view\.php\?id=(\d+)/;
  const match = fullmessage.match(urlPattern);

  if (match && match[1]) {
    return match[1];
  }

  return null;
};

export const validateYouTubeUrl = (url) => {
  const regex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
  return regex.test(url);
};

export function containsHTML(str) {
  const pattern = /<\/?[a-z][\s\S]*>/i;
  return pattern.test(str);
}

export const tabData = [
  {
    text: 'Light',
    value: 30,
  },
  {
    text: 'Medium',
    value: 60,
  },
  {
    text: 'Intense',
    value: 120,
  },
];

export const getCurrentDayName = (type) => {
  const days =
    type === 'short'
      ? ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
      : ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const d = new Date();
  const dayName = days[d.getDay()];
  return dayName;
};

export function convertMinutesToHours(minutes) {
  return +minutes / 60;
}

export function calculateYaxisLabels(totalHours, ArrayLength) {
  if (+totalHours === 0) return [50, 100, 150, 200];
  const devideValue = 4;
  const devidedValue = Math.ceil(+totalHours / devideValue);
  if (+totalHours === 0) return [50, 100, 150, 200];

  if (devidedValue < 1) return [0, 1, 2, 3, 4];

  const result = [];

  ArrayLength.forEach((value, index) => {
    if (index === ArrayLength) {
      result.push(value + devidedValue);
    } else {
      if (index === 0) {
        result.push(devidedValue);
      } else {
        result.push(result[0] + result[0] * index);
      }
    }
  });

  return result;
}

export const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const streakFilters = ['week', 'month', 'year'];

export const getSortedData = async (data, type) => {
  const labels = [];
  const graphData = [];
  let sortedData = [];
  let rowData = [];
  let totalMinutes = 0;
  let startMonth = '';
  let endMonth = '';

  if (type === 'week') {
    sortedData = data?.weekrecord;
    rowData = data?.weekrecord;
  } else if (type === 'month') {
    sortedData = data?.current_month_record;
    rowData = data?.current_month_record;
  } else {
    sortedData = data?.previous_months;
    rowData = data?.previous_months;

    if (type === '6month') {
      startMonth = sortedData[0]?.month?.split(' ')?.[0];
      endMonth = sortedData[sortedData?.length - 1]?.month;
    }
  }

  // sortedData = (type == "6month" || type == "year") ? sortedData?.reverse() : sortedData

  await sortedData.forEach((element: any, index: number) => {
    const value = +(+element?.mins / 60)?.toFixed(1);
    if (type === 'week') {
      labels.push(element?.day);
      graphData.push({ value, xAxis: index + 1 });
    } else if (type === 'month') {
      if (
        index === 3 ||
        index === 10 ||
        index === 17 ||
        index === 23 ||
        index === sortedData?.length - 1
      ) {
        labels.push(parseInt(element?.date?.split('-')[0]));
      } else {
        labels.push(' ');
      }
      graphData.push({ value, xAxis: index + 1 });
    } else if (type === '6month') {
      const month = element?.month?.split(' ')[0];
      labels.push(month);
      graphData.push({ value, xAxis: index + 1 });
    } else {
      const month = element?.month[0];
      labels.push(month);
      graphData.push({ value, xAxis: index + 1 });
    }

    totalMinutes = totalMinutes + convertMinutesToHours(+element?.mins);
  });

  return {
    labels,
    graphData,
    totalMinutes,
    startMonth,
    endMonth,
    rowData,
  };
};

export const sortCalendarResponse = (array) => {
  const response = {};
  array.forEach((item) => {
    response[item?.date] = {
      progress: item?.progress,
    };
  });

  return response;
};

export function formatDate(date) {
  const dateArray = date?.split('/');
  return dateArray[2] + '-' + dateArray[0] + '-' + dateArray[1];
}

export const profileTabs = [
  {
    name: 'My Profile',
    id: 'profile',
  },
  {
    name: 'Skills',
    id: 'skills',
  },
  {
    name: 'Personal Details',
    id: 'personaldetails',
  },
];

export const learningPathwaysHomeTabs = [
  {
    id: 'required',
    name: 'Required',
  },
  {
    id: 'recommended',
    name: 'Recommended',
  },
  {
    id: 'personal',
    name: 'My Choices',
  },

];

export const myLearningTabs = [
  {
    id: 'inprogress',
    name: 'Enrolled',
  },
  {
    id: 'required',
    name: 'Required',
  },
  {
    id: 'favourites',
    name: 'Favourites',
  },
];

export const talentProfileGradient = [
  'rgba(255, 255, 255, 1)',
  'rgba(255, 255, 255, 0)',
  'rgba(255, 255, 255, 0)',
];

export const talentProfileSkillTab = [
  {
    title: 'My Skills',
    route: 'SkillList',
    icon: AssetsImages.skillIcon,
    showAdd: false,
    showCollapse: false,
    addTitle: 'Edit my skills',
    showEdit: true,
    showWarning: true,
  },
];

export const tabScreenOptions = {
  tabBarIndicatorStyle: {
    backgroundColor: '#000',
  },
  tabBarLabelStyle: {
    fontSize: fontSize.small,
  },
};

export const cameraList = [
  {
    id: '1',
    title: 'View profile photo',
    icon: AssetsImages.viewProfilePhoto,
  },
  {
    id: '2',
    title: 'Take a photo',
    icon: AssetsImages.cameraIcon,
  },
  {
    id: '3',
    title: 'Upload from photos',
    icon: AssetsImages.gallery,
  },
];

export const fileOptions = [
  {
    id: '1',
    title: 'Take Picture',
    icon: AssetsImages.cameraIcon,
  },
  {
    id: '2',
    title: LabelConfig.supportCenter.selectFromGallery,
    icon: AssetsImages.viewProfilePhoto,
  },
  {
    id: '3',
    title: LabelConfig.supportCenter.selectFile,
    icon: AssetsImages.file,
  },
];

export const openCamera = () => {
  ImagePicker.openCamera({
    width: 300,
    height: 400,
    cropping: true,
  }).then((image) => { });
};

export const TrendingCourseCategory = [
  {
    id: 1,
    name: 'Artificial Intelligence',
  },
  {
    id: 2,
    name: 'Business Management',
  },
  {
    id: 3,
    name: 'Business Managements',
  },
];

function formatDateYYYYMMDD(dateString) {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed, so we add 1
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export function addSpareTime(events) {
  const dayStart = '00:00:00';
  const dayEnd = '24:00:00';
  const updatedEvents = [];
  if (!events?.length) {
    updatedEvents.push({
      start: formatDateYYYYMMDD(new Date()) + ' 00:00:00',
      end: formatDateYYYYMMDD(new Date()) + ' 24:00:00',
      startdatetime: formatDateYYYYMMDD(new Date()) + 'T00:00:00',
      enddatetime: formatDateYYYYMMDD(new Date()) + 'T23:59:00',
      title: 'Spare Time',
      summary: '',
      color: 'rgba(255, 255, 255, 1)',
      tag: 'Spare time',
      name: 'Spare Time',
      height: '100%',
    });
    return updatedEvents;
  }

  events.sort((a, b) => new Date(a?.startdatetime) - new Date(b?.startdatetime));

  // Helper function to format date to "YYYY-MM-DD HH:MM:SS"
  function formatDate(date) {
    return date.toISOString().replace('T', ' ').substring(0, 19);
  }

  // Add spare time at the beginning of the day if necessary
  const firstEventStart = new Date(events[0].startdatetime);
  if (firstEventStart > new Date(`${firstEventStart.toISOString().split('T')[0]}T${dayStart}`)) {
    const spareTimeEvent = {
      startdatetime: `${firstEventStart.toISOString().split('T')[0]}T${dayStart}`,
      enddatetime: events[0].startdatetime?.split('.000Z')?.[0],
      start: `${firstEventStart.toISOString().split('T')[0]}T${dayStart}`,
      end: formatDate(new Date(events[0].startdatetime)),
      title: 'Spare Time',
      // ... (other properties)
    };
    updatedEvents.push(spareTimeEvent);
  }

  // Add events and spare time between them
  for (let i = 0; i < events.length; i++) {
    // Add the current event with additional keys
    const currentEvent = {
      ...events[i],
      start: events[i].startdatetime?.replace('T', ' ')?.split('.000Z')?.[0],
      end: events[i].enddatetime?.replace('T', ' ')?.split('.000Z')?.[0],
      startdatetime: events[i].startdatetime?.split('.000Z')?.[0],
      enddatetime: events[i].enddatetime?.split('.000Z')?.[0],
    };
    updatedEvents.push(currentEvent);

    if (i < events.length - 1) {
      if (i < events.length - 1) {
        const spareTimeEvent = {
          startdatetime: events[i].enddatetime?.split('.000Z')?.[0],
          enddatetime: events[i + 1].startdatetime?.split('.000Z')?.[0],
          start: events[i].enddatetime?.replace('T', ' ')?.split('.000Z')?.[0],
          end: events[i + 1].startdatetime?.replace('T', ' ')?.split('.000Z')?.[0],
          title: 'Spare Time',
        };
        updatedEvents.push(spareTimeEvent);
      }
    }
  }

  // Add spare time at the end of the day if necessary
  const lastEventEnd = new Date(events[events.length - 1].enddatetime);
  const endOfDay = new Date(`${lastEventEnd.toISOString().split('T')[0]}T${dayEnd}`);
  if (lastEventEnd < endOfDay) {
    const spareTimeEvent = {
      startdatetime: events[events.length - 1].enddatetime?.split('.000Z')?.[0],
      enddatetime: `${lastEventEnd.toISOString().split('T')[0]}T23:59:00`,
      start: formatDate(new Date(events[events.length - 1].enddatetime)),
      end: `${lastEventEnd.toISOString().split('T')[0]}T${dayEnd}`
        .replace('T', ' ')
        .substring(0, 19),
      title: 'Spare Time',
    };
    updatedEvents.push(spareTimeEvent);
  }

  return updatedEvents;
}

export const PopularSearchesData = [
  {
    id: 1,
    name: 'Machine learning',
  },
  {
    id: 2,
    name: 'Project management',
  },
  {
    id: 3,
    name: 'Data analysis',
  },
  {
    id: 4,
    name: 'UX design',
  },
  {
    id: 5,
    name: 'AI',
  },
];

export const RecentSearchesData = [
  {
    id: 1,
    name: 'Machine learning',
  },
  {
    id: 2,
    name: 'Project management',
  },
  {
    id: 3,
    name: 'Data analysis',
  },
  {
    id: 4,
    name: 'UX design',
  },
  {
    id: 5,
    name: 'AI',
  },
];

export const requiredLearning = [
  {
    name: 'HRCI Human Resource Associate program',
    months: '3 months',
    courses: '16 courses',
    overdue: '2 days overdue',
    provider: 'Gov Academy',
    image: AssetsImages.secondCourse,
  },
  {
    name: 'Advanced HR conflict resolution program',
    months: '5 months',
    courses: '23 courses',
    provider: 'Gov Academy',
    progress: '70',
    currentModule: true,
    inProgress: true,
    image: AssetsImages.requiredCourse,
  },
  {
    name: 'Social media marketing',
    months: '5 months',
    courses: '23 courses',
    provider: 'LinkedIn Learning',
    showPeopleJoined: true,
    progress: '70',
    image: AssetsImages.secondCourse,
  },
];

export const flipIconStyles = { transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] };

export function excelJsonToFormatedJson(json) {
  function getContentWithStartingChar(item) {
    const keysX = Object.keys(item).map((item) => item.slice(0, 2).toLowerCase());
    const values = Object.values(item);
    const resultX = {};

    keysX.forEach((item, index) => {
      resultX[item] = values[index];
    });

    return resultX;
  }

  const keys = Object.keys(json[0]).map((item) => item.slice(0, 2).toLowerCase());
  const result = {};

  keys.forEach((item, index) => {
    json.forEach((jsonItem, jsonIndex) => {
      if (!result[item]) {
        result[item] = { translation: {} };
        return;
      }
      result[item]['translation'] = {
        ...result[item]['translation'],
        [getContentWithStartingChar(jsonItem)['en']]: getContentWithStartingChar(jsonItem)[item],
      };
    });
  });
  return result;
}

export function getFormattedCurrentTime() {
  const date = new Date();
  const { t, i18n } = useTranslation();
  const locale = I18nManager.isRTL ? 'ar-AE' : 'en-GB';

  const options = {
    month: 'short',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Dubai',
  };
  const formattedDate = new Intl.DateTimeFormat(locale, options).format(date);
  const [day, month, year] = formattedDate.split(' ');

  const currentTime = moment().locale('en').format('hh:mm a');
  const [time, period] = currentTime?.split(' ');

  return i18n?.language === 'ar'
    ? `${t('As of')} ${convertNumbersToArabicNumerals(day)} ${month} ${convertNumbersToArabicNumerals(year)} ${time}` +
    ' ' +
    t(period)
    : `${t('As of')} ${day} ${month} ${year} | ${time}` + ' ' + t(period);
}

export function lineHeight(fontSize) {
  return undefined;
  if (!I18nManager.isRTL) {
    return undefined;
  }
  const multiplier = fontSize > 20 ? 1.5 : 2;
  return parseInt(fontSize + fontSize * multiplier, 10);
}

export const fileNameRegex = /(?:_[^_]+_)([^_]+)(?=_[^_]+\.pdf)/;

export const marginTop = {
  marginTop: Platform.OS == 'android' && I18nManager.isRTL ? -1 : I18nManager.isRTL ? -3 : 0,
};

export function convertNumbersToArabicNumerals(str) {
  return str;
  if (!I18nManager.isRTL) return str;
  str = str?.toString();
  const id = ['۰', '۱', '۲', '٣', '٤', '۵', '٦', '۷', '۸', '۹'];

  const idObj = {
    '0': '0',
    '1': '1',
    '2': '2',
    '3': '3',
    '4': '4',
    '5': '5',
    '6': '6',
    '7': '7',
    '8': '8',
    '9': '9',
  };

  return str
    ?.split('')
    ?.map((item, index) => {
      return idObj?.[item];
    })
    ?.join('');
}

export const lineHeightForAndroidLTRMini = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 16 };
export const lineHeightForAndroidLTRSmall = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 20 };
export const lineHeightForAndroidLTRMed = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 22 };
export const lineHeightForAndroidLTR = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 24 };
export const lineHeightForAndroidLTRLarge = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 28 };
export const lineHeightForAndroidLTRXLarge = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 34 };
export const lineHeightForAndroidLTRXXLarge = Platform.OS == 'android' &&
  !I18nManager.isRTL && { lineHeight: 38 };

export const widgetPadding = 16;
export const defaultCreator = '';

export const progressTabs = [
  {
    id: 'all',
    name: 'All',
  },
  {
    id: 'inprogress',
    name: 'In Progress',
  },
  {
    id: 'notstarted',
    name: 'Not started',
  },
];

export const reportSection = [
  {
    name: 'Competency Review',
    detail: 'Review your skills and track your progress',
    image: AssetsImages.competency,
    background: 'rgba(229, 236, 243, 1)',
  },
  {
    name: 'IDP Reports',
    detail: 'View your IDP feedback reports and track your progress',
    image: AssetsImages.idpReports,
    background: 'rgba(238, 238, 238, 1)',
  },
];

export const myLearningRecordData = [
  {
    title: 'Completed courses',
    key: 'Self-paced',
    icon: AssetsImages.completedCourses,
    data: [] as CourseProgress['requiredcourses'],
    backgroundColor: Colors.learningCredentialItem1,
    route: 'MyLearningRecords',
    lastCompletedHeading: 'Last completed:',
    lastCompletedDes: 'Organisational code of conduct',
  },
  {
    title: 'Earned certificates',
    icon: AssetsImages.earnedCertificate,
    data: [],
    backgroundColor: Colors.learningCredentialItem2,
    key: 'Self-paced',
    route: 'MyLearningRecordsCertificates',
    lastCompletedHeading: 'Last certificate:',
    lastCompletedDes: 'HR capability program',
  },
  {
    title: 'Badges',
    icon: AssetsImages.military_tech,
    data: [],
    backgroundColor: Colors.learningCredentialItem3,
    key: 'My-Badge',
    route: 'MyBadges',
    lastCompletedHeading: 'Badge:',
    lastCompletedDes: 'Badge program',
  },
];
export const handleCourseNavigation = (item, inProgress) => {
  const courseid = item?.courseid || item?.id;

  if (item?.type === 'course' || item?.type == 'Course') {
    const isMultiDay = item?.course_day == 'Multi Day' || item?.course_days == 'Multi Day';

    const route =
      item?.course_type == 'Self-paced' || item?.coursetype == 'Self-paced'
        ? 'CourseDetail'
        : isMultiDay
          ? 'MultipleSession'
          : 'CourseDetail';


    navigate(route, { courseid: courseid, inProgress });
  }
};

export const navigateToCourseDetail = (item, navigation, inProgress) => {
  if ((item.type == 'program' || item.type == 'Program') && item.onboarding_program) {
    navigation.navigate('NewJoinerPrograms', { id: item.id });
  } else if (item.type != 'course' && (item.type == 'program' || item.type == 'Program')) {
    navigation.navigate('ProgramDetails', {
      id: item.id,
      name: item?.title,
      months: item?.duration,
      courses: item?.total_course,
      overDue: item?.due_date,
      provider: item?.curator_name || '',
      progress: item?.progress || '0',
      // isDetail,
      // itemStyle,
      showInProgress: item.status == 'inprogress' || false,
      // showPeopleJoined,
      // showMore,
      showRating: item?.rating,
      // showCurrentModule,
    });
  } else {
    handleCourseNavigation(item, inProgress);
  }
};

export const RenderContentType = ({ item, type, styles, t, textStyle, iconStyle }) => {
  const numberOfContent = (type == 'course' || type == 'Course') ? (item?.total_lesson || item.activity_count || '1') :
    (item.total_course || item.activity_count || '1');
  const contentType = (type == 'course' || type == 'Course') ? (numberOfContent > 1 ? `${t('Activities')}` : `${t('Activity')}`)
    : (numberOfContent > 1 ? `${t('Courses')}` : `${t('Course')}`)
  return (
    <SubItem
      iconStyle={iconStyle ? iconStyle : styles.iconStyle}
      headingStyle={textStyle ? textStyle : styles.headingStyle}
      heading={`${numberOfContent} ${contentType}`}
      icon={AssetsImages.lessonIcon}
    />
  )
}

export enum YesNoEnum {
  YES = 'YES',
  NO = 'NO',
}

export const ASYNC_STORAGE_KEYS = {
  BIOMETRICS: {
    LOGIN_METHOD: 'LOGIN_METHOD',
    LOGIN: 'LOGIN',
    SEEN_FEATURE: 'SEEN_FEATURE',
    DEVICE_ID: 'DEVICE_ID',
  }
};

export const emailOtpExpiry = 300; // seconds

export const bottomSheets = {
  request: {
    headingText: 'Your request to enroll in this course has been received!',
    primaryButtonText: 'Close',
    secondaryButtonText: '',
    subheadingText: 'Once your request has been approved, we’ll notify you of the outcome.',
    showOnlyPrimaryButton: true,
  },
  cancel: {
    headingText: 'Are you sure you want to cancel your request?',
    primaryButtonText: 'Keep request',
    secondaryButtonText: 'Cancel request',
    subheadingText: 'Canceling your request cannot be undone. Are you sure you want to proceed?',
    showOnlyPrimaryButton: false,
  },
  missingLineManager: {
    headingText: 'You don’t have a Line Manager assigned!',
    primaryButtonText: 'Close',
    secondaryButtonText: '',
    subheadingText: 'Please contact the administrator.',
    showOnlyPrimaryButton: true,
  },
}

export const TOKEN_ERROR_CODES = {
  INVALID_TOKEN: 'invalidtoken',
};
  
  

export const BIOMETRICS_ERRORS = {
  LOCAL: {
    SIGNATURE_FAILED: 'Signature failed',
    KEY_PERMANENTLY_INVALIDATED: 'Error generating signature: Key permanently invalidated', // this error occurs when the user changes their biometric settings (e.g., adds a new fingerprint or face) after the key was created
  },
  SERVER: {
    OK: {
      AUTH_FAILED: 'Biometric authentication failed', // 2 devices registered with same account
      INVALID_REQUEST: 'Invalid request, please try again with correct login details', // corrupted stored biometric data
    },
    BAD_REQUEST: {
      CONTRACT_FAIL: 'Request failed with status code 400', // this error occurs when the server is unable to process the request due to a contract failure, such as additional or wrong params not in API contract
    }
  }
};

// DEV/STG = 30s, PROD = 12h
export const MIN_FF_FETCH_INTERVAL_MS = __DEV__ ? 30000 : ********;

export const apiDefaults = {
  AI: false,
  AMS: false,
  Calendar: false,
};

export const frbsDefaults = {
  scholarships: false,
  idp: false,
  personalLists: true,
  biometrics: true,
  altEmailLogin: true,
  altOtpLogin: true,
  biometricsDebug: true,
  digitalLearningChallengeBanner: false,
  writeReview: false,
  aiFeature: true,
  coaching: true,
};
