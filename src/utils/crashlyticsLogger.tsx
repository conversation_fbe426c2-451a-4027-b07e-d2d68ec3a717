import crashlytics from '@react-native-firebase/crashlytics';
import { AxiosResponse } from 'axios';

const crashReportLogger = (
  error: Error,
  context: {
    component?: string;
    url?: string;
    additionalInfo?: string;
    serverResponse?: Response | AxiosResponse | undefined;
  } = {},
) => {
  let recordedError: Error;
  if (error instanceof Error) {
    recordedError = error;
  } else {
    // Create a detailed error message
    const errorMessage = typeof error === 'string' ? error : JSON.stringify(error);
    recordedError = new Error(`Non-lethal error thrown ${errorMessage}`);
  }
  const { component, url, additionalInfo, serverResponse } = context;

  let logMessage = `Error encountered: ${recordedError.message}`;

  if (component) logMessage += ` | Component: ${component}`;

  if (url) logMessage += ` | URL: ${url}`;

  if (additionalInfo) logMessage += ` | Additional Info: ${additionalInfo}`;

  if (serverResponse) logMessage += ` | Additional Info: ${serverResponse}`;

  crashlytics().log(logMessage);
  crashlytics().recordError(recordedError);
};

export default crashReportLogger;
