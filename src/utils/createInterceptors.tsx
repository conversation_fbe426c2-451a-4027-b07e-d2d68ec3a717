import axios from 'axios';
import AsyncStorage from "@react-native-async-storage/async-storage";
import crashReportLogger from './crashlyticsLogger';
import { TOKEN_ERROR_CODES } from './constants';
import { navigationRef } from '../navigation/navigationService';
import { confirmedLogout } from '@totara/actions/getWFMProfileAction';

export const createInterceptors = (dispatch: any) => {
  // Fetch Wrapper
  const setupFetchWrapper = () => {
    const originalFetch = window.fetch;

    window.fetch = async (input, init) => {
      const response = await originalFetch(input, init);

      const checkTokenErrorCode = (response: any) => {
        if (response?.errorcode && response?.errorcode === TOKEN_ERROR_CODES.INVALID_TOKEN) {
          crashReportLogger({} as Error, {
            component: 'createInterceptors fetch.errorcode',
            additionalInfo: 'Invalid token!',
          });
          dispatch(confirmedLogout(navigationRef.current));
        }
      };

      const clonedResponse = response.clone();
      checkTokenErrorCode(await clonedResponse.json());
      return response;
    };

    // Cleanup function to restore the original fetch
    return () => {
      window.fetch = originalFetch;
    };
  };

  // Axios Request Interceptor
  const requestInterceptor = axios.interceptors.request.use(
    (config) => {
      return config;
    },
    (error) => {
      crashReportLogger(error as Error, {
        component: 'createInterceptors',
        additionalInfo: 'Request error',
      });
      return Promise.reject(error);
    },
  );

  // Axios Response Interceptor
  const responseInterceptor = axios.interceptors.response.use(
    (response) => {
      const checkTokenErrorCode = (response: any) => {
        if (response?.data?.errorcode && response?.data?.errorcode === TOKEN_ERROR_CODES.INVALID_TOKEN) {
          crashReportLogger({} as Error, {
            component: 'createInterceptors axios.errorcode',
            additionalInfo: 'Invalid token!',
          });
          dispatch(confirmedLogout(navigationRef.current));
        }
      };

      checkTokenErrorCode(response);
      return response;
    },
    async (error) => {
      crashReportLogger(error as Error, {
        component: 'createInterceptors',
        additionalInfo: 'Response error',
      });
      if (error.response && error.response.status === 401) {
        const expiryTimestampString = await AsyncStorage.getItem('tokenExpiry');
        const expiryTimestamp = JSON.parse(expiryTimestampString!);
        const currentTime = Math.floor(Date.now() / 1000);
        if (currentTime >= expiryTimestamp) {
          dispatch(confirmedLogout(navigationRef.current));
        }
      }
      return Promise.reject(error);
    },
  );

  return { requestInterceptor, responseInterceptor, setupFetchWrapper };
};