import { Colors } from '@theme';

export const hexToRGBa = (hex, alpha = 1) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r},${g},${b},${alpha})`;
};

// LIGHT THEME
export const lightTheme = {
  backgroundColor: '#FFFFFF',
  bgLightDark: '#ffffff',
  textColor: '#000000',
  creamColor: '#E9E2DD',
  bgDark: '#F5F5F5',
  bgCreamColor: {
    backgroundColor: '#E9E2DD',
  },
  bgBlue: '#EDF1F6',
  helperText: '#0E79DB',
  cardBg: '#FFFFFF',
  placeholderText: 'rgba(86, 86, 86, 1)',
  chips: 'black',
  tintColor: '#000',
  borderColor: Colors.grayline,
  borderBackground: Colors.grayline,
  activeStateBtn: 'black',
  baseWhite: '#FFFFFF',
  baseGray: '#EEEEEE',
  red: 'DD3333',
  // Add more styles as needed
  defaultIconColor: '#1E1E1E',
  shadowNeutral: '#8C958F',

  // New theme colors
  white: '#FFFFFF',
  white80: hexToRGBa('#FFFFFF', 0.8),
  dark20: hexToRGBa('#1E1E1E', 0.2),
  pageBg: '#EDF1F6',
  tags: '#FFFFFF',
  cardBG: '#E9F1FA',
  credGreen: '#CDE4D5',
  credBlue: '#CBD9E7',
  credBeige: '#E6DED8',
  credDarkPurple: '#B5ADD3',
  credPurple: '#D1CDE4',
  textLinkBlue: '#3D91E2',
  skyBlue: '#C9E0F6',
  tagBlue: '#5498D9',
  snackBarBg: '#E4F2FF',
  darkBlue: '#173F7C',
  streaksBlue: '#74BBFF',

  blue100: '#7DA1C4',
  blue80: '#97B4D0',
  blue60: '#B1C7DC',
  blue40: '#CBD9E7',
  blue20: '#E5ECF3',
  blue10: '#E9F1FA',

  green100: '#01BF86',
  green80: '#34CC9E',
  green60: '#67D9B6',
  green40: '#99E5CF',
  green20: '#CCF2E7',
  green10: '#E6F9F3',

  thunder100: '#1E1E1E',
  thunder80: '#4B4B4B',
  thunder60: '#787878',
  thinder40: '#A5A5A5',
  thunder20: '#D2D2D2',
  thunder10: '#E1E1E1',
  thunder0: '#F5F5F5',

  oatMilk100: '#D5C8BE',
  oatMilk80: '#DDD3CB',
  oatMilk60: '#E6DED8',
  oatMilk40: '#EEE9E5',
  oatMilk20: '#EEE9E5',
  oatMilk10: '#FBF9F8',

  gunSmoke100: '#8C958F',
  gunSmoke80: '#A3AAA5',
  gunSmoke60: '#BABFBC',
  gunSmoke40: '#D1D5D2',
  gunSmoke20: '#E8EAE9',
  gunSmoke10: '#F3F4F4',

  coolGrey100: '#E2E2E2',
  coolGrey80: '#E8E8E8',
  coolGrey60: '#EEEEEE',
  coolGrey40: '#F3F3F3',
  coolGrey20: '#F9F9F9',
  coolGrey10: '#FCFCFC',

  error100: '#D40000',
  error80: '#DD3333',
  error60: '#E56666',
  error40: '#EE9999',
  error20: '#F6CCCC',
  error10: '#FBE5E5',

  warning100: '#F2A308',
  warning80: '#F5B539',
  warning60: '#F7C86B',
  warning40: '#FADA9C',
  warning20: '#FCEDCE',
  warning10: '#FEF6E6',

  success100: '#66B95F',
  success80: '#85C77F',
  success60: '#A3D59F',
  success40: '#C2E3BF',
  success20: '#E0F1DF',
  success10: '#F0F8EF',

  fluoGreen100: '#70B500',
  fluoGreen80: '#80CD03',
  fluoGreen60: '#89DB05',
  fluoGreen40: '#92E903',
  fluoGreen20: '#97F204',
  fluoGreen10: '#9EFF00',

  gradien1: '#B3E1FD',
  gradien1light: '#D6EFFF',
  gradien2: '#D1FFF5',
  gradien2light: '#E4FFF9',
  gradient3: '#F5EEFF',
  gradient3light: '#FAF7FF',

  bgShade1: '#C1E3F7',
  bgShade2: '#DDFDF5',
  bgShade3: '#E9EAFC',
  bgShade4: '#FBF3EA',
  bgShade5: '#EFF5FB',
  bgShade6: '#753F94',
  bgShade7: '#FDFBF7',
  bgShade8: '#FCF8FF',
  bgShade9: '#F4FAFC',
  bgShade10: '#EDF1F6',
  bgShade11: '#F6EDEE',
  bgShade12: '#EEF6ED',

  hbgShade1: '#D0DCE8',
  hbgShade2: '#C6D5E5',

  disabledGray: '#C6C6C6',
  transparent: 'transparent',
};

// DARK THEME
export const darkTheme = {
  backgroundColor: '#1a1a1a',
  textColor: Colors.activeStateDark,
  // textColor: '#FFFFFF',
  creamColor: '#E9E2DD',
  bgLightDark: '#3a3a3b',
  bgDark: '#262626',
  bgCreamColor: {
    backgroundColor: '#E9E2DD',
  },
  bgBlue: '#000',
  helperText: '#0E79DB',
  cardBg: '#262626',
  placeholderText: 'rgba(255, 255, 255,.5)',
  chips: '#262626',
  tintColor: '#fff',
  borderColor: '#d3d3d3',
  borderBackground: 'rgba(255,255,255,0.1)',
  activeStateBtn: Colors.activeStateDark,
  baseWhite: '#262626',
  baseGray: '#1A1A1A',
  red: 'DD3333',
  // Add more styles as needed
  defaultIconColor: '#C7C7C7',
  shadowNeutral: '#8C958F',
  // New theme colors
  white: '#262626',
  white80: hexToRGBa('#262626', 0.8),
  dark20: hexToRGBa('#1E1E1E', 0.2), // same as light
  pageBg: '#1A1A1A',
  tags: '#1E1E1E',
  cardBG: '#3F4C5B',
  credGreen: '#3F4C5B',
  credBlue: '#2E4034',
  credBeige: '#4D4138',
  credDarkPurple: '#706A89',
  credPurple: '#6B6778',
  textLinkBlue: '#3D91E2',
  skyBlue: '#798086',
  tagBlue: '#5498D9',
  snackBarBg: '#E4F2FF', // same as light
  darkBlue: '#5498D9',
  streaksBlue: '#74BBFF',

  blue100: '#7DA1C4',
  blue80: '#E9F1FA',
  blue60: '#E5ECF3',
  blue40: '#CBD9E7',
  blue20: '#B1C7DC',
  blue10: '#97B4D0',

  green100: '#01BF86',
  green80: '#E6F9F3',
  green60: '#CCF2E7',
  green40: '#99E5CF',
  green20: '#67D9B6',
  green10: '#34CC9E',

  thunder100: '#F5F5F5',
  thunder80: '#E1E1E1',
  thunder60: '#D2D2D2',
  thinder40: '#A5A5A5',
  thunder20: '#787878',
  thunder10: '#4B4B4B',
  thunder0: '#1E1E1E',

  oatMilk100: '#FBF9F8',
  oatMilk80: '#F7F4F2',
  oatMilk60: '#EEE9E5',
  oatMilk40: '#E6DED8',
  oatMilk20: '#DDD3CB',
  oatMilk10: '#D5C8BE',

  gunSmoke100: '#F3F4F4',
  gunSmoke80: '#E8EAE9',
  gunSmoke60: '#D1D5D2',
  gunSmoke40: '#BABFBC',
  gunSmoke20: '#A3AAA5',
  gunSmoke10: '#8C958F',

  coolGrey100: '#FCFCFC',
  coolGrey80: '#F9F9F9',
  coolGrey60: '#C8C8C8',
  coolGrey40: '#D9D6D6',
  coolGrey20: '#E8E8E8',
  coolGrey10: '#E2E2E2',

  error100: '#D40000',
  error80: '#DD3333',
  error60: '#E56666',
  error40: '#EE9999',
  error20: '#F6CCCC',
  error10: '#FBE5E5',

  warning100: '#F2A308',
  warning80: '#F5B539',
  warning60: '#F7C86B',
  warning40: '#FADA9C',
  warning20: '#FCEDCE',
  warning10: '#FEF6E6',

  success100: '#66B95F',
  success80: '#85C77F',
  success60: '#A3D59F',
  success40: '#C2E3BF',
  success20: '#E0F1DF',
  success10: '#F0F8EF',

  fluoGreen100: '#70B500',
  fluoGreen80: '#80CD03',
  fluoGreen60: '#89DB05',
  fluoGreen40: '#92E903',
  fluoGreen20: '#97F204',
  fluoGreen10: '#9EFF00',

  gradien1: '#273944',
  gradien1light: '#31383D',
  gradien2: '#314440',
  gradien2light: '#343B39',
  gradient3: '#1F1B24',
  gradient3light: '#2F2E31',

  bgShade1: '#48565E',
  bgShade2: '#495653',
  bgShade3: '#4D4D58',
  bgShade4: '#5C5752',
  bgShade5: '#354357',
  bgShade6: '#B76EE2',
  bgShade7: '#493D2C',
  bgShade8: '#434145',
  bgShade9: '#383B3C',
  bgShade10: '#464B52',
  bgShade11: '#54494A',
  bgShade12: '#485247',

  hbgShade1: '#2F3842',
  hbgShade2: '#333C46',

  disabledGray: '#C6C6C6',
  transparent: 'transparent',
};

export interface Theme {
  bgLightDark: string;
  bgDark: string;
  backgroundColor: string;
  textColor: string;
  creamColor: string;
  bgCreamColor: {
    backgroundColor: string;
  };
  cardBg: string;
  placeholderText: string;
  chips: string;
  // Add more styles as needed
  defaultIconColor: string;
  shadowNeutral: string;

  // New theme colors
  white: string;
  white80: string;
  dark20: string;
  pageBg: string;
  tags: string;
  cardBG: string;
  credGreen: string;
  credBlue: string;
  credBeige: string;
  credDarkPurple: string;
  credPurple: string;
  textLinkBlue: string;
  skyBlue: string;
  tagBlue: string;
  snackBarBg: string;
  darkBlue: string;
  streaksBlue: string;

  blue100: string;
  blue80: string;
  blue60: string;
  blue40: string;
  blue20: string;
  blue10: string;

  green100: string;
  green80: string;
  green60: string;
  green40: string;
  green20: string;
  green10: string;

  thunder100: string;
  thunder80: string;
  thunder60: string;
  thinder40: string;
  thunder20: string;
  thunder10: string;
  thunder0: string;

  oatMilk100: string;
  oatMilk80: string;
  oatMilk60: string;
  oatMilk40: string;
  oatMilk20: string;
  oatMilk10: string;

  gunSmoke100: string;
  gunSmoke80: string;
  gunSmoke60: string;
  gunSmoke40: string;
  gunSmoke20: string;
  gunSmoke10: string;

  coolGrey100: string;
  coolGrey80: string;
  coolGrey60: string;
  coolGrey40: string;
  coolGrey20: string;
  coolGrey10: string;

  error100: string;
  error80: string;
  error60: string;
  error40: string;
  error20: string;
  error10: string;

  warning100: string;
  warning80: string;
  warning60: string;
  warning40: string;
  warning20: string;
  warning10: string;

  success100: string;
  success80: string;
  success60: string;
  success40: string;
  success20: string;
  success10: string;

  fluoGreen100: string;
  fluoGreen80: string;
  fluoGreen60: string;
  fluoGreen40: string;
  fluoGreen20: string;
  fluoGreen10: string;

  gradien1: string;
  gradien1light: string;
  gradien2: string;
  gradien2light: string;
  gradient3: string;
  gradient3light: string;

  bgShade1: string;
  bgShade2: string;
  bgShade3: string;
  bgShade4: string;
  bgShade5: string;
  bgShade6: string;
  bgShade7: string;
  bgShade8: string;
  bgShade9: string;
  bgShade10: string;
  bgShade11: string;
  bgShade12: string;

  hbgShade1: string;
  hbgShade2: string;
  transparent: string;
}
