import { Toast } from 'native-base';
import { PermissionsAndroid, Platform } from 'react-native';
import ReactNativeBlobUtil from 'react-native-blob-util';
import DocumentPicker from 'react-native-document-picker';
import { getUrl } from './constants';
import { getFileExtensionFromUrl } from '../screens/ViewCertificates';
import crashReportLogger from './crashlyticsLogger';

const supportedExtensions = ['png', 'jpg', 'jpeg'];

async function downloadCall(url, callback, fileName, token, getExtension) {
  const isImage = supportedExtensions.includes(getFileExtensionFromUrl(url));

  const { dirs } = ReactNativeBlobUtil.fs;
  const dirToSave = Platform.OS === 'ios' ? dirs.DocumentDir : dirs.DownloadDir;
  const configfb = isImage
    ? {
      fileCache: true,
      addAndroidDownloads: {
        useDownloadManager: true,
        notification: true,
        mediaScannable: true,
        title: fileName
          ? `${fileName}`
          : `report.${getFileExtensionFromUrl(url)}`,
        path: `${dirs.DownloadDir}/${fileName ? fileName : 'report'
          }`,
      },
      useDownloadManager: true,
      notification: true,
      mediaScannable: true,
      title: fileName
        ? `${fileName}`
        : `report.${getFileExtensionFromUrl(url)}`,
      path: `${dirToSave}/${fileName ? fileName : 'report'}`,
    }
    : {
      fileCache: true,
      addAndroidDownloads: {
        useDownloadManager: true,
        notification: true,
        mediaScannable: true,
        title: fileName ? `${fileName}` : `report.pdf`,
        path: `${dirs.DownloadDir}/${fileName ? fileName : 'report'}`,
      },
      useDownloadManager: true,
      notification: true,
      mediaScannable: true,
      title: fileName ? `${fileName}` : `report.pdf`,
      path: `${dirToSave}/${fileName ? fileName : 'report'}`,
    };

  const configOptions = Platform.select({
    ios: configfb,
    android: configfb,
  });

  ReactNativeBlobUtil.config(configOptions || {})
    .fetch('GET', url, { 'Cache-Control': 'no-store', Authorization: `Bearer ${token}` })
    .progress((received, total) => { })
    .then((res) => {
      if (Platform.OS === 'ios') {
        ReactNativeBlobUtil.fs.writeFile(configfb.path, res.data, 'base64');
        ReactNativeBlobUtil.ios.previewDocument(configfb.path);
      }
      if (Platform.OS === 'android') {
        const android = ReactNativeBlobUtil.android;
        android.actionViewIntent(res.path(), 'application/pdf');
      }
      if (Platform.OS === 'android') {
        Toast.show({ text: 'File is downloaded!', textStyle: { textAlign: 'left' } });
      } else {
        Toast.show({ text: 'File is downloading!', textStyle: { textAlign: 'left' } });
      }
      callback();
    })
    .catch((e) => {
      callback();
      Toast.show({ text: 'Download Failed!', textStyle: { textAlign: 'left' } });
    });
}

export const permissionCall = (
  url: string,
  callback?: () => void,
  fileName?: string,
  token?: string,
  getExtension?: string,
) => {
  if (Platform.OS === 'ios') {
    downloadCall(url, callback, fileName, token, getExtension);
  } else {
    try {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE).then(
        (granted) => {
          if (Number(Platform.Version) >= 33) {
            downloadCall(url, callback, fileName, token, getExtension);
            return;
          }
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            downloadCall(url, callback, fileName, token, getExtension);
          } else {
            Toast.show({
              text: 'Permission not granted!',
              textStyle: { textAlign: 'left' },
            });
            callback?.();
          }
        },
      );
    } catch (err) {
      crashReportLogger(err as Error, {
        component: 'downloadPdf.ts',
        additionalInfo: 'API Error'
      })
    }
  }
};

export const pickPdf = async (callback) => {
  try {
    const result = await DocumentPicker.pick({
      type: [DocumentPicker.types.pdf],
    });

    if (result?.length) {
      callback(result[0]);
      return result[0];
    }
  } catch (err) {
    crashReportLogger(err as Error, {
      component: 'downloadPdf.ts',
      additionalInfo: 'Failed to pick PDF'
    })
    if (!DocumentPicker.isCancel(err)) {
      throw err;
    }
  }
};

export const viewAttachment = async (fileName, navigation, token, callback) => {
  if (!fileName?.includes('pdf')) {
    if (!fileName) return;
    navigation.navigate('AttachmentView', fileName);
  } else {
    const extension = getFileExtensionFromUrl(fileName);
    getContentUrl(fileName, token, callback, extension);
  }
};
const getContentUrl = async (attachment, token, callback, extension) => {
  const pdfContent = await getUrl(attachment, token);
  if (pdfContent) {
    permissionCall(pdfContent, callback, attachment, token, extension);
  }
};
