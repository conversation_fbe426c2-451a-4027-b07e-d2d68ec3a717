import { PixelRatio, Platform } from 'react-native';
import { I18nManager } from 'react-native';
import { Dimensions, PlatformIOSStatic } from 'react-native';
import DeviceInfo from 'react-native-device-info';
export const platformIOS = Platform as PlatformIOSStatic;

export const isPad = platformIOS.isPad;
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT,fontScale } = Dimensions.get('window');
// const scale = SCREEN_WIDTH / (isPad ? 270 : 320);
const scale = SCREEN_WIDTH / 320;

const isIOS = Platform.OS == 'ios';
export const isTablet = DeviceInfo.isTablet();
export function normalize(size: number) {
  const newSize = size * scale;
  if (Platform.OS === 'ios' && !isPad) {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  if (isPad || isTablet) {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 15;
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
  }
}

export function normalizeStreak(size: number) {
  const newSize = size * scale;
  if (Platform.OS === 'ios' && !isPad) {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  if (isPad || isTablet) {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 15;
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
  }
}

export const isAndroid = Platform.OS == 'android';

export default {
  mini_: normalize(isIOS ? 8 : 9),
  mini: normalize(isIOS ? 8 : 10),
  xmini: normalize(isIOS ? 9 : 11),
  xxmini: normalize(isIOS ? 10 : 12),
  semiMini: normalize(isIOS ? 11 : 12),
  small: normalize(isIOS ? 11 : 13),
  medium: normalize(isIOS ? 12 : 14),
  semiMedium: normalize(isIOS ? 13 : 15),
  semiMedium1: normalize(isIOS ? 14 : 16),
  semiSmall: normalize(11),
  semiLarge: normalize(14),
  semiXLarge: normalize(16),
  titleLarge: normalize(17),
  large: normalize(isIOS ? 14 : 15),
  large15: normalize(isIOS ? 15 : 17),
  xlarge: normalize(isIOS ? 16 : 18),
  xxlarge: normalize(18),
  xxxlarge: normalize(20),
  xxxxlarge: normalize(22),
  xxxxxlarge: normalize(24),
  xxxxxxlarge: normalize(26),
  large28: normalize(28),
  large30: normalize(30),
  large32: normalize(32),
  large36: normalize(36),
  large38: normalize(38),
  large40: normalize(40),
  large44: normalize(44),
  large48: normalize(48),
  large58: normalize(58),
  large90: normalize(90),
  large120: normalize(120),
  large80: normalize(200),
  custom: (size) => normalize(size),

  progPercent: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),    // 48px ==> H1 in DS (Design Typography)
  // tg_h2: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),       // 44px ==> H2 in DS
  tg_h3: normalize(I18nManager.isRTL ? (isAndroid ? 25 : 24) : isAndroid ? 26 : 25),       // 40px ==> H3 in DS
  // tg_h4: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),       // 36px ==> H4 in DS
  h1: normalize(I18nManager.isRTL ? (isAndroid ? 22 : 21) : isAndroid ? 23 : 22),             // 32px ==> H5 in DS
  // tg_h6: normalize(I18nManager.isRTL ? (isAndroid ? 22 : 21) : isAndroid ? 23 : 22),       // 30px ==> H6 in DS
  // tg_h7: normalize(I18nManager.isRTL ? (isAndroid ? 22 : 21) : isAndroid ? 23 : 22),       // 28px ==> H7 in DS
  h2: normalize(I18nManager.isRTL ? (isAndroid ? 19 : 18) : isAndroid ? 20 : 19),             // 26px ==> H8
  tg_PgHeading: normalize(I18nManager.isRTL ? (isAndroid ? 17 : 16) : isAndroid ? 18 : 17),// 24px ==> Page heading
  // tg_title: normalize(I18nManager.isRTL ? (isAndroid ? 19 : 18) : isAndroid ? 20 : 19),    // 22px ==> Title
  h3: normalize(I18nManager.isRTL ? (isAndroid ? 15 : 14) : isAndroid ? 16 : 15),             // 20px ==> Sub title
  h4: normalize(I18nManager.isRTL ? (isAndroid ? 14 : 13) : isAndroid ? 15 : 14),             // 18px ==> Small title
  h5: normalize(I18nManager.isRTL ? (isAndroid ? 13 : 12) : isAndroid ? 14 : 13),             // 16px ==> Running Text
  h6: normalize(I18nManager.isRTL ? (isAndroid ? 11 : 10) : isAndroid ? 12 : 11),             // 14px ==> Small Text
  h7: normalize(I18nManager.isRTL ? (isAndroid ? 10 : 9) : isAndroid ? 11 : 10),              // 12px ==> Tiny Text
  h8: normalize(I18nManager.isRTL ? (isAndroid ? 9 : 8) : isAndroid ? 10 : 9),                // 10px ==> X Tiny Text (JIC)

};

export const streakMduleSizes = {
  mini: normalizeStreak(isIOS ? 8 : 10),
  xmini: normalizeStreak(isIOS ? 9 : 11),
  xxmini: normalizeStreak(isIOS ? 10 : 12),
  semiMini: normalizeStreak(11),
  small: normalizeStreak(isIOS ? 11 : 13),
  medium: normalizeStreak(isIOS ? 12 : 14),
  semiMedium: normalizeStreak(isIOS ? 13 : 15),
  semiMedium1: normalizeStreak(isIOS ? 14 : 16),
  semiSmall: normalizeStreak(11),
  semiLarge: normalizeStreak(14),
  semiXLarge: normalizeStreak(16),
  titleLarge: normalizeStreak(17),
  large: normalizeStreak(isIOS ? 14 : 16),
  xlarge: normalizeStreak(isIOS ? 16 : 18),
  xxlarge: normalizeStreak(18),
  large19: normalizeStreak(19),
  xxxlarge: normalizeStreak(20),
  xxxxlarge: normalizeStreak(22),
  xxxxxlarge: normalizeStreak(24),
  xxxxxxlarge: normalizeStreak(26),
  large32: normalizeStreak(32),
  large38: normalizeStreak(38),
  large48: normalizeStreak(48),
  large58: normalizeStreak(58),
  large80: normalizeStreak(200),
};
