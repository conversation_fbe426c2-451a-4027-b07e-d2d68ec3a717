import { I18nManager } from "react-native";
import { Platform } from "react-native";


export const ArabicFonts = {
  light: 'SFProText-LightItalic',
  regular: Platform.OS === "ios" ? 'NotoKufiArabic-Regular' : 'Noto-Kufi-Arabic-Regular',
  medium: Platform.OS === "ios" ? 'NotoKufiArabic-Medium' : 'Noto-Kufi-Arabic-Medium',
  bold: Platform.OS === "ios" ? 'NotoKufiArabic-Bold' : 'Noto-Kufi-Arabic-Bold',
  semiBold: Platform.OS === "ios" ? 'NotoKufiArabic-SemiBold' : 'Noto-Kufi-Arabic-Semi-Bold',
  thin: Platform.OS === "ios" ? 'NotoKufiArabic-Light' : 'Noto-Kufi-Arabic-Light'
};

export default I18nManager.isRTL ? ArabicFonts : {
  light: 'SFProText-LightItalic',
  regular: Platform.OS === "ios" ? 'HelveticaNeue-Regular' : 'HelveticaNeueRegular', //400 Weight
  medium: Platform.OS === "ios" ? 'HelveticaNeue-Medium' : 'HelveticaNeueMedium', //500 Weight
  bold: Platform.OS === "ios" ? 'HelveticaNeue-Heavy' : 'HelveticaNeueHeavy',
  semiBold: Platform.OS === "ios" ? 'HelveticaNeue-Bold' : 'HelveticaNeueBold',//700
  thin: Platform.OS === "ios" ? 'HelveticaNeue-Light' : 'HelveticaNeueLight'
};;

export enum fontWeights {
  light = 300,
  regular = 400,
  normal = 500,
  semiBold = 600,
  bold = 700
};
