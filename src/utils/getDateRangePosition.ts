import moment from 'moment';

export function getDateRangePosition(
  date: moment.Moment,
  selectedDate: moment.Moment,
  startDate: moment.Moment | null,
  endDate: moment.Moment | null,
  calendarType: 'week' | 'month',
) {
  const isClicked = selectedDate && date.isSame(selectedDate, 'day');

    if (calendarType === 'week') {
        return {
            isStart: false,
            isEnd: false,
            isMiddle: false,
            isSelected: true,
            isClicked: isClicked,
        };
    } else {
        if (!date || !moment.isMoment(date)) {
            return {
              isStart: false,
              isEnd: false,
              isMiddle: false,
              isSelected: false,
              isClicked: false,
            };
          }
          if (!startDate || !moment.isMoment(startDate)) {
            return { isStart: false, isEnd: false, isMiddle: false, isSelected: false, isClicked };
          }
          if (!endDate || !moment.isMoment(endDate)) {
            const isSelected = date.isSame(startDate, 'day');
            return { isStart: isSelected, isEnd: isSelected, isMiddle: false, isSelected, isClicked };
          }
          try {
            const isSelected = date.isSameOrAfter(startDate, 'day') && date.isSameOrBefore(endDate, 'day');
            const isStart = date.isSame(startDate, 'day');
            const isEnd = date.isSame(endDate, 'day');
            const isMiddle = isSelected && !isStart && !isEnd;
            return { isStart, isEnd, isMiddle, isSelected, isClicked };
          } catch (error) {
            return { isStart: false, isEnd: false, isMiddle: false, isSelected: false, isClicked };
          }
    }
  
} 