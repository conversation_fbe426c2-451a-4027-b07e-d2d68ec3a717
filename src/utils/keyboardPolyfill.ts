import { Keyboard } from 'react-native';

/**
 * Polyfill for deprecated Keyboard.removeListener method
 * This fixes compatibility issues with native-base and newer React Native versions
 */
export const setupKeyboardPolyfill = () => {
  // Check if removeListener is missing (deprecated in newer RN versions)
  if (!Keyboard.removeListener && Keyboard.removeAllListeners) {
    // Add a polyfill that maps removeListener to removeAllListeners
    (Keyboard as any).removeListener = (eventName: string, callback?: any) => {
      // For backward compatibility, we'll remove all listeners for the event
      // This is not perfect but prevents the crash
      try {
        if (callback) {
          // Try to remove specific listener if possible
          const subscription = Keyboard.addListener(eventName, () => {});
          subscription.remove();
        } else {
          // Remove all listeners for this event type
          Keyboard.removeAllListeners(eventName);
        }
      } catch (error) {
        // Silently handle any errors
        console.warn('Keyboard polyfill warning:', error);
      }
    };
  }
};

/**
 * Safe keyboard listener removal
 */
export const safeRemoveKeyboardListener = (eventName: string, callback?: any) => {
  try {
    if (Keyboard.removeListener) {
      Keyboard.removeListener(eventName, callback);
    } else if (Keyboard.removeAllListeners) {
      Keyboard.removeAllListeners(eventName);
    }
  } catch (error) {
    console.warn('Failed to remove keyboard listener:', error);
  }
};