import { I18nManager } from "react-native";
import { isAndroid } from "./fontSize";

export default {
    // tg_h2: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),// 44px ==> H2 in DS 
    // tg_h3: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),// 40px ==> H3 in DS 
    // tg_h4: normalize(I18nManager.isRTL ? (isAndroid ? 33 : 32) : isAndroid ? 34 : 33),// 36px ==> H4 in DS 
    h1_LH: I18nManager.isRTL ? (isAndroid ? 36 : 40) : isAndroid ? 42 : 32, //done   // 32px
    // tg_h6: normalize(I18nManager.isRTL ? (isAndroid ? 22 : 21) : isAndroid ? 23 : 22),         // 30px ==> H6 in DS 
    // tg_h7: normalize(I18nManager.isRTL ? (isAndroid ? 22 : 21) : isAndroid ? 23 : 22),         // 28px ==> H7 in DS 
    h2_LH: isAndroid ? 28 : 28, // 26px
    // tg_PgHeading: normalize(I18nManager.isRTL ? (isAndroid ? 19 : 18) : isAndroid ? 20 : 19),         // 24px ==> Page heading
    // tg_title: normalize(I18nManager.isRTL ? (isAndroid ? 19 : 18) : isAndroid ? 20 : 19),         // 22px ==> Title
    h3_LH: I18nManager.isRTL ? (isAndroid ? 29 : 31) : isAndroid ? 26 : 26,//done     // 20px  
    h4_LH: I18nManager.isRTL ? (isAndroid ? 26 : 28) : isAndroid ? 23 : 23,//         // 18px
    h5_LH: I18nManager.isRTL ? (isAndroid ? 26 : 28) : isAndroid ? 23 : 23, //done    // 16px
    h6_LH: I18nManager.isRTL ? (isAndroid ? 20 : 22) : isAndroid ? 19 : 20,           // 14px
    h7_LH: I18nManager.isRTL ? (isAndroid ? 17 : 19) : isAndroid ? 17 : 17, //done    // 12px
    h8_LH: I18nManager.isRTL ? (isAndroid ? 16 : 18) : isAndroid ? 17 : 18,           // 10px

} 