import moment from 'moment';
import { I18nManager } from "react-native";
import 'moment/locale/ar';

export const IS_ARABIC = I18nManager.isRTL;
export const LOCALE = IS_ARABIC ? 'ar' : 'en';

export function formatNumbers(input: string) {
  const numbersMap = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  const numerals = Object.values(numbersMap);
  if (numerals.find((num) => (input ?? "").includes(num))) {
    return input;
  }

  return IS_ARABIC ? input.replace(/[0-9]/g, (digit) => numbersMap[digit]) : input;
}

export const fixAmPm = (time: string) =>
  time.replace('AM', 'ص').replace('PM', 'م').replace('am', 'ص').replace('pm', 'م');

export const formatTimeInterval = (start: string, end: string): string => {
  const isArabic = IS_ARABIC;

  const startTime = moment(start, 'hh:mm a');
  const endTime = moment(end, 'hh:mm a');

  const startArabic = formatNumbers(fixAmPm(startTime.format('hh:mm A')));
  const endArabic = formatNumbers(fixAmPm(endTime.format('hh:mm A')));

  const startEng = fixAmPm(startTime.format('hh:mm A'));
  const endEng = fixAmPm(endTime.format('hh:mm A'));


  return isArabic
    ? `${startArabic} - ${endArabic}`
    : `${startEng} - ${endEng}`;
}

export function handleTimeFormat(time: string) {
  if (IS_ARABIC) {
    const match = time.match(/(\d+)h\s+(\d+)m/);
    if (!match) return null;

    const hours = `${parseInt(match[1], 10)}`;
    const minutes = `${parseInt(match[2], 10)}`;

    const arabicHours = formatNumbers(hours);
    const arabicMinutes = formatNumbers(minutes);

    return `${arabicHours}س ${arabicMinutes}د`;
  }

  return time;
}

export const setMomentLocale = () => {
  moment.locale(IS_ARABIC ? 'ar' : 'en');
};
