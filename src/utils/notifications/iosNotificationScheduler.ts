import { NativeModules, Platform } from 'react-native';

interface NotificationScheduler {
  checkPermission(): Promise<{
    granted: boolean;
    status: string;
    message?: string;
  }>;

  requestPermission(): Promise<{
    granted: boolean;
    status: string;
    message?: string;
  }>;

  startNotifications(intervalMinutes: number, title: string, body: string): Promise<{
    success: boolean;
    message: string;
    intervalMinutes: number;
  }>;

  stopNotifications(): Promise<{
    success: boolean;
    message: string;
  }>;

  getStatus(): Promise<{
    enabled: boolean;
    intervalMinutes: number;
  }>;

  sendTestNotification(title: string, body: string): Promise<{
    success: boolean;
    message: string;
  }>;
}

const { NotificationScheduler: NativeIOSNotificationScheduler } = NativeModules;

export interface NotificationPermissionResult {
  granted: boolean;
  status: string;
  message?: string;
}

export interface NotificationStartResult {
  success: boolean;
  message: string;
  intervalMinutes: number;
}

export interface NotificationStopResult {
  success: boolean;
  message: string;
}

export interface NotificationStatus {
  enabled: boolean;
  intervalMinutes: number;
}

export interface TestNotificationResult {
  success: boolean;
  message: string;
}

class IOSNotificationScheduler {
  private scheduler: NotificationScheduler | null = null;

  constructor() {
    if (Platform.OS === 'ios' && NativeIOSNotificationScheduler) {
      this.scheduler = NativeIOSNotificationScheduler as NotificationScheduler;
    }
  }

  async checkPermission(): Promise<NotificationPermissionResult> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.checkPermission();
  }

  async requestPermission(): Promise<NotificationPermissionResult> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.requestPermission();
  }

  async startNotifications(intervalMinutes: number, title: string, body: string): Promise<NotificationStartResult> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.startNotifications(intervalMinutes, title, body);
  }

  async stopNotifications(): Promise<NotificationStopResult> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.stopNotifications();
  }

  async getStatus(): Promise<NotificationStatus> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.getStatus();
  }

  async sendTestNotification(title: string, body: string): Promise<TestNotificationResult> {
    if (!this.scheduler) throw new Error('iOS NotificationScheduler not available');
    return this.scheduler.sendTestNotification(title, body);
  }

  isSupported(): boolean {
    return Platform.OS === 'ios' && !!this.scheduler;
  }
}

export const iosNotificationScheduler = new IOSNotificationScheduler();
export default IOSNotificationScheduler; 