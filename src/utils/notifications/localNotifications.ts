import AsyncStorage from '@react-native-async-storage/async-storage';
import { ENV } from '@api/api_client';
import crashReportLogger from '@utils/crashlyticsLogger';
import { Notifications } from 'react-native-notifications';
import { Platform } from 'react-native';
import { workManagerNotifications } from './workManagerNotifications';
import { iosNotificationScheduler } from './iosNotificationScheduler';

export const LOCAL_NOTIFICATION_STORAGE_KEY = 'localNotificationScheduled';
export const NOTIFICATION_ID = 12345;

export interface LocalNotificationConfig {
  title: string;
  body: string;
  intervalMinutes: number;
}


export const scheduleLocalNotification = async (config: LocalNotificationConfig): Promise<void> => {
  try {
    await cancelLocalNotification();
    const { title, body, intervalMinutes } = config;
    const fireDate = new Date(Date.now() + (intervalMinutes * 60 * 1000));
    const uniqueId = `${NOTIFICATION_ID}_${Date.now()}`;
    const notification = {
      title,
      body,
      sound: 'default',
      badge: 1,
      type: 'local',
      silent: false,
      thread: 'sign_in_reminder',
      identifier: uniqueId,
      fireDate: fireDate,
      payload: {
        type: 'sign_in_reminder',
        intervalMinutes,
        scheduledAt: Date.now()
      }
    };
    Notifications.postLocalNotification(notification);
    await AsyncStorage.setItem(LOCAL_NOTIFICATION_STORAGE_KEY, JSON.stringify({
      id: NOTIFICATION_ID,
      title,
      body,
      scheduledTime: fireDate.getTime(),
      intervalMinutes
    }));
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'localNotifications scheduleLocalNotification',
      additionalInfo: 'Failed to schedule local notification'
    });
  }
};

export const cancelLocalNotification = async (): Promise<void> => {
  try {
    if (Platform.OS === 'android' && workManagerNotifications.isSupported()) {
      await workManagerNotifications.stopNotifications();
    } else if (Platform.OS === 'ios' && iosNotificationScheduler.isSupported()) {
      await iosNotificationScheduler.stopNotifications();
    }
    await AsyncStorage.removeItem(LOCAL_NOTIFICATION_STORAGE_KEY);
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'localNotifications cancelLocalNotification',
      additionalInfo: 'Failed to cancel local notification'
    });
  }
};

export const scheduleSignInReminder = async (title: string,body: string): Promise<void> => {
  try {
    const intervalMinutes = ENV === 'prod' ? 1440 : 30; // 24 hours in production, 30 minute in staging
    if (Platform.OS === 'android' && workManagerNotifications.isSupported()) {
      await workManagerNotifications.startNotifications(intervalMinutes, title, body);
    } else if (Platform.OS === 'ios' && iosNotificationScheduler.isSupported()) {
      await iosNotificationScheduler.startNotifications(intervalMinutes, title, body);
    }
  } catch (error) {
    console.error('Error in scheduleSignInReminder:', error);
    crashReportLogger(error as Error, {
      component: 'localNotifications scheduleSignInReminder',
      additionalInfo: 'Failed to schedule sign-in reminder'
    });
  }
};

export const setupNotificationListeners = (): void => {
  try {
    Notifications.events().registerNotificationReceivedForeground((notification: unknown, completion: (response: { alert: boolean; sound: boolean; badge: boolean }) => void) => {
      if ((notification as { payload?: { type?: string } })?.payload?.type !== 'sign_in_reminder') {
      }
      completion({ alert: true, sound: true, badge: false });
    });

    Notifications.events().registerNotificationOpened((notification: unknown, completion: () => void) => {
      if ((notification as { payload?: { type?: string } })?.payload?.type !== 'sign_in_reminder') {
      }
      completion();
    });
  } catch (error) {
    // Silently handle setup errors
  }
};

export const isNotificationScheduled = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android' && workManagerNotifications.isSupported()) {
      const status = await workManagerNotifications.getStatus();
      return status.enabled;
    } else if (Platform.OS === 'ios' && iosNotificationScheduler.isSupported()) {
      const status = await iosNotificationScheduler.getStatus();
      return status.enabled;
    }
    const stored = await AsyncStorage.getItem(LOCAL_NOTIFICATION_STORAGE_KEY);
    return stored !== null;
  } catch (error) {
    return false;
  }
};
