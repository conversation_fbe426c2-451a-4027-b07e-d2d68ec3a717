import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { request, check, PERMISSIONS, RESULTS, openSettings } from 'react-native-permissions';
import crashReportLogger from '@utils/crashlyticsLogger';

export const NOTIFICATION_PERMISSION_STATUS = 'notificationPermissionStatus';
export const FCM_TOKEN_SENT = 'fcmTokenSent';

export const checkNotificationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'ios') {
    const authStatus = await messaging().hasPermission();
    return (
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL
    );
  } else {
    const permission = PERMISSIONS.ANDROID.POST_NOTIFICATIONS;
    const result = await check(permission);
    return result === RESULTS.GRANTED;
  }
};

export const canRequestNotificationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'ios') {
    const authStatus = await messaging().hasPermission();
    return authStatus === messaging.AuthorizationStatus.NOT_DETERMINED;
  } else {
    try {
      const permissionStatus = await check(PERMISSIONS.ANDROID.POST_NOTIFICATIONS);

      if (permissionStatus === RESULTS.GRANTED) {
        return false;
      }
      if (permissionStatus === RESULTS.DENIED) {
        return true;
      }
      return false;
    } catch (error) {
      crashReportLogger(error as Error, {
        component: 'notificationPermissions canRequestNotificationPermissions',
        additionalInfo: 'Failed to get request for notification'
      });
      return false;
    }
  }
};

export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const granted =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      await AsyncStorage.setItem(NOTIFICATION_PERMISSION_STATUS, granted ? 'granted' : 'denied');
      return granted;
    } else {
      const permission = PERMISSIONS.ANDROID.POST_NOTIFICATIONS;
      const result = await request(permission);
      await AsyncStorage.setItem(NOTIFICATION_PERMISSION_STATUS, result);
      return result === RESULTS.GRANTED;
    }
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'notificationPermissions requestNotificationPermissions',
      additionalInfo: 'Failed to get request for notification permission'
    });
    return false;
  }
};
export const getFCMToken = async (): Promise<string | null> => {
  try {
    return await messaging().getToken();
  } catch (error) {
    crashReportLogger(error as Error, {
      component: 'notificationPermissions getFCMToken',
      additionalInfo: 'Failed to get FCM token'
    });
    // Error getting FCM token logged via crashReportLogger
    return null;
  }
};

export const isFCMTokenSent = async (): Promise<boolean> => {
  const tokenSent = await AsyncStorage.getItem(FCM_TOKEN_SENT);
  return tokenSent === 'true';
};

export const markFCMTokenSent = async (sent: boolean = true): Promise<void> => {
  await AsyncStorage.setItem(FCM_TOKEN_SENT, sent ? 'true' : 'false');
};

export const openAppNotificationSettings = async (): Promise<void> => {
  await openSettings();
};
