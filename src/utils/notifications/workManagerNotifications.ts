import { NativeModules, Platform } from 'react-native';

interface NotificationScheduler {
  checkPermission(): Promise<{
    granted: boolean;
    status: string;
    message?: string;
  }>;
  
  requestPermission(): Promise<{
    granted: boolean;
    status: string;
    message?: string;
  }>;
  
  startNotifications(intervalMinutes: number, title: string, body: string): Promise<{
    success: boolean;
    message: string;
    intervalMinutes: number;
  }>;
  
  stopNotifications(): Promise<{
    success: boolean;
    message: string;
  }>;
  
  getStatus(): Promise<{
    enabled: boolean;
    intervalMinutes: number;
  }>;
  
  sendTestNotification(title: string, body: string): Promise<{
    success: boolean;
    message: string;
  }>;
}

// Get the native module (only available on Android)
const { NotificationScheduler: AndroidNotificationScheduler } = NativeModules;

export interface NotificationPermissionResult {
  granted: boolean;
  status: string;
  message?: string;
}

export interface NotificationStartResult {
  success: boolean;
  message: string;
  intervalMinutes: number;
}

export interface NotificationStopResult {
  success: boolean;
  message: string;
}

export interface NotificationStatus {
  enabled: boolean;
  intervalMinutes: number;
}

export interface TestNotificationResult {
  success: boolean;
  message: string;
}

class WorkManagerNotifications {
  private scheduler: NotificationScheduler | null = null;

  constructor() {
    if (Platform.OS === 'android' && AndroidNotificationScheduler) {
      this.scheduler = AndroidNotificationScheduler as NotificationScheduler;
    }
  }

  async checkPermission(): Promise<NotificationPermissionResult> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
    
    return await this.scheduler.checkPermission();
  }

  async requestPermission(): Promise<NotificationPermissionResult> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
    
    return await this.scheduler.requestPermission();
  }

  async startNotifications(intervalMinutes: number = 1, title: string, body: string): Promise<NotificationStartResult> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
    
    // Check permissions first
    const permission = await this.scheduler.checkPermission();
    
    if (!permission.granted) {
      throw new Error('Notification permissions not granted: ' + permission.message);
    }
    
    const result = await this.scheduler.startNotifications(intervalMinutes, title, body);
    return result;
  }


  async stopNotifications(): Promise<NotificationStopResult> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
    
    return await this.scheduler.stopNotifications();
  }

  
  async getStatus(): Promise<NotificationStatus> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
    
    return await this.scheduler.getStatus();
  }

  async sendTestNotification(title: string, body: string): Promise<TestNotificationResult> {
    if (Platform.OS !== 'android' || !this.scheduler) {
      throw new Error('WorkManager notifications are only supported on Android');
    }
   
    const permission = await this.scheduler.checkPermission();
    
    if (!permission.granted) {
      throw new Error('Notification permissions not granted: ' + permission.message);
    }
    
    const result = await this.scheduler.sendTestNotification(title, body);
    return result;
  }

 
  isSupported(): boolean {
    return Platform.OS === 'android' && !!this.scheduler;
  }
}

export const workManagerNotifications = new WorkManagerNotifications();
export default WorkManagerNotifications;