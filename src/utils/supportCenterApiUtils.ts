import { ApiResponse, OperationResponse, ValidationError } from '../types/supportCenter';
import crashReportLogger from './crashlyticsLogger';

/**
 * Utility functions for handling Support Center API v2 responses
 */

export class SupportCenterApiError extends Error {
  public errorCode: string | null;
  public validationErrors: ValidationError[] | null;

  constructor(
    message: string,
    errorCode: string | null = null,
    validationErrors: ValidationError[] | null = null,
  ) {
    super(message);
    this.name = 'SupportCenterApiError';
    this.errorCode = errorCode;
    this.validationErrors = validationErrors;
  }
}

/**
 * Validates and extracts data from API v2 response
 */
export function validateApiResponse<T>(response: any): ApiResponse<T> {
  if (!response || typeof response !== 'object') {
    throw new SupportCenterApiError('Invalid API response format');
  }

  if (!response.operationResponse) {
    throw new SupportCenterApiError('Missing operationResponse in API response');
  }

  const operationResponse: OperationResponse = response.operationResponse;

  if (!operationResponse.success) {
    const errorMessage =
      operationResponse.errorDescription || operationResponse.message || 'An error occurred';

    throw new SupportCenterApiError(
      errorMessage,
      operationResponse.errorCode,
      operationResponse.validationErrors,
    );
  }

  return {
    data: response.data,
    pagination: response.pagination,
    operationResponse,
  };
}

/**
 * Handles API errors and logs them appropriately
 */
export function handleApiError(error: any, context: string): string {
  let userMessage = 'Something went wrong. Please try again.';

  if (error instanceof SupportCenterApiError) {
    // Log technical error details
    crashReportLogger(error, {
      component: context,
      additionalInfo: `API Error - Code: ${error.errorCode}, Validation Errors: ${JSON.stringify(error.validationErrors)}`,
    });

    // Return user-friendly message for validation errors
    if (error.validationErrors && error.validationErrors.length > 0) {
      userMessage = error.validationErrors.map((ve) => ve.message).join(', ');
    } else {
      userMessage = error.message;
    }
  } else {
    // Log unexpected errors
    crashReportLogger(error as Error, {
      component: context,
      additionalInfo: 'Unexpected error in Support Center API',
    });
  }

  return userMessage;
}

/**
 * Extracts validation errors for specific fields
 */
export function getFieldValidationError(
  validationErrors: ValidationError[] | null,
  fieldName: string,
): string | null {
  if (!validationErrors) return null;

  const fieldError = validationErrors.find((error) => error.field === fieldName);
  return fieldError ? fieldError.message : null;
}

/**
 * Checks if response has validation errors
 */
export function hasValidationErrors(error: any): boolean {
  return (
    error instanceof SupportCenterApiError &&
    !!error.validationErrors &&
    error.validationErrors.length > 0
  );
}

/**
 * Formats validation errors for display
 */
export function formatValidationErrors(validationErrors: ValidationError[]): string {
  return validationErrors.map((error) => `${error.field}: ${error.message}`).join('\n');
}

/**
 * Generic API call wrapper with error handling
 */
export async function makeApiCall<T>(apiCall: () => Promise<any>, context: string): Promise<T> {
  try {
    const response = await apiCall();
    const validatedResponse = validateApiResponse<T>(response);
    return validatedResponse.data;
  } catch (error) {
    const userMessage = handleApiError(error, context);
    throw new Error(userMessage);
  }
}
