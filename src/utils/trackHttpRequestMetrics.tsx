import perf from '@react-native-firebase/perf';
import { AxiosResponse } from 'axios';

export async function trackHttpRequestMetrics(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  response: Response | AxiosResponse,
  methodName: string,
): Promise<void> {
  const trace = perf().newTrace(`${methodName} ${method} ${url}`);
  const metrics = perf().newHttpMetric(url, method);
  await trace.start();
  await metrics.start();

  try {
    if (response) {
      metrics.setHttpResponseCode(response?.status);
      metrics.setResponseContentType(response?.headers.get('Content-Type'));
      metrics.setResponsePayloadSize(response?.headers.get('Content-Length') as unknown as number);
    }
  } finally {
    await trace.stop();
    await metrics.stop();
  }
}
