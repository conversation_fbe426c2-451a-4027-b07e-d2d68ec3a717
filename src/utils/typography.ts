import { I18nManager } from 'react-native';

export const typography = {
  h1: {
      fontSize: 48,
      lineHeight: I18nManager.isRTL ? 67 : 58
  },
  h2: {
      fontSize: 44,
      lineHeight: I18nManager.isRTL ? 62 : 53
  },
  h3: {
      fontSize: 40,
      lineHeight: I18nManager.isRTL ? 56 : 48
  },
  h4: {
      fontSize: 36,
      lineHeight: I18nManager.isRTL ? 50 : 43
  },
  h5: {
      fontSize: 32,
      lineHeight: I18nManager.isRTL ? 45 : 38
  },
  h6: {
      fontSize: 30,
      lineHeight: I18nManager.isRTL ? 42 : 36
  },
  h7: {
      fontSize: 28,
      lineHeight: I18nManager.isRTL ? 39 : 34
  },
  h8: {
      fontSize: 26,
      lineHeight: I18nManager.isRTL ? 37 : 31
  },
  pageHeading: {
      fontSize: 24,
      lineHeight: I18nManager.isRTL ? 34 : 29
  },
  title: {
      fontSize: 22,
      lineHeight: I18nManager.isRTL ? 31 : 26
  },
  subTitle: {
      fontSize: 20,
      lineHeight: I18nManager.isRTL ? 28 : 24
  },
  smallTitle: {
      fontSize: 18,
      lineHeight: I18nManager.isRTL ? 25 : 22
  },
  runningText: {
      fontSize: 16,
      lineHeight: I18nManager.isRTL ? 22 : 19
  },
  smallText: {
      fontSize: 14,
      lineHeight: I18nManager.isRTL ? 20 : 17
  },
  tinyText: {
      fontSize: 12,
      lineHeight: I18nManager.isRTL ? 17 : 14
  },
  tinySmallText: {
      fontSize: 10,
      lineHeight: I18nManager.isRTL ? 28 : 12
  }
};
