{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "skipLibCheck": true,
    "target": "es5",
    /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017','ES2018' or 'ESNEXT'. */ "module": "commonjs",
    /* Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', or 'ESNext'. */ "lib": [
      "es2015"
    ],
    "jsx": "react-native",
    /* Specify JSX code generation: 'preserve', 'react-native', or 'react'. */ "strict": true,
    /* Enable all strict type-checking options. */ "noImplicitAny": false,
    /* Raise error on expressions and declarations with an implied 'any' type. */
    "baseUrl": "./src",
    "paths": {
      "@*": ["*"],
      "@src": ["./src/*"],
      "@__mocks__/*": ["./src/__mocks__/*"],
      "@api/*": ["api/*"],
      "@assets/*": ["assets/*"],
      "@components/*": ["components/*"],
      "@hooks/*": ["hooks/*"],
      "@interfaces/*": ["interfaces/*"],
      "@lib/*": ["lib/*"],
      "@models/*": ["models/*"],
      "@navigation/*": ["navigation/*"],
      "@resources/*": ["resources/*"],
      "@screens/*": ["screens/*"],
      "@theme/*": ["theme/*"],
      "@totara/*": ["totara/*"],
    },
    "allowSyntheticDefaultImports": true,
    /* Allow default imports from modules with no default export. This does not affect code emit, just typechecking. */ "esModuleInterop": true,
    /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */ "resolveJsonModule": true
  },
  "exclude": ["node_modules"],
}
